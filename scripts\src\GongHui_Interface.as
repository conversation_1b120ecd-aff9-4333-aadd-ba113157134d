package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class GongHui_Interface extends MovieClip
   {
      private static var loadData:ClassLoader;
      
      public static var _this:GongHui_Interface;
      
      public static var LoadingYN:Boolean;
      
      public static var gongHui_ID:int;
      
      public static var playerInfo:Object;
      
      public static var ListInfo:Object;
      
      public static var BHCY_List:Array;
      
      public static var senHeListInfo:Object;
      
      private static var init:MovieClip;
      
      private static var senQing:MovieClip;
      
      private static var chuangJian:MovieClip;
      
      private static var senHe:MovieClip;
      
      public static var JieMian:MovieClip;
      
      public static var DaTing:MovieClip;
      
      private static var JuanXian:MovieClip;
      
      private static var RenWu:MovieClip;
      
      private static var HuoYue:MovieClip;
      
      private static var DaTing_mc:MovieClip;
      
      private static var selNum:int;
      
      public static var chuanJian_mc:MovieClip;
      
      private static var loadName:String = "GongHui_v1080.swf";
      
      public static var senhe_page:int = 1;
      
      public static var GHLB_page:int = 1;
      
      private static var gh_Page:int = 1;
      
      private static var gh_PageNum:int = 13;
      
      private static var sq_Page:int = 1;
      
      private static var sq_PageNum:int = 10;
      
      private static var sh_Page:int = 1;
      
      private static var sh_PageNum:int = 10;
      
      private static var gongHuiXXX:Boolean = false;
      
      public static var gongHuiShow:Boolean = false;
      
      public static var gongHuiNumMax:int = 0;
      
      public static var guoLv:Api4399WordCheck = new Api4399WordCheck();
      
      public static var dengJiArr:Array = [0,"80","168","360","568","904","1024","1152","1424","1712","2320","2480","2816","3168","3720","4488","4888","5512","6376","7496","8616","9728","10840","11952","13064","暂未开启该等级"];
      
      public static var dengJiArr2:Array = [0,"20","22","24","26","28","30","32","34","36","38","40","42","44","46","48","50","52","54","56","58","60","62","64","66","68"];
      
      public static var xxArr:Array = [0,0,0];
      
      public function GongHui_Interface()
      {
         super();
         _this = this;
      }
      
      private static function onClose(param1:MouseEvent) : *
      {
         var _loc2_:MovieClip = param1.target.parent;
         _loc2_.visible = false;
         if(_loc2_ == senHe)
         {
            senHeListInfo = null;
            BHCY_List = null;
         }
         if(_loc2_ == init || _loc2_ == JieMian)
         {
            playerInfo = null;
            gongHuiShow = false;
         }
         if(_loc2_ == DaTing)
         {
            GongGaoXX();
            JuanXian.visible = false;
         }
      }
      
      public static function Open() : *
      {
         Main.DuoKai_Fun();
         LoadSkin();
         if(_this)
         {
            _this.visible = true;
            _this.y = 0;
            _this.x = 0;
         }
      }
      
      private static function LoadSkin() : *
      {
         loadData = new ClassLoader(loadName);
         loadData.addEventListener(Event.COMPLETE,onSkinOK);
         LoadInGame.Open(loadData);
      }
      
      private static function onSkinOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("创建申请界面") as Class;
         init = new _loc2_();
         var _loc3_:Class = loadData.getClass("申请加入界面") as Class;
         senQing = new _loc3_();
         var _loc4_:Class = loadData.getClass("创建公会界面") as Class;
         chuangJian = new _loc4_();
         var _loc5_:Class = loadData.getClass("审核界面") as Class;
         senHe = new _loc5_();
         var _loc6_:Class = loadData.getClass("公会界面") as Class;
         JieMian = new _loc6_();
         JieMian.visible = false;
         var _loc7_:Class = loadData.getClass("公会大厅") as Class;
         DaTing = new _loc7_();
         var _loc8_:Class = loadData.getClass("捐献界面") as Class;
         JuanXian = new _loc8_();
         JuanXian.visible = false;
         var _loc9_:Class = loadData.getClass("任务界面") as Class;
         RenWu = new _loc9_();
         var _loc10_:Class = loadData.getClass("活跃度界面") as Class;
         HuoYue = new _loc10_();
         var _loc11_:Class = loadData.getClass("公会活动") as Class;
         GongHuiTiaoZan.HuoDong = new _loc11_();
         var _loc12_:Class = loadData.getClass("公会挑战") as Class;
         GongHuiTiaoZan.TiaoZhan = new _loc12_();
         var _loc13_:Class = loadData.getClass("公会祭坛") as Class;
         GongHui_jiTan.jiTan = new _loc13_();
         var _loc14_:GongHui_Interface = new GongHui_Interface();
         Main._stage.addChild(_loc14_);
         LoadingYN = true;
         TiaoShi.txtShow("加载图形完成");
         Api_4399_GongHui.SelUserInfo();
         TiaoShi.txtShow("加载玩家信息");
      }
      
      public static function PlayerInfoOk(param1:Object) : *
      {
         playerInfo = param1;
         TiaoShi.txtShow("加载玩家信息完成");
         if(param1.unionInfo)
         {
            gongHui_ID = param1.unionInfo.id;
            TiaoShi.txtShow("公会ID" + gongHui_ID);
            if(gongHuiShow || JieMian.visible)
            {
               TiaoShi.txtShow("公会界面 刷新显示");
               ShowExp();
               gongHuiShow = false;
            }
            else
            {
               TiaoShi.txtShow("公会界面 打开");
               JieMianOpen();
            }
         }
         else
         {
            TiaoShi.txtShow("无公会");
            gongHuiXXX = true;
            InitOpen();
         }
      }
      
      public static function InfoXX() : *
      {
         var _loc1_:String = GetInfo();
         var _loc2_:Array = (playerInfo.member.extra as String).split("$");
         if(playerInfo.member.extra != _loc1_)
         {
            Api_4399_GongHui.InfoXX(_loc1_);
            TiaoShi.txtShow("修改个人信息" + _loc1_);
            playerInfo.member.extra = _loc1_;
         }
      }
      
      public static function GetInfo() : String
      {
         var _loc1_:String = "v1$" + Main.player1.getLevel() + "$" + Main.player1.GetZY() + "$" + Main.logName2;
         TiaoShi.txtShow("获取个人信息" + _loc1_);
         return _loc1_;
      }
      
      public static function ListOk(param1:Object = null) : *
      {
         if(param1)
         {
            ListInfo = param1;
            gongHuiNumMax = param1.rowCount;
            TiaoShi.txtShow("加载公会列表完成");
            ListInfoPaiXu();
            SenQingOpen();
         }
         else
         {
            TiaoShi.txtShow("加载公会列表失败");
         }
      }
      
      public static function ListInfoPaiXu() : *
      {
         var _loc1_:Array = ListInfo.unionList;
         var _loc2_:int = 0;
         while(_loc2_ < _loc1_.length)
         {
            TiaoShi.txtShow(_loc1_[_loc2_].title + "," + _loc1_[_loc2_].level + "," + _loc1_[_loc2_].count);
            _loc2_++;
         }
         _loc1_.sortOn(["level","count"],Array.DESCENDING | Array.NUMERIC);
      }
      
      public static function SenHeListOk(param1:Object = null) : *
      {
         if(param1)
         {
            senHeListInfo = param1;
            TiaoShi.txtShow("加载待审核列表完成");
            SenHeOpen();
         }
         else
         {
            TiaoShi.txtShow("加载待审核列表失败");
         }
      }
      
      public static function CengYuanListOk(param1:Array) : *
      {
         if(param1)
         {
            BHCY_List = param1;
            TiaoShi.txtShow("加载帮会成员列表完成");
            daTing_Open();
         }
         else
         {
            TiaoShi.txtShow("加载帮会成员列表失败");
         }
      }
      
      public static function JieMianOpen() : *
      {
         gongHuiXXX = false;
         InfoXX();
         GongHuiOpen();
      }
      
      public static function GongHuiOpen() : *
      {
         var _loc1_:Array = [157,158,159,160,161,162,163];
         Api_4399_GongHui.getNum(_loc1_);
         _this.x = _this.y = 0;
         JieMian.visible = true;
         JieMian.x = JieMian.y = 0;
         _this.addChild(JieMian);
         if(playerInfo.unionInfo.uId != playerInfo.member.uId || playerInfo.member.roleId == "10")
         {
            JieMian.senHe_btn.visible = false;
         }
         JieMian.senHe_btn.addEventListener(MouseEvent.CLICK,SenHeOpen);
         JieMian.daTing_btn.addEventListener(MouseEvent.CLICK,daTing_Open);
         JieMian.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         JieMian.renWu_btn.addEventListener(MouseEvent.CLICK,RenWu_Open);
         JieMian.huoYue_btn.addEventListener(MouseEvent.CLICK,HuoYue_Open);
         JieMian.qt_btn.addEventListener(MouseEvent.CLICK,QiTa_Open);
         JieMian.huoDong_btn.addEventListener(MouseEvent.CLICK,GongHuiTiaoZan.Open_HD);
         JieMian.jiTan_btn.addEventListener(MouseEvent.CLICK,GongHui_jiTan.Open);
      }
      
      public static function QiTa_Open(param1:*) : *
      {
         JiaRu_Open();
      }
      
      public static function InitOpen() : *
      {
         _this.x = _this.y = 0;
         _this.addChild(init);
         init.visible = true;
         init.x = init.y = 0;
         init.jiaRu_btn.addEventListener(MouseEvent.CLICK,JiaRu_Open);
         init.chuanjian_btn.addEventListener(MouseEvent.CLICK,Chuanjian_Open);
         init.close_btn.addEventListener(MouseEvent.CLICK,onClose);
      }
      
      public static function SenQingOpen() : *
      {
         var _loc2_:Object = null;
         var _loc4_:int = 0;
         TiaoShi.txtShow("公会列表界面 SenQingOpen()");
         init.visible = false;
         _this.x = _this.y = 0;
         senQing.visible = true;
         senQing.x = senQing.y = 0;
         _this.addChild(senQing);
         var _loc1_:Array = ListInfo.unionList;
         var _loc3_:int = 1;
         while(_loc3_ <= sq_PageNum)
         {
            _loc4_ = (sq_Page - 1) * sq_PageNum + _loc3_ - 1;
            _loc2_ = senQing["num_" + _loc3_];
            _loc2_._txt1.text = _loc2_._txt2.text = _loc2_._txt3.text = _loc2_._txt4.text = _loc2_._txt5.text = "";
            _loc2_._btn6.visible = _loc2_._mc7.visible = false;
            if(_loc1_[_loc4_])
            {
               _loc2_._txt1.text = _loc4_ + 1;
               _loc2_._txt2.text = _loc1_[_loc4_].title;
               _loc2_._txt3.text = _loc1_[_loc4_].level;
               _loc2_._txt4.text = _loc1_[_loc4_].username;
               _loc2_._txt5.text = _loc1_[_loc4_].count;
               if(gongHuiXXX)
               {
                  _loc2_._btn6.addEventListener(MouseEvent.CLICK,senQingFun);
                  _loc2_._btn6.visible = _loc2_._mc7.visible = true;
                  if(_loc1_[_loc4_].noBtn)
                  {
                     _loc2_._btn6.visible = false;
                  }
               }
            }
            _loc3_++;
         }
         senQing.page_txt.text = sq_Page + "/" + int(_loc1_.length / sq_PageNum + 1);
         TiaoShi.txtShow("帮会总数 " + ListInfo.rowCount);
         senQing.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         senQing.back_btn.addEventListener(MouseEvent.CLICK,SQ_onBack);
         senQing.next_btn.addEventListener(MouseEvent.CLICK,SQ_onNext);
      }
      
      public static function JiaRu_Open(param1:* = null) : *
      {
         if(!ListInfo)
         {
            Api_4399_GongHui.getList();
         }
         else
         {
            SenQingOpen();
            init.visible = false;
         }
      }
      
      public static function SQ_onBack(param1:*) : *
      {
         if(sq_Page > 1)
         {
            --sq_Page;
            SenQingOpen();
         }
      }
      
      public static function SQ_onNext(param1:*) : *
      {
         var _loc2_:int = ListInfo.unionList.length / sq_PageNum + 1;
         if(sq_Page < _loc2_)
         {
            ++sq_Page;
            SenQingOpen();
         }
      }
      
      public static function senQingFun(param1:MouseEvent) : *
      {
         var _loc2_:String = (param1.target as SimpleButton).parent.name;
         var _loc3_:int = int(_loc2_.substr(4,3)) + (sq_Page - 1) * sq_PageNum;
         TiaoShi.txtShow("申请加入公会请求" + _loc2_ + "-" + _loc3_);
         var _loc4_:Array = ListInfo.unionList;
         var _loc5_:int = int(_loc4_[_loc3_ - 1].unionId);
         var _loc6_:String = GetInfo();
         TiaoShi.txtShow("申请加入公会ID" + _loc5_ + ",描述:" + _loc6_);
         (param1.target as SimpleButton).visible = false;
         _loc4_[_loc3_ - 1].noBtn = true;
         Api_4399_GongHui.SenQing(_loc5_,_loc6_);
      }
      
      public static function daTing_Open(param1:* = null) : *
      {
         var _loc2_:Object = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc8_:String = null;
         if(!BHCY_List)
         {
            TiaoShi.txtShow("查询成员列表gongHui_ID = " + gongHui_ID);
            Api_4399_GongHui.getBHCY_List(gongHui_ID);
         }
         else
         {
            TiaoShi.txtShow("打开公会大厅 BHCY_List.length = " + BHCY_List.length);
            DaTing._tiShi_mc.visible = false;
            DaTing.No_Mc.visible = true;
            DaTing_mc = DaTing.info_mc;
            if(playerInfo.unionInfo.uId == playerInfo.member.uId || playerInfo.member.roleId == "10")
            {
               DaTing_mc = DaTing.info_mc2;
               TiaoShi.txtShow("DaTing.info_mc2");
               if(playerInfo.unionInfo.uId == playerInfo.member.uId)
               {
                  playerInfo.member.roleId = "11";
                  DaTing_mc = DaTing.info_mc3;
                  TiaoShi.txtShow("DaTing.info_mc3");
               }
               DaTing.No_Mc.visible = false;
            }
            DaTing_mc["X1_btn"].addEventListener(MouseEvent.CLICK,X1_fun);
            if(DaTing_mc == DaTing.info_mc2)
            {
               DaTing_mc["X2_btn"].addEventListener(MouseEvent.CLICK,X2_fun);
            }
            else if(DaTing_mc == DaTing.info_mc3)
            {
               TiaoShi.txtShow("DaTing.info_mc3 x2");
               DaTing_mc["X2_btn"].addEventListener(MouseEvent.CLICK,X2_fun);
               DaTing_mc["X3_btn"].addEventListener(MouseEvent.CLICK,X3_fun);
               DaTing_mc["X4_btn"].addEventListener(MouseEvent.CLICK,X4_fun);
            }
            DaTing_mc.x_mc.addEventListener(MouseEvent.MOUSE_OUT,DaTing_mc_Out);
            DaTing_mc.x = DaTing_mc.y = -5000;
            _loc3_ = 1;
            while(_loc3_ <= gh_PageNum)
            {
               _loc5_ = (gh_Page - 1) * gh_PageNum + _loc3_ - 1;
               _loc2_ = DaTing["num_" + _loc3_];
               _loc2_.removeEventListener(MouseEvent.MOUSE_OVER,MouseIn);
               _loc2_.removeEventListener(MouseEvent.ROLL_OUT,MouseOut);
               _loc2_.removeEventListener(MouseEvent.CLICK,MouseCLICK);
               _loc2_._txt1.text = _loc2_._txt2.text = _loc2_._txt3.text = _loc2_._txt4.text = _loc2_._txt5.text = "";
               _loc2_.sel_mc.visible = false;
               _loc2_.mouseChildren = false;
               if(BHCY_List[_loc5_])
               {
                  TiaoShi.txtShow("BHCY_List[numX].extra = " + BHCY_List[_loc5_].extra);
                  _loc6_ = (BHCY_List[_loc5_].extra as String).split("$");
                  _loc2_._txt1.text = BHCY_List[_loc5_].nickName;
                  if(_loc6_[1])
                  {
                     _loc2_._txt2.text = _loc6_[1];
                  }
                  if(_loc6_[2])
                  {
                     _loc2_._txt3.text = _loc6_[2];
                  }
                  _loc2_._txt4.text = GetQX_str(BHCY_List[_loc5_]);
                  _loc7_ = int(BHCY_List[_loc5_].contribution) / 10;
                  _loc2_._txt5.text = _loc7_;
                  _loc2_.numX = _loc5_;
                  _loc2_.addEventListener(MouseEvent.MOUSE_OVER,MouseIn);
                  _loc2_.addEventListener(MouseEvent.ROLL_OUT,MouseOut);
                  _loc2_.addEventListener(MouseEvent.CLICK,MouseCLICK);
               }
               _loc3_++;
            }
            _this.addChild(DaTing);
            if(JuanXian.visible)
            {
               _this.addChild(JuanXian);
               TiaoShi.txtShow("JuanXian~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~!!!!");
            }
            DaTing.visible = true;
            DaTing.name_txt.text = playerInfo.unionInfo.title;
            DaTing.lv_txt.text = playerInfo.unionInfo.level;
            DaTing.renShu_txt.text = BHCY_List.length + "/" + dengJiArr2[playerInfo.unionInfo.level];
            _loc4_ = int(playerInfo.unionInfo.experience) / 10;
            DaTing.exp_txt.text = _loc4_ + "/" + dengJiArr[playerInfo.unionInfo.level];
            DaTing.gongGao_txt.text = playerInfo.unionInfo.extra;
            DaTing.close_btn.addEventListener(MouseEvent.CLICK,onClose);
            DaTing.back_btn.addEventListener(MouseEvent.CLICK,dt_onBack);
            DaTing.next_btn.addEventListener(MouseEvent.CLICK,dt_onNext);
            DaTing.juanXian_btn.addEventListener(MouseEvent.CLICK,JuanXian_Open);
            DaTing.tuiChu_btn.addEventListener(MouseEvent.CLICK,tuiChu_Fun);
            DaTing.jieSan_btn.addEventListener(MouseEvent.CLICK,jieSan_Fun);
            DaTing.jieSan_btn2.addEventListener(MouseEvent.CLICK,QuXiaoJieSan);
            DaTing.tuiChu_btn.visible = DaTing.jieSan_btn.visible = DaTing.jieSan_btn2.visible = false;
            if(playerInfo.unionInfo.uId == playerInfo.member.uId)
            {
               if(playerInfo.unionInfo.dissolveDate != 0)
               {
                  DaTing.jieSan_btn2.visible = true;
               }
               else
               {
                  DaTing.jieSan_btn.visible = true;
               }
            }
            else
            {
               DaTing.tuiChu_btn.visible = true;
            }
            DaTing.jieSan_time.text = "";
            if(playerInfo.unionInfo.dissolveDate != 0)
            {
               _loc8_ = transDate(playerInfo.unionInfo.dissolveDate);
               DaTing.jieSan_time.text = "此公会将于" + _loc8_ + "解散";
            }
            DaTing.page_txt.text = gh_Page + "/" + int(BHCY_List.length / gh_PageNum + 1);
         }
      }
      
      public static function transDate(param1:Number) : String
      {
         date = new Date(param1 * 1000);
         return date.fullYear + "." + (date.month + 1) + "." + date.date;
      }
      
      public static function QuXiaoJieSan(param1:*) : *
      {
         Api_4399_GongHui.JieSan(0);
         playerInfo.unionInfo.dissolveDate = 0;
         daTing_Open();
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"取消解散公会");
      }
      
      public static function jieSan_Fun(param1:*) : *
      {
         DaTing._tiShi_mc.visible = true;
         DaTing._tiShi_mc.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         DaTing._tiShi_mc.ok_btn.addEventListener(MouseEvent.CLICK,jieSan_OK);
         DaTing._tiShi_mc._txt.text = "是否解散公会, 确认后3天生效, 此期间可以取消";
      }
      
      public static function jieSan_OK(param1:*) : *
      {
         Api_4399_GongHui.JieSan();
         gongHuiShow = true;
         DaTing.tuiChu_btn.visible = DaTing.jieSan_btn.visible = DaTing.jieSan_btn2.visible = false;
         DaTing._tiShi_mc.visible = false;
         DaTing.jieSan_time.text = "此公会将于3天后解散";
         var _loc2_:uint = uint(setTimeout(Api_4399_GongHui.SelUserInfo,2000));
      }
      
      public static function GetQX_str(param1:Object) : String
      {
         if(param1.roleId == "11" || playerInfo.unionInfo.uId == param1.uId)
         {
            TiaoShi.txtShow("GetQX_str 会长");
            return "会长";
         }
         if(param1.roleId == "10")
         {
            TiaoShi.txtShow("GetQX_str 副会长");
            return "副会长";
         }
         TiaoShi.txtShow("GetQX_str 成员");
         return "成员";
      }
      
      public static function dt_onBack(param1:*) : *
      {
         if(gh_Page > 1)
         {
            --gh_Page;
            daTing_Open();
         }
      }
      
      public static function dt_onNext(param1:*) : *
      {
         var _loc2_:int = BHCY_List.length / gh_PageNum + 1;
         if(gh_Page < _loc2_)
         {
            ++gh_Page;
            daTing_Open();
         }
      }
      
      public static function MouseIn(param1:MouseEvent) : *
      {
         var _loc4_:MovieClip = null;
         var _loc2_:int = 1;
         while(_loc2_ <= gh_PageNum)
         {
            _loc4_ = DaTing["num_" + _loc2_];
            _loc4_.sel_mc.visible = false;
            _loc2_++;
         }
         (param1.target as MovieClip).sel_mc.visible = true;
         var _loc3_:int = int((param1.target.name as String).substr(4,3));
         selNum = (gh_Page - 1) * gh_PageNum + _loc3_ - 1;
      }
      
      public static function MouseOut(param1:*) : *
      {
         var _loc3_:MovieClip = null;
         var _loc2_:int = 1;
         while(_loc2_ <= gh_PageNum)
         {
            _loc3_ = DaTing["num_" + _loc2_];
            _loc3_.sel_mc.visible = false;
            _loc2_++;
         }
      }
      
      public static function MouseCLICK(param1:*) : *
      {
         DaTing_mc.x = DaTing.mouseX - 50;
         DaTing_mc.y = DaTing.mouseY - 50;
         DaTing_mc.numX = param1.target.numX;
         TiaoShi.txtShow("e.target.numX:" + param1.target.numX + ",选中:" + selNum);
         TiaoShi.txtShow("DaTing_mc.x:" + DaTing_mc.x + ", DaTing_mc.y:" + DaTing_mc.y);
      }
      
      public static function DaTing_mc_Out(param1:MouseEvent) : *
      {
         DaTing_mc.y = -5000;
         DaTing_mc.x = -5000;
         TiaoShi.txtShow("关闭菜单");
      }
      
      public static function X1_fun(param1:MouseEvent) : *
      {
         DaTing_mc.y = -5000;
         DaTing_mc.x = -5000;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"功能暂未开放");
      }
      
      public static function X2_fun(param1:MouseEvent) : *
      {
         if(BHCY_List[selNum].uId == playerInfo.member.uId)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"无法对自己执行该操作");
            return;
         }
         var _loc2_:int = int(BHCY_List[selNum].roleId);
         var _loc3_:int = int(playerInfo.member.roleId);
         if(_loc3_ <= _loc2_ || playerInfo.unionInfo.uId == BHCY_List[selNum].uId)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"无法执行该操作");
            return;
         }
         DaTing._tiShi_mc.visible = true;
         DaTing._tiShi_mc.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         DaTing._tiShi_mc.ok_btn.addEventListener(MouseEvent.CLICK,X2_Ok);
         DaTing._tiShi_mc._txt.text = "是否将" + BHCY_List[selNum].nickName + "逐出公会";
      }
      
      public static function X2_Ok(param1:MouseEvent) : *
      {
         Api_4399_GongHui.TiRen(BHCY_List[selNum].uId,BHCY_List[selNum].index);
         TiaoShi.txtShow("踢人 uId:" + BHCY_List[selNum].uId + ", index:" + BHCY_List[selNum].index);
         BHCY_List.splice(selNum,1);
         daTing_Open();
         DaTing_mc.y = -5000;
         DaTing_mc.x = -5000;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"移除成员操作成功");
         DaTing._tiShi_mc.visible = false;
         DaTing._tiShi_mc.ok_btn.removeEventListener(MouseEvent.CLICK,X2_Ok);
      }
      
      public static function tuiChu_Fun(param1:*) : *
      {
         DaTing._tiShi_mc.visible = true;
         DaTing._tiShi_mc.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         DaTing._tiShi_mc.ok_btn.addEventListener(MouseEvent.CLICK,tuiChu_Ok);
         DaTing._tiShi_mc._txt.text = "是否退出公会, 退出后24小时内无法加入公会";
      }
      
      public static function tuiChu_Ok(param1:MouseEvent) : *
      {
         Api_4399_GongHui.TuiChu();
         DaTing.visible = false;
         JieMian.visible = false;
         playerInfo = null;
         DaTing._tiShi_mc.ok_btn.removeEventListener(MouseEvent.CLICK,tuiChu_Ok);
      }
      
      public static function X3_fun(param1:MouseEvent) : *
      {
         if(BHCY_List[selNum].uId == playerInfo.member.uId)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"无法对自己执行该操作");
            return;
         }
         if(BHCY_List[selNum].roleId == "10")
         {
            Api_4399_GongHui.set_QX(BHCY_List[selNum].uId,BHCY_List[selNum].index,0);
            BHCY_List[selNum].roleId = "0";
            daTing_Open();
            TiaoShi.txtShow("任命 uId:" + BHCY_List[selNum].uId + ", index:" + BHCY_List[selNum].index);
         }
         else
         {
            Api_4399_GongHui.set_QX(BHCY_List[selNum].uId,BHCY_List[selNum].index,10);
            BHCY_List[selNum].roleId = "10";
            daTing_Open();
         }
         DaTing_mc.y = -5000;
         DaTing_mc.x = -5000;
      }
      
      public static function X4_fun(param1:MouseEvent) : *
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"转让公会功能暂未开放");
      }
      
      public static function GongGaoXX(param1:* = null) : *
      {
         if(Boolean(playerInfo) && playerInfo.unionInfo.extra != DaTing.gongGao_txt.text)
         {
            TiaoShi.txtShow("修改公会公告!!!");
            Api_4399_GongHui.GH_InfoXX(DaTing.gongGao_txt.text);
            playerInfo.unionInfo.extra = DaTing.gongGao_txt.text;
         }
      }
      
      public static function SenHeOpen(param1:* = null) : *
      {
         var _loc2_:Array = null;
         var _loc3_:Object = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:Array = null;
         if(!senHeListInfo)
         {
            Api_4399_GongHui.getSenHe();
         }
         else
         {
            TiaoShi.txtShow("审核界面");
            _this.addChild(senHe);
            senHe.visible = true;
            senHe.close_btn.addEventListener(MouseEvent.CLICK,onClose);
            senHe.back_btn.addEventListener(MouseEvent.CLICK,SH_onBack);
            senHe.next_btn.addEventListener(MouseEvent.CLICK,SH_onNext);
            _loc2_ = senHeListInfo.applyList;
            TiaoShi.txtShow("信息数:" + _loc2_.length);
            _loc4_ = 1;
            while(_loc4_ <= sh_PageNum)
            {
               _loc5_ = (sh_Page - 1) * sh_PageNum + _loc4_ - 1;
               _loc3_ = senHe["num_" + _loc4_];
               _loc3_._txt1.text = _loc3_._txt2.text = _loc3_._txt3.text = "";
               _loc3_._btn4.visible = _loc3_._btn5.visible = false;
               if(_loc2_[_loc5_])
               {
                  _loc3_._txt1.text = _loc3_._txt2.text = _loc3_._txt3.text = "暂无数据";
                  _loc6_ = _loc2_[_loc5_].extra.split("$");
                  if(_loc6_[3])
                  {
                     _loc3_._txt1.text = _loc6_[3];
                  }
                  if(_loc6_[1])
                  {
                     _loc3_._txt2.text = _loc6_[1];
                  }
                  if(_loc6_[2])
                  {
                     _loc3_._txt3.text = _loc6_[2];
                  }
                  _loc3_._btn4.visible = true;
                  _loc3_._btn5.visible = true;
                  _loc3_._btn4.addEventListener(MouseEvent.CLICK,SenHeFun_OK);
                  _loc3_._btn5.addEventListener(MouseEvent.CLICK,SenHeFun_No);
                  _loc3_.alpha = 1;
               }
               _loc4_++;
            }
            senHe.page_txt.text = sh_Page + "/" + int(_loc2_.length / sh_PageNum + 1);
         }
      }
      
      public static function SenHeFun_OK(param1:*) : *
      {
         var _loc2_:MovieClip = (param1.target as SimpleButton).parent;
         var _loc3_:String = _loc2_.name;
         var _loc4_:int = int(_loc3_.substr(4,3));
         _loc4_ += (sh_Page - 1) * sh_PageNum;
         TiaoShi.txtShow("审核 同意加入公会请求" + _loc3_ + "-" + _loc4_);
         var _loc5_:Object = senHeListInfo.applyList[_loc4_ - 1];
         Api_4399_GongHui.SenHe(_loc5_.uId,_loc5_.index);
         senHeListInfo.applyList.splice(_loc4_ - 1,1);
         SenHeOpen();
      }
      
      public static function SenHeFun_No(param1:*) : *
      {
         var _loc2_:MovieClip = (param1.target as SimpleButton).parent;
         var _loc3_:String = _loc2_.name;
         var _loc4_:int = int(_loc3_.substr(4,3));
         _loc4_ += (sh_Page - 1) * sh_PageNum;
         TiaoShi.txtShow("审核 拒绝加入公会请求" + _loc3_ + "-" + _loc4_);
         var _loc5_:Object = senHeListInfo.applyList[_loc4_ - 1];
         Api_4399_GongHui.SenHe(_loc5_.uId,_loc5_.index,0);
         senHeListInfo.applyList.splice(_loc4_ - 1,1);
         SenHeOpen();
      }
      
      public static function SH_onBack(param1:*) : *
      {
         if(sh_Page > 1)
         {
            --sh_Page;
            SenHeOpen();
         }
      }
      
      public static function SH_onNext(param1:*) : *
      {
         var _loc2_:int = senHeListInfo.applyList.length / sh_PageNum + 1;
         if(sh_Page < _loc2_)
         {
            ++sh_Page;
            SenHeOpen();
         }
      }
      
      public static function Chuanjian_Open(param1:*) : *
      {
         init.visible = false;
         chuangJian.visible = true;
         _this.addChild(chuangJian);
         chuangJian.chuangJian_btn.addEventListener(MouseEvent.CLICK,Chuanjian_Fun);
         chuangJian.close_btn.addEventListener(MouseEvent.CLICK,onClose);
      }
      
      public static function Chuanjian_Fun(param1:MouseEvent) : *
      {
         chuanJian_mc = param1.target.parent;
         var _loc2_:String = chuanJian_mc.name_txt.text;
         guoLv.checkWord(_loc2_,OKOKOk);
         chuangJian.chuangJian_btn.removeEventListener(MouseEvent.CLICK,Chuanjian_Fun);
      }
      
      public static function OKOKOk(param1:int) : void
      {
         var _loc2_:String = null;
         chuangJian.chuangJian_btn.addEventListener(MouseEvent.CLICK,Chuanjian_Fun);
         if(param1 == 0)
         {
            _loc2_ = chuanJian_mc.name_txt.text;
            Api_4399_GongHui.AddGongHui(_loc2_,GetInfo());
            chuangJian.visible = false;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2," 公会名称不能包含\'敏感词\' ");
         }
      }
      
      public static function JuanXian_Open(param1:* = null) : *
      {
         JuanXian.visible = true;
         _this.addChild(JuanXian);
         JuanXian._txt1.text = playerInfo.unionInfo.level;
         var _loc2_:int = int(playerInfo.unionInfo.experience) / 10;
         JuanXian._txt2.text = _loc2_ + "/" + dengJiArr[playerInfo.unionInfo.level];
         JuanXian._txt3.text = "消耗:" + GetJinBi() + "金币";
         JuanXian.J_btn1.addEventListener(MouseEvent.CLICK,JinBi_fun);
         JuanXian.J_btn2.addEventListener(MouseEvent.CLICK,DianQuan_fun);
         JuanXian.close_btn.addEventListener(MouseEvent.CLICK,onClose);
      }
      
      public static function JinBi_fun(param1:* = null) : *
      {
         if(GongHuiRenWu.rwArr2[4])
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币捐献每天只能执行1次");
            return;
         }
         if(Main.player1.getGold() >= GetJinBi())
         {
            Main.player1.payGold(GetJinBi());
            Api_4399_GongHui.setRW(68);
            Api_4399_GongHui.upNum(158);
            JuanXian_Open();
            Main.Save(false);
            GongHuiRenWu.rwArr2[4] = true;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币捐献成功");
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
         }
      }
      
      private static function GetJinBi() : int
      {
         var _loc1_:int = 2000;
         if(Main.player1.level.getValue() >= 80)
         {
            _loc1_ = 80000;
         }
         else if(Main.player1.level.getValue() >= 70)
         {
            _loc1_ = 50000;
         }
         else if(Main.player1.level.getValue() >= 60)
         {
            _loc1_ = 20000;
         }
         else if(Main.player1.level.getValue() >= 40)
         {
            _loc1_ = 15000;
         }
         else if(Main.player1.level.getValue() >= 20)
         {
            _loc1_ = 8000;
         }
         else if(Main.player1.level.getValue() >= 10)
         {
            _loc1_ = 5000;
         }
         return _loc1_;
      }
      
      public static function DianQuan_fun(param1:* = null) : *
      {
         if(Shop4399.moneyAll.getValue() >= 10)
         {
            Api_4399_GongHui.DuiHuanGX(10);
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点券不足");
         }
      }
      
      public static function RenWu_Open(param1:* = null) : *
      {
         var _loc3_:MovieClip = null;
         var _loc4_:* = undefined;
         RenWu.visible = true;
         _this.addChild(RenWu);
         GongHuiRenWu.SX(Main.player1.getLevel());
         GongHuiRenWu.rwArr[4] = true;
         RenWu.all_mc.visible = false;
         var _loc2_:int = 1;
         while(_loc2_ <= 3)
         {
            _loc3_ = RenWu["rw_num" + _loc2_];
            if(GongHuiRenWu.rwArr[_loc2_][4])
            {
               _loc3_.visible = false;
            }
            else
            {
               _loc4_ = GongHuiRenWu.isType(GongHuiRenWu.rwArr[_loc2_][0]);
               _loc3_.t1.text = _loc4_[2];
               _loc3_.t2.text = _loc4_[8];
               _loc3_.t3.text = _loc4_[9];
               if(_loc4_[10] == 1)
               {
                  _loc3_.t4.text = GongHuiRenWu.rwArr[_loc2_][1] + "/" + _loc4_[5];
               }
               else
               {
                  _loc3_.t4.text = GongHuiRenWu.rwArr[_loc2_][2] + "/" + _loc4_[7];
               }
               _loc3_.lingQu_btn.addEventListener(MouseEvent.CLICK,lingQu);
            }
            _loc2_++;
         }
         if(!RenWu["rw_num1"].visible && !RenWu["rw_num2"].visible && !RenWu["rw_num3"].visible)
         {
            RenWu.all_mc.visible = true;
         }
         RenWu.close_btn.addEventListener(MouseEvent.CLICK,onClose);
      }
      
      public static function lingQu(param1:MouseEvent = null) : *
      {
         var _loc2_:int = int((param1.target.parent.name as String).substr(6,1));
         TiaoShi.txtShow("领取奖励 = " + _loc2_ + ", rwArr.length = " + GongHuiRenWu.rwArr.length);
         var _loc3_:Array = GongHuiRenWu.isType(GongHuiRenWu.rwArr[_loc2_][0]);
         if(Boolean(GongHuiRenWu.rwArr[_loc2_][3]) && !GongHuiRenWu.rwArr[_loc2_][4])
         {
            Main.player1.addGold(_loc3_[8]);
            Main.player1.setEXP(Main.player1.getEXP() + _loc3_[9]);
            if(Main.P1P2)
            {
               Main.player2.setEXP(Main.player2.getEXP() + _loc3_[9]);
            }
            GongHuiRenWu.rwArr[_loc2_][4] = true;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
            MCgo(param1.target.parent);
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"任务尚未完成");
         }
      }
      
      public static function HuoYue_Show() : *
      {
         if(HuoYue)
         {
            HuoYue.t1.text = xxArr[0] + "/10";
            HuoYue.t2.text = xxArr[0] + "/20";
            HuoYue.t3.text = xxArr[1] + "/5";
            HuoYue.t4.text = xxArr[2] + "/3";
         }
      }
      
      public static function HuoYue_Open(param1:* = null) : *
      {
         HuoYue.visible = true;
         _this.addChild(HuoYue);
         HuoYue.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         HuoYue.t1.text = xxArr[0] + "/10";
         HuoYue.t2.text = xxArr[0] + "/20";
         HuoYue.t3.text = xxArr[1] + "/5";
         HuoYue.t4.text = xxArr[2] + "/3";
         var _loc2_:int = 0;
         if(xxArr[0] >= 10)
         {
            HuoYue.mc1.gotoAndStop(2);
            _loc2_ += 10;
         }
         if(xxArr[0] >= 20)
         {
            HuoYue.mc2.gotoAndStop(2);
            _loc2_ += 10;
         }
         if(xxArr[1] >= 5)
         {
            HuoYue.mc3.gotoAndStop(2);
            _loc2_ += 10;
         }
         if(xxArr[2] >= 3)
         {
            HuoYue.mc4.gotoAndStop(2);
            _loc2_ += 20;
         }
         HuoYue.num_txt.text = _loc2_;
         HuoYue.jinDu.gotoAndStop(_loc2_ / 10 + 1);
         HuoYue.LQ_1.visible = HuoYue.LQ_2.visible = HuoYue.LQ_3.visible = false;
         HuoYue.ok1.visible = HuoYue.ok2.visible = HuoYue.ok3.visible = false;
         HuoYue.no_1.addEventListener(MouseEvent.CLICK,onLingQuNo);
         HuoYue.no_2.addEventListener(MouseEvent.CLICK,onLingQuNo);
         HuoYue.no_3.addEventListener(MouseEvent.CLICK,onLingQuNo);
         if(_loc2_ >= 20)
         {
            if(GongHuiRenWu.rwArr2[1])
            {
               HuoYue.ok1.visible = true;
            }
            else
            {
               HuoYue.LQ_1.visible = true;
               HuoYue.LQ_1.addEventListener(MouseEvent.CLICK,onLingQu);
            }
         }
         if(_loc2_ >= 30)
         {
            if(GongHuiRenWu.rwArr2[2])
            {
               HuoYue.ok2.visible = true;
            }
            else
            {
               HuoYue.LQ_2.visible = true;
               HuoYue.LQ_2.addEventListener(MouseEvent.CLICK,onLingQu);
            }
         }
         if(_loc2_ >= 50)
         {
            if(GongHuiRenWu.rwArr2[3])
            {
               HuoYue.ok3.visible = true;
            }
            else
            {
               HuoYue.LQ_3.visible = true;
               HuoYue.LQ_3.addEventListener(MouseEvent.CLICK,onLingQu);
            }
         }
      }
      
      public static function onLingQu(param1:MouseEvent = null) : *
      {
         var _loc2_:int = int(param1.target.name.substr(3,1));
         TiaoShi.txtShow("onLingQu " + _loc2_);
         if(_loc2_ == 1)
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() + 100);
            if(Main.P1P2)
            {
               Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() + 100);
            }
            Main.Save(false);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得100击杀点");
            GongHuiRenWu.rwArr2[_loc2_] = true;
         }
         else if(_loc2_ == 2)
         {
            if(Main.player1.getBag().backSuppliesBagNum() >= 6)
            {
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得野性之血5个,复活药1个");
               GongHuiRenWu.rwArr2[_loc2_] = true;
               Main.Save(false);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(_loc2_ == 3)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63385));
               GongHuiRenWu.rwArr2[_loc2_] = true;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得公会星灵礼包1个");
               Main.Save(false);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         HuoYue_Open();
      }
      
      public static function onLingQuNo(param1:MouseEvent = null) : *
      {
         NewMC.Open("文字提示",Main._stage,480,350,30,0,true,1,"活跃度未达成");
      }
      
      public static function ShowExp() : *
      {
         JuanXian._txt1.text = DaTing.lv_txt.text = playerInfo.unionInfo.level;
         var _loc1_:int = int(playerInfo.unionInfo.experience) / 10;
         JuanXian._txt2.text = DaTing.exp_txt.text = _loc1_ + "/" + dengJiArr[playerInfo.unionInfo.level];
         if(DaTing.visible && JieMian.visible)
         {
            daTing_Open();
            TiaoShi.txtShow("ShowExp~~~~~~~~打开大厅");
         }
      }
      
      public static function MCgo(param1:MovieClip) : *
      {
         param1.addEventListener(Event.ENTER_FRAME,MCgoXXX);
      }
      
      public static function MCgoXXX(param1:Event) : *
      {
         var _loc2_:MovieClip = param1.target;
         _loc2_.y -= 12;
         _loc2_.alpha -= 0.1;
         if(_loc2_.alpha <= 0)
         {
            _loc2_.addEventListener(Event.ENTER_FRAME,MCgoXXX);
         }
      }
   }
}

