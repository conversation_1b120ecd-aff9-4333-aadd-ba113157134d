package com.hotpoint.braveManIII.views.strPanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.utils.*;
   import src.*;
   import src.tool.*;
   
   public class StrPanel extends MovieClip
   {
      public static var data:PlayerData;
      
      public static var _instance:StrPanel;
      
      private static var loadData:ClassLoader;
      
      private static var open_yn:Boolean;
      
      public static var state:Number = 0;
      
      public static var stateTow:Number = 0;
      
      private static var loadName:String = "Panel_Str_v1200.swf";
      
      private var tooltip:ItemsTooltip;
      
      private var ps:Number = 1;
      
      private var oldStateTow:Number = 0;
      
      private var strBag:StrBag;
      
      private var strSlot:StrSlot;
      
      private var pro:VT;
      
      private var ranNum:VT;
      
      private var times:uint = 0;
      
      private var timer:Timer;
      
      private var stopTime:uint = 20;
      
      private var bo:Boolean = true;
      
      public var mast_hc:*;
      
      public var s1_mc:*;
      
      public var s2_mc:*;
      
      public var strengthen:*;
      
      public var s_0:*;
      
      public var s_1:*;
      
      public var s_2:*;
      
      public var gold_name:*;
      
      public var strBtn:*;
      
      public var mc_ss:*;
      
      public var p_0:*;
      
      public var p_1:*;
      
      public var p_2:*;
      
      public var p_3:*;
      
      public var p_4:*;
      
      public var p_5:*;
      
      public var p_6:*;
      
      public var p_7:*;
      
      public var player_gold:*;
      
      public var pla_0:*;
      
      public var pla_1:*;
      
      public var str_0:*;
      
      public var str_1:*;
      
      public var closePanel:*;
      
      public var pro_text:*;
      
      public var str_name:*;
      
      public var b_0:*;
      
      public var b_1:*;
      
      public var b_2:*;
      
      public var b_3:*;
      
      public var b_4:*;
      
      public var b_5:*;
      
      public var b_6:*;
      
      public var b_7:*;
      
      public var b_8:*;
      
      public var b_9:*;
      
      public var b_10:*;
      
      public var b_11:*;
      
      public var b_12:*;
      
      public var b_13:*;
      
      public var b_14:*;
      
      public var b_15:*;
      
      public var b_16:*;
      
      public var b_17:*;
      
      public var b_18:*;
      
      public var b_19:*;
      
      public var b_20:*;
      
      public var b_21:*;
      
      public var b_22:*;
      
      public var b_23:*;
      
      public var face_mc:*;
      
      public var face_txt:*;
      
      private var YN:Boolean = false;
      
      private var SaveOk:Boolean = false;
      
      public function StrPanel()
      {
         super();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("com.hotpoint.braveManIII.views.strPanel.StrPanel") as Class;
         StrPanel._instance = new _loc2_();
         StrPanel._instance.strBag = StrBag.creatBag();
         StrPanel._instance.strSlot = StrSlot.creatSlot();
         StrPanel._instance.tooltip = new ItemsTooltip();
         StrPanel._instance.addEvent();
         StrPanel._instance.addTimeEvent();
         InitIcon();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitIcon() : *
      {
         var _loc1_:Number = 0;
         var _loc2_:MovieClip = null;
         var _loc3_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < 3)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = int(_instance["s_" + _loc1_].getChildIndex(_instance["s_" + _loc1_].pic_xx));
            _loc2_.x = _instance["s_" + _loc1_].pic_xx.x;
            _loc2_.y = _instance["s_" + _loc1_].pic_xx.y;
            _loc2_.name = "pic_xx";
            _instance["s_" + _loc1_].removeChild(_instance["s_" + _loc1_].pic_xx);
            _instance["s_" + _loc1_].pic_xx = _loc2_;
            _instance["s_" + _loc1_].addChild(_loc2_);
            _instance["s_" + _loc1_].setChildIndex(_loc2_,_loc3_);
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 8)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = int(_instance["p_" + _loc1_].getChildIndex(_instance["p_" + _loc1_].pic_xx));
            _loc2_.x = _instance["p_" + _loc1_].pic_xx.x;
            _loc2_.y = _instance["p_" + _loc1_].pic_xx.y;
            _loc2_.name = "pic_xx";
            _instance["p_" + _loc1_].removeChild(_instance["p_" + _loc1_].pic_xx);
            _instance["p_" + _loc1_].pic_xx = _loc2_;
            _instance["p_" + _loc1_].addChild(_loc2_);
            _instance["p_" + _loc1_].setChildIndex(_loc2_,_loc3_);
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 24)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = int(_instance["b_" + _loc1_].getChildIndex(_instance["b_" + _loc1_].pic_xx));
            _loc2_.x = _instance["b_" + _loc1_].pic_xx.x;
            _loc2_.y = _instance["b_" + _loc1_].pic_xx.y;
            _loc2_.name = "pic_xx";
            _instance["b_" + _loc1_].removeChild(_instance["b_" + _loc1_].pic_xx);
            _instance["b_" + _loc1_].pic_xx = _loc2_;
            _instance["b_" + _loc1_].addChild(_loc2_);
            _instance["b_" + _loc1_].setChildIndex(_loc2_,_loc3_);
            _loc1_++;
         }
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(StrPanel._instance == null)
         {
            Loading();
            open_yn = true;
            return;
         }
         Main._stage.addChild(StrPanel._instance);
         StrPanel._instance.visible = true;
         StrPanel._instance.initPanel();
         StrPanel._instance.addChild(StrPanel._instance.tooltip);
         StrPanel._instance.tooltip.visible = false;
         StrPanel._instance.mast_hc.visible = false;
         StrPanel._instance.face_mc.visible = false;
      }
      
      public static function close() : void
      {
         if(StrPanel._instance != null)
         {
            if(StrPanel._instance.visible == true)
            {
               StrPanel._instance.visible = false;
            }
         }
         else
         {
            open_yn = false;
         }
      }
      
      public static function QiangHuaOK(param1:* = null) : *
      {
         if(Boolean(_instance) && Boolean(_instance.SaveOk))
         {
            if(_instance.s1_mc.currentFrame > 75)
            {
               _instance.s1_mc.gotoAndStop(1);
               _instance.SaveOk = false;
               if(_instance.YN)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"强化成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"强化失败");
               }
               _instance.getPro();
               _instance.strName();
               _instance.goldName();
               _instance.proText();
               _instance.QiangHuaJiaCheng();
               _instance.mast_hc.visible = false;
               _instance.strBtn.visible = true;
               _instance.removeEventListener(Event.ENTER_FRAME,QiangHuaOK);
            }
            else
            {
               _instance.addEventListener(Event.ENTER_FRAME,QiangHuaOK);
            }
         }
      }
      
      private function initPanel() : void
      {
         this.pro = VT.createVT(0);
         this.ranNum = VT.createVT(100);
         this.ps = 1;
         data = Main["player" + this.ps];
         state = 0;
         stateTow = 0;
         this.getEquipIng();
         this.p1orp2();
         this.addBag(StrData.getEquipOrSkillGem());
         this.initFrame();
         this.mc_ss.visible = false;
         this.strSlot.clearBag();
         this.initFrameSlot();
         this.btnChange();
         this.btnStata(0,2,"pla_");
         this.btnStr();
         this.getPro();
         this.proText();
         this.s1_mc.gotoAndStop(1);
         this.s2_mc.gotoAndStop(1);
         this.strName();
         this.goldName();
         this.playerGold();
         this.QiangHuaJiaCheng();
      }
      
      private function p1orp2() : void
      {
         this.pla_0.visible = false;
         this.pla_1.visible = false;
         if(Main.P1P2)
         {
            this.pla_0.visible = true;
            this.pla_1.visible = true;
            this.btnStata(0,2,"pla_");
         }
      }
      
      private function addEvent() : void
      {
         this.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,this.daoJuOver);
         this.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,this.daoJuOut);
         this.addEventListener(DaoJuEvent.DAOJU_CLICK_MESSAGE,this.daoJuClick);
         this.addEventListener(BtnEvent.DO_CHANGE,this.changeHandle);
         this.addEventListener(BtnEvent.DO_CLICK,this.clickHandle);
         this.addEventListener(BtnEvent.DO_CLOSE,this.closeHandle);
      }
      
      private function removeEvent() : void
      {
         this.removeEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,this.daoJuOver);
         this.removeEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,this.daoJuOut);
         this.removeEventListener(DaoJuEvent.DAOJU_CLICK_MESSAGE,this.daoJuClick);
         this.removeEventListener(BtnEvent.DO_CHANGE,this.changeHandle);
         this.removeEventListener(BtnEvent.DO_CLICK,this.clickHandle);
         this.removeEventListener(BtnEvent.DO_CLOSE,this.closeHandle);
      }
      
      private function addTimeEvent() : void
      {
         this.timer = new Timer(1);
         this.timer.addEventListener(TimerEvent.TIMER,this.onTimerHandle);
      }
      
      private function getEquipIng() : void
      {
         var _loc2_:* = 0;
         var _loc3_:Equip = null;
         var _loc1_:Number = 0;
         while(_loc1_ < 8)
         {
            _loc2_ = _loc1_;
            if((_loc1_ == 0 || _loc1_ == 1 || _loc1_ == 3 || _loc1_ == 4) && Main.water.getValue() != 1)
            {
               _loc2_ += 8;
            }
            _loc3_ = data.getEquipSlot().getEquipFromSlot(_loc2_);
            if(_loc3_ != null)
            {
               this["p_" + _loc1_].howNum.text = "";
               this["p_" + _loc1_].pic_xx.gotoAndStop(_loc3_.getFrame());
            }
            else
            {
               this["p_" + _loc1_].howNum.text = "";
               this["p_" + _loc1_].pic_xx.gotoAndStop(1);
            }
            _loc1_++;
         }
      }
      
      private function btnChange() : void
      {
         this.str_0.type.gotoAndStop(1);
         this.str_1.type.gotoAndStop(1);
         this.btnStata(state,2,"str_");
         this["str_" + state].type.gotoAndStop(stateTow + 1);
      }
      
      private function btnStata(param1:Number, param2:Number, param3:String) : void
      {
         this[param3 + param1].isClick = true;
         var _loc4_:Number = 0;
         while(_loc4_ < param2)
         {
            if(param1 != _loc4_)
            {
               this[param3 + _loc4_].isClick = false;
            }
            _loc4_++;
         }
      }
      
      private function addBag(param1:Array) : void
      {
         var _loc2_:Number = 0;
         this.strBag.clearBag();
         if(param1 != null)
         {
            _loc2_ = 0;
            while(_loc2_ < param1[0].length)
            {
               this.strBag.addBag(param1[0][_loc2_],param1[1][_loc2_]);
               _loc2_++;
            }
         }
      }
      
      private function initFrame() : void
      {
         var _loc3_:Array = null;
         var _loc1_:int = 0;
         var _loc2_:Number = 0;
         while(_loc2_ < 24)
         {
            if(this.strBag.getObj(_loc2_) != null)
            {
               _loc3_ = this.strBag.getObj(_loc2_);
               this["b_" + _loc2_].howNum.text = "";
               this["b_" + _loc2_].pic_xx.gotoAndStop(_loc3_[0].getFrame());
               if(_loc3_[0] is Gem)
               {
                  if(_loc3_[0].getIsPile())
                  {
                     this["b_" + _loc2_].howNum.text = _loc3_[0].getTimes();
                  }
               }
               _loc1_++;
            }
            else
            {
               this["b_" + _loc2_].pic_xx.gotoAndStop(1);
               this["b_" + _loc2_].howNum.text = "";
            }
            _loc2_++;
         }
         this.face_mc.visible = false;
         if(_loc1_ == 0)
         {
            if(StrPanel.state == 0 && StrPanel.stateTow == 1)
            {
               this.face_mc.face_txt.text = "背包中缺少强化石,可到商城购买";
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"背包中缺少强化石,可到商城购买");
               this.face_mc.visible = true;
            }
            else if(StrPanel.state == 1 && StrPanel.stateTow == 0)
            {
               this.face_mc.face_txt.text = "背包中缺少技能石";
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"背包中缺少技能石");
               this.face_mc.visible = true;
            }
            else if(StrPanel.state == 1 && StrPanel.stateTow == 1)
            {
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"背包中缺少宝石碎片");
               this.face_mc.face_txt.text = "背包中缺少宝石碎片";
               this.face_mc.visible = true;
            }
         }
         TiaoShi.txtShow("背包道具数量 = " + _loc1_);
         TiaoShi.txtShow("StrPanel.state = " + StrPanel.state);
         TiaoShi.txtShow("StrPanel.stateTow = " + StrPanel.stateTow);
      }
      
      private function daoJuClick(param1:DaoJuEvent) : void
      {
         var _loc4_:Object = null;
         var _loc5_:* = 0;
         var _loc6_:Equip = null;
         var _loc7_:Array = null;
         var _loc2_:String = param1.target.name.substr(0,2);
         var _loc3_:String = String(param1.target.name).substr(2);
         if(_loc2_ != "s_")
         {
            if(this.bo)
            {
               this.mc_ss.visible = true;
               this.mc_ss.x = param1.target.x;
               this.mc_ss.y = param1.target.y;
            }
            if(_loc2_ == "b_")
            {
               if(this.strBag.getObj(_loc3_) != null)
               {
                  _loc4_ = (this.strBag.getObj(_loc3_) as Array)[0];
                  if(this.strSlot.addTj(_loc4_))
                  {
                     this.strSlot.addBag(this.strBag.getObj(_loc3_),0);
                  }
               }
            }
            else if(_loc2_ == "p_")
            {
               if(data.getEquipSlot().getEquipFromSlot(_loc3_) != null)
               {
                  _loc5_ = uint(_loc3_);
                  if((_loc3_ == 0 || _loc3_ == 1 || _loc3_ == 3 || _loc3_ == 4) && Main.water.getValue() != 1)
                  {
                     _loc5_ += 8;
                  }
                  _loc6_ = data.getEquipSlot().getEquipFromSlot(_loc5_);
                  if(state == 1)
                  {
                     this.strSlot.clearBag();
                     state = 0;
                     stateTow = 0;
                  }
                  _loc7_ = [_loc6_,_loc5_];
                  if(this.strSlot.addTj(_loc6_))
                  {
                     this.strSlot.addBag(_loc7_,1);
                  }
               }
            }
         }
         else
         {
            this.strSlot.clearOnly(Number(_loc3_));
            this.mc_ss.visible = false;
         }
         this.initFrameSlot();
         this.changeBag();
         this.btnStr();
         this.getPro();
         this.proText();
         this.strName();
         this.goldName();
         this.playerGold();
      }
      
      private function proText() : void
      {
         if(this.pro.getValue() <= 0)
         {
            this.pro_text.text = "";
         }
         else
         {
            this.pro_text.text = String(this.pro.getValue()) + "%";
         }
      }
      
      private function changeBag() : void
      {
         if(this.strSlot.getObj(0) != null)
         {
            stateTow = 1;
         }
         else
         {
            stateTow = 0;
         }
         if(stateTow != this.oldStateTow)
         {
            this.mc_ss.visible = false;
            this.oldStateTow = stateTow;
         }
         this.addBag(StrData.getEquipOrSkillGem());
         this.btnChange();
         this.initFrame();
      }
      
      private function initFrameSlot() : void
      {
         var _loc2_:Array = null;
         var _loc1_:Number = 0;
         while(_loc1_ < 3)
         {
            this["s_" + _loc1_].visible = false;
            if(this.strSlot.getObj(_loc1_) != null)
            {
               this["s_" + _loc1_].visible = true;
               _loc2_ = this.strSlot.getObj(_loc1_);
               this["s_" + _loc1_].howNum.text = "";
               this["s_" + _loc1_].pic_xx.gotoAndStop(_loc2_[0].getFrame());
            }
            _loc1_++;
         }
      }
      
      private function daoJuOver(param1:DaoJuEvent) : void
      {
         var _loc5_:* = 0;
         var _loc2_:String = param1.target.name.substr(0,2);
         var _loc3_:String = param1.target.name.substr(2);
         var _loc4_:Object = null;
         if(_loc2_ == "b_" && this.strBag.getObj(_loc3_) != null)
         {
            _loc4_ = (this.strBag.getObj(_loc3_) as Array)[0];
         }
         else if(_loc2_ == "p_" && data.getEquipSlot().getEquipFromSlot(_loc3_) != null)
         {
            _loc5_ = uint(_loc3_);
            if((_loc3_ == 0 || _loc3_ == 1 || _loc3_ == 3 || _loc3_ == 4) && Main.water.getValue() != 1)
            {
               _loc5_ += 8;
            }
            _loc4_ = data.getEquipSlot().getEquipFromSlot(_loc5_);
         }
         else if(_loc2_ == "s_" && this.strSlot.getObj(_loc3_) != null)
         {
            _loc4_ = (this.strSlot.getObj(_loc3_) as Array)[0];
         }
         if(_loc4_ != null)
         {
            this.tooltip.visible = true;
            this.tooltip.x = mouseX;
            this.tooltip.y = mouseY;
            if(_loc4_ is Equip)
            {
               this.tooltip.equipTooltip(_loc4_ as Equip);
            }
            else if(_loc4_ is Gem)
            {
               this.tooltip.gemTooltip(_loc4_ as Gem);
            }
         }
      }
      
      private function daoJuOut(param1:DaoJuEvent) : void
      {
         this.tooltip.visible = false;
      }
      
      private function closeHandle(param1:BtnEvent) : void
      {
         close();
      }
      
      private function clickHandle(param1:BtnEvent) : void
      {
         this.timer.start();
         this.removeEvent();
         this.mast_hc.visible = true;
      }
      
      private function changeHandle(param1:BtnEvent) : void
      {
         var _loc2_:String = param1.target.name.substr(0,3);
         var _loc3_:String = param1.target.name.substr(4,1);
         if(_loc2_ == "str")
         {
            state = Number(_loc3_);
            stateTow = 0;
            this.oldStateTow = 0;
         }
         else if(_loc2_ == "pla")
         {
            this.ps = Number(_loc3_) + 1;
            data = Main["player" + this.ps];
            this.btnStata(_loc3_,2,"pla_");
            state = 0;
            stateTow = 0;
            this.oldStateTow = 0;
            this.getEquipIng();
         }
         this.strSlot.clearBag();
         this.addBag(StrData.getEquipOrSkillGem());
         this.btnChange();
         this.initFrame();
         this.initFrameSlot();
         this.getPro();
         this.proText();
         this.strName();
         this.goldName();
         this.QiangHuaJiaCheng();
         this.btnStr();
         this.mc_ss.visible = false;
      }
      
      private function btnStr() : void
      {
         this.strBtn.gotoAndStop(2);
         if(this.strSlot.getObj(0) != null && this.strSlot.getObj(1) != null)
         {
            if(state == 0)
            {
               if(data.getGold() >= StrData.getStrenthenGold((this.strSlot.getObj(0) as Array)[0]))
               {
                  this.strBtn.gotoAndStop(1);
               }
            }
            else if(state == 1)
            {
               if(data.getGold() >= StrData.getGemUpGold((this.strSlot.getObj(0) as Array)[0]))
               {
                  this.strBtn.gotoAndStop(1);
               }
            }
         }
      }
      
      private function getPro() : void
      {
         var _loc1_:Number = NaN;
         var _loc2_:Number = NaN;
         this.pro.setValue(0);
         if(state == 0)
         {
            if(this.strSlot.getObj(0) != null && this.strSlot.getObj(1) != null)
            {
               if(this.strSlot.getObj(2) == null)
               {
                  this.pro.setValue(StrData.getEquipUpLevel((this.strSlot.getObj(0) as Array)[0],(this.strSlot.getObj(1) as Array)[0]));
               }
               else
               {
                  _loc1_ = Number(StrData.getEquipUpLevel((this.strSlot.getObj(0) as Array)[0],(this.strSlot.getObj(1) as Array)[0]));
                  _loc2_ = Number(StrData.getLuckGemPosition((this.strSlot.getObj(2) as Array)[0]));
                  this.pro.setValue(_loc1_ + _loc2_);
               }
            }
         }
         else if(state == 1)
         {
            if(this.strSlot.getObj(0) != null)
            {
               if(this.strSlot.getObj(2) == null)
               {
                  this.pro.setValue(StrData.getGemUpLevel((this.strSlot.getObj(0) as Array)[0]));
               }
               else
               {
                  _loc1_ = Number(StrData.getGemUpLevel((this.strSlot.getObj(0) as Array)[0]));
                  _loc2_ = Number(StrData.getLuckGemPosition((this.strSlot.getObj(2) as Array)[0]));
                  this.pro.setValue(_loc1_ + _loc2_);
               }
            }
         }
         if(this.pro.getValue() >= 100)
         {
            this.pro.setValue(100);
         }
      }
      
      private function onTimerHandle(param1:TimerEvent) : void
      {
         ++this.times;
         if(this.s1_mc.currentFrame == 1)
         {
            this.s1_mc.gotoAndPlay(2);
            this.s2_mc.gotoAndPlay(2);
            this.strBtn.visible = false;
         }
         if(this.times >= this.stopTime)
         {
            this.timer.stop();
            this.times = 0;
            this.addEvent();
            if(Boolean(this.strSlot.addTj((this.strSlot.getObj(0) as Array)[0])) && Boolean(this.strSlot.addTj((this.strSlot.getObj(1) as Array)[0])))
            {
               if(Math.random() * this.ranNum.getValue() <= this.pro.getValue())
               {
                  this.addSx();
                  this.YN = true;
                  AchData.setStrOkNum(this.ps);
               }
               else
               {
                  this.YN = false;
                  AchData.setStrLostNum(this.ps);
               }
               this.removeGem();
               this.addBag(StrData.getEquipOrSkillGem());
               this.payGold();
            }
            else
            {
               stateTow = 0;
               this.strSlot.clearBag();
               this.addBag(StrData.getEquipOrSkillGem());
            }
            this.initFrameSlot();
            this.initFrame();
            this.btnStr();
            this.playerGold();
            Main.Save();
            this.SaveOk = true;
         }
      }
      
      private function removeGem() : void
      {
         var _loc1_:Gem = null;
         if(this.strSlot.getObj(1) != null)
         {
            _loc1_ = (this.strSlot.getObj(1) as Array)[0];
            data.getBag().delGem((this.strSlot.getObj(1) as Array)[1],1);
            if(data.getBag().getGemById(_loc1_.getId()) == null)
            {
               this.strSlot.clearOnly(1);
            }
         }
         if(this.strSlot.getObj(2) != null)
         {
            _loc1_ = (this.strSlot.getObj(2) as Array)[0];
            data.getBag().delGem((this.strSlot.getObj(2) as Array)[1],1);
            if(data.getBag().getGemById(_loc1_.getId()) == null)
            {
               this.strSlot.clearOnly(2);
            }
         }
      }
      
      private function addSx() : void
      {
         if(state == 0)
         {
            this.strEquip();
         }
         else if(state == 1)
         {
            this.strGem();
         }
      }
      
      private function strEquip() : void
      {
         var _loc1_:Equip = null;
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc4_:Gem = null;
         if(this.strSlot.getObj(0) != null && this.strSlot.getObj(1) != null)
         {
            _loc2_ = Number((this.strSlot.getObj(0) as Array)[1]);
            _loc3_ = Number((this.strSlot.getObj(0) as Array)[2]);
            _loc4_ = (this.strSlot.getObj(1) as Array)[0];
            if(_loc3_ == 0)
            {
               _loc1_ = data.getBag().getEquipFromBag(_loc2_);
            }
            else if(_loc3_ == 1)
            {
               _loc1_ = data.getEquipSlot().getEquipFromSlot(_loc2_);
            }
            StrData.strengthenOver(_loc1_,_loc4_);
         }
      }
      
      private function strGem() : void
      {
         var _loc1_:Number = NaN;
         var _loc2_:Gem = null;
         var _loc3_:Gem = null;
         if(this.strSlot.getObj(0) != null && this.strSlot.getObj(1) != null)
         {
            _loc1_ = Number((this.strSlot.getObj(0) as Array)[1]);
            _loc2_ = (this.strSlot.getObj(0) as Array)[0];
            _loc3_ = StrData.strengthenSkillOver(_loc2_);
            data.getBag().delGem(_loc1_,1);
            data.getBag().addToGemBag(_loc3_,_loc1_);
            this.strSlot.addBag(new Array(_loc3_,_loc1_),0);
         }
      }
      
      private function strName() : void
      {
         var _loc1_:Object = null;
         this.str_name.text = "";
         if(this.strSlot.getObj(0) != null)
         {
            _loc1_ = (this.strSlot.getObj(0) as Array)[0];
            if(_loc1_ is Equip)
            {
               this.str_name.text = (_loc1_ as Equip).getName() + "+" + (_loc1_ as Equip).getReinforceLevel();
            }
            else if(_loc1_ is Gem)
            {
               this.str_name.text = (_loc1_ as Gem).getName() + "+" + (_loc1_ as Gem).getStrengthenLevel();
            }
         }
      }
      
      private function goldName() : void
      {
         this.gold_name.text = "";
         if(this.strSlot.getObj(0) != null)
         {
            if(state == 0)
            {
               this.gold_name.text = StrData.getStrenthenGold((this.strSlot.getObj(0) as Array)[0]).toString();
            }
            else if(state == 1)
            {
               this.gold_name.text = StrData.getGemUpGold((this.strSlot.getObj(0) as Array)[0]).toString();
            }
         }
      }
      
      private function payGold() : void
      {
         if(this.strSlot.getObj(0) != null)
         {
            if(state == 0)
            {
               data.payGold(StrData.getStrenthenGold((this.strSlot.getObj(0) as Array)[0]));
            }
            else if(state == 1)
            {
               data.payGold(StrData.getGemUpGold((this.strSlot.getObj(0) as Array)[0]));
            }
         }
      }
      
      private function playerGold() : void
      {
         this.player_gold.text = String(data.getGold());
      }
      
      private function QiangHuaJiaCheng() : *
      {
         var _loc1_:int = data.getEquipSlot().getSuitStrength();
         if(_loc1_ < 4)
         {
            this.strengthen.gotoAndStop(1);
            return;
         }
         switch(_loc1_)
         {
            case 4:
               this.strengthen.gotoAndStop(2);
               break;
            case 5:
               this.strengthen.gotoAndStop(3);
               break;
            case 6:
               this.strengthen.gotoAndStop(4);
               break;
            case 7:
               this.strengthen.gotoAndStop(5);
               break;
            case 8:
               this.strengthen.gotoAndStop(6);
               break;
            case 9:
               this.strengthen.gotoAndStop(7);
               break;
            case 10:
               this.strengthen.gotoAndStop(8);
         }
      }
   }
}

