package src
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.other.*;
   import src.tool.*;
   
   public class GongHuiTiaoZan extends MovieClip
   {
      public static var HuoDong:MovieClip;
      
      public static var TiaoZhan:MovieClip;
      
      public static var tzData:Array;
      
      public static var tzAll:int = 3;
      
      public static var tzNum:int = 1;
      
      public static var tzID:int = 1;
      
      public function GongHuiTiaoZan()
      {
         super();
      }
      
      private static function onClose(param1:MouseEvent) : *
      {
         var _loc2_:MovieClip = param1.target.parent;
         _loc2_.visible = false;
      }
      
      public static function Open_HD(param1:MouseEvent) : *
      {
         HuoDong.visible = true;
         HuoDong.y = 0;
         HuoDong.x = 0;
         GongHui_Interface._this.addChild(HuoDong);
         HuoDong.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         HuoDong.hd1_btn.addEventListener(MouseEvent.CLICK,Open_TZ);
      }
      
      private static function Open_TZ(param1:MouseEvent = null) : *
      {
         HuoDong.visible = false;
         TiaoZhan.visible = true;
         TiaoZhan.y = 0;
         TiaoZhan.x = 0;
         GongHui_Interface._this.addChild(TiaoZhan);
         tzData_Init();
         tzShow();
         TiaoZhan.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         TiaoZhan.tiaoZhan_btn.addEventListener(MouseEvent.CLICK,TiaoZhan_Fun);
         TiaoZhan.back_btn.addEventListener(MouseEvent.CLICK,Back_Fun);
         TiaoZhan.next_btn.addEventListener(MouseEvent.CLICK,Next_Fun);
      }
      
      private static function tzShow() : *
      {
         TiaoZhan.back_btn.visible = TiaoZhan.next_btn.visible = TiaoZhan.black_mc.visible = TiaoZhan.life_txt.visible = false;
         if(tzID < tzAll)
         {
            TiaoZhan.next_btn.visible = true;
         }
         if(tzID > 1)
         {
            TiaoZhan.back_btn.visible = true;
         }
         TiaoZhan.tiaoZhan_btn.visible = true;
         TiaoZhan.kill_mc.gotoAndStop("开启");
         TweenMax.to(TiaoZhan.pic_mc,0,{"colorMatrixFilter":{"saturation":1}});
         if(tzID > tzNum)
         {
            TiaoZhan.tiaoZhan_btn.visible = false;
            TweenMax.to(TiaoZhan.pic_mc,0,{"colorMatrixFilter":{"saturation":0}});
            if(tzID == 2)
            {
               TiaoZhan.kill_mc.gotoAndStop("5级开启");
            }
            else
            {
               TiaoZhan.kill_mc.gotoAndStop("10级开启");
            }
         }
         else if(tzData[tzID][1])
         {
            TiaoZhan.kill_mc.gotoAndStop("已击杀");
            TiaoZhan.tiaoZhan_btn.visible = false;
         }
         else
         {
            TiaoZhan.black_mc.visible = TiaoZhan.life_txt.visible = true;
            TiaoZhan.life_txt.text = "剩余血量:" + tzData[tzID][0] + "%";
         }
         TiaoZhan.pic_mc.gotoAndStop(tzID);
         TiaoZhan.picX_mc.gotoAndStop(tzID);
      }
      
      public static function tzData_Init() : *
      {
         var _loc2_:int = 0;
         if(!tzData || tzData[0] < Main.serverTime.getValue())
         {
            tzData = new Array();
            tzData[0] = Main.serverTime.getValue();
            _loc2_ = 0;
            while(_loc2_ < tzAll)
            {
               tzData[_loc2_ + 1] = [100,false];
               _loc2_++;
            }
            TiaoShi.txtShow("重置公会挑战!!!!!!!!!!!!" + Main.serverTime.getValue());
         }
         var _loc1_:int = int(GongHui_Interface.playerInfo.unionInfo.level);
         if(_loc1_ >= 10)
         {
            tzNum = 3;
         }
         else if(_loc1_ >= 5)
         {
            tzNum = 2;
         }
         else
         {
            tzNum = 1;
         }
      }
      
      public static function BossHpXX(param1:Enemy) : *
      {
         if(GameData.gameLV != 6 || !param1 || GameData.BossIS != param1)
         {
            return;
         }
         var _loc2_:int = param1.lifeMAX.getValue();
         var _loc3_:int = param1.life.getValue();
         if(Boolean(Main.tiaoShiYN) && !tzData)
         {
            tzData = [0,[100,false],[100,false],[100,false]];
         }
         var _loc4_:int = 100;
         if(_loc3_ <= 0)
         {
            _loc4_ = 0;
            _loc3_ = 0;
            if(!tzData[Main.gameNum.getValue() - 5000][1])
            {
               tzData[Main.gameNum.getValue() - 5000][1] = true;
               Api_4399_GongHui.setRW(70);
            }
         }
         else
         {
            TiaoShi.txtShow("num:" + _loc3_ + ",max:" + _loc2_);
            _loc4_ = _loc3_ / _loc2_ * 100;
            if(_loc4_ < 1)
            {
               _loc4_ = 1;
            }
         }
         tzData[Main.gameNum.getValue() - 5000][0] = _loc4_;
         TiaoShi.txtShow("剩余血量百分比:" + _loc4_ + "%");
      }
      
      public static function TiaoZhan_Fun(param1:MouseEvent = null) : *
      {
         if(!Main.P1P2 && Main.player1.killPoint.getValue() >= 50)
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - 50);
         }
         else
         {
            if(!(Boolean(Main.P1P2) && Main.player1.killPoint.getValue() >= 50 && Main.player2.killPoint.getValue() >= 50))
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"击杀点不足");
               return;
            }
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - 50);
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - 50);
         }
         TiaoShi.txtShow("挑战Boss:" + tzID);
         Main.gameNum.setValue(5000 + tzID);
         Main.gameNum2.setValue(1);
         GameData.gameLV = 6;
         Main._this.Loading();
         TiaoZhan.visible = false;
         GongHui_Interface.JieMian.visible = false;
         GongHui_Interface.DaTing.visible = false;
         Main.Save(false);
      }
      
      public static function Back_Fun(param1:*) : *
      {
         if(tzID > 0)
         {
            --tzID;
         }
         tzShow();
      }
      
      public static function Next_Fun(param1:*) : *
      {
         if(tzID < tzAll)
         {
            ++tzID;
         }
         tzShow();
      }
   }
}

