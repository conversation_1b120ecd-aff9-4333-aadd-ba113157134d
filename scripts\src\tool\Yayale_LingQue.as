package src.tool
{
   import com.adobe.serialization.json.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.net.*;
   import flash.text.*;
   import src.*;
   
   public class Yayale_LingQue extends MovieClip
   {
      private static var skin:MovieClip;
      
      private static var skin2:MovieClip;
      
      private static var loadData:ClassLoader;
      
      private static var objX_ID:int;
      
      private static var who:Player;
      
      public static var _this:Yayale_LingQue = new Yayale_LingQue();
      
      public static var loadName:String = "bibabo_v1095.swf";
      
      public static var loadingOK:int = 0;
      
      private static var openNum:int = 0;
      
      private static var urlLoader:URLLoader = new URLLoader();
      
      private static var phpUrl:URLRequest = new URLRequest("http://my.4399.com/jifen/activation");
      
      public function Yayale_LingQue()
      {
         super();
      }
      
      public static function Open() : *
      {
      }
      
      public static function Close(param1:* = null) : *
      {
         skin.parent.removeChild(skin);
      }
      
      public static function LoadSkin() : *
      {
         if(loadingOK == 0)
         {
            loadingOK = 1;
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
         }
      }
      
      private static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = null;
         TiaoShi.txtShow("比巴卜魔力联盟 加载完成");
         loadingOK = 2;
         _loc2_ = loadData.getClass("Skin_1") as Class;
         skin = new _loc2_();
         _this.addChild(skin);
         skin._txt.addEventListener(MouseEvent.CLICK,onTXT);
         skin._btn.addEventListener(MouseEvent.CLICK,onLingQue);
         skin._close_btn.addEventListener(MouseEvent.CLICK,Close);
         skin._txt2.addEventListener(MouseEvent.CLICK,onTXT2);
         skin._btn2.addEventListener(MouseEvent.CLICK,onLingQue);
         _loc2_ = loadData.getClass("Skin_2") as Class;
         skin2 = new _loc2_();
         _this.addChild(skin2);
         skin2.dh1.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.dh2.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.dh3.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.dh4.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.dh5.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.close.addEventListener(MouseEvent.CLICK,close2);
         if(openNum == 1)
         {
            Open();
            openNum = 0;
         }
         else if(openNum == 2)
         {
            DuiHuanOpen(who);
            openNum = 0;
         }
      }
      
      private static function onTXT(param1:*) : *
      {
         skin._txt.text = "";
      }
      
      private static function onTXT2(param1:*) : *
      {
         skin._txt2.text = "";
      }
      
      private static function onLingQue(param1:MouseEvent) : *
      {
         var _loc2_:String = null;
         if(Main.P1P2)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1 || Main.player2.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
               return;
            }
         }
         else if(Main.player1.getBag().backOtherBagNum() < 1)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
            return;
         }
         phpUrl.method = URLRequestMethod.POST;
         if(param1.target.name == "_btn")
         {
            _loc2_ = skin._txt.text;
         }
         else
         {
            _loc2_ = skin._txt2.text;
         }
         TiaoShi.txtShow("e.target.name = " + param1.target.name);
         var _loc3_:URLVariables = new URLVariables();
         _loc3_.activation = _loc2_;
         _loc3_.uid = Main.userId;
         _loc3_.uniqueId = 7;
         var _loc4_:* = _loc3_.activation + "-" + _loc3_.uid + "-" + _loc3_.uniqueId + "-" + "d7adb1789427ea38327ad146e92e1dd4";
         var _loc5_:String = MD5contrast.GetScoreMD5(_loc4_);
         _loc3_.token = _loc5_;
         phpUrl.data = _loc3_;
         urlLoader.load(phpUrl);
         urlLoader.addEventListener(Event.COMPLETE,completeHandler_All);
         skin._btn2.visible = false;
         skin._btn.visible = false;
      }
      
      private static function completeHandler_All(param1:Event) : void
      {
         var _loc2_:String = (param1.currentTarget as URLLoader).data;
         var _loc3_:Object = JSONs.decode(_loc2_,true);
         skin._btn.visible = skin._btn2.visible = true;
         if(_loc3_.code == 100)
         {
            TiaoShi.txtShow("礼包ID = " + _loc3_.result);
            if(_loc3_.result == 75)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[7] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[7];
               _loc2_ = "双节礼包领取成功!";
               Main.Save();
            }
            else if(_loc3_.result == 25)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[4] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[4];
               _loc2_ = "安全礼包领取成功!";
               Main.Save();
            }
            else if(_loc3_.result == 78)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[6] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[6];
               _loc2_ = "桌面礼包领取成功!";
               Main.Save();
            }
            else if(_loc3_.result == 95)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[8] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[8];
               _loc2_ = "速升礼包领取成功!";
               Main.Save();
            }
            else if(_loc3_.result == 113)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[9] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[9];
               _loc2_ = "豆娃礼包领取成功!";
               Main.Save();
            }
            else if(_loc3_.result == 123)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[12] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[12];
               _loc2_ = "绝世礼包领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 154)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[14] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[14];
               _loc2_ = "周年礼包领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 190)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
               }
               _loc2_ = "星灵礼包领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 290)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[21] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[21];
               _loc2_ = "双蛋豪华礼包领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 269)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[22] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[22];
               _loc2_ = "双蛋礼包领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 314)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[23] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[23];
               _loc2_ = "4399新春礼包领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 570)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[25] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[25];
               _loc2_ = "客户端专属礼包 领取成功!!";
               Api_4399_GongHui.upNum(15);
               Main.Save();
            }
            else if(_loc3_.result == 894)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[26] >= 3)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取3次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[26];
               _loc2_ = "五一礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 1362)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[29] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[29];
               _loc2_ = "赛龙舟礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 1878)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[30] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[30];
               _loc2_ = "尊享礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 2270)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[31] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[31];
               _loc2_ = "七夕礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 2922)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[34] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
               }
               TeShuHuoDong.AddLiBaoNum(35);
               _loc2_ = "中秋礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 7686)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[48] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
               }
               TeShuHuoDong.AddLiBaoNum(48);
               _loc2_ = "羊年礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 10012)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[57] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
               }
               TeShuHuoDong.AddLiBaoNum(57);
               _loc2_ = "4399游戏盒尊贵礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 10014)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[58] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
               }
               TeShuHuoDong.AddLiBaoNum(58);
               _loc2_ = "4399游戏盒分享礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 10996)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[59] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
               }
               TeShuHuoDong.AddLiBaoNum(59);
               _loc2_ = "勇士的信仰翻牌礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 13770)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[68] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
               }
               TeShuHuoDong.AddLiBaoNum(68);
               _loc2_ = "豪华礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 13772)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[69] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
               }
               TeShuHuoDong.AddLiBaoNum(69);
               _loc2_ = "伴我同行礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 19114)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[82] >= 2)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档每天只能领取2次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
               }
               TeShuHuoDong.AddLiBaoNum(82);
               _loc2_ = "国庆礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 19422)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
               }
               TeShuHuoDong.AddLiBaoNum(83);
               _loc2_ = "星灵礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 19420)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
               }
               TeShuHuoDong.AddLiBaoNum(84);
               _loc2_ = "神宠礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 21050)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[97] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63374));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63374));
               }
               TeShuHuoDong.AddLiBaoNum(97);
               _loc2_ = "双旦pk礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22122)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[102] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63380));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63380));
               }
               TeShuHuoDong.AddLiBaoNum(102);
               _loc2_ = "猴年礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 20888)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[93] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63370));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63370));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63370));
               }
               TeShuHuoDong.AddLiBaoNum(93);
               _loc2_ = "12月份1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 20890)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[94] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63371));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63371));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63371));
               }
               TeShuHuoDong.AddLiBaoNum(94);
               _loc2_ = "12月份7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 20892)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[95] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63372));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63372));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63372));
               }
               TeShuHuoDong.AddLiBaoNum(95);
               _loc2_ = "12月份15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 20894)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[96] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63373));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63373));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63373));
               }
               TeShuHuoDong.AddLiBaoNum(96);
               _loc2_ = "12月份25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 21482)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[98] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63354));
               TeShuHuoDong.AddLiBaoNum(98);
               _loc2_ = "1月份1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 21484)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[99] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63355));
               TeShuHuoDong.AddLiBaoNum(99);
               _loc2_ = "1月份7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 21486)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[100] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63356));
               TeShuHuoDong.AddLiBaoNum(100);
               _loc2_ = "1月份15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 21488)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[101] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63357));
               TeShuHuoDong.AddLiBaoNum(101);
               _loc2_ = "1月份25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22520)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[98] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(98);
               _loc2_ = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22522)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[99] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(99);
               _loc2_ = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22524)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[100] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(100);
               _loc2_ = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22526)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[101] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(101);
               _loc2_ = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22528)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[107] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(107);
               _loc2_ = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22530)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[108] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(108);
               _loc2_ = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22532)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[109] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(109);
               _loc2_ = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 22534)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[110] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(110);
               _loc2_ = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 23200)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[111] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63378));
               TeShuHuoDong.AddLiBaoNum(111);
               _loc2_ = "比巴卜普通礼包 领取成功!!";
               Main.Save();
            }
            else if(_loc3_.result == 23202)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[112] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63379));
               TeShuHuoDong.AddLiBaoNum(112);
               _loc2_ = "比巴卜至尊礼包 领取成功!!";
               Main.Save();
            }
            else
            {
               _loc2_ = "验证码错误!!";
            }
         }
         else if(_loc3_.code == 101)
         {
            _loc2_ = "参数错误";
         }
         else if(_loc3_.code == 102)
         {
            _loc2_ = "验证码不存在";
         }
         else if(_loc3_.code == 103)
         {
            _loc2_ = "验证码还没被兑换";
         }
         else if(_loc3_.code == 104)
         {
            _loc2_ = "验证码被使用过";
         }
         else if(_loc3_.code == 105)
         {
            _loc2_ = "验证码只能被领取者使用";
         }
         else if(_loc3_.code == 106)
         {
            _loc2_ = "您的账号已经使用过此礼包的激活码";
         }
         else if(_loc3_.code == 107)
         {
            _loc2_ = "token无效";
         }
         else if(_loc3_.code == 108)
         {
            _loc2_ = "激活码失效了";
         }
         else if(_loc3_.code == 109)
         {
            _loc2_ = "激活失败";
         }
         else if(_loc3_.code == 110)
         {
            _loc2_ = "您的账号今天已使用过激活码";
         }
         else
         {
            _loc2_ = "未知错误";
         }
         NewMC.Open("文字提示",Main._stage,480,450,60,0,true,1,_loc2_);
      }
      
      public static function DuiHuanOpen(param1:Player) : *
      {
         LoadSkin();
         if(loadingOK == 2)
         {
            Main._this.addChild(skin2);
            ItemsPanel.close();
            skin2.visible = true;
         }
         else
         {
            NewMC.Open("文字提示",Main._this,400,400,30,0,true,2,"伢牙乐兑换界面加载中,请稍候...");
            openNum = 2;
         }
         who = param1;
      }
      
      private static function DuiahuanX(param1:MouseEvent) : *
      {
         var _loc2_:int = int((param1.target.name as String).substr(2,1));
         if(_loc2_ == 1)
         {
            if(who.getBag().backOtherBagNum() >= 2 && who.getBag().backequipBagNum() >= 1)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addEquipBag(EquipFactory.createEquipByID(14643));
               i = 0;
               while(i < 5)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ++i;
               }
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(_loc2_ == 2)
         {
            if(who.getBag().backOtherBagNum() >= 2 && who.getBag().backequipBagNum() >= 1)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addEquipBag(EquipFactory.createEquipByID(14644));
               i = 0;
               while(i < 5)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ++i;
               }
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(_loc2_ == 3)
         {
            if(who.getBag().backOtherBagNum() >= 2 && who.getBag().backequipBagNum() >= 1)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addEquipBag(EquipFactory.createEquipByID(14649));
               i = 0;
               while(i < 5)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ++i;
               }
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(_loc2_ == 4)
         {
            if(who.getBag().backOtherBagNum() >= 2 && who.getBag().backequipBagNum() >= 1)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addEquipBag(EquipFactory.createEquipByID(14652));
               i = 0;
               while(i < 5)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ++i;
               }
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(_loc2_ == 5)
         {
            if(who.getBag().backOtherBagNum() >= 2)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
      }
      
      private static function close2(param1:*) : *
      {
         skin2.visible = false;
      }
   }
}

