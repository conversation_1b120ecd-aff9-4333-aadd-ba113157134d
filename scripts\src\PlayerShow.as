package src
{
   import com.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import src.other.*;
   import src.tool.*;
   
   public class PlayerShow extends MovieClip
   {
      public var dataArr:Array = [1,1,1,[0],[0]];
      
      public var skin:Skin;
      
      public var skin_W:Skin_WuQi;
      
      public var skin_Z:Skin_ZhuangBei;
      
      public var skin_Z2:Skin_ZhuangBei;
      
      public var skin_Z3:Skin_ZhuangBei;
      
      public var skin_Z2_V:Boolean = true;
      
      public var skin_Z3_V:Boolean = true;
      
      public var headFrame:int = 1;
      
      public function PlayerShow()
      {
         super();
         mouseChildren = false;
         mouseEnabled = false;
      }
      
      public function Loading(param1:Array = null) : Boolean
      {
         if(TiaoZhanPaiHang_Interface.selTypeX != 0)
         {
            return true;
         }
         this.dataArr = param1;
         if(!this.dataArr || this.dataArr.length < 5)
         {
            return true;
         }
         var _loc2_:int = int(this.dataArr[0]);
         var _loc3_:int = int(this.dataArr[4][0]);
         var _loc4_:int = int(this.dataArr[3][0]);
         if(_loc4_ != 0 && this.dataArr[3][2] == 53)
         {
            this.dataArr[3][2] = 12;
         }
         if(_loc4_ != 0 && !NewLoad.zhuangBeiSkin[_loc2_][_loc4_] || _loc3_ != 0 && !NewLoad.zhuangBeiSkin[_loc2_][_loc3_])
         {
            NewLoad.PaiHangLoad(this.dataArr);
            return false;
         }
         return true;
      }
      
      public function AddSkin() : *
      {
         var _loc5_:Class = null;
         var _loc6_:String = null;
         if(this.skin_Z3)
         {
            this.skin_Z3.parent.removeChild(this.skin_Z3);
            this.skin_Z3 = null;
         }
         if(this.skin)
         {
            this.skin.parent.removeChild(this.skin);
            this.skin = null;
         }
         if(this.skin_Z)
         {
            this.skin_Z.parent.removeChild(this.skin_Z);
            this.skin_Z = null;
         }
         if(this.skin_W)
         {
            this.skin_W.parent.removeChild(this.skin_W);
            this.skin_W = null;
         }
         if(!this.dataArr || this.dataArr.length < 5)
         {
            return;
         }
         this.visible = true;
         var _loc1_:int = int(this.dataArr[0]);
         var _loc2_:int = int(this.dataArr[4][0]);
         this.headFrame = this.dataArr[1];
         var _loc3_:int = int(this.dataArr[3][0]);
         if(_loc2_ != 0)
         {
            _loc6_ = this.dataArr[4][2];
            _loc5_ = NewLoad.zhuangBeiSkin[_loc1_][_loc2_].getClass(_loc6_) as Class;
            this.skin_Z3 = new _loc5_();
            this.skin_Z3.gotoAndStop("站");
            this.skin_Z3.mouseChildren = this.skin_Z3.mouseEnabled = false;
            addChild(this.skin_Z3);
         }
         var _loc4_:int = int(this.dataArr[0]);
         _loc5_ = Player.PlayerMcArr[_loc4_].getClass("src.Skin.Skin_player" + _loc4_) as Class;
         this.skin = new _loc5_();
         this.skin.skinNum = _loc4_;
         this.skin.Xml = Skin.PlayerXml[_loc4_];
         this.skin.playX = this;
         this.skin.Stop();
         this.skin.mouseChildren = this.skin.mouseEnabled = false;
         addChild(this.skin);
         if(_loc3_ != 0)
         {
            _loc6_ = this.dataArr[3][2];
            _loc5_ = NewLoad.zhuangBeiSkin[_loc1_][_loc3_].getClass(_loc6_) as Class;
            this.skin_Z = new _loc5_();
            this.skin_Z.gotoAndStop("站");
            this.skin_Z.mouseChildren = this.skin_Z.mouseEnabled = false;
            addChild(this.skin_Z);
         }
         _loc5_ = Skin_WuQi.PlayerMcArr[_loc1_].getClass(this.dataArr[2]) as Class;
         this.skin_W = new _loc5_();
         this.skin_W.gotoAndStop("站");
         this.skin_W.mouseChildren = this.skin_W.mouseEnabled = false;
         addChild(this.skin_W);
      }
   }
}

