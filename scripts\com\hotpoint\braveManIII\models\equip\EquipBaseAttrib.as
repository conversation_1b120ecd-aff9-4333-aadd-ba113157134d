package com.hotpoint.braveManIII.models.equip
{
   import com.hotpoint.braveManIII.models.common.VT;
   
   public class EquipBaseAttrib
   {
      private var _colorType:VT;
      
      private var _attribType:VT;
      
      private var _value:VT;
      
      private var _beishu:VT = VT.createVT(0);
      
      private var _beishuValue:VT = VT.createVT(0);
      
      public function EquipBaseAttrib()
      {
         super();
      }
      
      public static function creatEquipBaseAttrib(param1:Number, param2:Number, param3:Number) : EquipBaseAttrib
      {
         var _loc4_:EquipBaseAttrib = new EquipBaseAttrib();
         _loc4_._colorType = VT.createVT(param1);
         _loc4_._attribType = VT.createVT(param2);
         _loc4_._value = VT.createVT(param3);
         return _loc4_;
      }
      
      public function get colorType() : VT
      {
         return this._colorType;
      }
      
      public function set colorType(param1:VT) : void
      {
         this._colorType = param1;
      }
      
      public function get attribType() : VT
      {
         return this._attribType;
      }
      
      public function set attribType(param1:VT) : void
      {
         this._attribType = param1;
      }
      
      public function get value() : VT
      {
         return this._value;
      }
      
      public function set value(param1:VT) : void
      {
         this._value = param1;
      }
      
      public function get beishu() : VT
      {
         return this._beishu;
      }
      
      public function set beishu(param1:VT) : void
      {
         this._beishu = param1;
      }
      
      public function get beishuValue() : VT
      {
         return this._beishuValue;
      }
      
      public function set beishuValue(param1:VT) : void
      {
         this._beishuValue = param1;
      }
      
      public function getColorType() : Number
      {
         return this._colorType.getValue();
      }
      
      public function getAttribType() : Number
      {
         return this._attribType.getValue();
      }
      
      public function getValue() : Number
      {
         return this._value.getValue();
      }
      
      public function setValue(param1:Number) : void
      {
         this._value.setValue(param1);
      }
      
      public function getBeishu() : Number
      {
         return this._beishu.getValue();
      }
      
      public function setBeishu(param1:Number) : void
      {
         this._beishu.setValue(param1);
      }
      
      public function getBeishuValue() : Number
      {
         return this._beishuValue.getValue();
      }
      
      public function setBeishuValue(param1:Number) : void
      {
         this._beishuValue.setValue(param1);
      }
      
      public function getClone() : EquipBaseAttrib
      {
         return EquipBaseAttrib.creatEquipBaseAttrib(this._colorType.getValue(),this._attribType.getValue(),this._value.getValue());
      }
   }
}

