package src.other
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_guangDun extends MovieClip
   {
      public static var noTime:int;
      
      public var who:Player;
      
      public var gjTime:int;
      
      public function Skill_guangDun()
      {
         super();
      }
      
      public static function addToPlayer(param1:Player) : *
      {
         var _loc3_:Class = null;
         var _loc2_:Number = Math.random() * 100;
         if(_loc2_ > 35 || noTime > 0)
         {
            if(_loc2_ > 35 && noTime <= 0)
            {
               noTime = 80;
            }
            return;
         }
         if(EquipYN(param1))
         {
            _loc3_ = NewLoad.OtherData.getClass("圣光护壁") as Class;
            param1.guangDun = new _loc3_();
            param1.guangDun.who = param1;
            param1.guangDun.init();
            Main.world.moveChild_Other.addChild(param1.guangDun);
         }
      }
      
      public static function EquipYN(param1:Player) : Boolean
      {
         var _loc2_:Equip = null;
         if(<PERSON><PERSON>an(param1) && Boolean(param1.guangDun))
         {
            param1.guangDun.removeX();
         }
         if(Boolean(param1) && Boolean(param1.data.getEquipSlot().getEquipFromSlot(6)))
         {
            _loc2_ = param1.data.getEquipSlot().getEquipFromSlot(6);
            if(_loc2_ && _loc2_.getFrame() == 485 && _loc2_.getRemainingTime() > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function init() : *
      {
         this.gjTime = 216;
         this.addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function removeX() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         this.who.guangDun = null;
         this.removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.gjTime = 0;
      }
      
      public function onENTER_FRAME(param1:*) : *
      {
         --this.gjTime;
         if(this.gjTime <= 0)
         {
            this.removeX();
         }
         this.Move();
      }
      
      public function Move() : *
      {
         this.x = this.who.x;
         this.y = this.who.y;
      }
   }
}

