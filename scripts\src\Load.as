package src
{
   import com.hotpoint.braveManIII.repository.skill.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.npc.*;
   import src.tool.*;
   
   public class Load extends MovieClip
   {
      public static var one:Boolean;
      
      public static var loadingX:Load;
      
      public static var percentLoaded:int;
      
      public static var All_Loaded:Boolean;
      
      public static var loadOk:Boolean;
      
      public static var enemyArrX2:Array;
      
      public static var XMLclass:ClassLoader;
      
      public static var loadName:int = 0;
      
      public static var loadName2:int = 14;
      
      public static var txtArr:Array = ["右手的武器需要将背包中的武器拖动到右手武器槽里才能替换","点击“菜单”栏内的“键位设置”按钮可以设置游戏快捷键","在关卡中可以点击“菜单”栏内的“返回城镇”按钮快速回城","玩家等级达到25级并且拥有“胜利的凭证”与“8000金币”，即可在奥古斯汀处进行转职","转职之后可以学习更多更炫的转职技能","您只能从两个职业中选择一个进行转职","女神碎片可在“冰雪废墟”、“废弃都市”与“幽灵船”关卡内获得","每为女神像修补一块女神碎片便可以开启一个神秘副本","获得女神像碎片后，只需点击女神像相应缺口即可对女神像进行修补","神秘副本的Boss鳄鱼公爵有一定机率掉落转职物品“胜利的凭证”","可以通过“镶嵌属性石”让装备更加强劲","1P和2P的仓库是共用的，可以通过仓库进行1P、2P间的物品交换","“装备商人—道格拉斯”可以进行“强化”“合成”“镶嵌”等功能","如果游戏过程比较卡，可以适当降低游戏画质","在“技能导师—奥古斯汀”处可学习各系职业的“主动技能”和“被动技能”","如果游戏卡在加载界面，建议刷新游戏页面尝试","剑系的“断空追击”“狱火天焰”技能，发动时有无敌状态","属性石分为“防御”“攻击”“生命”“魔法”“暴击”“闪避”六种类型","神秘副本的BOSS都有几率掉落“史诗武器”“强化石”等稀有物品","所有BOSS都有几率掉落该BOSS的“技能石”","通过“强化装备”可以大幅度提升装备属性","转职后装备两把相同类型的武器，可以大幅提升玩家属性噢","熟练连击技巧可以使你更轻松的过关","打低级关卡赚钱是一个很好的赚钱办法噢"];
      
      public static var mapArr:Array = [];
      
      public static var enemyArr:Array = [];
      
      public static var otherArr:Array = [];
      
      private static var 提示time:int = 135;
      
      private static var timeXX:uint = 0;
      
      public static var loadGameNumYY:int = 1;
      
      private static var yaoYuan_All_Arr:Array = [7,4,8,6,5,52,9,59,16,13,5,60,62,22,3000];
      
      public static var loadGameNum:int = 1;
      
      private static var Enemy_All_Arr:Array = [1,2,3,4,5,6,7,8,9,10,11,13,18,19,20,21,22,101,102,103,201,203,1001];
      
      public static var loadGameNumQ:int = 1;
      
      private static var Enemy_All_ArrX:Array = [5,6,7,8,9,10,13,20,24,25,104,213,234,1000];
      
      public static var loadGameNumQX:int = 1;
      
      private static var Enemy_All_ArrXX:Array = [212,24,25,54,21,18,104,16,29,2000];
      
      public static var loadGameNum2015:int = 1;
      
      private static var Enemy_All_Arr2015:Array = [2015];
      
      public static var loadGameNum2:int = 1;
      
      private static var Enemy_All_Arr2:Array = [51,52,53,81];
      
      public static var loadGameNum3:int = 1;
      
      private static var Enemy_All_Arr3:Array = [54,55,56,82];
      
      public static var loadGameNum4:int = 1;
      
      private static var Enemy_All_Arr4:Array = [60,61,84];
      
      public function Load()
      {
         super();
      }
      
      public static function Open(param1:int = 0, param2:int = 0) : *
      {
         if(!loadingX)
         {
            loadingX = new Load();
         }
         Main._stage.addChild(loadingX);
         loadingX.x = param1;
         loadingX.y = param2;
         loadingX.visible = true;
         Loading();
         loadingX._mc.gotoAndPlay(1);
         loadingX.addEventListener(Event.ENTER_FRAME,onLoading);
      }
      
      public static function Close() : *
      {
         if(!loadingX)
         {
            loadingX = new Load();
         }
         loadingX.x = 5000;
         loadingX.y = 5000;
         loadingX.visible = false;
         loadingX._mc.stop();
         loadingX.removeEventListener(Event.ENTER_FRAME,onLoading);
         Main._this._stage_txt.text += "Load.Close() \n";
      }
      
      private static function onLoading(param1:*) : *
      {
         loadingX.load_mc.load100_mc.scaleX = Load.percentLoaded / 100;
         loadingX.load_txt.text = Load.percentLoaded + "%";
         if(Load.loadName == -1)
         {
            loadingX.load_name_txt.text = "加载完成";
         }
         else
         {
            loadingX.load_name_txt.text = Load.loadName + "/" + Load.loadName2;
         }
         ++提示time;
         if(提示time % 135 == 0)
         {
            提示();
         }
      }
      
      private static function 提示() : *
      {
         var _loc1_:int = Math.random() * txtArr.length;
         loadingX.gun_txt.text = txtArr[_loc1_];
      }
      
      public static function Loading(param1:int = 0, param2:Boolean = true) : *
      {
         if(!param2)
         {
            loadingX.visible = false;
         }
         else if(!loadingX.visible)
         {
            loadingX.visible = true;
            return;
         }
         if(Main.gameNum.getValue() == 0)
         {
            if(Main.newPlay == 1)
            {
               Load.loadName2 = 27;
            }
            else
            {
               Load.loadName2 = 23;
            }
         }
         else if(Main.gameNum.getValue() == 81)
         {
            Load.loadName2 = 5;
         }
         else if(Main.gameNum.getValue() == 1000)
         {
            Load.loadName2 = Enemy_All_ArrX.length;
         }
         else if(Main.gameNum.getValue() == 2000)
         {
            Load.loadName2 = Enemy_All_ArrXX.length;
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            Load.loadName2 = yaoYuan_All_Arr.length;
         }
         else if(Main.gameNum.getValue() != 17)
         {
            Load.loadName2 = 3;
         }
         else
         {
            Load.loadName2 = Enemy_All_Arr.length;
         }
         if(param1 == 0)
         {
            loadName = 1;
         }
         else
         {
            loadName = param1;
         }
         if(loadName == 1)
         {
            Loading_Map();
         }
         else if(loadName == 2)
         {
            if(Main.gameNum.getValue() == 17)
            {
               Loading_Enemy_All();
            }
            else if(Main.gameNum.getValue() == 81)
            {
               Loading_Enemy_All2();
            }
            else if(Main.gameNum.getValue() == 82)
            {
               Loading_Enemy_All3();
            }
            else if(Main.gameNum.getValue() == 84)
            {
               Loading_Enemy_All4();
            }
            else if(Main.gameNum.getValue() == 999)
            {
               All_Loaded = true;
            }
            else if(Main.gameNum.getValue() == 1000)
            {
               Loading_Enemy_AllX();
            }
            else if(Main.gameNum.getValue() == 2000)
            {
               Loading_Enemy_AllXX();
            }
            else if(Main.gameNum.getValue() == 2015)
            {
               Loading_Enemy2015();
            }
            else if(Main.gameNum.getValue() == 3000)
            {
               Loading_Enemy_All_YY();
            }
            else
            {
               Loading_Enemy();
            }
         }
         else if(loadName == 3)
         {
            Loading_Data();
         }
         else if(loadName == 4)
         {
            Loading_Player_1();
         }
         else if(loadName == 5)
         {
            Loading_Player_2();
         }
         else if(loadName == 6)
         {
            Loading_Player_3();
         }
         else if(loadName == 7)
         {
            Loading_WuQi_1();
         }
         else if(loadName == 8)
         {
            Loading_WuQi_2();
         }
         else if(loadName == 9)
         {
            Loading_WuQi_3();
         }
         else if(loadName == 10)
         {
            Loading_Data2();
         }
         else if(loadName == 11)
         {
            Loading_Music();
         }
         else if(loadName == 12)
         {
            Loading_ChongWu();
         }
         else if(loadName == 13)
         {
            Loading_ChongWu2();
         }
         else if(loadName == 14)
         {
            if(Main.newPlay == 1)
            {
               Load.Loading(15);
            }
            else
            {
               Loading_SelMap();
            }
         }
         else if(loadName == 15)
         {
            Loading_Npc();
         }
         else if(loadName == 16)
         {
            Load_Other();
         }
         else if(loadName == 17)
         {
            Load_Other2();
         }
         else if(loadName == 18)
         {
            if(Main.newPlay == 1)
            {
               Load.Loading(19);
            }
            else
            {
               Load_ChongZhi();
            }
         }
         else if(loadName == 19)
         {
            Loading_Player_4();
         }
         else if(loadName == 20)
         {
            Loading_WuQi_4();
         }
         else if(loadName == 21)
         {
            NewLoadX();
         }
         else if(loadName == -1)
         {
            if(Main.newPlay == 1)
            {
               Main.newPlay = 2;
            }
            if(param2)
            {
               All_Loaded = true;
            }
            loadOk = true;
         }
      }
      
      public static function md5_Start() : *
      {
         var _loc1_:ByteArray = null;
         var _loc2_:ByteArray = null;
         ++timeXX;
         if(timeXX % 16200 == 15200)
         {
            _loc1_ = Obj_Compare.getObj_ByteArray(SkillFactory.myXml);
            if(Obj_Compare.CompareByteArray(_loc1_,SkillFactory.myXmlMD5) == false)
            {
               Main.NoGame("修改技能表");
               return;
            }
         }
         else if(timeXX % 16200 == 16100)
         {
            _loc2_ = Obj_Compare.getObj_ByteArray(Enemy.EnemyXmlArr[GameData.gameLV]);
            if(Obj_Compare.CompareByteArray(_loc2_,Enemy.EnemyXmlArrMd5[GameData.gameLV]) == false)
            {
               Main.NoGame("修改怪物数据");
               return;
            }
         }
      }
      
      public static function Loading_Enemy_All_YY() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(yaoYuan_All_Arr[loadGameNumYY - 1]);
         if(!Enemy.EnemyArr[_loc1_])
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoading_Enemy_All_YY);
         }
         else
         {
            onLoading_Enemy_All_YY();
         }
      }
      
      private static function onLoading_Enemy_All_YY(param1:* = null) : *
      {
         ++loadGameNumYY;
         loadName = loadGameNumYY;
         if(loadName <= yaoYuan_All_Arr.length)
         {
            Loading_Enemy_All_YY();
         }
         else
         {
            loadGameNumYY = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(Enemy_All_Arr[loadGameNum - 1]);
         if(!Enemy.EnemyArr[_loc1_])
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoading_Enemy_All);
         }
         else
         {
            onLoading_Enemy_All();
         }
      }
      
      private static function onLoading_Enemy_All(param1:* = null) : *
      {
         ++loadGameNum;
         loadName = loadGameNum;
         if(loadName <= Enemy_All_Arr.length)
         {
            Loading_Enemy_All();
         }
         else
         {
            loadGameNum = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_AllX() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(Enemy_All_ArrX[loadGameNumQ - 1]);
         if(!Enemy.EnemyArr[_loc1_])
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoading_Enemy_AllX);
         }
         else
         {
            onLoading_Enemy_AllX();
         }
      }
      
      private static function onLoading_Enemy_AllX(param1:* = null) : *
      {
         ++loadGameNumQ;
         loadName = loadGameNumQ;
         if(loadName <= Enemy_All_ArrX.length)
         {
            Loading_Enemy_AllX();
         }
         else
         {
            loadGameNumQ = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_AllXX() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(Enemy_All_ArrXX[loadGameNumQX - 1]);
         if(!Enemy.EnemyArr[_loc1_])
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoading_Enemy_AllXX);
         }
         else
         {
            onLoading_Enemy_AllXX();
         }
      }
      
      private static function onLoading_Enemy_AllXX(param1:* = null) : *
      {
         ++loadGameNumQX;
         loadName = loadGameNumQX;
         if(loadName <= Enemy_All_ArrXX.length)
         {
            Loading_Enemy_AllXX();
         }
         else
         {
            loadGameNumQX = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy2015() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(Enemy_All_Arr2015[loadGameNum2015 - 1]);
         if(!Enemy.EnemyArr[_loc1_])
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoading_Enemy_All2015);
         }
         else
         {
            onLoading_Enemy_All2015();
         }
      }
      
      private static function onLoading_Enemy_All2015(param1:* = null) : *
      {
         ++loadGameNum2015;
         loadName = loadGameNum2015;
         if(loadName <= Enemy_All_Arr2015.length)
         {
            ChongWu.chongWu_Data[26] = Enemy.EnemyArr[loadGameNumXX2015];
            Loading_Enemy2015();
         }
         else
         {
            loadGameNum2015 = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All2() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(Enemy_All_Arr2[loadGameNum2 - 1]);
         if(!Enemy.EnemyArr[_loc1_])
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoading_Enemy_All2);
         }
         else
         {
            onLoading_Enemy_All2();
         }
      }
      
      private static function onLoading_Enemy_All2(param1:* = null) : *
      {
         ++loadGameNum2;
         loadName = loadGameNum2;
         if(loadName <= Enemy_All_Arr2.length)
         {
            Loading_Enemy_All2();
         }
         else
         {
            loadGameNum2 = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All3() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(Enemy_All_Arr3[loadGameNum3 - 1]);
         if(!Enemy.EnemyArr[_loc1_])
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoading_Enemy_All3);
         }
         else
         {
            onLoading_Enemy_All3();
         }
      }
      
      private static function onLoading_Enemy_All3(param1:* = null) : *
      {
         ++loadGameNum3;
         loadName = loadGameNum3;
         if(loadName <= Enemy_All_Arr3.length)
         {
            Loading_Enemy_All3();
         }
         else
         {
            loadGameNum3 = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All4() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(Enemy_All_Arr4[loadGameNum4 - 1]);
         if(!Enemy.EnemyArr[_loc1_])
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoading_Enemy_All4);
         }
         else
         {
            onLoading_Enemy_All4();
         }
      }
      
      private static function onLoading_Enemy_All4(param1:* = null) : *
      {
         ++loadGameNum4;
         loadName = loadGameNum4;
         if(loadName <= Enemy_All_Arr4.length)
         {
            Loading_Enemy_All4();
         }
         else
         {
            loadGameNum4 = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Map() : *
      {
         var _loc1_:int = 0;
         var _loc2_:String = null;
         if(Map.MapArr[Main.gameNum.getValue()])
         {
            Load.Loading(2);
         }
         else
         {
            _loc1_ = int(Main.gameNum.getValue());
            if(_loc1_ > 18 && _loc1_ < 30)
            {
               _loc1_ = 18;
            }
            if(_loc1_ > 5000 && _loc1_ < 5100)
            {
               _loc1_ = 2015;
            }
            _loc2_ = mapArr[_loc1_];
            Map.MapArr[Main.gameNum.getValue()] = new ClassLoader(_loc2_);
            Map.MapArr[Main.gameNum.getValue()].addEventListener(Event.COMPLETE,onLoaded_Map);
         }
      }
      
      private static function onLoaded_Map(param1:* = null) : *
      {
         Load.Loading(2);
      }
      
      public static function Loading_Enemy() : *
      {
         var _loc1_:int = 0;
         var _loc2_:String = null;
         if(Main.gameNum.getValue() == 0)
         {
            Load.Loading(3);
         }
         else if(Enemy.EnemyArr[Main.gameNum.getValue()])
         {
            loadName = 3;
            Loading_EnemyX2();
         }
         else
         {
            _loc1_ = int(Main.gameNum.getValue());
            if(_loc1_ == 5001)
            {
               _loc1_ = 3;
            }
            else if(_loc1_ == 5002)
            {
               _loc1_ = 4;
            }
            else if(_loc1_ == 5003)
            {
               _loc1_ = 8;
            }
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoaded_Enemy);
         }
      }
      
      private static function onLoaded_Enemy(param1:* = null) : *
      {
         if(Main.gameNum.getValue() == 0)
         {
            Load.Loading(3);
         }
         else
         {
            loadName = 3;
            Loading_EnemyX2();
         }
      }
      
      public static function Loading_EnemyX2() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = int(Main.wts.getBoss());
         if(_loc1_ == -1 || Boolean(Enemy.EnemyArr[_loc1_]))
         {
            onLoaded_EnemyX2();
         }
         else
         {
            _loc2_ = enemyArr[_loc1_];
            Enemy.EnemyArr[_loc1_] = new ClassLoader(_loc2_);
            Enemy.EnemyArr[_loc1_].addEventListener(Event.COMPLETE,onLoaded_EnemyX2);
         }
      }
      
      private static function onLoaded_EnemyX2(param1:* = null) : *
      {
         All_Loaded = true;
      }
      
      public static function Loading_Data() : *
      {
         if(!XMLclass)
         {
            XMLclass = new ClassLoader(otherArr[0]);
            XMLclass.addEventListener(Event.COMPLETE,onLoaded_Data);
         }
         else
         {
            Load.Loading(4);
         }
      }
      
      private static function onLoaded_Data(param1:* = null) : *
      {
         Load.Loading(4);
      }
      
      public static function Loading_Player_1() : *
      {
         if(!Player.PlayerMcArr[0])
         {
            Player.PlayerMcArr[0] = new ClassLoader(otherArr[1]);
            Player.PlayerMcArr[0].addEventListener(Event.COMPLETE,onLoaded_Player1);
         }
         else
         {
            Load.Loading(5);
         }
      }
      
      private static function onLoaded_Player1(param1:* = null) : *
      {
         Load.Loading(5);
      }
      
      public static function Loading_Player_2() : *
      {
         if(!Player.PlayerMcArr[1])
         {
            Player.PlayerMcArr[1] = new ClassLoader(otherArr[2]);
            Player.PlayerMcArr[1].addEventListener(Event.COMPLETE,onLoaded_Player2);
         }
         else
         {
            Load.Loading(6);
         }
      }
      
      private static function onLoaded_Player2(param1:* = null) : *
      {
         Load.Loading(6);
      }
      
      public static function Loading_Player_3() : *
      {
         if(!Player.PlayerMcArr[2])
         {
            Player.PlayerMcArr[2] = new ClassLoader(otherArr[3]);
            Player.PlayerMcArr[2].addEventListener(Event.COMPLETE,onLoaded_Player3);
         }
         else
         {
            Load.Loading(7);
         }
      }
      
      private static function onLoaded_Player3(param1:* = null) : *
      {
         Load.Loading(7);
      }
      
      public static function Loading_Player_4() : *
      {
         if(!Player.PlayerMcArr[3])
         {
            Player.PlayerMcArr[3] = new ClassLoader(otherArr[19]);
            Player.PlayerMcArr[3].addEventListener(Event.COMPLETE,onLoaded_Player4);
         }
         else
         {
            Load.Loading(20);
         }
      }
      
      private static function onLoaded_Player4(param1:* = null) : *
      {
         Load.Loading(20);
      }
      
      public static function Loading_WuQi_1() : *
      {
         if(!Skin_WuQi.PlayerMcArr[0])
         {
            Skin_WuQi.PlayerMcArr[0] = new ClassLoader(otherArr[7]);
            Skin_WuQi.PlayerMcArr[0].addEventListener(Event.COMPLETE,onWuQi1);
         }
         else
         {
            Load.Loading(8);
         }
      }
      
      private static function onWuQi1(param1:* = null) : *
      {
         Load.Loading(8);
      }
      
      public static function Loading_WuQi_2() : *
      {
         if(!Skin_WuQi.PlayerMcArr[1])
         {
            Skin_WuQi.PlayerMcArr[1] = new ClassLoader(otherArr[8]);
            Skin_WuQi.PlayerMcArr[1].addEventListener(Event.COMPLETE,onWuQi2);
         }
         else
         {
            Load.Loading(9);
         }
      }
      
      private static function onWuQi2(param1:* = null) : *
      {
         Load.Loading(9);
      }
      
      public static function Loading_WuQi_3() : *
      {
         if(!Skin_WuQi.PlayerMcArr[2])
         {
            Skin_WuQi.PlayerMcArr[2] = new ClassLoader(otherArr[9]);
            Skin_WuQi.PlayerMcArr[2].addEventListener(Event.COMPLETE,onWuQi3);
         }
         else
         {
            Load.Loading(10);
         }
      }
      
      private static function onWuQi3(param1:* = null) : *
      {
         Load.Loading(10);
      }
      
      public static function Loading_WuQi_4() : *
      {
         if(!Skin_WuQi.PlayerMcArr[3])
         {
            Skin_WuQi.PlayerMcArr[3] = new ClassLoader(otherArr[20]);
            Skin_WuQi.PlayerMcArr[3].addEventListener(Event.COMPLETE,onWuQi4);
         }
         else
         {
            Load.Loading(21);
         }
      }
      
      private static function onWuQi4(param1:* = null) : *
      {
         Load.Loading(21);
      }
      
      private static function Loading_Data2() : *
      {
         onLoading_Data2();
      }
      
      private static function onLoading_Data2(param1:* = null) : *
      {
         Load.Loading(11);
      }
      
      private static function Loading_Music() : *
      {
         if(!MusicBox.MusicData)
         {
            MusicBox.MusicData = new ClassLoader(otherArr[11]);
            MusicBox.MusicData.addEventListener(Event.COMPLETE,onLoading_Music);
         }
         else
         {
            Load.Loading(12);
         }
      }
      
      private static function onLoading_Music(param1:* = null) : *
      {
         Load.Loading(12);
      }
      
      private static function Loading_ChongWu() : *
      {
         if(!ChongWu.loadData)
         {
            ChongWu.loadData = new ClassLoader(otherArr[12]);
            ChongWu.loadData.addEventListener(Event.COMPLETE,onLoading_ChongWu);
         }
         else
         {
            Load.Loading(13);
         }
      }
      
      private static function onLoading_ChongWu(param1:* = null) : *
      {
         Load.Loading(13);
      }
      
      private static function Loading_ChongWu2() : *
      {
         Load.Loading(14);
      }
      
      private static function onLoading_ChongWu2(param1:* = null) : *
      {
         Load.Loading(14);
      }
      
      private static function Loading_SelMap() : *
      {
         if(!SelMap.loadData)
         {
            SelMap.loadData = new ClassLoader(otherArr[17]);
            SelMap.loadData.addEventListener(Event.COMPLETE,onLoading_SelMap);
         }
         else
         {
            Load.Loading(15);
         }
      }
      
      private static function onLoading_SelMap(param1:* = null) : *
      {
         Load.Loading(15);
      }
      
      private static function Loading_Npc() : *
      {
         if(!All_Npc.loadData)
         {
            All_Npc.loadData = new ClassLoader(otherArr[18]);
            All_Npc.loadData.addEventListener(Event.COMPLETE,onLoading_Npc);
         }
         else
         {
            Load.Loading(16);
         }
      }
      
      private static function onLoading_Npc(param1:* = null) : *
      {
         Load.Loading(16);
      }
      
      public static function Load_Other() : *
      {
         if(!NewLoad.OtherData)
         {
            NewLoad.OtherData = new ClassLoader("Other_v1507.swf");
            NewLoad.OtherData.addEventListener(Event.COMPLETE,LoadOtherOK);
         }
         else
         {
            Load.Loading(17);
         }
      }
      
      public static function LoadOtherOK(param1:*) : *
      {
         Load.Loading(17);
         NewLoad.Other_YN = true;
      }
      
      public static function Load_Other2() : *
      {
         if(!NewLoad.XiaoGuoData)
         {
            NewLoad.XiaoGuoData = new ClassLoader("newMc_v1721.swf");
            NewLoad.XiaoGuoData.addEventListener(Event.COMPLETE,LoadOtherOK2);
         }
         else
         {
            Load.Loading(18);
         }
      }
      
      public static function LoadOtherOK2(param1:*) : *
      {
         Load.Loading(18);
         NewLoad.Other_YN = true;
      }
      
      public static function Load_ChongZhi() : *
      {
         if(!NewLoad.chongZhiData)
         {
            NewLoad.chongZhiData = new ClassLoader("ChongZhi_v1601.swf");
            NewLoad.chongZhiData.addEventListener(Event.COMPLETE,Load_ChongZhiOK);
         }
         else
         {
            Load.Loading(19);
         }
      }
      
      public static function Load_ChongZhiOK(param1:*) : *
      {
         Load.Loading(19);
      }
      
      public static function NewLoadX() : *
      {
         NewLoad.Loading();
      }
      
      public static function newLoad_Ok(param1:* = null) : *
      {
         if(Main.newPlay == 1)
         {
            Main.gameNum.setValue(888);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            Loading_Map2();
         }
         else
         {
            Load.Loading(-1);
            one = true;
         }
      }
      
      public static function Loading_Map2() : *
      {
         var _loc1_:int = 0;
         var _loc2_:String = null;
         if(Map.MapArr[Main.gameNum.getValue()])
         {
            loadName = 25;
            Loading_Enemy2();
         }
         else
         {
            loadName = 24;
            _loc1_ = int(Main.gameNum.getValue());
            _loc2_ = mapArr[_loc1_];
            Map.MapArr[Main.gameNum.getValue()] = new ClassLoader(_loc2_);
            Map.MapArr[Main.gameNum.getValue()].addEventListener(Event.COMPLETE,onLoaded_Map2);
         }
      }
      
      private static function onLoaded_Map2(param1:* = null) : *
      {
         Main._this._stage_txt.text += "Load newLoad_Ok......................1() \n";
         loadName = 25;
         Loading_Enemy2();
      }
      
      public static function Loading_Enemy2() : *
      {
         var _loc1_:String = null;
         if(Main.gameNum.getValue() == 0)
         {
            onLoaded_Enemy2();
         }
         else
         {
            _loc1_ = enemyArr[Main.gameNum.getValue()];
            Enemy.EnemyArr[Main.gameNum.getValue()] = new ClassLoader(_loc1_);
            Enemy.EnemyArr[Main.gameNum.getValue()].addEventListener(Event.COMPLETE,onLoaded_Enemy2);
         }
      }
      
      private static function onLoaded_Enemy2(param1:* = null) : *
      {
         Main._this._stage_txt.text += "Load newLoad_Ok......................2() \n";
         Load.Loading(-1);
      }
   }
}

