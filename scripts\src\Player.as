package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.elves.Elves;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.pet.Pet;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.skill.Skill;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import flash.display.*;
   import flash.events.*;
   import src.Skin.*;
   import src._data.*;
   import src.other.*;
   import src.tool.*;
   
   public class Player extends MovieClip
   {
      public static var LV_data:XML;
      
      public static var enemySkillMC:*;
      
      public static var szCD:int = 0;
      
      public static var skillYAO:int = 0;
      
      public static var upLevelInfo:Boolean = false;
      
      public static var maxLevel:int = 99;
      
      public static var All:Array = [];
      
      public static var PlayerMcLoaded:Boolean = false;
      
      public static var PlayerMcArr:Array = new Array();
      
      public static var num_1:VT = VT.createVT();
      
      public static var num_15:VT = VT.createVT();
      
      public static var num_20:VT = VT.createVT();
      
      public static var num_25:VT = VT.createVT();
      
      public static var num_30:VT = VT.createVT();
      
      public static var num_32:VT = VT.createVT(0.32);
      
      public static var num_35:VT = VT.createVT();
      
      public static var num_7:VT = VT.createVT();
      
      public static var num_5:VT = VT.createVT();
      
      public static var num_27:VT = VT.createVT();
      
      public static var num_13:VT = VT.createVT();
      
      public static var num_65:VT = VT.createVT();
      
      public static var Vip_Point_YN:Boolean = true;
      
      private static var Temp11:VT = VT.createVT(VT.GetTempVT("10+1/10"));
      
      public static var boolYS:Boolean = true;
      
      public var jianCD_lei:Boolean = false;
      
      public var jianCD:Boolean = false;
      
      public var fbChengFa:Boolean = false;
      
      public var darkTime:int = 0;
      
      public var isDarkState:Boolean = false;
      
      public var debuff:int = 0;
      
      public var gongji_UP:Number = 1;
      
      public var jianSang_UP:Number = 1;
      
      public var noYingZhiTime:int = 0;
      
      public var cengshu:Array = [];
      
      public var zhongqiuState:Number = 0;
      
      public var jianrenState:Number = 0;
      
      public var shengmingState:Number = 0;
      
      public var reXueType:Boolean = false;
      
      public var lianjiBool:Boolean = false;
      
      public var isDead:Boolean = false;
      
      public var noMove:Boolean = false;
      
      private var tempXXX2:VT = VT.createVT(VT.GetTempVT("8/4"));
      
      public var hpXX2num:int = 0;
      
      public var hitXX:HitXX;
      
      public var shuangShouBeiShu:Number = 1;
      
      public var energySlot:EnergySlot = new EnergySlot();
      
      public var skin:Skin;
      
      public var cengHao_mc:CengHao = new CengHao();
      
      public var backMC:MovieClip;
      
      public var skin_Z:Skin_ZhuangBei;
      
      public var skin_Z2:Skin_ZhuangBei;
      
      public var skin_Z3:Skin_ZhuangBei;
      
      public var skin_Z2_V:Boolean = true;
      
      public var skin_Z3_V:Boolean = true;
      
      public var headFrame:int = 1;
      
      public var skin_W:Skin_WuQi;
      
      public var playerCW:ChongWu;
      
      public var playerJL:JingLing;
      
      public var hit:MovieClip = null;
      
      private var nodead_C:Class = NewLoad.XiaoGuoData.getClass("undeath") as Class;
      
      private var nodead:MovieClip = new this.nodead_C();
      
      public var deadX:Dead;
      
      public var 职业附加:Array = [1,1,1,1];
      
      public var 套装强化:Array = [0,0,0,0,0,false,false,false,false];
      
      public var 装备附加:Array = [];
      
      public var time:int = 1000;
      
      public var lianJi:VT = VT.createVT(0);
      
      public var lianJiTime:VT = VT.createVT(60);
      
      public var data:PlayerData;
      
      public var nextExp:VT = VT.createVT();
      
      public var speedTemp:VT = VT.createVT(0);
      
      public var hp:VT = VT.createVT();
      
      public var mp:VT = VT.createVT();
      
      public var hp_Max:VT = VT.createVT();
      
      public var mp_Max:VT = VT.createVT();
      
      public var gongji:VT = VT.createVT();
      
      public var fangyu:VT = VT.createVT();
      
      public var baoji:VT = VT.createVT();
      
      public var sanbi:VT = VT.createVT();
      
      public var walk_power:VT = VT.createVT();
      
      public var walk_power2:Number = 1;
      
      public var yingzhi:VT = VT.createVT();
      
      public var gongji2:VT = VT.createVT();
      
      public var fangyu2:VT = VT.createVT();
      
      public var use_hp_Max:VT = VT.createVT();
      
      public var use_mp_Max:VT = VT.createVT();
      
      public var use_gongji:VT = VT.createVT();
      
      public var use_gongji2:VT = VT.createVT();
      
      public var use_fangyu:VT = VT.createVT();
      
      public var use_fangyu2:VT = VT.createVT();
      
      public var use_baoji:VT = VT.createVT();
      
      public var use_sanbi:VT = VT.createVT();
      
      public var temp_hp_Max:VT = VT.createVT();
      
      public var temp_mp_Max:VT = VT.createVT();
      
      public var temp_gongji:VT = VT.createVT();
      
      public var temp_fangyu:VT = VT.createVT();
      
      public var temp_baoji:VT = VT.createVT();
      
      public var temp_sanbi:VT = VT.createVT();
      
      public var jianCDnum:int = 1;
      
      public var jianCDtemp:int = 0;
      
      public var jianCDBool:Boolean = false;
      
      public var hpDownXX:Number = 1;
      
      public var AllSkillCD:Array = new Array();
      
      public var AllSkillCDXX:Array = new Array();
      
      public var All_ObjCD:Array = new Array();
      
      public var All_ObjCDXX:Array = new Array();
      
      public var num_hp:VT = VT.createVT();
      
      public var num_mp:VT = VT.createVT();
      
      public var num_gj:VT = VT.createVT();
      
      public var num_fy:VT = VT.createVT();
      
      public var num_sb:VT = VT.createVT();
      
      public var num_bj:VT = VT.createVT();
      
      public var num_sd:VT = VT.createVT();
      
      public var num_pm:VT = VT.createVT();
      
      public var num_mk:VT = VT.createVT();
      
      public var num_baoSang:VT = VT.createVT();
      
      public var RL:Boolean = true;
      
      private var jump_power:* = 145;
      
      private var jump_time:* = 8;
      
      private var parabola:Number = 0.3;
      
      public var jumping:int = 0;
      
      private var jumpX2:Boolean = false;
      
      private var jumpType:int = 2;
      
      public var gravity:int = 2;
      
      private var gravityNum:* = 0;
      
      public var gjXX_num:int = 0;
      
      private var SwitchingTime:int;
      
      public var paiHangShow:Array = [1,1,1,[0],[0]];
      
      public var fly4004_3_YN:Boolean = false;
      
      public var noHit:Boolean = false;
      
      public var noHit82:Boolean = false;
      
      public var noYingZhi:Boolean = false;
      
      private var XXSupplies:Supplies;
      
      public var guangQiu:Skill_guangQiu;
      
      public var guangDun:Skill_guangDun;
      
      private var heiAnMove:Boolean;
      
      public var heiAnJiNeng_width:int = 0;
      
      public var heiAnJiNeng:Array = [0,false,false,false,false,false,false,false,false];
      
      public var rzJN_hpArr:Array = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
      
      public var fangYuPOWER:VT = VT.createVT();
      
      private var KeyArrStr:Array = ["上","下","左","右","攻击","跳","切换","技能1","技能2","技能3","技能4","消耗1","消耗2","消耗3","转职","怪物"];
      
      private var getAllMoveSpeed:VT = VT.createVT(-99);
      
      private var getAllSuitSkill:Array;
      
      private var getAllEquipSkill:Array;
      
      private var ZB_HpMpUP_Arr:Array = [];
      
      internal var xuemaiTime:int = 0;
      
      internal var xuemaiTime2:int = 0;
      
      private var dkState:MovieClip = new DarkPower();
      
      private var timeXXX:int = 0;
      
      public var noHead:Boolean = false;
      
      public var flagTempMP:Boolean = true;
      
      public var flagTempXL:Boolean = true;
      
      public var flagTempSY:Boolean = true;
      
      public var flagTempSJ:Boolean = true;
      
      public var flagTempRX:Boolean = true;
      
      public var flagTempBJ:Boolean = true;
      
      public var timeTemp:int = 0;
      
      public var timeTempmp:int = 0;
      
      public var rexuewudi:Boolean = false;
      
      internal var kouxue:Boolean = false;
      
      internal var tankaiTime:int = 0;
      
      internal var iceTIME:int = 0;
      
      internal var jKey:Boolean = false;
      
      public var flyTime:int = 0;
      
      public var fly:Boolean = false;
      
      public var KeyControl_YN:Boolean = true;
      
      public var newLifeTime:VT = VT.createVT();
      
      internal var tempCXHF:int = 0;
      
      internal var tempVaPer:int = 0;
      
      internal var tempChiXuTime:int = 0;
      
      private var huiFuTime:int = -1;
      
      private var runX:int = 0;
      
      private var runY:int = 0;
      
      private var runArr:Array = new Array();
      
      public var playerYS:YongShi61;
      
      private var feixingTime:int = 0;
      
      private var feixingBool:Boolean = false;
      
      private var class_ps:Class = NewLoad.XiaoGuoData.getClass("高能喷射") as Class;
      
      private var feixingBool2:Boolean = false;
      
      private var feixingBool3:Boolean = false;
      
      private var penSheTime:int = 0;
      
      private var nbTime:int = 0;
      
      public function Player()
      {
         super();
         mouseChildren = false;
         mouseEnabled = false;
         this.deadX = new Dead();
         addChild(this.deadX);
         this.deadX.visible = false;
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public static function Init() : *
      {
         num_hp = VT.createVT(1);
         num_mp = VT.createVT(1);
         num_gj = VT.createVT(1);
         num_fy = VT.createVT(1);
         num_sb = VT.createVT(1);
         num_bj = VT.createVT(1);
         num_pm = VT.createVT(1);
         num_mk = VT.createVT(1);
         num_sd = VT.createVT(0);
         num_5 = VT.createVT(0.05);
         num_1 = VT.createVT(0.1);
         num_15 = VT.createVT(0.15);
         num_20 = VT.createVT(0.2);
         num_25 = VT.createVT(0.25);
         num_30 = VT.createVT(0.3);
         num_35 = VT.createVT(0.35);
         num_7 = VT.createVT(0.07);
         num_13 = VT.createVT(0.13);
         num_27 = VT.createVT(0.27);
         num_65 = VT.createVT(1);
      }
      
      public static function getPlayerLvData(param1:PlayerData) : XML
      {
         var _loc2_:* = undefined;
         var _loc3_:int = 0;
         for(_loc2_ in LV_data.角色)
         {
            _loc3_ = int(LV_data.角色[_loc2_].等级);
            if(_loc3_ == param1.getLevel())
            {
               return LV_data.角色[_loc2_];
            }
         }
         return null;
      }
      
      public static function 一起信春哥() : *
      {
         Main.player_1.deadX.visible = false;
         Main.player_1.信春哥();
         Main.player_1.energySlot.setZero();
         Main.player_1.runArr = new Array();
         if(Main.P1P2)
         {
            Main.player_2.deadX.visible = false;
            Main.player_2.信春哥();
            Main.player_2.runArr = new Array();
            Main.player_2.energySlot.setZero();
         }
      }
      
      public static function getEquipDataXX() : *
      {
         Main.player_1.getEquipData(true);
         if(Main.P1P2)
         {
            Main.player_2.getEquipData(true);
         }
      }
      
      public static function Vip_Point() : *
      {
         if(Shop4399.totalPaiedMoney.getValue() < 0)
         {
            if(Vip_Point_YN)
            {
               Api_4399_All.TotalPaied();
               Vip_Point_YN = false;
            }
         }
      }
      
      public function HeiAnJiNengOk(param1:int = 1) : *
      {
         var _loc3_:Equip = null;
         var _loc4_:Number = NaN;
         var _loc2_:Equip = this.data.getEquipSlot().getEquipFromSlot(6);
         if(_loc2_ && _loc2_.getFrame() == 483 && _loc2_.getRemainingTime() > 0)
         {
            this.heiAnJiNeng[0] = 1;
            _loc3_ = this.data.getEquipSlot().getEquipFromSlot(7);
            if(_loc3_ && _loc3_.getFrame() == 484 && _loc3_.getRemainingTime() > 0)
            {
               this.heiAnJiNeng[0] = 2;
            }
            else
            {
               _loc4_ = Math.random() * 100;
               if(_loc4_ > 50)
               {
                  return;
               }
            }
         }
         else
         {
            this.heiAnJiNeng[0] = 0;
         }
         if(this.heiAnJiNeng[0])
         {
            if(this.data.skinNum == 1)
            {
               param1 += 4;
            }
            this.heiAnJiNeng[param1] = true;
         }
      }
      
      public function HeiAnJiNeng(param1:int = 1) : *
      {
         var _loc2_:int = 0;
         if(this.mp.getValue() < 1)
         {
            return;
         }
         if(this.data.skinNum == 1)
         {
            param1 += 4;
         }
         if(Boolean(this.heiAnJiNeng[0]) && Boolean(this.heiAnJiNeng[param1]))
         {
            this.heiAnJiNeng[param1] = false;
            if(this.RL)
            {
               this.runPower(350,10,1,"黑暗使徒时装技能");
            }
            else
            {
               this.runPower(-350,10,1,"黑暗使徒时装技能");
            }
            _loc2_ = this.mp.getValue() - this.use_mp_Max.getValue() / 100 * 2 * (1 - this.data.getStampSlot().getValueSlot9());
            if(_loc2_ < 1)
            {
               this.mp.setValue(1);
            }
            else
            {
               this.mp.setValue(_loc2_);
            }
         }
      }
      
      public function HeiAnJiNengGo() : *
      {
         var _loc1_:Class = null;
         var _loc2_:MovieClip = null;
         var _loc3_:* = undefined;
         if(this.heiAnMove)
         {
            _loc1_ = NewLoad.XiaoGuoData.getClass("黑暗使徒时装技能") as Class;
            _loc2_ = new _loc1_();
            this.addChild(_loc2_);
            if(this.RL)
            {
               _loc2_.scaleX = 1;
            }
            else
            {
               _loc2_.scaleX = -1;
            }
            _loc3_ = Math.abs(this.heiAnJiNeng_width);
            if(_loc3_ < 100)
            {
               _loc3_ = 100;
            }
            _loc2_.scaleX *= _loc3_ / 350;
         }
      }
      
      public function RZJN_hpDown() : *
      {
         this.rzJN_hpArr.splice(0,1);
         this.rzJN_hpArr.push(0);
      }
      
      public function RZJN_hpUP(param1:Number) : *
      {
         this.rzJN_hpArr[29] += param1;
      }
      
      public function RZJN() : *
      {
         var _loc1_:Number = 0;
         var _loc2_:* = 0;
         while(_loc2_ < this.rzJN_hpArr.length)
         {
            _loc1_ += this.rzJN_hpArr[_loc2_];
            _loc2_++;
         }
         var _loc3_:* = this.hp.getValue() + _loc1_;
         var _loc4_:int = this.use_hp_Max.getValue();
         if(_loc3_ < _loc4_)
         {
            this.hp.setValue(_loc3_);
         }
         else
         {
            this.hp.setValue(_loc4_);
         }
         NewMC.Open("回血效果",this,0,60,0,_loc1_);
      }
      
      public function hpDown_new(param1:int, param2:Boolean = false) : *
      {
         var _loc6_:Number = NaN;
         var _loc7_:Array = null;
         var _loc8_:int = 0;
         if(this.guangDun)
         {
            param1 *= 0.8;
         }
         this.RZJN_hpUP(param1);
         var _loc3_:int = this.hp.getValue() - param1;
         if(_loc3_ <= 0 && this.isDead == false && Main.gameNum.getValue() != 999)
         {
            _loc6_ = 0;
            _loc7_ = this.data.getEquipSlot().getAllSuitSkill();
            for(_loc8_ in _loc7_)
            {
               if(_loc7_[_loc8_] == 57362)
               {
                  if(Boolean(this.getRandom(50)) && this.flagTempRX)
                  {
                     addChild(this.nodead);
                     this.rexuewudi = true;
                     addEventListener(Event.ENTER_FRAME,this.wudi);
                     this.flagTempRX = false;
                     _loc6_ = 1;
                  }
               }
            }
            this.hp.setValue(_loc6_);
         }
         else
         {
            this.hp.setValue(_loc3_);
         }
         if(this.hp.getValue() <= 0)
         {
            this.isDead = true;
         }
         var _loc4_:int = this.x + Math.random() * 50 - 25;
         var _loc5_:int = this.y + Math.random() * 50 - 25;
         if(param1 == 0)
         {
            NewMC.Open("闪避2",Main.world.moveChild_Other,_loc4_,_loc5_,15,0,true,2);
         }
         else if(!param2)
         {
            NewMC.Open("_被打数字",Main.world.moveChild_Other,_loc4_,_loc5_,15,param1,true,2);
         }
         else
         {
            NewMC.Open("_暴击数字",Main.world.moveChild_Other,_loc4_,_loc5_,20,param1,true);
         }
      }
      
      private function onADDED_TO_STAGE(param1:*) : *
      {
         Main.world.moveChild_Player.addChild(this);
         All[All.length] = this;
         Main._stage.addEventListener(KeyboardEvent.KEY_DOWN,this.onKEY_DOWN);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public function 不信春哥() : *
      {
         this.isDead = false;
         this.debuff = 0;
         if(Main.gameNum.getValue() == 0 && this.visible)
         {
            this.复活药使用();
            return;
         }
         if(this.data.buffNine[9] > 0)
         {
            if(this.data.reBorn == 0)
            {
               this.复活药使用();
               this.data.reBorn = 3600;
               return;
            }
         }
         if(!this.deadX.visible)
         {
            NewMC.Open("死亡倒计时",this);
         }
         this.deadX.frame = this.headFrame;
         if(this.noHead)
         {
            this.deadX.head_mc.gotoAndStop(24);
            this.deadX.head_mc.visible = true;
         }
         else
         {
            this.deadX.head_mc.gotoAndStop(this.deadX.frame);
         }
         this.deadX.visible = true;
         this.skin_Z.visible = false;
         this.skin_W.visible = false;
         if(this.skin_Z2)
         {
            this.skin_Z2.visible = false;
         }
         if(this.skin_Z3)
         {
            this.skin_Z3.visible = false;
         }
         this.energySlot.setZero();
      }
      
      public function 信春哥() : *
      {
         this.isDead = false;
         if(this.visible == false)
         {
            return;
         }
         this.scaleY = 1;
         this.scaleX = 1;
         if(this.deadX && this.skin && this.skin_Z && Boolean(this.skin_W))
         {
            this.deadX.visible = false;
            this.skin.visible = true;
            this.skin_Z.visible = true;
            this.skin_W.visible = true;
            this.skin.GoTo("站",1,true);
         }
         this.PK_hpMax();
         this.hp.setValue(9999999);
         this.mp.setValue(9999999);
         continuousTime = 0;
         if(this.data.getPetSlot().getJilu() >= 0 && this.data.getPetSlot().getPetFromSlot(this.data.getPetSlot().getJilu()) && this.data.getPetSlot().getPetFromSlot(this.data.getPetSlot().getJilu()).getType() > 1)
         {
            this.data.playerCW_Data = this.data.getPetSlot().getPetFromSlot(this.data.getPetSlot().getJilu());
         }
      }
      
      private function onREMOVED_FROM_STAGE(param1:*) : *
      {
         this.skin.Over();
      }
      
      public function ExpUP(param1:int, param2:int = 1) : String
      {
         if(param2 != 2 && this.hp.getValue() <= 0)
         {
            return;
         }
         if(param2 == 3)
         {
            param1 = this.nextExp.getValue() * param1 / 100;
         }
         if(this.data.buffNine[0] > 0)
         {
            param1 *= 1.15;
         }
         else if(this.data.buffNine[8] > 0)
         {
            param1 *= 1.3;
         }
         var _loc3_:int = Math.abs(this.data.getEXP() + param1);
         if(this.data.getEXP() > 1234567890)
         {
            _loc3_ = 99999999;
         }
         this.data.setEXP(_loc3_);
         if(this.data.getLevel() < maxLevel)
         {
            if(this.data.getEXP() >= this.nextExp.getValue())
            {
               return this.LevelUP(param2);
            }
         }
         return "ok";
      }
      
      public function LevelUP(param1:int = 1) : *
      {
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         if(this.data.getLevel() < Player.maxLevel)
         {
            if(param1 == 1 || param1 == 3)
            {
               this.data.setEXP(0);
               this.LevelUP_X();
            }
            else if(param1 == 2)
            {
               while(this.data.getEXP() >= this.nextExp.getValue() && this.data.getLevel() < Player.maxLevel)
               {
                  this.data.setEXP(this.data.getEXP() - this.nextExp.getValue());
                  this.LevelUP_X();
               }
            }
            NewMC.Open("升级效果",this);
            if(this.data.getLevel() == 2)
            {
               NewMC.Open("文字提示",Main._this,470,500,150,0,true,1,"恭喜你升到2级! 你可以在主城找【奥古斯汀】学习技能啦!");
               upLevelInfo = true;
            }
            if(this.data.getLevel() >= 50)
            {
               this.data.setRebirth();
            }
         }
      }
      
      public function LevelUP_X() : *
      {
         this.data.setLevel(this.data.getLevel() + VT.GetTempVT("8/8"));
         TiaoShi.tempVar = "data.getLevel() = " + this.data.getLevel() + " , data.getEXP() = " + this.data.getEXP();
         if(this.data.isRebirth())
         {
            this.data.addPoint(VT.GetTempVT("10/2"));
         }
         else
         {
            this.data.addPoint(VT.GetTempVT("8/2"));
         }
         this.LoadPlayerLvData();
         this.hp.setValue(9999999);
         this.mp.setValue(9999999);
      }
      
      public function MoneyUP(param1:int) : *
      {
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         this.data.addGold(param1);
      }
      
      public function Load_All_Player_Data() : *
      {
         TiaoShi.txtShow("读取玩家数据");
         this.LoadPlayerLvData();
         this.GetAllSkillCD();
         this.GetAllObjCD();
         this.newSkin();
         this.信春哥();
         this.left_Reight();
         this.ChongSeng_no50lv();
      }
      
      private function ChongSeng_no50lv() : *
      {
         if(this.data.getLevel() >= 50)
         {
            this.data.setRebirth();
         }
      }
      
      public function left_Reight() : *
      {
         if(this.data.getEquipSkillSlot().getGemFromSkillSlot(0) != null)
         {
            if(Main.gameNum.getValue() == 0)
            {
               this.energySlot.energyLeftNum.setValue(0);
               this.energySlot.energyLeftMax.setValue(SkillFactory.getSkillById(this.data.getEquipSkillSlot().getGemFromSkillSlot(0).getGemSkill()).getEp());
            }
         }
         if(this.data.getEquipSkillSlot().getGemFromSkillSlot(1) != null)
         {
            if(Main.gameNum.getValue() == 0)
            {
               this.energySlot.energyRightNum.setValue(0);
               this.energySlot.energyRightMax.setValue(SkillFactory.getSkillById(this.data.getEquipSkillSlot().getGemFromSkillSlot(1).getGemSkill()).getEp());
            }
         }
      }
      
      public function LoadPlayerLvData() : *
      {
         if(this.data.getLevel() > maxLevel)
         {
            this.data.setLevel(maxLevel);
         }
         this.双手加成();
         var _loc1_:XML = Player.getPlayerLvData(this.data);
         this.nextExp.setValue(int(_loc1_.经验));
         var _loc2_:int = int(_loc1_.HP) + this.data.getEquipSlot().getAllHP();
         this.hp_Max.setValue(_loc2_);
         var _loc3_:int = int(_loc1_.MP) + this.data.getEquipSlot().getAllMP();
         this.mp_Max.setValue(_loc3_);
         var _loc4_:int = (int(_loc1_.攻击) + this.data.getEquipSlot().getAllAttack()) * this.shuangShouBeiShu;
         this.gongji.setValue(_loc4_);
         var _loc5_:int = int(_loc1_.防御) + this.data.getEquipSlot().getAllDefense();
         this.fangyu.setValue(_loc5_);
         var _loc6_:int = int(_loc1_.暴击) + this.data.getEquipSlot().getAllCrit();
         this.baoji.setValue(_loc6_);
         var _loc7_:int = int(_loc1_.闪避) + this.data.getEquipSlot().getAllDuck();
         this.sanbi.setValue(_loc7_);
         var _loc8_:int = int(_loc1_.硬值) + this.data.getEquipSlot().getAllHardValue();
         this.yingzhi.setValue(_loc8_);
         var _loc9_:int = this.data.getEquipSlot().getAllPOMO();
         this.gongji2.setValue(_loc9_);
         var _loc10_:int = this.data.getEquipSlot().getAllMOKANG();
         this.fangyu2.setValue(_loc10_);
         this.walk_power.setValue(6 + this.data.getEquipSlot().getAllMoveSpeed());
         this.LoadAll_D_Skill();
      }
      
      private function TestEquipSlot() : *
      {
         if(this.data.getEquipSlot().testAllEquip() == true)
         {
            Main.NoGame("5星装备");
         }
      }
      
      private function LoadAll_D_Skill() : *
      {
         var _loc1_:int = 0;
         var _loc2_:String = null;
         var _loc3_:Array = null;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         for(_loc1_ in this.data.getSkillArr())
         {
            _loc2_ = (this.data.getSkillArr()[_loc1_][0] as String).substr(0,1);
            if(_loc2_ == "d")
            {
               if(this.data.getSkillArr()[_loc1_][0] == "d1" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  this.jumpX2 = true;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d2" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc4_ = Number((_loc3_[0] as VT).getValue());
                  this.hp_Max.setValue(this.hp_Max.getValue() + _loc4_);
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d3" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc4_ = Number((_loc3_[0] as VT).getValue());
                  this.mp_Max.setValue(this.mp_Max.getValue() + _loc4_);
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d4" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc4_ = Number((_loc3_[0] as VT).getValue());
                  this.baoji.setValue(this.baoji.getValue() + _loc4_);
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d5" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc4_ = Number((_loc3_[0] as VT).getValue());
                  this.sanbi.setValue(this.sanbi.getValue() + _loc4_);
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d6" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc5_ = Number((_loc3_[0] as VT).getValue());
                  this.职业附加[0] = 1 + _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d7" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc5_ = Number((_loc3_[0] as VT).getValue());
                  this.职业附加[1] = 1 + _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d8" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc5_ = Number((_loc3_[0] as VT).getValue());
                  this.职业附加[2] = 1 + _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "k16" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc5_ = Number((_loc3_[0] as VT).getValue());
                  this.职业附加[3] = 1 + _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d9" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc5_ = Number((_loc3_[0] as VT).getValue());
                  this.套装强化[0] = _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d10" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc5_ = Number((_loc3_[0] as VT).getValue());
                  this.套装强化[1] = _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d11" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  _loc5_ = Number((_loc3_[0] as VT).getValue());
                  this.套装强化[2] = _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d12" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  this.套装强化[3] = _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d13" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  _loc3_ = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[_loc1_][0],this.data.getSkillArr()[_loc1_][1]).getSkillValueArray();
                  this.套装强化[4] = _loc5_;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d14" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  this.套装强化[5] = true;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d15" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  this.套装强化[6] = true;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "d16" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  this.套装强化[7] = true;
               }
               else if(this.data.getSkillArr()[_loc1_][0] == "k16" && this.data.getSkillArr()[_loc1_][1] > 0)
               {
                  this.套装强化[8] = true;
               }
            }
         }
      }
      
      private function getEquipData(param1:Boolean = false) : *
      {
         var _loc2_:Array = null;
         var _loc3_:int = 0;
         if(this.getAllMoveSpeed.getValue() == -99 || param1)
         {
            this.getAllMoveSpeed.setValue(this.data.getEquipSlot().getAllMoveSpeed());
         }
         if(!this.getAllSuitSkill || param1)
         {
            this.getAllSuitSkill = this.data.equipSlot.getAllSuitSkill();
         }
         if(!this.getAllEquipSkill || param1)
         {
            _loc2_ = this.data.getEquipSlot().getAllEquipSkill();
            this.getAllEquipSkill = new Array();
            for(_loc3_ in _loc2_)
            {
               this.getAllEquipSkill[_loc3_] = SkillFactory.getSkillById(_loc2_[_loc3_]);
            }
         }
      }
      
      public function LoadAll_ZB_Skill() : *
      {
         var _loc3_:Number = NaN;
         var _loc1_:Array = new Array();
         var _loc2_:Number = Number(new Date().getTime());
         this.getEquipData();
         this.use_hp_Max.setValue(this.hp_Max.getValue());
         this.use_mp_Max.setValue(this.mp_Max.getValue());
         this.use_gongji.setValue(this.gongji.getValue());
         this.use_fangyu.setValue(this.fangyu.getValue());
         this.use_baoji.setValue(this.baoji.getValue());
         this.use_sanbi.setValue(this.sanbi.getValue());
         this.use_fangyu2.setValue(this.fangyu2.getValue());
         this.use_gongji2.setValue(this.gongji2.getValue());
         this.walk_power.setValue(6 + this.getAllMoveSpeed.getValue() + this.speedTemp.getValue());
         if(this.walk_power.getValue() <= 0)
         {
            this.walk_power.setValue(0);
         }
         this.reSet();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.zengFu();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.huiZhangJiaCheng();
         this.chongwujiacheng();
         this.QiangHuaJiaCheng();
         this.chenghaoJiaCheng();
         this.AnHeiJiaCheng();
         this.ZhuFu3_up();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.VIPJiaCheng();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.jinglingjiacheng();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.cardjiacheng();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.stampjiacheng();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.装备技能();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.ALLTempcompute();
         this.TaoZhuangXiaoGuo();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.rexueSkill();
         this.ALLcompute();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.huanhuaJiaCheng();
         this.stampjiacheng2();
         this.XingLingJiaCeng();
         this.xuemaiJiaCheng();
         this.caiyaoJiaCheng();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.nineBuffJiaCheng();
         this.zhongQiuJiaCheng();
         this.jianRenJiaCheng();
         this.shengMingJiaCheng();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         this.chongwuSQLQ();
         this.PK_hpMax();
         this.ZB_HpMpUP(true);
         this.数值溢出修正();
         _loc3_ = Number(new Date().getTime());
         _loc1_.push(int(_loc3_ - _loc2_));
         TiaoShi.tempTimeArr = _loc1_;
      }
      
      private function PK_hpMax() : *
      {
         if(Main.gameNum.getValue() == 999)
         {
            this.use_hp_Max.setValue(this.use_hp_Max.getValue() * InitData.pkHpMaxNum_Player.getValue());
         }
         else
         {
            this.use_hp_Max.setValue(this.use_hp_Max.getValue());
         }
      }
      
      private function 装备技能() : *
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         var _loc4_:Number = NaN;
         for(_loc1_ in this.getAllEquipSkill)
         {
            _loc2_ = int(this.getAllEquipSkill[_loc1_].getSkillActOn());
            _loc3_ = this.getAllEquipSkill[_loc1_].getSkillValueArray();
            if(_loc2_ == 32)
            {
               if(this.lianJi.getValue() > _loc3_[1].getValue())
               {
                  _loc4_ = Number(_loc3_[0].getValue());
                  this.num_gj.setValue(this.num_gj.getValue() + _loc4_);
               }
            }
            else if(_loc2_ == 35)
            {
               _loc4_ = this.use_baoji.getValue() + _loc3_[0].getValue();
               this.use_baoji.setValue(_loc4_);
            }
            else if(_loc2_ == 200)
            {
               if(Boolean(this.data.getEquipSlot().getEquipFromSlot(6)) && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
               {
                  this.num_fy.setValue(this.num_fy.getValue() + _loc3_[0].getValue());
                  this.num_gj.setValue(this.num_gj.getValue() + _loc3_[1].getValue());
               }
            }
            else if(_loc2_ == 201)
            {
               if(Boolean(this.data.getEquipSlot().getEquipFromSlot(7)) && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  this.num_hp.setValue(this.num_hp.getValue() + _loc3_[0].getValue());
                  this.num_bj.setValue(this.num_bj.getValue() + _loc3_[1].getValue());
               }
            }
         }
      }
      
      private function rexueSkill() : *
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:Array = null;
         var _loc7_:Number = NaN;
         var _loc8_:int = 0;
         var _loc1_:Array = XingLingFactory.GetTolal_Data_VT();
         var _loc2_:uint = uint(_loc1_[1].getValue());
         var _loc3_:uint = this.data.getStampSlot().getValueSlot5();
         for(_loc4_ in this.getAllEquipSkill)
         {
            _loc5_ = int(this.getAllEquipSkill[_loc4_].getSkillActOn());
            _loc6_ = this.getAllEquipSkill[_loc4_].getSkillValueArray();
            if(_loc5_ == 31)
            {
               _loc8_ = (this.temp_hp_Max.getValue() + _loc2_ + _loc3_) * _loc6_[1].getValue();
               if(this.hp.getValue() < _loc8_)
               {
                  _loc7_ = Number(_loc6_[0].getValue());
                  this.num_gj.setValue(this.num_gj.getValue() + _loc7_);
               }
            }
         }
      }
      
      private function ZB_HpMpUP(param1:Boolean = false) : *
      {
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         if(param1)
         {
            for(i in this.getAllEquipSkill)
            {
               _loc2_ = int(this.getAllEquipSkill[i].getSkillActOn());
               _loc3_ = this.getAllEquipSkill[i].getSkillValueArray();
               if(_loc2_ == 33)
               {
                  _loc4_ = _loc3_[0].getValue() - this.套装强化[0];
                  _loc5_ = _loc3_[1].getValue() * this.use_mp_Max.getValue();
                  this.ZB_HpMpUP_Arr[0] = [_loc4_,_loc5_];
               }
               else if(_loc2_ == 34)
               {
                  _loc6_ = _loc3_[0].getValue() - this.套装强化[1];
                  _loc7_ = _loc3_[1].getValue() * this.use_hp_Max.getValue();
                  if(PK_UI.PK_ing)
                  {
                     _loc7_ /= 20;
                  }
                  this.ZB_HpMpUP_Arr[1] = [_loc6_,_loc7_];
               }
            }
         }
         if(this.ZB_HpMpUP_Arr[0] && this.ZB_HpMpUP_Arr[0][1] != 0 && this.time % this.ZB_HpMpUP_Arr[0][0] == 0)
         {
            this.MP_UP(this.ZB_HpMpUP_Arr[0][1]);
         }
         if(this.ZB_HpMpUP_Arr[1] && this.ZB_HpMpUP_Arr[1][1] != 0 && this.time % this.ZB_HpMpUP_Arr[1][0] == 0)
         {
            this.HP_UP(this.ZB_HpMpUP_Arr[1][1]);
         }
      }
      
      private function huiZhangJiaCheng() : *
      {
         this.num_hp.setValue(this.data.getBadgeSlot().getHP() + this.num_hp.getValue());
         this.num_bj.setValue(this.data.getBadgeSlot().getCRIT() + this.num_bj.getValue());
         this.num_gj.setValue(this.data.getBadgeSlot().getATT() + this.num_gj.getValue());
         this.num_fy.setValue(this.data.getBadgeSlot().getDEF() + this.num_fy.getValue());
         this.num_mp.setValue(this.data.getBadgeSlot().getMP() + this.num_mp.getValue());
         this.num_sd.setValue(this.data.getBadgeSlot().getSPEED() + this.num_sd.getValue());
      }
      
      private function VIPJiaCheng() : *
      {
         var _loc1_:int = 0;
         while(_loc1_ < Main.player1.getTitleSlot().getListLength())
         {
            if(Main.player1.getTitleSlot().getTitleFromSlot(_loc1_).getId() == 25)
            {
               this.num_hp.setValue(num_5.getValue() + this.num_hp.getValue());
               this.num_bj.setValue(num_5.getValue() + this.num_bj.getValue());
               this.num_gj.setValue(num_5.getValue() + this.num_gj.getValue());
               this.num_fy.setValue(num_5.getValue() + this.num_fy.getValue());
               this.num_mp.setValue(num_5.getValue() + this.num_mp.getValue());
               this.num_sb.setValue(num_5.getValue() + this.num_sb.getValue());
               this.num_pm.setValue(num_5.getValue() + this.num_pm.getValue());
               this.num_mk.setValue(num_5.getValue() + this.num_mk.getValue());
               if(this == Main.player_1)
               {
                  Vip_Point();
               }
            }
            _loc1_++;
         }
      }
      
      private function zengFu() : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:Array = null;
         var _loc1_:Array = this.data.equipSlot.getAllEquipNewSkill();
         for(_loc2_ in _loc1_)
         {
            _loc3_ = int(SkillFactory.getSkillById(_loc1_[_loc2_]).getSkillActOn());
            _loc4_ = SkillFactory.getSkillById(_loc1_[_loc2_]).getSkillValueArray();
            if(_loc3_ == 39)
            {
               this.num_gj.setValue(this.num_gj.getValue() + _loc4_[0].getValue());
            }
            else if(_loc3_ == 40)
            {
               this.num_bj.setValue(this.num_bj.getValue() + _loc4_[0].getValue());
            }
            else if(_loc3_ == 41)
            {
               Main.NoGame("闪避");
            }
            else if(_loc3_ == 42)
            {
               this.num_hp.setValue(this.num_hp.getValue() + _loc4_[0].getValue());
            }
            else if(_loc3_ == 43)
            {
               this.num_fy.setValue(this.num_fy.getValue() + _loc4_[0].getValue());
            }
         }
      }
      
      private function reSet() : *
      {
         this.num_gj.setValue(InitData.BuyNum_1.getValue());
         this.num_fy.setValue(InitData.BuyNum_1.getValue());
         this.num_hp.setValue(InitData.BuyNum_1.getValue());
         this.num_mp.setValue(InitData.BuyNum_1.getValue());
         this.num_sb.setValue(InitData.BuyNum_1.getValue());
         this.num_bj.setValue(InitData.BuyNum_1.getValue());
         this.num_pm.setValue(InitData.BuyNum_1.getValue());
         this.num_mk.setValue(InitData.BuyNum_1.getValue());
         this.num_sd.setValue(InitData.BuyNum_0.getValue());
      }
      
      private function ALLcompute() : *
      {
         this.use_hp_Max.setValue(int(this.use_hp_Max.getValue() * this.num_hp.getValue()));
         this.use_mp_Max.setValue(int(this.use_mp_Max.getValue() * this.num_mp.getValue()));
         this.use_gongji.setValue(this.use_gongji.getValue() * this.num_gj.getValue());
         this.use_fangyu.setValue(this.use_fangyu.getValue() * this.num_fy.getValue());
         this.use_sanbi.setValue(this.use_sanbi.getValue() * this.num_sb.getValue());
         this.use_baoji.setValue(this.use_baoji.getValue() * this.num_bj.getValue());
         this.use_gongji2.setValue(this.use_gongji2.getValue() * this.num_pm.getValue());
         this.use_fangyu2.setValue(this.use_fangyu2.getValue() * this.num_mk.getValue());
         this.walk_power.setValue(this.walk_power.getValue() + this.num_sd.getValue());
      }
      
      private function ALLTempcompute() : *
      {
         this.temp_hp_Max.setValue(int(this.use_hp_Max.getValue() * this.num_hp.getValue()));
         this.temp_mp_Max.setValue(int(this.use_mp_Max.getValue() * this.num_mp.getValue()));
         this.temp_gongji.setValue(this.use_gongji.getValue() * this.num_gj.getValue());
         this.temp_fangyu.setValue(this.use_fangyu.getValue() * this.num_fy.getValue());
         this.temp_sanbi.setValue(this.use_sanbi.getValue() * this.num_sb.getValue());
         this.temp_baoji.setValue(this.use_baoji.getValue() * this.num_bj.getValue());
      }
      
      private function chongwuSQLQ() : *
      {
         var _loc1_:Array = null;
         if(this.playerCW)
         {
            if(Boolean(this.playerCW.data.getPetEquip()) && this.playerCW.data.getPetEquip().getType() == 21)
            {
               _loc1_ = this.playerCW.data.getPetEquip().getAffect();
               this.use_fangyu.setValue(this.use_fangyu.getValue() - this.use_fangyu.getValue() * _loc1_[0]);
               this.use_sanbi.setValue(this.use_sanbi.getValue() - this.use_sanbi.getValue() * _loc1_[0]);
            }
         }
      }
      
      private function caiyaoJiaCheng() : *
      {
         var _loc1_:Number = NaN;
         if(CaiYaoPanel.saveArr[2] > 0)
         {
            _loc1_ = 1 + (CaiYaoPanel.saveArr[2] * CaiYaoPanel.addArr[2] + CaiYaoPanel.saveArr[5] * CaiYaoPanel.addArr[5] + CaiYaoPanel.saveArr[8] * CaiYaoPanel.addArr[8] + CaiYaoPanel.saveArr[11] * CaiYaoPanel.addArr[11] + CaiYaoPanel.saveArr[14] * CaiYaoPanel.addArr[14]) * 0.01;
            this.use_hp_Max.setValue(int(this.use_hp_Max.getValue() * _loc1_));
         }
      }
      
      private function xuemaiJiaCheng() : *
      {
         if(Boolean(NewPetPanel.XueMai) && Boolean(this.playerCW))
         {
            if(NewPetPanel.XueMai.isHaveSX(1))
            {
               this.use_gongji.setValue(200 + this.use_gongji.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(2))
            {
               this.use_fangyu.setValue(100 + this.use_fangyu.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(5))
            {
               this.use_mp_Max.setValue(400 + this.use_mp_Max.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(6))
            {
               this.walk_power.setValue(1 + this.walk_power.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(7))
            {
               this.use_fangyu2.setValue(this.use_fangyu2.getValue() + 20);
            }
            if(NewPetPanel.XueMai.isHaveSX(8))
            {
               this.use_gongji2.setValue(this.use_gongji2.getValue() + 20);
            }
            if(NewPetPanel.XueMai.isHaveSX(9))
            {
               this.use_baoji.setValue(300 + this.use_baoji.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(10))
            {
               this.use_hp_Max.setValue(700 + this.use_hp_Max.getValue());
            }
         }
      }
      
      private function huanhuaJiaCheng() : *
      {
         if(this.data.getEquipSlot().getEquipFromSlot(5).getHuanHua() == 2)
         {
            this.walk_power.setValue(this.walk_power.getValue() + 1);
         }
      }
      
      private function jianRenJiaCheng() : *
      {
         if(this.jianrenState == 2)
         {
            this.use_gongji.setValue(this.use_gongji.getValue() + this.use_gongji.getValue() * 0.5);
         }
      }
      
      private function nineBuffJiaCheng() : *
      {
         if(this.data.buffNine[1] > 0)
         {
            if(this.data.buffNine[9] > 0)
            {
               this.use_fangyu.setValue(this.use_fangyu.getValue() + this.use_fangyu.getValue() * 0.2);
               this.use_mp_Max.setValue(this.use_mp_Max.getValue() + this.use_mp_Max.getValue() * 0.2);
               this.use_hp_Max.setValue(this.use_hp_Max.getValue() + this.use_hp_Max.getValue() * 0.2);
               this.use_gongji.setValue(this.use_gongji.getValue() + this.use_gongji.getValue() * 0.2);
               this.use_gongji2.setValue(this.use_gongji2.getValue() + this.use_gongji2.getValue() * 0.1);
               this.use_baoji.setValue(this.use_baoji.getValue() + this.use_baoji.getValue() * 0.4);
               this.walk_power.setValue(this.walk_power.getValue() + 4);
               this.use_fangyu2.setValue(this.use_fangyu2.getValue() + this.use_fangyu2.getValue() * 0.1);
            }
            else
            {
               if(this.data.buffNine[1] > 0)
               {
                  this.use_fangyu.setValue(this.use_fangyu.getValue() + this.use_fangyu.getValue() * 0.1);
               }
               if(this.data.buffNine[2] > 0)
               {
                  this.use_mp_Max.setValue(this.use_mp_Max.getValue() + this.use_mp_Max.getValue() * 0.1);
               }
               if(this.data.buffNine[3] > 0)
               {
                  this.use_hp_Max.setValue(this.use_hp_Max.getValue() + this.use_hp_Max.getValue() * 0.1);
               }
               if(this.data.buffNine[4] > 0)
               {
                  this.use_gongji.setValue(this.use_gongji.getValue() + this.use_gongji.getValue() * 0.1);
               }
               if(this.data.buffNine[5] > 0)
               {
                  this.walk_power.setValue(this.walk_power.getValue() + 2);
               }
               if(this.data.buffNine[6] > 0)
               {
                  this.use_baoji.setValue(this.use_baoji.getValue() + this.use_baoji.getValue() * 0.2);
               }
               if(this.data.buffNine[7] > 0)
               {
                  this.use_fangyu2.setValue(this.use_fangyu2.getValue() + this.use_fangyu2.getValue() * 0.05);
               }
               if(this.data.buffNine[8] > 0)
               {
                  this.use_gongji2.setValue(this.use_gongji2.getValue() + this.use_gongji2.getValue() * 0.05);
               }
            }
         }
      }
      
      private function zhongQiuJiaCheng() : *
      {
         if(this.zhongqiuState == 2)
         {
            this.use_gongji.setValue(this.use_gongji.getValue() + this.use_gongji.getValue());
         }
      }
      
      private function shengMingJiaCheng() : *
      {
         if(this.shengmingState == 2)
         {
            this.use_hp_Max.setValue(this.use_hp_Max.getValue() + this.use_hp_Max.getValue() * 0.5);
            this.use_fangyu.setValue(this.use_fangyu.getValue() + this.use_fangyu.getValue() * 0.3);
         }
      }
      
      private function chenghaoJiaCheng() : *
      {
         if(this.data.getTitleSlot().getTitleAttrib())
         {
            this.use_hp_Max.setValue(this.data.getTitleSlot().getTitleAttrib().getHP() + this.use_hp_Max.getValue());
            this.use_baoji.setValue(this.data.getTitleSlot().getTitleAttrib().getCrit() + this.use_baoji.getValue());
            this.use_gongji.setValue(this.data.getTitleSlot().getTitleAttrib().getAttack() + this.use_gongji.getValue());
            this.use_fangyu.setValue(this.data.getTitleSlot().getTitleAttrib().getDefense() + this.use_fangyu.getValue());
            this.use_mp_Max.setValue(this.data.getTitleSlot().getTitleAttrib().getMP() + this.use_mp_Max.getValue());
            this.walk_power.setValue(this.data.getTitleSlot().getTitleAttrib().getMoveSpeed() + this.walk_power.getValue());
         }
      }
      
      private function AnHeiJiaCheng() : *
      {
         if(LingHunShi_Interface.lhs_Data)
         {
            if(Boolean(LingHunShi_Interface.lhs_Data[1]) && Boolean(LingHunShi_Interface.lhs_Data[1].getValue()))
            {
               this.num_gj.setValue(this.num_gj.getValue() + 0.01 * LingHunShi_Interface.lhs_Data[1].getValue());
            }
            if(Boolean(LingHunShi_Interface.lhs_Data[2]) && Boolean(LingHunShi_Interface.lhs_Data[2].getValue()))
            {
               this.num_fy.setValue(this.num_fy.getValue() + 0.01 * LingHunShi_Interface.lhs_Data[2].getValue());
            }
            if(Boolean(LingHunShi_Interface.lhs_Data[3]) && Boolean(LingHunShi_Interface.lhs_Data[3].getValue()))
            {
               this.num_baoSang.setValue(0.01 * LingHunShi_Interface.lhs_Data[3].getValue());
            }
            if(Boolean(LingHunShi_Interface.lhs_Data[4]) && Boolean(LingHunShi_Interface.lhs_Data[4].getValue()))
            {
               this.num_hp.setValue(this.num_hp.getValue() + 0.01 * LingHunShi_Interface.lhs_Data[4].getValue());
            }
         }
      }
      
      private function jinglingjiacheng() : *
      {
         if(this.data.playerJL_Data)
         {
            this.use_hp_Max.setValue(int(this.data.playerJL_Data.getHP() * 13.3) + this.use_hp_Max.getValue());
            this.use_baoji.setValue(int(this.data.playerJL_Data.getCRIT() * 7.3) + this.use_baoji.getValue());
            this.use_gongji.setValue(int(this.data.playerJL_Data.getATT()) + this.use_gongji.getValue());
            this.use_fangyu.setValue(int(this.data.playerJL_Data.getDEF() * 1.9) + this.use_fangyu.getValue());
            this.use_mp_Max.setValue(int(this.data.playerJL_Data.getMP() * 8.8) + this.use_mp_Max.getValue());
         }
         this.use_hp_Max.setValue(this.data.getElvesSlot().backElvesSkill1() * this.use_hp_Max.getValue() + this.use_hp_Max.getValue());
         this.use_fangyu.setValue(this.data.getElvesSlot().backElvesSkill4() * this.use_fangyu.getValue() + this.use_fangyu.getValue());
      }
      
      private function stampjiacheng() : *
      {
         this.num_gj.setValue(this.data.getStampSlot().getValueSlot3() + this.num_gj.getValue());
         this.num_fy.setValue(this.data.getStampSlot().getValueSlot4() + this.num_fy.getValue());
         this.walk_power.setValue(this.data.getStampSlot().getValueSlot7() + this.walk_power.getValue());
      }
      
      private function stampjiacheng2() : *
      {
         this.use_hp_Max.setValue(this.use_hp_Max.getValue() + this.data.getStampSlot().getValueSlot5());
         this.use_gongji.setValue(this.use_gongji.getValue() + this.data.getStampSlot().getValueSlot6());
         this.use_hp_Max.setValue(this.data.getElvesSlot().backElvesSkill7() + this.use_hp_Max.getValue());
         this.use_gongji.setValue(this.data.getElvesSlot().backElvesSkill9() + this.use_gongji.getValue());
      }
      
      private function XingLingJiaCeng() : *
      {
         var _loc1_:Array = XingLingFactory.GetTolal_Data_VT();
         this.use_hp_Max.setValue(this.use_hp_Max.getValue() + _loc1_[1].getValue());
         this.use_mp_Max.setValue(this.use_mp_Max.getValue() + _loc1_[2].getValue());
         this.use_gongji.setValue(this.use_gongji.getValue() + _loc1_[3].getValue());
         this.use_fangyu.setValue(this.use_fangyu.getValue() + _loc1_[4].getValue());
         this.use_baoji.setValue(this.use_baoji.getValue() + _loc1_[5].getValue());
         this.use_sanbi.setValue(this.use_sanbi.getValue() + _loc1_[6].getValue());
         this.use_gongji2.setValue(this.use_gongji2.getValue() + _loc1_[7].getValue());
         this.use_fangyu2.setValue(this.use_fangyu2.getValue() + _loc1_[8].getValue());
      }
      
      private function cardjiacheng() : *
      {
         if(CardPanel.monsterSlot)
         {
            this.use_hp_Max.setValue(int(CardPanel.monsterSlot.getAllHpup()) + this.use_hp_Max.getValue());
            this.use_baoji.setValue(int(CardPanel.monsterSlot.getAllCritup()) + this.use_baoji.getValue());
            this.use_gongji.setValue(int(CardPanel.monsterSlot.getAllAttup()) + this.use_gongji.getValue());
            this.use_fangyu.setValue(int(CardPanel.monsterSlot.getAllDefup()) + this.use_fangyu.getValue());
            this.use_mp_Max.setValue(int(CardPanel.monsterSlot.getAllMpup()) + this.use_mp_Max.getValue());
         }
      }
      
      private function chongwujiacheng() : *
      {
         if(this.playerCW)
         {
            this.num_hp.setValue(this.playerCW.data.getLife() + this.num_hp.getValue());
            this.num_bj.setValue(this.playerCW.data.getCrit() + this.num_bj.getValue());
            this.num_gj.setValue(this.playerCW.data.getAtt() + this.num_gj.getValue());
            this.num_fy.setValue(this.playerCW.data.getDef() + this.num_fy.getValue());
         }
      }
      
      private function ZhuFu3_up() : *
      {
         var _loc1_:Equip = null;
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         var _loc4_:Number = NaN;
         i = 0;
         while(i < 8)
         {
            _loc1_ = this.data.getEquipSlot().getEquipFromSlot(i);
            if(_loc1_ && _loc1_._blessAttrib && _loc1_._blessAttrib.getBeishu() >= 2)
            {
               if(!(this.data.skinNum == 0 && i == 5))
               {
                  if(!(this.data.skinNum == 1 && i == 2))
                  {
                     _loc2_ = _loc1_._blessAttrib.getBeishu() - 1;
                     _loc3_ = Zhufu2Factory.allData[_loc2_];
                     _loc4_ = 1.5;
                     if(Main.isVip())
                     {
                        _loc4_ = 1;
                     }
                     if(_loc1_.getPosition() == 0 || _loc1_.getPosition() == 5 || _loc1_.getPosition() == 6 || _loc1_.getPosition() == 7)
                     {
                        this.use_gongji.setValue(this.use_gongji.getValue() + _loc3_[6] / _loc4_);
                     }
                     else if(_loc1_.getPosition() == 2)
                     {
                        this.use_fangyu.setValue(this.use_fangyu.getValue() + _loc3_[7] / _loc4_);
                     }
                     else if(_loc1_.getPosition() == 1)
                     {
                        this.use_baoji.setValue(this.use_baoji.getValue() + _loc3_[8] / _loc4_);
                     }
                     else if(_loc1_.getPosition() == 4)
                     {
                        this.use_fangyu2.setValue(this.use_fangyu2.getValue() + _loc3_[9] / _loc4_);
                     }
                     else if(_loc1_.getPosition() == 3)
                     {
                        this.use_gongji2.setValue(this.use_gongji2.getValue() + _loc3_[10] / _loc4_);
                     }
                     else if(_loc1_.getPosition() == 8)
                     {
                        this.use_hp_Max.setValue(this.use_hp_Max.getValue() + _loc3_[11] / _loc4_);
                     }
                     else if(_loc1_.getPosition() == 9)
                     {
                        this.use_mp_Max.setValue(this.use_mp_Max.getValue() + _loc3_[12] / _loc4_);
                     }
                  }
               }
            }
            ++i;
         }
      }
      
      private function QiangHuaJiaCheng() : *
      {
         var _loc1_:int = this.data.getEquipSlot().getSuitStrength();
         if(_loc1_ < 4)
         {
            return;
         }
         switch(_loc1_)
         {
            case 4:
               this.num_hp.setValue(this.num_hp.getValue() + num_1.getValue());
               break;
            case 5:
               this.num_hp.setValue(this.num_hp.getValue() + num_15.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_1.getValue());
               break;
            case 6:
               this.num_hp.setValue(this.num_hp.getValue() + num_15.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_15.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 1);
               break;
            case 7:
               this.num_hp.setValue(this.num_hp.getValue() + num_15.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_20.getValue());
               this.num_fy.setValue(this.num_fy.getValue() + num_7.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 1);
               break;
            case 8:
               this.num_hp.setValue(this.num_hp.getValue() + num_20.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_25.getValue());
               this.num_fy.setValue(this.num_fy.getValue() + num_7.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 1);
               break;
            case 9:
               this.num_hp.setValue(this.num_hp.getValue() + num_20.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_25.getValue());
               this.num_fy.setValue(this.num_fy.getValue() + num_1.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 1);
               this.num_gj.setValue(this.num_gj.getValue() + num_5.getValue());
               break;
            case 10:
               this.num_hp.setValue(this.num_hp.getValue() + num_27.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_25.getValue());
               this.num_fy.setValue(this.num_fy.getValue() + num_15.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 2);
               this.num_gj.setValue(this.num_gj.getValue() + num_32.getValue());
         }
      }
      
      private function TaoZhuangXiaoGuo() : *
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:Number = NaN;
         var _loc8_:int = 0;
         var _loc1_:Array = XingLingFactory.GetTolal_Data_VT();
         var _loc2_:uint = uint(_loc1_[1].getValue());
         var _loc3_:uint = this.data.getStampSlot().getValueSlot5();
         this.lianjiBool = false;
         for(_loc4_ in this.getAllSuitSkill)
         {
            if(this.getAllSuitSkill[_loc4_] == 57200)
            {
               _loc5_ = (this.temp_hp_Max.getValue() + _loc2_ + _loc3_) * 0.3;
               if(this.hp.getValue() < _loc5_)
               {
                  this.num_gj.setValue(this.num_gj.getValue() + num_13.getValue());
               }
            }
            if(this.getAllSuitSkill[_loc4_] == 57362)
            {
               _loc5_ = (this.temp_hp_Max.getValue() + _loc2_ + _loc3_) * 0.3;
               if(this.hp.getValue() < _loc5_)
               {
                  this.num_gj.setValue(this.num_gj.getValue() + num_65.getValue());
               }
            }
            else if(this.getAllSuitSkill[_loc4_] == 57201)
            {
               _loc5_ = (this.temp_hp_Max.getValue() + _loc2_ + _loc3_) * 0.4;
               if(this.hp.getValue() < _loc5_)
               {
                  this.num_fy.setValue(this.num_fy.getValue() + num_20.getValue());
               }
            }
            else if(this.getAllSuitSkill[_loc4_] == 57210)
            {
               _loc6_ = this.lianJi.getValue();
               if(_loc6_ > 2000)
               {
                  _loc6_ = 2000;
               }
               _loc7_ = _loc6_ / 10 * 0.01;
               if(_loc7_ > 5)
               {
                  _loc7_ = 5;
               }
               this.num_gj.setValue(this.num_gj.getValue() + _loc7_);
            }
            else if(this.getAllSuitSkill[_loc4_] == 57360)
            {
               this.lianjiBool = true;
               _loc6_ = this.lianJi.getValue();
               if(_loc6_ > 500)
               {
                  _loc6_ = 500;
               }
               _loc8_ = int(_loc6_ / 50);
               _loc7_ = _loc8_ * 0.23;
               if(_loc7_ > 5)
               {
                  _loc7_ = 5;
               }
               this.num_gj.setValue(this.num_gj.getValue() + _loc7_);
            }
         }
      }
      
      private function 数值溢出修正() : *
      {
         if(this.hp.getValue() > this.use_hp_Max.getValue())
         {
            this.hp.setValue(this.use_hp_Max.getValue());
         }
         if(this.mp.getValue() > this.use_mp_Max.getValue())
         {
            this.mp.setValue(this.use_mp_Max.getValue());
         }
         if(TiaoShi.Hpup > 0)
         {
            this.hp.setValue(this.use_hp_Max.getValue() + TiaoShi.Hpup);
         }
      }
      
      private function HP_UP(param1:int) : *
      {
         if(param1 <= 0)
         {
            return;
         }
         var _loc2_:int = param1 + this.hp.getValue();
         if(_loc2_ > this.use_hp_Max.getValue())
         {
            this.hp.setValue(this.use_hp_Max.getValue());
         }
         else
         {
            this.hp.setValue(_loc2_);
         }
      }
      
      private function MP_UP(param1:int) : *
      {
         if(param1 <= 0)
         {
            return;
         }
         var _loc2_:int = param1 + this.mp.getValue();
         if(_loc2_ > this.use_mp_Max.getValue())
         {
            this.mp.setValue(this.use_mp_Max.getValue());
         }
         else
         {
            this.mp.setValue(_loc2_);
         }
      }
      
      public function 连击计数() : *
      {
         this.lianJiTime = VT.createVT();
         this.lianJi.setValue(this.lianJi.getValue() + 1);
         WinShow.LianJi(this.lianJi.getValue());
         JingLing.lianJiNumXX1010(this);
      }
      
      private function 连击计时() : *
      {
         this.lianJiTime.setValue(this.lianJiTime.getValue() + 1);
         if(this.lianjiBool && this.lianJiTime.getValue() > 87)
         {
            this.lianJi = VT.createVT();
            this.lianJi.setValue(0);
         }
         else if(!this.lianjiBool && this.lianJiTime.getValue() > 60)
         {
            this.lianJi = VT.createVT();
            this.lianJi.setValue(0);
         }
      }
      
      private function jianCDTime() : *
      {
         if(this.jianCDBool)
         {
            ++this.jianCDtemp;
            if(this.jianCDtemp > 270)
            {
               TiaoShi.txtShow("夏日时装减CD");
               this.jianCDnum = 1;
               this.GetAllSkillCD();
               this.jianCDBool = false;
               this.jianCDtemp = 0;
            }
         }
      }
      
      public function GetAllSkillCD() : *
      {
         var _loc1_:int = 0;
         this.AllSkillCD = this.data.skillCdArr();
         this.AllSkillCDXX = DeepCopyUtil.clone(this.AllSkillCD);
         for(_loc1_ in this.AllSkillCD)
         {
            this.AllSkillCDXX[_loc1_][2] = 1;
            this.AllSkillCDXX[_loc1_][1] = 0;
            if(this.data.getSkillLevel(this.AllSkillCDXX[_loc1_][0]) > 0)
            {
               this.AllSkillCDXX[_loc1_][2] = 50;
               this.AllSkillCDXX[_loc1_][1] = this.AllSkillCD[_loc1_][1];
            }
         }
         if(this.jianCDnum == 11)
         {
            this.AllSkillCD = this.data.skillCdArr();
            for(_loc1_ in this.AllSkillCD)
            {
               this.AllSkillCD[_loc1_][1] -= 162;
               if(this.AllSkillCD[_loc1_][1] < 0)
               {
                  this.AllSkillCD[_loc1_][1] = 0;
               }
               this.AllSkillCDXX[_loc1_][2] = 1;
               this.AllSkillCDXX[_loc1_][1] = 0;
               if(this.data.getSkillLevel(this.AllSkillCDXX[_loc1_][0]) > 0)
               {
                  this.AllSkillCDXX[_loc1_][2] = 50;
                  this.AllSkillCDXX[_loc1_][1] = this.AllSkillCD[_loc1_][1];
               }
            }
            this.jianCDBool = true;
         }
      }
      
      public function GetAllObjCD() : *
      {
         var _loc1_:int = 0;
         this.All_ObjCD = this.data.ObjCdArr();
         this.All_ObjCDXX = DeepCopyUtil.clone(this.All_ObjCD);
         for(_loc1_ in this.All_ObjCD)
         {
            this.All_ObjCDXX[_loc1_][2] = 50;
         }
      }
      
      public function GetKeyArr(param1:Array) : *
      {
         var _loc2_:int = 0;
         if(param1.length == this.data._keyArr.length)
         {
            _loc2_ = 0;
            while(_loc2_ < this.data._keyArr.length)
            {
               if(param1[_loc2_] is int)
               {
               }
               _loc2_++;
            }
            this.KeyArr = this.data._keyArr;
         }
      }
      
      private function xuemaiHuiFu() : *
      {
         if(Boolean(NewPetPanel.XueMai) && Boolean(this.playerCW))
         {
            if(NewPetPanel.XueMai.isHaveSX(11))
            {
               ++this.xuemaiTime;
               if(this.xuemaiTime >= 540)
               {
                  this.HpUp(500);
                  this.xuemaiTime = 0;
               }
            }
            if(NewPetPanel.XueMai.isHaveSX(12))
            {
               ++this.xuemaiTime2;
               if(this.xuemaiTime2 >= 540)
               {
                  this.MpUp(200);
                  this.xuemaiTime2 = 0;
               }
            }
         }
      }
      
      private function DKTime() : *
      {
         var _loc1_:int = 0;
         if(this.isDarkState)
         {
            ++this.darkTime;
            _loc1_ = 135;
            if(this.data.getEquipSlot().getEquipFromSlot(7) && this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 405 && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
            {
               _loc1_ = 270;
            }
            if(this.darkTime < _loc1_)
            {
               this.addChild(this.dkState);
            }
            else
            {
               this.removeChild(this.dkState);
               this.isDarkState = false;
               this.darkTime = 0;
            }
         }
      }
      
      private function onENTER_FRAME(param1:*) : *
      {
         if(this.noYingZhiTime > 0)
         {
            --this.noYingZhiTime;
         }
         this.RZJN_hpDown();
         ++szCD;
         ++this.time;
         this.DKTime();
         this.连击计时();
         this.nineBuffTime();
         this.药品使用();
         this.xuemaiHuiFu();
         this.chibangFeiXing();
         this.shizhuangPenShe();
         this.huiFuYinZang();
         if(this.isDead || this.hp.getValue() <= 0)
         {
            this.hp.setValue(0);
            this.isDead = true;
            this.skin.visible = false;
            this.不信春哥();
            return;
         }
         if(!this.isDead)
         {
            this.skin.visible = true;
         }
         ++this.timeXXX;
         if(this.timeXXX % 40 == 0)
         {
            this.LoadAll_ZB_Skill();
         }
         else
         {
            this.ZB_HpMpUP();
         }
         if(Main.world)
         {
            this.CDtime();
            this.jianCDTime();
            this.SkinValue();
            this.KeyControl();
            this.MoveData();
            this.MoveRun();
            this.HeiAnJiNengGo();
         }
         if(this.data.getEquipSlot().getEquipFromSlot(6) && this.skin_Z2 && this.skin_Z2_V && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            this.skin_Z2.visible = true;
            this.skin_Z.visible = false;
         }
      }
      
      public function 双手加成() : *
      {
         var _loc1_:Array = null;
         var _loc2_:* = undefined;
         var _loc3_:* = undefined;
         var _loc4_:int = 0;
         this.shuangShouBeiShu = 1;
         if(this.data.isTransferOk())
         {
            _loc1_ = this.data.getTransferOk();
            _loc2_ = this.data.getEquipSlot().getEquipFromSlot(2).getPosition();
            _loc3_ = this.data.getEquipSlot().getEquipFromSlot(5).getPosition();
            for(_loc4_ in _loc1_)
            {
               if(_loc1_[_loc4_] == 0)
               {
                  if(_loc2_ == _loc3_ && _loc3_ == 5)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
               else if(_loc1_[_loc4_] == 1)
               {
                  if(_loc2_ == _loc3_ && _loc3_ == 6)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
               else if(_loc1_[_loc4_] == 2)
               {
                  if(_loc2_ == _loc3_ && _loc3_ == 7)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
               else if(Boolean(_loc1_[_loc4_]) && _loc1_[_loc4_] == 3)
               {
                  if(_loc2_ == _loc3_ && _loc3_ == 0)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
            }
         }
      }
      
      private function HeadXX() : *
      {
         var _loc3_:int = 0;
         var _loc4_:String = null;
         var _loc5_:int = 0;
         var _loc1_:Equip = this.data.getEquipSlot().getEquipFromSlot(0);
         var _loc2_:Equip = this.data.getEquipSlot().getEquipFromSlot(1);
         if(Main.water.getValue() != 1)
         {
            _loc1_ = this.data.getEquipSlot().getEquipFromSlot(8);
         }
         if(this.skin_Z2 && this.skin_Z2_V && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            _loc3_ = int(this.data.getEquipSlot().getEquipFromSlot(6).getClassName2());
            this.headFrame = _loc3_;
            this.noHead = false;
            this.paiHangShow[1] = this.headFrame;
            _loc4_ = this.data.getEquipSlot().getEquipFromSlot(6).getClassName();
            if(_loc4_ == "S19" || _loc4_ == "S23")
            {
               this.noHead = true;
            }
         }
         else if(Boolean(this.data) && Boolean(_loc1_))
         {
            _loc5_ = int(_loc1_.getClassName());
            this.headFrame = _loc5_;
            this.noHead = false;
            if(_loc5_ == 24)
            {
               if(_loc2_ && _loc2_.getClassName() == "甲红10" && Main.water.getValue() == 1)
               {
                  this.paiHangShow[1] = 0;
                  this.noHead = true;
               }
               else
               {
                  this.paiHangShow[1] = this.headFrame;
                  this.noHead = false;
               }
            }
            if(Boolean(_loc2_) && _loc2_.getClassName() == "甲红6")
            {
               if(Main.water.getValue() != 1)
               {
                  eadFrame = 1;
                  this.noHead = false;
               }
               else
               {
                  eadFrame = 53;
                  this.noHead = true;
               }
               TiaoShi.txtShow("发型1 = " + _loc2_.getClassName());
            }
         }
         else
         {
            this.headFrame = 1;
            if(_loc2_ && _loc2_.getClassName() == "甲红10" && Main.water.getValue() == 1)
            {
               this.paiHangShow[1] = 0;
               this.noHead = true;
            }
            else
            {
               this.paiHangShow[1] = 1;
               this.noHead = false;
            }
            if(Boolean(_loc2_) && _loc2_.getClassName() == "甲红6")
            {
               if(Main.water.getValue() != 1)
               {
                  eadFrame = 1;
                  this.noHead = false;
               }
               else
               {
                  eadFrame = 53;
                  this.noHead = true;
               }
               TiaoShi.txtShow("发型2 = " + _loc2_.getClassName());
            }
         }
      }
      
      private function CDtime() : *
      {
         var _loc1_:int = 0;
         if(this.jianCD_lei)
         {
            Skill_lei.JianCDXXXX(this);
         }
         for(_loc1_ in this.AllSkillCDXX)
         {
            if(this.AllSkillCDXX[_loc1_][1] < this.AllSkillCD[_loc1_][1] && this.data.getSkillLevel(this.AllSkillCDXX[_loc1_][0]) > 0)
            {
               ++this.AllSkillCDXX[_loc1_][1];
               if(this.jianCD)
               {
                  this.AllSkillCDXX[_loc1_][1] += 13;
                  if(this.AllSkillCDXX[_loc1_][1] > this.AllSkillCD[_loc1_][1])
                  {
                     this.AllSkillCDXX[_loc1_][1] = this.AllSkillCD[_loc1_][1];
                  }
                  this.jianCD = false;
               }
               this.AllSkillCDXX[_loc1_][2] = int(this.AllSkillCDXX[_loc1_][1] / this.AllSkillCD[_loc1_][1] * 50);
            }
         }
         for(_loc1_ in this.All_ObjCDXX)
         {
            if(this.All_ObjCDXX[_loc1_][1] < this.All_ObjCD[_loc1_][1])
            {
               ++this.All_ObjCDXX[_loc1_][1];
               this.All_ObjCDXX[_loc1_][2] = int(this.All_ObjCDXX[_loc1_][1] / this.All_ObjCD[_loc1_][1] * 50);
            }
         }
      }
      
      private function selCD(param1:String) : int
      {
         var _loc2_:int = 0;
         for(_loc2_ in this.AllSkillCDXX)
         {
            if(this.AllSkillCDXX[_loc2_][0] == param1)
            {
               if(this.AllSkillCDXX[_loc2_][1] == this.AllSkillCD[_loc2_][1])
               {
                  return _loc2_;
               }
               return -1;
            }
         }
         return -1;
      }
      
      private function sel_objCD(param1:String) : int
      {
         var _loc2_:int = 0;
         for(_loc2_ in this.All_ObjCD)
         {
            if(this.All_ObjCDXX[_loc2_][0] == param1)
            {
               if(this.All_ObjCDXX[_loc2_][1] == this.All_ObjCD[_loc2_][1])
               {
                  return _loc2_;
               }
               return -1;
            }
         }
         return -1;
      }
      
      public function selCD50(param1:String) : int
      {
         var _loc2_:int = 0;
         for(_loc2_ in this.AllSkillCDXX)
         {
            if(this.AllSkillCDXX[_loc2_][0] == param1)
            {
               return this.AllSkillCDXX[_loc2_][2];
            }
         }
         return 50;
      }
      
      public function sel_objCD50(param1:String) : int
      {
         var _loc2_:int = 0;
         for(_loc2_ in this.All_ObjCDXX)
         {
            if(this.All_ObjCDXX[_loc2_][0] == param1)
            {
               return this.All_ObjCDXX[_loc2_][2];
            }
         }
         return 50;
      }
      
      public function wudi(param1:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 81)
         {
            removeChild(this.nodead);
            this.rexuewudi = false;
            this.timeTemp = 0;
            this.flagTempRX = true;
            removeEventListener(Event.ENTER_FRAME,this.wudi);
         }
      }
      
      public function timeLimitBJ(param1:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 54)
         {
            this.timeTemp = 0;
            this.flagTempBJ = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitBJ);
         }
      }
      
      public function timeLimitSJ(param1:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 351)
         {
            this.timeTemp = 0;
            this.flagTempSJ = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitSJ);
         }
      }
      
      public function timeLimitSY(param1:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 284)
         {
            this.timeTemp = 0;
            this.flagTempSY = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitSY);
         }
      }
      
      public function timeLimitXL(param1:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 284)
         {
            this.timeTemp = 0;
            this.flagTempXL = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitXL);
         }
      }
      
      public function timeLimitMP(param1:*) : *
      {
         ++this.timeTempmp;
         if(this.timeTempmp > 216)
         {
            this.timeTempmp = 0;
            this.flagTempMP = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitMP);
         }
      }
      
      public function setInTimeCDMP() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitMP);
      }
      
      public function setInTimeCDXL() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitXL);
      }
      
      public function setInTimeCDBJ() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitBJ);
      }
      
      public function setInTimeCDSJ() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitSJ);
      }
      
      public function setInTimeCDSY() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitSY);
      }
      
      public function HpXX(param1:*, param2:Boolean = false) : *
      {
         var _loc7_:Player2 = null;
         var _loc8_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc10_:int = 0;
         var _loc11_:Player = null;
         var _loc12_:Number = NaN;
         var _loc13_:int = 0;
         var _loc14_:Class = null;
         var _loc15_:Boolean = false;
         var _loc16_:Class = null;
         var _loc17_:Class = null;
         var _loc18_:Enemy = null;
         var _loc19_:Number = NaN;
         var _loc20_:HitXX = null;
         var _loc21_:Class = null;
         if(this.newLifeTime.getValue() > InitData.BuyNum_0.getValue() + InitData.BuyNum_1.getValue())
         {
            return;
         }
         if(this.noHit)
         {
            this.noHit = false;
            return;
         }
         if(this.noHit82)
         {
            return;
         }
         if(this.rexuewudi)
         {
            return;
         }
         if(param1 is HitXX)
         {
            this.hitXX = param1;
         }
         ++WinShow.txt_3;
         var _loc3_:Number = Number(param1.gongJi_hp);
         var _loc4_:int = int(param1.times);
         var _loc5_:int = 0;
         NewMC.Open("被攻击",this);
         if(_loc3_ == -1001)
         {
            _loc3_ = int(this.use_hp_Max.getValue() * 0.02);
         }
         if(param1.parent is Fly && (param1.parent as Fly)._name == "暗黑牢笼")
         {
            param1.parent.hitWho = this;
         }
         if(this.cengshu.length > 0)
         {
            for(_loc13_ in this.cengshu)
            {
               if(this.cengshu[_loc13_].type == 521)
               {
                  _loc3_ *= 999;
               }
            }
         }
         if(param1 is HitXX && param1.who is Player2)
         {
            _loc7_ = param1.who as Player2;
            _loc8_ = _loc7_.use_gongji.getValue();
            _loc9_ = 1;
            if(_loc7_.data.skinArr[_loc7_.data.skinNum] == 0)
            {
               _loc9_ = Number(_loc7_.职业附加[0]);
            }
            else if(_loc7_.data.skinArr[_loc7_.data.skinNum] == 1)
            {
               _loc9_ = Number(_loc7_.职业附加[1]);
            }
            else if(_loc7_.data.skinArr[_loc7_.data.skinNum] == 2)
            {
               _loc9_ = Number(_loc7_.职业附加[2]);
            }
            else if(_loc7_.data.skinArr[_loc7_.data.skinNum] == 3)
            {
               _loc9_ = Number(_loc7_.职业附加[3]);
            }
            if(this.data.skinArr[this.data.skinNum] == 0 && this.skin.runType == "转职技能1" && this.skin.frame < 20)
            {
               if(this.data.getSkillLevel("a12") > 0)
               {
                  _loc3_ *= 0.3;
               }
            }
            _loc10_ = this.use_fangyu.getValue() + this.fangYuPOWER.getValue();
            _loc8_ = _loc7_.use_gongji.getValue();
            _loc5_ = _loc3_ * _loc9_ * _loc8_ - _loc10_ / _loc4_;
            if(_loc5_ < 0)
            {
               _loc5_ = 0;
            }
            _loc5_ += _loc3_ * (Math.random() * 3 + 2) / 100;
            if(param2)
            {
               _loc5_ *= this.tempXXX2.getValue();
            }
         }
         else if(Main.gameNum.getValue() == 0 && param1.who != this && param1 is HitXX && param1.who is Player)
         {
            _loc11_ = param1.who as Player;
            _loc8_ = _loc11_.use_gongji.getValue();
            _loc9_ = 1;
            if(_loc11_.data.skinArr[_loc11_.data.skinNum] == 0)
            {
               _loc9_ = Number(_loc11_.职业附加[0]);
            }
            else if(_loc11_.data.skinArr[_loc11_.data.skinNum] == 1)
            {
               _loc9_ = Number(_loc11_.职业附加[1]);
            }
            else if(_loc11_.data.skinArr[_loc11_.data.skinNum] == 2)
            {
               _loc9_ = Number(_loc11_.职业附加[2]);
            }
            else if(_loc11_.data.skinArr[_loc11_.data.skinNum] == 3)
            {
               _loc9_ = Number(_loc11_.职业附加[3]);
            }
            if(this.data.skinArr[this.data.skinNum] == 0 && this.skin.runType == "转职技能1" && this.skin.frame < 20)
            {
               if(this.data.getSkillLevel("a12") > 0)
               {
                  _loc3_ *= 0.3;
               }
            }
            _loc10_ = this.use_fangyu.getValue() + this.fangYuPOWER.getValue();
            _loc8_ = _loc11_.use_gongji.getValue();
            _loc5_ = _loc3_ * _loc9_ * _loc8_ - _loc10_ / _loc4_;
            if(_loc5_ < 0)
            {
               _loc5_ = 0;
            }
            _loc5_ += _loc3_ * (Math.random() * 3 + 2) / 100;
            if(param2)
            {
               _loc5_ *= this.tempXXX2.getValue();
            }
         }
         else
         {
            if(this.data.skinArr[this.data.skinNum] == 0 && this.skin.runType == "转职技能1" && this.skin.frame < 20)
            {
               if(this.data.getSkillLevel("a12") > 0)
               {
                  _loc3_ *= 0.3;
               }
            }
            _loc10_ = this.use_fangyu.getValue() + this.fangYuPOWER.getValue();
            _loc5_ = _loc3_ - _loc10_ / _loc4_;
            if(_loc5_ < 0)
            {
               _loc5_ = 0;
            }
            _loc5_ += _loc3_ * (Math.random() * 3 + 2) / 100;
            if(param2)
            {
               _loc5_ *= this.tempXXX2.getValue();
            }
         }
         if(param1.who is Enemy && Main.water.getValue() != 1)
         {
            _loc5_ = _loc5_ * (1 + (param1.who as Enemy).魔攻力.getValue() / 100) / (1 + this.use_fangyu2.getValue() / 100);
         }
         if(this.lianJi.getValue() > 50 && this.data.getElvesSlot().backElvesSkill5() > 0)
         {
            _loc5_ *= 1 - this.data.getElvesSlot().backElvesSkill5();
         }
         if(Boolean(NewPetPanel.XueMai) && Boolean(this.playerCW))
         {
            if(NewPetPanel.XueMai.isHaveSX(4))
            {
               _loc5_ *= 0.95;
            }
         }
         if(this.data.getEquipSlot().getEquipFromSlot(6))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 415)
            {
               _loc5_ *= 0.9;
            }
         }
         if(this.data.getEquipSlot().getEquipFromSlot(7))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 416)
            {
               _loc5_ *= 0.9;
            }
         }
         if(this.speedTemp.getValue() <= -6)
         {
            _loc5_ += this.use_hp_Max.getValue() * 0.02;
         }
         _loc5_ *= this.jianSang_UP;
         if(this.data.getStampSlot().getValueSlot8() > 0)
         {
            _loc5_ -= _loc5_ * this.data.getStampSlot().getValueSlot8();
         }
         if(this.jianrenState == 2)
         {
            _loc5_ *= 0.7;
         }
         if(CaiYaoPanel.saveArr[0] > 0)
         {
            _loc12_ = 1 - (CaiYaoPanel.saveArr[0] * CaiYaoPanel.addArr[0] + CaiYaoPanel.saveArr[3] * CaiYaoPanel.addArr[3] + CaiYaoPanel.saveArr[6] * CaiYaoPanel.addArr[6] + CaiYaoPanel.saveArr[9] * CaiYaoPanel.addArr[9] + CaiYaoPanel.saveArr[12] * CaiYaoPanel.addArr[12]) * 0.01;
            _loc5_ *= _loc12_;
         }
         if(Skin.xishouWuDi)
         {
            if(Skin.xishouHP > 0)
            {
               Skin.xishouHP -= _loc5_;
               NewMC.Open("闪避2",Main.world.moveChild_Other,xxx,yyy,15,0,true,2);
            }
            else
            {
               Skin.xishouWuDi = false;
            }
            return;
         }
         if(Main.gameNum.getValue() == 999 && _loc5_ > param1.gongJi_hp_MAX)
         {
            _loc5_ = int(param1.gongJi_hp_MAX);
            if(param1.gongJi_hp_MAX > 300000 && this.hitXX.who is Player2)
            {
               (this.hitXX.who as Player2).noJiFen = true;
               (this.hitXX.who as Player2).hp.setValue(0);
               TiaoShi.txtShow("清除秒杀玩家,重新加载~~~~");
               return;
            }
         }
         if(param1.parent is Fly && (param1.parent as Fly)._name == "灵魂抽取")
         {
            for(_loc13_ in Enemy.All)
            {
               if(Boolean(Enemy.All[_loc13_]) && (Enemy.All[_loc13_] as Enemy).id == 3022)
               {
                  (Enemy.All[_loc13_] as Enemy).hpUpEnemy(_loc5_);
                  NewMC.Open("回血效果",Enemy.All[_loc13_],0,0,0,_loc5_);
                  TiaoShi.txtShow("回复" + _loc5_);
               }
            }
         }
         if(param1.who is Enemy && (param1.who as Enemy).id == 2015)
         {
            _loc5_ = this.use_hp_Max.getValue() * 0.02;
         }
         if(param1.who is Enemy && (param1.who as Enemy).id == 4010)
         {
            if(param1.parent is Fly && (param1.parent as Fly)._name == "水泡飞行")
            {
               _loc14_ = NewLoad.XiaoGuoData.getClass("水泡控制") as Class;
               this.skin_W.addChild(new _loc14_());
            }
            for(_loc13_ in Fly.All)
            {
               if(Fly.All[_loc13_]._name == "水泡标记")
               {
                  _loc15_ = true;
                  _loc13_ = 0;
                  while(_loc13_ < Fly.All.length)
                  {
                     if(Boolean(Fly.All[_loc13_]) && Fly.All[_loc13_]._name == "水泡控制")
                     {
                        Fly.All[_loc13_].time = 54;
                        _loc15_ = false;
                     }
                     _loc13_++;
                  }
                  if(_loc15_)
                  {
                     _loc14_ = NewLoad.XiaoGuoData.getClass("水泡控制") as Class;
                     this.skin_W.addChild(new _loc14_());
                  }
               }
            }
         }
         if(param1.who is Enemy && (param1.who as Enemy).id == 4010)
         {
            _loc15_ = true;
            _loc13_ = 0;
            while(_loc13_ < Fly.All.length)
            {
               if(Boolean(Fly.All[_loc13_]) && Fly.All[_loc13_]._name == "水泡标记")
               {
                  Fly.All[_loc13_].time = 216;
                  _loc15_ = false;
               }
               _loc13_++;
            }
            if(_loc15_)
            {
               _loc16_ = NewLoad.XiaoGuoData.getClass("水泡标记") as Class;
               this.skin_W.addChild(new _loc16_());
            }
         }
         if(param1.who is Enemy && (param1.who as Enemy).id == 1056)
         {
            if((param1.who as Enemy).jianShangPER < 0.7)
            {
               (param1.who as Enemy).jianShangPER += 0.015;
            }
            (param1.who as Enemy).skin.lexAdd += 0.02;
            _loc17_ = NewLoad.XiaoGuoData.getClass("吸血分子") as Class;
            this.skin_W.addChild(new _loc17_());
         }
         if(param1.who is Enemy && (param1.who as Enemy).className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value += _loc5_ * 20;
         }
         if(Main.gameNum.getValue() == 17)
         {
            _loc5_ += this.use_hp_Max.getValue() * 0.03;
         }
         if(param1.who is Enemy && (param1.who as Enemy).id == 7007)
         {
            _loc18_ = param1.who;
            if(_loc18_.skin.redTime > 0)
            {
               _loc5_ *= 2;
            }
         }
         _loc5_ *= this.hpDownXX;
         if(GameData.gameLV == 6 && Main.gameNum.getValue() > 5000 && Main.gameNum.getValue() < 5100)
         {
            _loc5_ = int(this.GongHuiBoos_HpXX(param1.gongJi_hp));
         }
         this.hpDown_new(_loc5_,param2);
         if(Boolean(this.playerJL) && param1.who is Enemy)
         {
            this.playerJL.BD1009(param1.who,_loc5_);
         }
         if(this.data.getEquipSlot().getEquipFromSlot(6))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14455 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14461 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14462 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14463 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14464 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14465)
            {
               _loc19_ = Math.random() * 20;
               if(_loc19_ < 1)
               {
                  if(Main.gameNum.getValue() == 999)
                  {
                     if(this.hp.getValue() > 0)
                     {
                        this.hp.setValue(this.hp.getValue() + Math.round(this.use_hp_Max.getValue() / 5 / InitData.pkHpMaxNum_Player.getValue()));
                        NewMC.Open("回血效果",this,0,0,0,Math.round(this.use_hp_Max.getValue() / 5 / InitData.pkHpMaxNum_Player.getValue()));
                     }
                  }
                  else if(this.hp.getValue() > 0)
                  {
                     this.hp.setValue(this.hp.getValue() + Math.round(this.use_hp_Max.getValue() / 5));
                     NewMC.Open("回血效果",this,0,0,0,Math.round(this.use_hp_Max.getValue() / 5));
                  }
               }
            }
            else if(this.data.getEquipSlot().getEquipFromSlot(6).getId() >= 14622 && this.data.getEquipSlot().getEquipFromSlot(6).getId() <= 14627 || this.data.getEquipSlot().getEquipFromSlot(6).getId() >= 14613 && this.data.getEquipSlot().getEquipFromSlot(6).getId() <= 14618)
            {
               if(this.hitXX.who is Enemy)
               {
                  _loc20_ = new HitXX();
                  _loc20_.who = this;
                  _loc20_.type = 102;
                  _loc20_.space = 81;
                  _loc20_.totalTime = 81;
                  _loc20_.numValue = 0;
                  if(this.iceTIME == 0)
                  {
                     addEventListener(Event.ENTER_FRAME,this.iceCD);
                     _loc19_ = Math.round(Math.random() * 100);
                     if(_loc19_ <= 25)
                     {
                        new BuffEnemy(_loc20_,this.hitXX.who);
                     }
                  }
               }
            }
            else if(this.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 415)
            {
               if(szCD > 54)
               {
                  szCD = 0;
                  if(Math.random() * 100 < 30)
                  {
                     _loc21_ = NewLoad.XiaoGuoData.getClass("时装冲击波") as Class;
                     this.skin_W.addChild(new _loc21_());
                     this.kouxue = true;
                     this.tankaiTime = 0;
                     addEventListener(Event.ENTER_FRAME,this.tankai);
                  }
               }
            }
         }
         var _loc6_:int = param1.硬直 - this.skin.被攻击硬直;
         if(Boolean(param1.who) && param1.who is Player2)
         {
            _loc6_ = _loc6_ / 5 * 4;
         }
         if(this.noYingZhi || this.noYingZhiTime > 0)
         {
            _loc6_ = -1;
         }
         if(_loc6_ > this.skin.continuousTime && _loc6_ >= 0 && (!this.guangQiu || this.guangQiu.typeX < 3) && !this.guangDun)
         {
            this.skin.GoTo("被打",_loc6_);
         }
         Skill_guangDun.addToPlayer(this);
         this.runArr = new Array();
         if(!this.noYingZhi)
         {
            if(param1.RL)
            {
               this.runPower(param1.runArr[0],param1.runArr[1],param1.runArr[2]);
            }
            else
            {
               this.runPower(-param1.runArr[0],param1.runArr[1],param1.runArr[2]);
            }
         }
      }
      
      private function GongHuiBoos_HpXX(param1:int) : int
      {
         var _loc2_:int = (this.use_fangyu.getValue() + this.fangYuPOWER.getValue()) / 1000;
         if(_loc2_ > 5)
         {
            _loc2_ = 5;
         }
         return int((param1 - _loc2_) * this.hp_Max.getValue() / 100);
      }
      
      public function HpXX2(param1:int) : *
      {
         this.hpDown_new(param1);
      }
      
      public function Hit3000(param1:int, param2:int = 100) : *
      {
         var _loc3_:int = this.hp_Max.getValue() * param1 / param2;
         this.hpDown_new(_loc3_);
      }
      
      private function tankai(param1:*) : *
      {
         var _loc2_:* = undefined;
         var _loc3_:int = 0;
         var _loc4_:HitXX = null;
         ++this.tankaiTime;
         if(this.tankaiTime < 9)
         {
            for(_loc2_ in Enemy.All)
            {
               if((Enemy.All[_loc2_] as Enemy).isFunction)
               {
                  _loc3_ = Enemy.All[_loc2_].x - this.x;
                  if(_loc3_ < 300 && _loc3_ >= 0)
                  {
                     Enemy.All[_loc2_].x += 33;
                     if(this.kouxue)
                     {
                        _loc4_ = new HitXX();
                        _loc4_.who = this;
                        _loc4_.gongJi_hp = 1;
                        _loc4_.times = 1;
                        (Enemy.All[_loc2_] as Enemy).HpXX(_loc4_);
                     }
                     if(Math.random() * 100 < 15)
                     {
                        if(Boolean(this.data.getEquipSlot().getEquipFromSlot(7)) && this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 416)
                        {
                           Enemy.All[_loc2_].skin.GoTo("被打",27);
                        }
                     }
                  }
                  if(_loc3_ > -300 && _loc3_ < 0)
                  {
                     Enemy.All[_loc2_].x -= 33;
                     if(this.kouxue)
                     {
                        _loc4_ = new HitXX();
                        _loc4_.who = this;
                        _loc4_.gongJi_hp = 1;
                        _loc4_.times = 1;
                        (Enemy.All[_loc2_] as Enemy).HpXX(_loc4_);
                     }
                     if(Math.random() * 100 < 15)
                     {
                        if(Boolean(this.data.getEquipSlot().getEquipFromSlot(7)) && this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 416)
                        {
                           Enemy.All[_loc2_].skin.GoTo("被打",27);
                        }
                     }
                  }
               }
            }
            this.kouxue = false;
         }
         else
         {
            removeEventListener(Event.ENTER_FRAME,this.tankai);
         }
      }
      
      private function iceCD(param1:*) : *
      {
         ++this.iceTIME;
         if(this.iceTIME == 135)
         {
            this.iceTIME = 0;
            removeEventListener(Event.ENTER_FRAME,this.iceCD);
         }
      }
      
      private function getRandom(param1:Number) : Boolean
      {
         var _loc2_:Number = Math.random() * 100;
         if(_loc2_ < param1)
         {
            return true;
         }
         return false;
      }
      
      private function SkinValue() : *
      {
         if(this.skin != null)
         {
            this.gravity = this.skin.gravity;
            if(this.skin.moveArr != null)
            {
               if(this.RL)
               {
                  this.runPower(this.skin.moveArr[0],this.skin.moveArr[1],this.skin.moveArr[2]);
               }
               else
               {
                  this.runPower(-this.skin.moveArr[0],this.skin.moveArr[1],this.skin.moveArr[2]);
               }
            }
         }
      }
      
      private function onKEY_DOWN(param1:KeyboardEvent) : *
      {
         if(param1.keyCode == this.data._keyArr[5])
         {
            this.jKey = true;
         }
      }
      
      private function KeyControl() : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:* = 0;
         var _loc5_:int = 0;
         var _loc6_:Class = null;
         if(!this.KeyControl_YN)
         {
            return;
         }
         if(DuoKai_Info._noSave)
         {
            return;
         }
         if(!this.skin)
         {
            return;
         }
         if(Boolean(this.playerJL) && (this == Main.player_1 && Boolean(BasicKey.getKeyState(66,1)) || this == Main.player_2 && Boolean(BasicKey.getKeyState(106,1))))
         {
            this.playerJL.ZD();
         }
         if(this.skin.continuousTime > 0)
         {
            if(this.data.skinArr[this.data.skinNum] == 3 && this.getKeyStatus("转职",1) && this.selCD("k12") != -1)
            {
               if(!this.getKeyStatus("上",3))
               {
                  if(!this.getKeyStatus("下",2))
                  {
                     if(!this.getKeyStatus("上",2))
                     {
                        this.SkinPlay("转职技能1");
                        if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
                        {
                           this.fbChengFa = true;
                        }
                     }
                  }
               }
            }
            return;
         }
         if(!this.skin.Get_StopRun())
         {
            if(this.jumping > 0 && this.skin.runType.substr(0,2) == "攻击")
            {
               this.MoveFun();
            }
            else if(this.data.skinArr[this.data.skinNum] == 3 && this.skin.runType == "转职技能4")
            {
               this.MoveFun();
            }
            return;
         }
         if(this.jumping > 0 && this.getKeyStatus("跳",1) && !PK_UI.PK_ing)
         {
            if(this.mp.getValue() < 1)
            {
               this.mp.setValue(1);
               this.fly = false;
               this.flyTime = 0;
            }
            else
            {
               this.fly = true;
            }
         }
         if(this.getKeyStatus("跳",2) == false)
         {
            this.flyTime = 0;
         }
         if(PK_UI.PK_ing)
         {
            this.fly = false;
            this.flyTime = 0;
         }
         if(this.fly && this.mp.getValue() > 1 && this.getKeyStatus("跳",2) && this.skin_Z3_V && this.data.getEquipSlot().getEquipFromSlot(7) && this.data.getEquipSlot().getEquipFromSlot(7).getColor() >= 4 && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
         {
            _loc2_ = 2;
            ++this.flyTime;
            if(this.data.getEquipSlot().getEquipFromSlot(6))
            {
               if(this.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 423)
               {
                  _loc2_ = 4;
               }
            }
            if(this.flyTime > 4)
            {
               this.feixingBool3 = true;
               if(this.flyTime % 24 == 10)
               {
                  _loc5_ = this.mp.getValue() - this.use_mp_Max.getValue() / 100 * _loc2_ * (1 - this.data.getStampSlot().getValueSlot9());
                  if(_loc5_ < 1)
                  {
                     this.mp.setValue(1);
                  }
                  else
                  {
                     this.mp.setValue(_loc5_);
                  }
               }
               this.gravity = this.gravityNum = 0;
               _loc3_ = this.walk_power.getValue() * 1.5;
               _loc4_ = this.data.getEquipSlot().getEquipFromSlot(7).getId();
               if(_loc4_ >= 14604 && _loc4_ <= 14609)
               {
                  _loc3_ += 2;
               }
               if(this.getKeyStatus("左",2))
               {
                  this.runPower(-_loc3_ * this.walk_power2,0,1);
                  this.getRL(false);
               }
               if(this.getKeyStatus("右",2))
               {
                  this.runPower(_loc3_ * this.walk_power2,0,1);
                  this.getRL(true);
               }
               if(this.getKeyStatus("上",2))
               {
                  this.runPower(0,8 * this.walk_power2,1);
               }
               if(this.getKeyStatus("下",2))
               {
                  this.runPower(0,-8 * this.walk_power2,1);
               }
               this.SkinPlay("跳");
               return;
            }
         }
         else
         {
            this.feixingBool3 = false;
         }
         if(Boolean(this.jKey) && (this.jumping == 0 || Boolean(this.jumpX2) && this.jumping < 2))
         {
            this.jKey = false;
            this.runPower(0,this.jump_power,this.jump_time,"跳");
            this.SkinPlay("跳");
            this.jumping += 1;
            if(Boolean(this.jumpX2) && this.jumping == 2)
            {
               NewMC.Open("二段跳效果",Main.world.moveChild_Other,this.x,this.y);
            }
         }
         else if(this.jKey)
         {
            this.jKey = false;
         }
         var _loc1_:String = "站";
         if(this.getKeyStatus("左",3))
         {
            _loc1_ = "跑";
            if(!this.noMove)
            {
               this.runPower(-this.walk_power.getValue() * 1.5 * this.walk_power2,0,1);
            }
            this.getRL(false);
         }
         else if(this.getKeyStatus("右",3))
         {
            _loc1_ = "跑";
            if(!this.noMove)
            {
               this.runPower(this.walk_power.getValue() * 1.5 * this.walk_power2,0,1);
            }
            this.getRL(true);
         }
         else if(this.getKeyStatus("左",2))
         {
            _loc1_ = "走";
            if(!this.noMove)
            {
               this.runPower(-this.walk_power.getValue() * this.walk_power2,0,1);
            }
            this.getRL(false);
         }
         else if(this.getKeyStatus("右",2))
         {
            _loc1_ = "走";
            if(!this.noMove)
            {
               this.runPower(this.walk_power.getValue() * this.walk_power2,0,1);
            }
            this.getRL(true);
         }
         if(this.getKeyStatus("攻击",1))
         {
            if(_loc1_ == "跑" && this.jumping == 0)
            {
               this.SkinPlay("跑攻");
            }
            else if(this.getKeyStatus("上",2))
            {
               this.SkinPlay("上挑");
            }
            else if(this.getKeyStatus("下",2))
            {
               this.SkinPlay("下斩");
            }
            else if(this.jumping > 0)
            {
               this.SkinPlay("攻击1");
               if(this.isDarkState)
               {
                  _loc6_ = NewLoad.XiaoGuoData.getClass("时装死灵球") as Class;
                  this.skin_W.addChild(new _loc6_());
               }
            }
            else
            {
               this.skin.timeNum = 25;
               this.SkinPlay("攻击");
               if(this.isDarkState)
               {
                  _loc6_ = NewLoad.XiaoGuoData.getClass("时装死灵球") as Class;
                  this.skin_W.addChild(new _loc6_());
               }
            }
            if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 2)
            {
               this.fbChengFa = true;
            }
         }
         else if(this.getKeyStatus("怪物",1))
         {
            this.SkinPlay("怪物技能");
         }
         else if(this.getKeyStatus("技能1",1))
         {
            this.SkinPlay("技能1");
            if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
            {
               this.fbChengFa = true;
            }
         }
         else if(this.getKeyStatus("技能2",1))
         {
            this.SkinPlay("技能2");
            if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
            {
               this.fbChengFa = true;
            }
         }
         else if(this.getKeyStatus("技能3",1))
         {
            this.SkinPlay("技能3");
            if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
            {
               this.fbChengFa = true;
            }
         }
         else if(this.getKeyStatus("技能4",1))
         {
            this.SkinPlay("技能4");
            if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
            {
               this.fbChengFa = true;
            }
         }
         else if(this.getKeyStatus("转职",1))
         {
            if(this.getKeyStatus("上",3))
            {
               this.SkinPlay("转职技能4");
               if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
               {
                  this.fbChengFa = true;
               }
            }
            else if(this.getKeyStatus("下",2))
            {
               this.SkinPlay("转职技能3");
               if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
               {
                  this.fbChengFa = true;
               }
            }
            else if(this.getKeyStatus("上",2))
            {
               this.SkinPlay("转职技能2");
               if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
               {
                  this.fbChengFa = true;
               }
            }
            else
            {
               this.SkinPlay("转职技能1");
               if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 3)
               {
                  this.fbChengFa = true;
               }
            }
         }
         else if(this.jumping == 0)
         {
            this.SkinPlay(_loc1_);
         }
         else
         {
            this.SkinPlay("跳");
         }
         --this.SwitchingTime;
         if(this.getKeyStatus("切换",1) && this.SwitchingTime <= 0)
         {
            this.SwitchingTime = 6;
            this.QieHuanSkin();
         }
      }
      
      public function QieHuanSkin() : *
      {
         if(NewLoad.loadArr.length > 0)
         {
            TiaoShi.txtShow("pk玩家加载中, 禁止切换");
            return;
         }
         if(this.data.skinNum == 0)
         {
            this.data.skinNum = 1;
            this.newSkin();
         }
         else
         {
            this.data.skinNum = 0;
            this.newSkin();
         }
      }
      
      private function 药品使用() : *
      {
         if(Boolean(GameOver.noFuHuo) || this.visible == false)
         {
            return;
         }
         if(this.getKeyStatus("消耗1",1))
         {
            this.UserObj(0);
         }
         else if(this.getKeyStatus("消耗2",1))
         {
            this.UserObj(1);
         }
         else if(this.getKeyStatus("消耗3",1))
         {
            this.UserObj(2);
         }
      }
      
      private function 复活药使用() : *
      {
         var _loc1_:* = undefined;
         var _loc2_:int = 0;
         for(_loc1_ in Enemy.All)
         {
            if(Enemy.All[_loc1_].id == 3022)
            {
               Enemy.All[_loc1_].rebornTimes = 1;
               Enemy.All[_loc1_].skin.runOver = true;
               Enemy.All[_loc1_].skin.GoTo("攻击3");
            }
         }
         JiHua_Interface.ppp2_13 = true;
         this.信春哥();
         this.alpha = 0.7;
         this.newLifeTime.setValue(InitData.BuyNum_1.getValue());
         addEventListener(Event.ENTER_FRAME,this.NewLife);
         All[All.length] = this;
         GameData.deadTime = 5;
         this.newSkin();
         if(Main.gameNum.getValue() == 2000)
         {
            GameData.winYN = false;
            _loc2_ = Main.gameNum2.getValue() - 3;
            if(_loc2_ < 1)
            {
               _loc2_ = 1;
            }
            Main.gameNum2.setValue(_loc2_);
            Main._this.Loading();
         }
      }
      
      private function 刷新背包显示() : *
      {
         BagItemsShow.suppliesShow();
      }
      
      private function NewLife(param1:*) : *
      {
         this.newLifeTime.setValue(this.newLifeTime.getValue() + InitData.BuyNum_1.getValue());
         if(this.newLifeTime.getValue() % 10 > 4)
         {
            this.alpha = 1;
         }
         else
         {
            this.alpha = 0.7;
         }
         if(this.newLifeTime.getValue() > InitData.BuyNum_80.getValue())
         {
            this.newLifeTime = VT.createVT();
            this.alpha = 1;
            removeEventListener(Event.ENTER_FRAME,this.NewLife);
         }
      }
      
      private function UserObj(param1:int) : *
      {
         var _loc4_:int = 0;
         var _loc5_:Supplies = null;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:int = 0;
         var _loc12_:int = 0;
         var _loc13_:int = 0;
         var _loc14_:int = 0;
         var _loc15_:int = 0;
         var _loc16_:int = 0;
         var _loc17_:int = 0;
         var _loc18_:int = 0;
         var _loc19_:int = 0;
         var _loc20_:Number = NaN;
         if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 1)
         {
            this.fbChengFa = true;
         }
         if(this.data.getSuppliesSlot().getNumFromSuppliesSlot(this.data.getBag(),param1) < 1)
         {
            return;
         }
         if(this.hp.getValue() <= 0 && GameData.gameLV != 6)
         {
            _loc5_ = this.data.getSuppliesSlot().getFromSuppliesSlot(param1);
            _loc6_ = _loc5_.getAffectMode();
            if(_loc6_ == 2)
            {
               if(Boolean(PK_JiFen_UI._this) && Boolean(PK_JiFen_UI._this.visible))
               {
                  return;
               }
               this.复活药使用();
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
               Main.Save(false);
            }
            return;
         }
         var _loc2_:String = this.data.getSuppliesSlot().getFromSuppliesSlot(param1).getName();
         if(!this.CanUserObj(_loc2_))
         {
            return;
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(param1).getAffectMode() == 3)
         {
            if(skillYAO >= 3)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"一关内最多使用3次，已达到上限");
               return;
            }
            if(this.data.skinNum == 0 && Boolean(this.data.getEquipSkillSlot().getGemFromSkillSlot(0)))
            {
               if(this.energySlot.getEnergyPer(this) < 100)
               {
                  this.energySlot.energyLeftNum.setValue(98745305);
               }
               ++skillYAO;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"技能石充能完毕！");
               _loc7_ = int(this.sel_objCD(_loc2_));
               this.All_ObjCDXX[_loc7_][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
            }
            if(this.data.skinNum == 1 && Boolean(this.data.getEquipSkillSlot().getGemFromSkillSlot(1)))
            {
               if(this.energySlot.getEnergyPer(this) < 100)
               {
                  this.energySlot.energyRightNum.setValue(92145035);
               }
               ++skillYAO;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"技能石充能完毕！");
               _loc7_ = int(this.sel_objCD(_loc2_));
               this.All_ObjCDXX[_loc7_][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(param1).getAffectMode() == 5)
         {
            if(JingLingCatch.count == 1)
            {
               if(this.data.getElvesSlot().backElvesSlotNum() <= 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"精灵栏已满，请清理扩充");
                  return;
               }
               if(JingLingCatch.isMove)
               {
                  JingLingCatch.myplayer = this.data;
                  JingLingCatch.catchJL();
                  _loc7_ = int(this.sel_objCD(_loc2_));
                  this.All_ObjCDXX[_loc7_][1] = 0;
                  this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"精灵正在被捕捉中");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"没有发现精灵踪迹");
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(param1).getAffectMode() == 6)
         {
            if(this.zhongqiuState == 0)
            {
               if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
               {
                  this.zhongqiuState = 2;
               }
               else
               {
                  this.zhongqiuState = 1;
               }
               this.jianrenState = 0;
               this.shengmingState = 0;
               _loc7_ = int(this.sel_objCD(_loc2_));
               this.All_ObjCDXX[_loc7_][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"已经处于疯狂状态");
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(param1).getAffectMode() == 7)
         {
            if(this.jianrenState == 0)
            {
               if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
               {
                  this.jianrenState = 2;
               }
               else
               {
                  this.jianrenState = 1;
               }
               this.shengmingState = 0;
               this.zhongqiuState = 0;
               _loc7_ = int(this.sel_objCD(_loc2_));
               this.All_ObjCDXX[_loc7_][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"已经处于坚韧状态");
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(param1).getAffectMode() == 8)
         {
            if(this.shengmingState == 0)
            {
               if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
               {
                  this.shengmingState = 2;
               }
               else
               {
                  this.shengmingState = 1;
               }
               this.jianrenState = 0;
               this.zhongqiuState = 0;
               _loc7_ = int(this.sel_objCD(_loc2_));
               this.All_ObjCDXX[_loc7_][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"充斥生命之力状态");
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(param1).getAffectMode() == 4)
         {
            _loc8_ = this.use_mp_Max.getValue() * InitData.zongzi.getValue();
            _loc9_ = this.mp.getValue() + _loc8_;
            _loc10_ = this.use_mp_Max.getValue();
            if(_loc9_ < _loc10_)
            {
               this.mp.setValue(_loc9_);
            }
            else
            {
               this.mp.setValue(_loc10_);
            }
            NewMC.Open("回蓝效果",this,0,0,0,_loc8_);
            _loc11_ = this.use_hp_Max.getValue() * InitData.zongzi.getValue();
            _loc9_ = this.hp.getValue() + _loc11_;
            _loc12_ = this.use_hp_Max.getValue();
            if(_loc9_ < _loc12_)
            {
               this.hp.setValue(_loc9_);
            }
            else
            {
               this.hp.setValue(_loc12_);
            }
            NewMC.Open("回血效果",this,0,60,0,_loc11_);
            _loc7_ = int(this.sel_objCD(_loc2_));
            this.All_ObjCDXX[_loc7_][1] = 0;
            this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
         }
         this.XXSupplies = this.data.getSuppliesSlot().getFromSuppliesSlot(param1);
         var _loc3_:Array = this.XXSupplies.getAffectAttrib();
         for(_loc4_ in _loc3_)
         {
            _loc13_ = int((_loc3_[_loc4_] as SuppliesAffect).getAttribType());
            _loc14_ = int((_loc3_[_loc4_] as SuppliesAffect).getValue());
            _loc15_ = int(this.XXSupplies.getUseLevel());
            _loc18_ = int(this.XXSupplies.getPercent());
            if(_loc13_ == 1 && this.data.getLevel() >= _loc15_)
            {
               _loc20_ = this.data.getStampSlot().getValueSlot2();
               _loc14_ *= 1 + _loc20_;
               _loc16_ = this.hp.getValue() + _loc14_;
               _loc17_ = this.use_hp_Max.getValue();
               if(_loc16_ < _loc17_)
               {
                  this.hp.setValue(_loc16_);
               }
               else
               {
                  this.hp.setValue(_loc17_);
               }
               NewMC.Open("回血效果",this,0,0,0,_loc14_);
               _loc7_ = int(this.sel_objCD(_loc2_));
               this.All_ObjCDXX[_loc7_][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
               this.tempChiXuTime = this.XXSupplies.getDuration();
               this.tempVaPer = this.XXSupplies.getPercent();
               addEventListener(Event.ENTER_FRAME,this.chixuhuifu);
            }
            else if(_loc13_ == 2 && this.data.getLevel() >= _loc15_)
            {
               _loc20_ = this.data.getStampSlot().getValueSlot1();
               _loc19_ = Math.round(this.use_mp_Max.getValue() * (_loc18_ / 100));
               _loc14_ = (_loc14_ + _loc19_) * (1 + _loc20_);
               _loc16_ = this.mp.getValue() + _loc14_;
               _loc17_ = this.use_mp_Max.getValue();
               if(_loc16_ < _loc17_)
               {
                  this.mp.setValue(_loc16_);
               }
               else
               {
                  this.mp.setValue(_loc17_);
               }
               NewMC.Open("回蓝效果",this,0,0,0,_loc14_);
               _loc7_ = int(this.sel_objCD(_loc2_));
               this.All_ObjCDXX[_loc7_][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),param1);
            }
         }
         this.刷新背包显示();
      }
      
      private function chixuhuifu(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         if(this.hp.getValue() <= 0)
         {
            removeEventListener(Event.ENTER_FRAME,this.chixuhuifu);
         }
         else
         {
            ++this.tempCXHF;
            _loc2_ = this.use_hp_Max.getValue();
            _loc3_ = Math.round(_loc2_ * (this.tempVaPer / 100));
            _loc4_ = this.hp.getValue() + _loc3_;
            if(this.tempCXHF <= this.tempChiXuTime)
            {
               if(this.tempCXHF % 27 == 0)
               {
                  if(_loc4_ < _loc2_)
                  {
                     this.hp.setValue(_loc4_);
                  }
                  else
                  {
                     this.hp.setValue(_loc2_);
                  }
                  NewMC.Open("回血效果",this,0,0,0,_loc3_);
               }
            }
            else
            {
               this.tempCXHF = 0;
               removeEventListener(Event.ENTER_FRAME,this.chixuhuifu);
            }
         }
      }
      
      public function CanSkill(param1:String, param2:int) : Boolean
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc3_:Skill = SkillFactory.getSkillByTypeIAndLevel(param1,param2);
         if(_loc3_ != null)
         {
            _loc4_ = this.mp.getValue() - _loc3_.getMp() * (1 - this.data.getStampSlot().getValueSlot9());
            if(_loc4_ <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"魔法不足,无法发动技能");
               return false;
            }
            _loc5_ = int(this.selCD(param1));
            if(_loc5_ == -1)
            {
               return false;
            }
            _loc6_ = this.data.getEquipSlot().getAllSuitSkill();
            for(_loc7_ in _loc6_)
            {
               if(_loc6_[_loc7_] == 57363)
               {
                  if(this.flagTempMP)
                  {
                     this.flagTempMP = false;
                     this.setInTimeCDMP();
                     this.AllSkillCDXX[_loc5_][1] = 0;
                     return true;
                  }
               }
            }
            this.mp.setValue(_loc4_);
            this.AllSkillCDXX[_loc5_][1] = 0;
            if(this.playerJL)
            {
               this.playerJL.BD1017(_loc5_);
            }
            return true;
         }
         return true;
      }
      
      public function CanUserObj(param1:String) : Boolean
      {
         var _loc2_:int = int(this.sel_objCD(param1));
         if(_loc2_ == -1)
         {
            return false;
         }
         return true;
      }
      
      private function huiFuYinZang() : *
      {
         var _loc1_:Number = NaN;
         --this.huiFuTime;
         if(this.huiFuTime == 0)
         {
            _loc1_ = this.data.getStampSlot().getValueSlot10();
            if(_loc1_ > 0)
            {
               this.HpUp(_loc1_,2);
               TiaoShi.txtShow("恢复印章效果!" + _loc1_);
            }
         }
      }
      
      public function huiFuYinZangXX() : *
      {
         if(this.huiFuTime < 0)
         {
            this.huiFuTime = 81;
         }
      }
      
      public function runPower(param1:Number = 0, param2:Number = 0, param3:int = 0, param4:String = "") : *
      {
         var _loc9_:int = 0;
         if(param4 == "跳")
         {
            _loc9_ = this.runArr.length - 1;
            while(_loc9_ >= 0)
            {
               if(this.runArr[_loc9_][5] == "跳")
               {
                  this.runArr.splice(_loc9_,1);
               }
               _loc9_--;
            }
         }
         if(param3 <= 3)
         {
            this.runArr[this.runArr.length] = [param1,param2,1,0,0,param4];
            return;
         }
         var _loc5_:* = param1 * this.parabola / param3;
         var _loc6_:* = param2 * this.parabola / param3;
         var _loc7_:Number = param1 * (1 - this.parabola) / (param3 * param3 - param3 * (param3 - 1) / 2);
         var _loc8_:Number = param2 * (1 - this.parabola) / (param3 * param3 - param3 * (param3 - 1) / 2);
         this.runArr[this.runArr.length] = [_loc5_,_loc6_,param3,_loc7_,_loc8_,param4];
      }
      
      private function MoveData() : *
      {
         this.runX = this.runY = 0;
         this.heiAnJiNeng_width = 0;
         this.heiAnMove = false;
         var _loc1_:int = this.runArr.length - 1;
         while(_loc1_ >= 0)
         {
            if(this.runArr[_loc1_][2] > 0)
            {
               this.runX += this.runArr[_loc1_][0] + this.runArr[_loc1_][3] * this.runArr[_loc1_][2];
               this.runY -= this.runArr[_loc1_][1] + this.runArr[_loc1_][4] * this.runArr[_loc1_][2];
               --this.runArr[_loc1_][2];
               if(this.runArr[_loc1_][5] == "黑暗使徒时装技能")
               {
                  this.heiAnMove = true;
               }
            }
            else
            {
               this.runArr.splice(_loc1_,1);
            }
            _loc1_--;
         }
         if(this.runY < 0)
         {
            this.jumpType = 1;
            this.gravityNum = 0;
         }
         else
         {
            this.jumpType = 2;
            if(this.gravity != 0)
            {
               this.gravityNum += 1;
            }
            this.runY += this.gravity * this.gravityNum;
         }
      }
      
      private function MoveRun() : *
      {
         var _loc1_:Boolean = false;
         var _loc9_:Boolean = false;
         var _loc10_:Player = null;
         var _loc12_:Boolean = false;
         var _loc13_:Boolean = false;
         var _loc14_:Boolean = false;
         var _loc15_:Boolean = false;
         var _loc16_:Boolean = false;
         if(this.runX > 0)
         {
            _loc1_ = true;
         }
         var _loc2_:int = this.x;
         var _loc3_:int = Math.abs(this.runX);
         var _loc4_:int = Math.abs(this.runY);
         var _loc5_:int = this.x + Main.world.x;
         var _loc6_:int = this.y;
         var _loc7_:int = int(Main.world.x);
         var _loc8_:int = _loc4_;
         while(_loc8_ > 0)
         {
            if(this.jumpType == 1)
            {
               _loc12_ = Boolean(Main.world.MapData1.hitTestPoint(_loc5_,_loc6_ - 100,true));
               if(!_loc12_)
               {
                  _loc6_--;
               }
            }
            else if(this.jumpType == 2)
            {
               _loc13_ = Boolean(Main.world.MapData.hitTestPoint(_loc5_,_loc6_ - 3,true));
               _loc14_ = Boolean(Main.world.MapData.hitTestPoint(_loc5_,_loc6_ - 1,true));
               _loc15_ = Boolean(Main.world.MapData.hitTestPoint(_loc5_,_loc6_ + 6,true));
               if(_loc13_)
               {
                  _loc6_ -= 2;
                  this.jumping = 0;
               }
               else if(_loc14_)
               {
                  this.runY = 0;
                  this.gravityNum = 0;
                  this.jumping = 0;
               }
               else if(_loc15_)
               {
                  _loc6_ += 3;
                  this.jumping = 0;
               }
               else
               {
                  _loc6_++;
               }
            }
            else if(this.jumpType == 3)
            {
               _loc16_ = Boolean(Main.world.MapData1.hitTestPoint(_loc5_,_loc6_ - 5,true));
               if(!_loc16_)
               {
                  this.jumpType = 2;
                  break;
               }
               _loc6_ += 2;
            }
            _loc8_--;
         }
         this.y = _loc6_;
         if(Main.player_2)
         {
            if(Main.player_1.hp.getValue() > 0 && Main.player_2.hp.getValue() > 0)
            {
               _loc9_ = true;
            }
            else
            {
               _loc9_ = false;
            }
            if(this == Main.player_1)
            {
               _loc10_ = Main.player_2;
            }
            else
            {
               _loc10_ = Main.player_1;
            }
         }
         var _loc11_:int = _loc3_;
         while(_loc11_ > 0)
         {
            if(_loc1_ && !Main.world.MapData1.hitTestPoint(_loc5_ + 20,_loc6_ - 50,true))
            {
               if(!_loc9_)
               {
                  if(_loc5_ > 520 && _loc7_ > 940 - Main.world.stopX_2)
                  {
                     _loc7_--;
                  }
                  if(_loc5_ - Main.world.x < Main.world.stopX_2)
                  {
                     _loc5_++;
                  }
               }
               else
               {
                  if(_loc5_ > 520 && _loc7_ > 940 - Main.world.stopX_2 && _loc10_.x + _loc7_ > 0)
                  {
                     _loc7_--;
                  }
                  if(_loc5_ - Main.world.x < Main.world.stopX_2 && Math.abs(_loc5_ - _loc10_.x - _loc7_ + 2) < 940)
                  {
                     _loc5_++;
                  }
               }
            }
            else if(!_loc1_ && !Main.world.MapData1.hitTestPoint(_loc5_ - 20,_loc6_ - 50,true))
            {
               if(!_loc9_)
               {
                  if(_loc5_ < 420 && _loc7_ < -Main.world.stopX_1)
                  {
                     _loc7_++;
                  }
                  if(_loc5_ - Main.world.x > Main.world.stopX_1)
                  {
                     _loc5_--;
                  }
               }
               else
               {
                  if(_loc5_ < 420 && _loc7_ < -Main.world.stopX_1 && _loc10_.x + _loc7_ < 940)
                  {
                     _loc7_++;
                  }
                  if(_loc5_ - Main.world.x > Main.world.stopX_1 && Math.abs(_loc5_ - _loc10_.x - _loc7_ - 2) < 940)
                  {
                     _loc5_--;
                  }
               }
            }
            _loc11_--;
         }
         this.x = _loc5_ - Main.world.x;
         Main.world.x = _loc7_;
         if(this.heiAnMove)
         {
            _loc2_ = this.x - _loc2_;
            this.heiAnJiNeng_width = _loc2_;
         }
      }
      
      public function getKeyStatus(param1:*, param2:int = 1, param3:Boolean = false) : Boolean
      {
         var _loc4_:int = 0;
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         if(param1 is String)
         {
            _loc4_ = 0;
            while(_loc4_ < this.data._keyArr.length)
            {
               if(this.KeyArrStr[_loc4_] == param1)
               {
                  return Boolean(BasicKey.getKeyState(this.data._keyArr[_loc4_],param2));
               }
               _loc4_++;
            }
            return false;
         }
         if(param1 is Array)
         {
            _loc6_ = new Array();
            _loc4_ = 0;
            while(_loc4_ < (param1 as Array).length)
            {
               _loc7_ = 0;
               while(_loc7_ < this.data._keyArr.length)
               {
                  if(this.KeyArrStr[_loc7_] == param1[_loc4_])
                  {
                     _loc6_[_loc6_.length] = this.data._keyArr[_loc7_];
                  }
                  _loc7_++;
               }
               _loc4_++;
            }
            return BasicKey.getTargetState(_loc6_);
         }
         return false;
      }
      
      public function newSkin() : *
      {
         var _loc1_:Number = 1;
         var _loc2_:Number = 2;
         if(this == Main.player_2)
         {
            _loc1_ = 2;
            _loc2_ = 3;
         }
         NewLoad.Loading(_loc2_,_loc1_);
         Skill_guangQiu.addToPlayer(this);
      }
      
      public function newSkin_LoadEnd() : *
      {
         if(!this.backMC)
         {
            this.backMC = new MovieClip();
            this.addChild(this.backMC);
         }
         this.newZhuangBei3();
         if(this.skin)
         {
            this.skin.gotoAndStop("站");
            this.skin.parent.removeChild(this.skin);
            this.skin = null;
         }
         this.OpenJL();
         this.AddSkin();
         this.newZhuangBei();
         this.HeadXX();
         this.newWuQi();
         this.OpenCW();
         if(SixOne_Interface.state2021.getValue() == 1 || SixOne_Interface.state2021.getValue() == 2)
         {
            this.OpenYS();
         }
         this.LoadPlayerLvData();
         this.getRL(this.RL);
         addChild(this.cengHao_mc);
      }
      
      private function newZhuangBei3() : *
      {
         if(this.skin_Z3)
         {
            this.skin_Z3.parent.removeChild(this.skin_Z3);
            this.skin_Z3 = null;
         }
         this.AddSkin_Z3();
      }
      
      public function newZhuangBei() : *
      {
         if(this.skin_Z2)
         {
            this.skin_Z2.parent.removeChild(this.skin_Z2);
            this.skin_Z2 = null;
         }
         if(this.skin_Z)
         {
            this.skin_Z.parent.removeChild(this.skin_Z);
            this.skin_Z = null;
         }
         this.AddSkin_Z();
      }
      
      public function newWuQi() : *
      {
         if(this.skin_W)
         {
            this.skin_W.parent.removeChild(this.skin_W);
            this.skin_W = null;
         }
         this.AddSkin_W();
      }
      
      private function AddSkin() : *
      {
         var _loc1_:int = int(this.data.skinArr[this.data.skinNum]);
         var _loc2_:Class = PlayerMcArr[_loc1_].getClass("src.Skin.Skin_player" + _loc1_) as Class;
         this.skin = new _loc2_();
         this.skin.skinNum = _loc1_;
         this.skin.Xml = Skin.PlayerXml[_loc1_];
         this.skin.playX = this;
         addChild(this.skin);
         this.skin.mouseEnabled = false;
         this.skin.mouseChildren = false;
      }
      
      private function AddSkin_Z3() : *
      {
         var _loc1_:int = 0;
         var _loc2_:* = undefined;
         var _loc3_:* = undefined;
         var _loc4_:* = undefined;
         var _loc5_:Class = null;
         if(this.data.getEquipSlot().getEquipFromSlot(7) != null)
         {
            _loc1_ = int(this.data.skinArr[this.data.skinNum]);
            _loc2_ = this.data.getEquipSlot().getEquipFromSlot(7).getClassName();
            _loc3_ = this.data.getEquipSlot().getEquipFromSlot(7).getClassName3();
            _loc4_ = this.data.getEquipSlot().getEquipFromSlot(7).getClassName4();
            _loc5_ = NewLoad.zhuangBeiSkin[_loc1_][_loc3_].getClass(_loc2_) as Class;
            this.skin_Z3 = new _loc5_();
            addChild(this.skin_Z3);
            if(this.skin_Z3)
            {
               if(this.skin_Z3_V && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  this.skin_Z3.visible = true;
                  this.paiHangShow[4] = [_loc3_,_loc4_,_loc2_];
               }
               else
               {
                  this.skin_Z3.visible = false;
               }
            }
         }
      }
      
      private function AddSkin_Z() : *
      {
         var _loc2_:String = null;
         var _loc3_:int = 0;
         var _loc5_:String = null;
         var _loc1_:int = int(this.data.skinArr[this.data.skinNum]);
         this.paiHangShow[0] = _loc1_;
         var _loc4_:Number = 1;
         if(Main.water.getValue() != 1)
         {
            _loc4_ = 9;
         }
         if(this.data.getEquipSlot().getEquipFromSlot(_loc4_) == null)
         {
            _loc2_ = "甲白1";
            _loc3_ = 1;
            _loc5_ = "1_v2";
         }
         else
         {
            _loc2_ = this.data.getEquipSlot().getEquipFromSlot(_loc4_).getClassName();
            _loc3_ = this.data.getEquipSlot().getEquipFromSlot(_loc4_).getClassName3();
            _loc5_ = this.data.getEquipSlot().getEquipFromSlot(_loc4_).getClassName4();
         }
         var _loc6_:Class = NewLoad.zhuangBeiSkin[_loc1_][_loc3_].getClass(_loc2_) as Class;
         this.skin_Z = new _loc6_();
         addChild(this.skin_Z);
         this.skin_Z.mouseChildren = this.skin_Z.mouseEnabled = false;
         this.paiHangShow[3] = [_loc3_,_loc5_,_loc2_];
         var _loc7_:Array = new Array();
         if(this.data.getEquipSlot().getEquipFromSlot(6) != null)
         {
            _loc2_ = this.data.getEquipSlot().getEquipFromSlot(6).getClassName();
            _loc3_ = this.data.getEquipSlot().getEquipFromSlot(6).getClassName3();
            _loc5_ = this.data.getEquipSlot().getEquipFromSlot(6).getClassName4();
            _loc6_ = NewLoad.zhuangBeiSkin[_loc1_][_loc3_].getClass(_loc2_) as Class;
            this.skin_Z2 = new _loc6_();
            addChild(this.skin_Z2);
            this.skin_Z.visible = false;
            _loc7_ = [_loc3_,_loc5_,_loc2_];
         }
         if(this.skin_Z2 && this.skin_Z2_V && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            this.skin_Z2.visible = true;
            this.skin_Z.visible = false;
            this.skin_Z2.mouseChildren = this.skin_Z2.mouseEnabled = false;
            this.paiHangShow[3] = _loc7_;
         }
         else if(!this.skin_Z2_V || this.skin_Z2 && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() <= 0)
         {
            if(this.skin_Z2)
            {
               this.skin_Z2.visible = false;
            }
            this.skin_Z.visible = true;
         }
      }
      
      private function AddSkin_W() : *
      {
         var _loc2_:String = null;
         var _loc1_:int = this.data.skinNum;
         if(_loc1_ == 0)
         {
            _loc2_ = this.data.getEquipSlot().getEquipFromSlot(2).getClassName();
         }
         else if(_loc1_ == 1)
         {
            _loc2_ = this.data.getEquipSlot().getEquipFromSlot(5).getClassName();
         }
         var _loc3_:Class = Skin_WuQi.PlayerMcArr[this.data.skinArr[this.data.skinNum]].getClass(_loc2_) as Class;
         this.paiHangShow[2] = _loc2_;
         this.skin_W = new _loc3_();
         addChild(this.skin_W);
         this.skin_W.mouseEnabled = false;
         this.skin_W.mouseChildren = false;
      }
      
      private function SkinPlay(param1:String, param2:int = 0) : *
      {
         if(this.skin)
         {
            this.skin.GoTo(param1,param2);
         }
      }
      
      private function getRL(param1:Boolean) : *
      {
         this.RL = param1;
         if(param1)
         {
            this.skin.scaleX = this.skin_Z.scaleX = this.skin_W.scaleX = -1;
            if(this.skin_Z2)
            {
               this.skin_Z2.scaleX = -1;
            }
            if(this.skin_Z3)
            {
               this.skin_Z3.scaleX = -1;
            }
            if(this.cengHao_mc.frame == 363)
            {
               this.cengHao_mc.scaleX = 1;
            }
         }
         else if(!param1)
         {
            this.skin.scaleX = this.skin_Z.scaleX = this.skin_W.scaleX = 1;
            if(this.skin_Z2)
            {
               this.skin_Z2.scaleX = 1;
            }
            if(this.skin_Z3)
            {
               this.skin_Z3.scaleX = 1;
            }
            if(this.cengHao_mc.frame == 363)
            {
               this.cengHao_mc.scaleX = -1;
            }
         }
      }
      
      private function MoveFun() : *
      {
         if(this.getKeyStatus("左",3))
         {
            this.runPower(-this.walk_power.getValue() * 1.5 * this.walk_power2,0,1);
         }
         else if(this.getKeyStatus("右",3))
         {
            this.runPower(this.walk_power.getValue() * 1.5 * this.walk_power2,0,1);
         }
         else if(this.getKeyStatus("左",2))
         {
            this.runPower(-this.walk_power.getValue() * this.walk_power2,0,1);
         }
         else if(this.getKeyStatus("右",2))
         {
            this.runPower(this.walk_power.getValue() * this.walk_power2,0,1);
         }
      }
      
      public function OpenCW() : *
      {
         if(Boolean(this.data.playerCW_Data) && this.data.playerCW_Data.getType() == 1)
         {
            this.data.playerCW_Data = null;
            return;
         }
         if(this.data.playerCW_Data && !this.playerCW && this.data.playerCW_Data.getFood() > 0)
         {
            this.NewCW(this.data.playerCW_Data);
         }
      }
      
      public function NewCW(param1:Pet) : *
      {
         if(this.playerCW)
         {
            this.playerCW.Close();
            TiaoShi.cwArr.push("NewCW.close");
         }
         TiaoShi.cwArr.push("NewCW.close????");
         this.data.playerCW_Data = param1;
         this.playerCW = new ChongWu(param1,this);
      }
      
      public function OpenJL() : *
      {
         if(this.playerJL)
         {
            this.playerJL.JingLingOPEN();
            if(Main.gameNum.getValue() == 0)
            {
               this.playerJL.jiNengZD_num = 0;
            }
         }
         else if(Boolean(this.data.playerJL_Data) && this.visible)
         {
            this.NewJL(this.data.playerJL_Data);
         }
      }
      
      public function NewJL(param1:Elves) : *
      {
         if(this.playerJL)
         {
            this.playerJL.Close();
         }
         this.data.playerJL_Data = param1;
         this.playerJL = new JingLing(param1,this);
      }
      
      public function HpUp(param1:Number, param2:int = 1) : *
      {
         var _loc4_:int = 0;
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         var _loc3_:int = this.use_hp_Max.getValue();
         if(param2 == 1)
         {
            _loc4_ = this.hp.getValue() + param1;
         }
         else if(param2 == 2)
         {
            param1 = _loc3_ * param1 / 100;
            _loc4_ = this.hp.getValue() + param1;
         }
         else if(param2 == 3)
         {
            this.hp.setValue(param1);
         }
         else
         {
            param1 = 0;
            _loc4_ = 0;
         }
         if(_loc4_ < _loc3_)
         {
            this.hp.setValue(_loc4_);
         }
         else
         {
            this.hp.setValue(_loc3_);
         }
         NewMC.Open("回血效果",this,0,0,0,param1);
      }
      
      public function MpUp(param1:Number, param2:int = 1) : *
      {
         if(this.hp.getValue() <= 0 || param1 <= 0)
         {
            return;
         }
         var _loc3_:int = this.use_mp_Max.getValue();
         if(param2 == 1)
         {
            mpUpNum = this.mp.getValue() + param1;
         }
         else if(param2 == 2)
         {
            param1 = _loc3_ * param1 / 100;
            mpUpNum = this.mp.getValue() + param1;
         }
         else
         {
            param1 = 0;
            mpUpNum = 0;
         }
         if(mpUpNum < _loc3_)
         {
            this.mp.setValue(mpUpNum);
         }
         else
         {
            this.mp.setValue(_loc3_);
         }
         NewMC.Open("回蓝效果",this,0,0,0,param1);
      }
      
      public function OpenYS() : *
      {
         if(SixOne_Interface.booltemp)
         {
            if(boolYS)
            {
               if(this.playerYS)
               {
                  this.playerYS.close();
               }
               this.playerYS = new YongShi61(this);
            }
         }
      }
      
      public function CloseYS() : *
      {
         if(this.playerYS)
         {
            this.playerYS.close();
         }
      }
      
      private function chibangFeiXing() : *
      {
         if(this.data.getEquipSlot().getEquipFromSlot(7))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 424)
            {
               ++this.feixingTime;
               if(this.gravity == 0 && this.feixingBool == false && this.jumping > 0)
               {
                  this.speedTemp.setValue(this.speedTemp.getValue() + 20);
                  this.feixingBool = true;
               }
               if(this.gravity > 0)
               {
                  this.rotation = 0;
                  this.speedTemp.setValue(0);
                  this.feixingBool = false;
               }
               if(this.feixingTime >= 3)
               {
                  if(this.gravity == 0 && this.speedTemp.getValue() > 0)
                  {
                     this.feixingTime = 0;
                     this.speedTemp.setValue(this.speedTemp.getValue() - 1);
                  }
               }
            }
         }
      }
      
      private function shizhuangPenShe() : *
      {
         if(this.data.getEquipSlot().getEquipFromSlot(6))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 423)
            {
               if(this.feixingBool3 == true && this.feixingBool2 == false)
               {
                  if(Main.gameNum.getValue() == 0)
                  {
                     return;
                  }
                  this.skin_W.addChild(new this.class_ps());
                  this.feixingBool2 = true;
               }
               if(this.feixingBool3 == false)
               {
                  for(i in Fly.All)
                  {
                     if((Fly.All[i] as Fly)._name == "高能喷射" && (Fly.All[i] as Fly).who == this)
                     {
                        (Fly.All[i] as Fly).life = 0;
                     }
                  }
                  this.feixingBool2 = false;
               }
            }
         }
      }
      
      public function nineBuffTime() : *
      {
         ++this.nbTime;
         if(this.data.buffNine[0] > 0)
         {
            if(this.nbTime % 27 == 0)
            {
               if(this.data.buffNine[9] > 0)
               {
                  --this.data.buffNine[9];
                  if(this.data.buffNine[9] == 0)
                  {
                     this.data.buffNine[8] = 7200;
                  }
                  if(this.data.reBorn > 0)
                  {
                     --this.data.reBorn;
                  }
               }
               if(this.data.buffNine[8] > 0)
               {
                  --this.data.buffNine[8];
                  if(this.data.buffNine[8] == 0)
                  {
                     this.data.buffNine[7] = 7200;
                  }
               }
               else if(this.data.buffNine[7] > 0)
               {
                  --this.data.buffNine[7];
                  if(this.data.buffNine[7] == 0)
                  {
                     this.data.buffNine[6] = 7200;
                  }
               }
               else if(this.data.buffNine[6] > 0)
               {
                  --this.data.buffNine[6];
                  if(this.data.buffNine[6] == 0)
                  {
                     this.data.buffNine[5] = 7200;
                  }
               }
               else if(this.data.buffNine[5] > 0)
               {
                  --this.data.buffNine[5];
                  if(this.data.buffNine[5] == 0)
                  {
                     this.data.buffNine[4] = 7200;
                  }
               }
               else if(this.data.buffNine[4] > 0)
               {
                  --this.data.buffNine[4];
                  if(this.data.buffNine[4] == 0)
                  {
                     this.data.buffNine[3] = 10800;
                  }
               }
               else if(this.data.buffNine[3] > 0)
               {
                  --this.data.buffNine[3];
                  if(this.data.buffNine[3] == 0)
                  {
                     this.data.buffNine[2] = 10800;
                  }
               }
               else if(this.data.buffNine[2] > 0)
               {
                  --this.data.buffNine[2];
                  if(this.data.buffNine[2] == 0)
                  {
                     this.data.buffNine[1] = 10800;
                  }
               }
               else if(this.data.buffNine[1] > 0)
               {
                  --this.data.buffNine[1];
                  if(this.data.buffNine[1] == 0)
                  {
                     this.data.buffNine[0] = 10800;
                  }
               }
               else if(this.data.buffNine[0] > 0)
               {
                  --this.data.buffNine[0];
               }
            }
         }
      }
   }
}

