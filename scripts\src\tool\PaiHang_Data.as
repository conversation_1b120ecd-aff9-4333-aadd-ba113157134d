package src.tool
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   
   public class PaiHang_Data
   {
      public static var now_pID:int;
      
      public static var now_pID_0:int;
      
      public static var now_pID_x:int;
      
      public static var showTime:String;
      
      public static var saveXX:Array;
      
      public static var newTime:int = 0;
      
      public static var inGameNum:Array = [-1,0,0,0,0,0,0,0,0,0];
      
      public static var sArr2:Array = new Array();
      
      public static var jiFenArr:Array = new Array();
      
      public static var daLu:int = 0;
      
      public static var paiHangArr:Array = new Array();
      
      public static var gameNum_x1_1:Array = [2212,2218,2220,2222,2224,2226,2228,2230,2232,2234];
      
      public static var gameNum_x2_1:Array = [2213,2219,2221,2223,2225,2227,2229,2231,2233,2235];
      
      public static var gameNum_x1_2:Array = [2214,2236,2238,2240,2242,2244,2246,2248];
      
      public static var gameNum_x2_2:Array = [2215,2237,2239,2241,2243,2245,2247,2249];
      
      public static var gameNum_x1_3:Array = [2210,2250,2252,2254,2256,2258,2260,2262,2264,2266,2268,2270,2272];
      
      public static var gameNum_x2_3:Array = [2211,2251,2253,2255,2257,2259,2261,2263,2265,2267,2269,2271,2273];
      
      public static var gameNum_x1_4:Array = [2216,2274,2276,2278,2280,2282,2284,2286,2288,2290,2292,2294,2296];
      
      public static var gameNum_x2_4:Array = [2217,2275,2277,2279,2281,2283,2285,2287,2289,2291,2293,2295,2297];
      
      public static var gameNum_x1_0:Array = [2208];
      
      public static var gameNum_x2_0:Array = [2209];
      
      public static var time:uint = 0;
      
      public static var sNum:VT = VT.createVT();
      
      public static var sNumX:int = 0;
      
      public static var myXml:XML = new XML();
      
      public static var dataArr:Array = [];
      
      public static var myXml2:XML = new XML();
      
      public static var AllData:Array = new Array();
      
      public function PaiHang_Data()
      {
         super();
      }
      
      public static function creatFactory() : *
      {
         var _loc1_:XML = null;
         var _loc2_:* = 0;
         myXml2 = XMLAsset.createXML(Data2.duiHuanData);
         for each(_loc1_ in myXml2.兑换神权利器)
         {
            _loc2_ = uint(_loc1_.序号);
            AllData[_loc2_] = new Array();
            AllData[_loc2_][0] = VT.createVT(Number(_loc1_.物品ID));
            AllData[_loc2_][1] = int(_loc1_.帧数);
            AllData[_loc2_][2] = VT.createVT(Number(_loc1_.概率));
         }
      }
      
      public static function TimeStart() : String
      {
         if(time > 0)
         {
            --time;
         }
         var _loc1_:int = int(time);
         var _loc2_:int = _loc1_ / 27;
         var _loc3_:int = _loc2_ / 3600;
         var _loc4_:int = (_loc2_ - _loc3_ * 3600) / 60;
         var _loc5_:int = (_loc2_ - _loc3_ * 3600) % 60;
         var _loc6_:int = (_loc1_ - (_loc3_ * 3600 + _loc4_ * 60 + _loc5_) * 27) * 37;
         var _loc7_:String = String(_loc4_);
         if(_loc4_ < 10)
         {
            _loc7_ = "0" + _loc4_;
         }
         var _loc8_:String = String(_loc5_);
         if(_loc5_ < 10)
         {
            _loc8_ = "0" + _loc5_;
         }
         var _loc9_:String = String(_loc6_).substr(0,2);
         var _loc10_:String = _loc7_ + ":" + _loc8_ + "." + _loc6_;
         showTime = _loc10_;
         return _loc10_;
      }
      
      public static function EnemyOver() : int
      {
         var _loc1_:int = 0;
         if(time <= 0)
         {
            return;
         }
         if(GameData.gameLV == 5)
         {
            _loc1_ = Math.random() * InitData.tNum_100.getValue();
            if(_loc1_ < 2)
            {
               ++sNumX;
               sNum.setValue(sNum.getValue() + InitData.tNum_300.getValue());
               NewMC.Open("文字提示",Main._stage,470,350,30,0,true,2,"获得300积分");
               TiaoShi.txtShow("积分球:300");
               return 300;
            }
            if(_loc1_ < 5)
            {
               ++sNumX;
               sNum.setValue(sNum.getValue() + InitData.tNum_200.getValue());
               NewMC.Open("文字提示",Main._stage,470,350,30,0,true,2,"获得200积分");
               TiaoShi.txtShow("积分球:200");
               return 200;
            }
            if(_loc1_ < 10)
            {
               ++sNumX;
               sNum.setValue(sNum.getValue() + InitData.tNum_100.getValue());
               NewMC.Open("文字提示",Main._stage,470,350,30,0,true,2,"获得100积分");
               TiaoShi.txtShow("积分球:100");
               return 100;
            }
         }
         return undefined;
      }
      
      public static function All_0() : *
      {
         if(Boolean(PaiHang_Data.dataArr[Main.gameNum.getValue()]) && Main.gameNum2.getValue() == 1)
         {
            time = (PaiHang_Data.dataArr[Main.gameNum.getValue()][1] as VT).getValue();
            sNum = VT.createVT();
            sNumX = 0;
         }
      }
      
      public static function InitDataX() : *
      {
         var _loc1_:XML = null;
         var _loc2_:* = 0;
         for each(_loc1_ in myXml.挑战关卡)
         {
            _loc2_ = uint(_loc1_.关卡);
            dataArr[_loc2_] = new Array();
            dataArr[_loc2_][0] = VT.createVT(Number(_loc1_.金币));
            dataArr[_loc2_][1] = VT.createVT(Number(_loc1_.时间));
         }
      }
      
      public static function InitSave() : *
      {
         var _loc2_:int = 0;
         if(inGameNum[0] < Main.serverTime.getValue())
         {
            TiaoShi.txtShow("InitSave() serverTime = " + Main.serverTime.getValue());
            inGameNum[0] = Main.serverTime.getValue();
            _loc2_ = 1;
            while(_loc2_ <= 62)
            {
               inGameNum[_loc2_] = 2;
               _loc2_++;
            }
         }
         var _loc1_:int = Main.serverTime.getValue() / 100;
         if(newTime != _loc1_)
         {
            TiaoShi.txtShow("重置挑战排行榜:" + newTime + "," + _loc1_);
            sArr2 = new Array();
            newTime = _loc1_;
         }
         if(!sArr2[0])
         {
            sArr2[0] = new Array();
         }
         _loc2_ = 1;
         while(_loc2_ <= 9)
         {
            if(!sArr2[0][_loc2_])
            {
               sArr2[0][_loc2_] = VT.createVT();
            }
            _loc2_++;
         }
         if(!sArr2[1])
         {
            sArr2[1] = new Array();
         }
         _loc2_ = 10;
         while(_loc2_ <= 16)
         {
            if(!sArr2[1][_loc2_])
            {
               sArr2[1][_loc2_] = VT.createVT();
            }
            _loc2_++;
         }
         if(!sArr2[2])
         {
            sArr2[2] = new Array();
         }
         _loc2_ = 51;
         while(_loc2_ <= 62)
         {
            if(!sArr2[2][_loc2_])
            {
               sArr2[2][_loc2_] = VT.createVT();
            }
            _loc2_++;
         }
         if(!sArr2[4])
         {
            sArr2[4] = new Array();
         }
         _loc2_ = 18;
         while(_loc2_ <= 29)
         {
            if(!sArr2[4][_loc2_])
            {
               sArr2[4][_loc2_] = VT.createVT();
            }
            _loc2_++;
         }
         _loc2_ = 0;
         while(_loc2_ < 5)
         {
            if(!jiFenArr[_loc2_])
            {
               jiFenArr[_loc2_] = VT.createVT();
            }
            _loc2_++;
         }
      }
      
      private static function SelDaLu() : int
      {
         var _loc1_:int = int(Main.gameNum.getValue());
         if(_loc1_ >= 1 && _loc1_ <= 9)
         {
            TiaoShi.txtShow("SelDaLu() 提交大陆1 ");
            return 0;
         }
         if(_loc1_ >= 10 && _loc1_ <= 16)
         {
            TiaoShi.txtShow("SelDaLu() 提交大陆2 ");
            return 1;
         }
         if(_loc1_ >= 51 && _loc1_ <= 62)
         {
            TiaoShi.txtShow("SelDaLu() 提交大陆3 ");
            return 2;
         }
         if(_loc1_ >= 18 && _loc1_ <= 29)
         {
            TiaoShi.txtShow("SelDaLu() 提交大陆4 ");
            return 4;
         }
         return 3;
      }
      
      public static function Show() : Array
      {
         var _loc2_:int = 0;
         var _loc1_:Array = new Array();
         TiaoShi.txtShow("Show~~~~~~~~1");
         var _loc3_:* = 0;
         TiaoShi.txtShow("Show~~~~~~~~2");
         if(Main.player_1.playerCW)
         {
            if(Main.player_1.playerCW.data.getPetEquip())
            {
               if(Main.player_1.playerCW.data.getPetEquip().getType() == 15)
               {
                  WinShow.txt_3 -= Main.player_1.playerCW.data.getPetEquip().getColor();
                  if(WinShow.txt_3 < 0)
                  {
                     WinShow.txt_3 = 0;
                  }
               }
               if(Main.player_1.playerCW.data.getPetEquip().getType() == 16)
               {
                  time += Main.player_1.playerCW.data.getPetEquip().getColor() * 27;
               }
            }
         }
         if(Boolean(Main.P1P2) && Boolean(Main.player_2.playerCW))
         {
            if(Main.player_2.playerCW.data.getPetEquip())
            {
               if(Main.player_2.playerCW.data.getPetEquip().getType() == 15)
               {
                  WinShow.txt_3 -= Main.player_2.playerCW.data.getPetEquip().getColor();
                  if(WinShow.txt_3 < 0)
                  {
                     WinShow.txt_3 = 0;
                  }
               }
               if(Main.player_2.playerCW.data.getPetEquip().getType() == 16)
               {
                  time += Main.player_2.playerCW.data.getPetEquip().getColor() * 27;
               }
            }
         }
         _loc2_ = time * 37;
         TiaoShi.txtShow("Show~~~~~~~~3");
         if(WinShow.txt_3 > 0 && WinShow.txt_3 < 50)
         {
            _loc3_ = (_loc2_ + sNum.getValue()) * WinShow.txt_3 / 100;
         }
         else if(WinShow.txt_3 > 50 && WinShow.txt_3 < 999999)
         {
            _loc3_ = (_loc2_ + sNum.getValue()) * 50 / 100;
         }
         TiaoShi.txtShow("Show~~~~~~~~4");
         TiaoShi.txtShow("剩余时间:" + time);
         TiaoShi.txtShow("时间积分:" + _loc2_);
         TiaoShi.txtShow("关卡积分:" + sNum.getValue());
         TiaoShi.txtShow("被击扣分:" + _loc3_);
         var _loc4_:uint = _loc2_ + sNum.getValue() - _loc3_;
         TiaoShi.txtShow("~~~~~~~~关卡积分:" + _loc4_ + "\n");
         if(sArr2[daLu][Main.gameNum.getValue()])
         {
            TiaoShi.txtShow("~~~~~~~~之前最高分:" + (sArr2[daLu][Main.gameNum.getValue()] as VT).getValue() + "\n");
         }
         daLu = SelDaLu();
         if(_loc4_ > (sArr2[daLu][Main.gameNum.getValue()] as VT).getValue())
         {
            _loc1_[8] = true;
            sArr2[daLu][Main.gameNum.getValue()] = VT.createVT(_loc4_);
         }
         else
         {
            _loc1_[8] = false;
         }
         TiJiaoFun(_loc4_);
         _loc1_[0] = TimeStart();
         _loc1_[1] = _loc2_;
         _loc1_[2] = sNumX;
         _loc1_[3] = sNum.getValue();
         _loc1_[4] = WinShow.txt_3;
         _loc1_[5] = _loc3_;
         _loc1_[6] = sArr2[daLu][Main.gameNum.getValue()].getValue();
         _loc1_[7] = _loc4_;
         return _loc1_;
      }
      
      private static function TiJiaoFun(param1:int) : *
      {
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc10_:int = 0;
         Sel_now_pID();
         var _loc2_:Array = new Array();
         var _loc3_:int = int(PaiHang_Data.now_pID);
         _loc2_[0] = new Object();
         _loc2_[0].rId = _loc3_;
         _loc2_[0].score = int(param1);
         _loc2_[0].extra = Main.player_1.paiHangShow;
         if(Main.tiaoShiYN)
         {
            _loc2_[0].score = int(param1 / 3);
         }
         var _loc4_:int = int(PaiHang_Data.now_pID_0);
         var _loc5_:Array = new Array();
         _loc7_ = 1;
         while(_loc7_ <= 100)
         {
            if(sArr2[daLu][_loc7_])
            {
               _loc6_ += sArr2[daLu][_loc7_].getValue();
            }
            _loc7_++;
         }
         _loc5_[0] = new Object();
         _loc5_[0].rId = _loc4_;
         _loc5_[0].score = int(_loc6_);
         _loc5_[0].extra = Main.player_1.paiHangShow;
         if(Main.tiaoShiYN)
         {
            _loc5_[0].score = int(_loc6_ / 3);
         }
         var _loc8_:int = int(PaiHang_Data.now_pID_x);
         var _loc9_:Array = new Array();
         var _loc11_:int = 0;
         while(_loc11_ <= 4)
         {
            if(_loc11_ != 3)
            {
               _loc7_ = 1;
               while(_loc7_ <= 100)
               {
                  if(sArr2[_loc11_][_loc7_])
                  {
                     _loc10_ += sArr2[_loc11_][_loc7_].getValue();
                  }
                  _loc7_++;
               }
            }
            _loc11_++;
         }
         _loc9_[0] = new Object();
         _loc9_[0].rId = _loc8_;
         _loc9_[0].score = int(_loc10_);
         _loc9_[0].extra = Main.player_1.paiHangShow;
         if(Main.tiaoShiYN)
         {
            _loc9_[0].score = int(_loc10_ / 3);
         }
         TiaoShi.txtShow("提交成绩:\n" + _loc3_ + "关卡积分" + _loc2_[0].score + "\n" + _loc4_ + "大陆积分" + _loc5_[0].score + "\n" + _loc8_ + "总积分" + _loc9_[0].score);
         Api_4399_All.SubmitScore(Main.saveNum,_loc2_);
         Api_4399_All.SubmitScore(Main.saveNum,_loc5_);
         Api_4399_All.SubmitScore(Main.saveNum,_loc9_);
         paiHangArr[_loc3_] = null;
         paiHangArr[_loc4_] = null;
         paiHangArr[_loc8_] = null;
      }
      
      private static function Sel_now_pID() : *
      {
         var _loc1_:int = 0;
         if(Main.P1P2)
         {
            _loc1_ = 2;
         }
         else
         {
            _loc1_ = 1;
         }
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:int = int(Main.gameNum.getValue());
         if(_loc5_ >= 1 && _loc5_ <= 9)
         {
            _loc2_ = 1;
            _loc3_ = _loc5_;
         }
         if(_loc5_ >= 10 && _loc5_ <= 16)
         {
            _loc2_ = 2;
            _loc3_ = _loc5_ - 9;
         }
         if(_loc5_ >= 51 && _loc5_ <= 62)
         {
            _loc2_ = 3;
            _loc3_ = _loc5_ - 50;
         }
         if(_loc5_ >= 18 && _loc5_ <= 29)
         {
            _loc2_ = 4;
            _loc3_ = _loc5_ - 17;
         }
         now_pID_x = PaiHang_Data["gameNum_x" + _loc1_ + "_0"][0];
         now_pID_0 = PaiHang_Data["gameNum_x" + _loc1_ + "_" + _loc2_][0];
         now_pID = PaiHang_Data["gameNum_x" + _loc1_ + "_" + _loc2_][_loc3_];
         TiaoShi.txtShow("Sel_now_pID:\ngameNum_x" + _loc1_ + "_" + _loc2_ + ", xID" + _loc3_);
      }
      
      public static function getJiFen(param1:int, param2:int, param3:int) : *
      {
         if(param1 <= 0 || param2 <= 0)
         {
            return;
         }
         if(param1 == 2)
         {
            param2 += 9;
         }
         if(param1 == 3)
         {
            param2 += 50;
         }
         if(param1 == 4)
         {
            param2 += 17;
         }
         param1--;
         sArr2[param1][param2] = VT.createVT(param3);
         TiaoShi.txtShow("查询排行榜分数 替换存档分数" + param1 + " ? " + param2 + " ? " + param3);
      }
      
      public static function DataAry1(param1:Array, param2:int) : *
      {
         var _loc3_:int = 0;
         if(!PaiHang_Data.paiHangArr[param2])
         {
            PaiHang_Data.paiHangArr[param2] = new Array();
         }
         for(_loc3_ in param1)
         {
            PaiHang_Data.paiHangArr[param2][_loc3_ + 1] = param1[_loc3_];
         }
      }
      
      public static function DataAry2(param1:Array, param2:int) : *
      {
         var _loc3_:int = 0;
         var _loc4_:Object = null;
         if(!PaiHang_Data.paiHangArr[param2])
         {
            PaiHang_Data.paiHangArr[param2] = new Array();
         }
         if(param1 != null && param1.length != 0)
         {
            for(_loc3_ in param1)
            {
               _loc4_ = param1[_loc3_];
               if(int(_loc4_.index) == Main.saveNum)
               {
                  PaiHang_Data.paiHangArr[param2][0] = _loc4_;
                  return;
               }
            }
         }
      }
   }
}

