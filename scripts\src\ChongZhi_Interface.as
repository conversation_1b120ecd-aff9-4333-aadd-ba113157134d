package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class ChongZhi_Interface extends MovieClip
   {
      public static var _this:ChongZhi_Interface;
      
      public static var HDpointNUM:VT = VT.createVT(-1);
      
      public static var openYn:Boolean = false;
      
      public static var lingQu:Boolean = false;
      
      public static var lingQu2:Boolean = false;
      
      public static var lingQu3:<PERSON>olean = false;
      
      public static var lingQu4:Boolean = false;
      
      public static var lingQu5:<PERSON>olean = false;
      
      public static var lingQu6:Boolean = false;
      
      public static var lingQu8:int = 0;
      
      public static var lingQu9:Boolean = false;
      
      public static var lingQu10:Boolean = false;
      
      public static var lingQu10Num:int = 0;
      
      public static var lingQu11:Boolean = false;
      
      public static var lingQu12:Boolean = false;
      
      public static var lingQu13:Boolean = false;
      
      public static var lingQu14:<PERSON>olean = false;
      
      public static var lingQu16:Boolean = false;
      
      public static var selYN:Boolean = false;
      
      public var skin:MovieClip;
      
      public function ChongZhi_Interface()
      {
         super();
         _this = this;
         var _loc1_:Class = NewLoad.chongZhiData.getClass("mc1") as Class;
         this.skin = new _loc1_();
         _this.XX_mc.addChild(this.skin);
         _this.XX_mc.x = 0;
         _this.XX_mc.y = 0;
         this.skin.gotoAndStop(2);
         this.skin.time_txt.text = "活动日期: 至2022年7月16日";
         _this.lingqu_btn.visible = false;
         _this.go_btn.visible = true;
         lingqu_btn.addEventListener(MouseEvent.CLICK,lingQuFun);
         addEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         Open();
      }
      
      public static function Open() : *
      {
         var _loc1_:ChongZhi_Interface = null;
         if(!_this)
         {
            _loc1_ = new ChongZhi_Interface();
         }
         _this.go_btn.addEventListener(MouseEvent.CLICK,OpenWEB);
         _this.close_btn.addEventListener(MouseEvent.CLICK,Close);
         _this.addEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         _this.y = 0;
         _this.x = 0;
         if(!selYN && !lingQu16)
         {
            Api_4399_All.GetTotalRecharged(16);
            selYN = true;
         }
         Main._stage.addChild(_this);
         Show();
      }
      
      public static function Close(param1:* = null) : *
      {
         var _loc2_:ChongZhi_Interface = null;
         if(!_this)
         {
            _loc2_ = new ChongZhi_Interface();
            Main._this.addChild(_loc2_);
         }
         _this.go_btn.removeEventListener(MouseEvent.CLICK,OpenWEB);
         _this.close_btn.removeEventListener(MouseEvent.CLICK,Close);
         _this.removeEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         _this.y = -5000;
         _this.x = -5000;
      }
      
      public static function onENTER_FRAME(param1:*) : *
      {
         if(!openYn && Main.gameNum.getValue() == 0)
         {
            openYn = true;
            _this.x = _this.y = 0;
         }
         if(Main.gameNum.getValue() != 0)
         {
            _this.x = _this.y = -5000;
         }
      }
      
      public static function lingQuFun(param1:* = null) : *
      {
         if(Main.player1.getBag().backOtherBagNum() >= 1)
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63291));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得\'驭炎龙\'宠物蛋一个, 已放入背包!");
            lingQu16 = true;
            Show();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,请整理背包!");
         }
      }
      
      public static function Show() : *
      {
         _this.lingqu_btn.visible = false;
         _this.go_btn.visible = true;
         _this.lingqu_btn.y = -5000;
         _this.lingqu_btn.x = -5000;
         if(HDpointNUM.getValue() > 299 && !lingQu16)
         {
            _this.lingqu_btn.visible = true;
            _this.lingqu_btn.x = 420;
            _this.lingqu_btn.y = 408;
            _this.go_btn.visible = false;
         }
      }
      
      public static function OpenWEB(param1:*) : *
      {
         Main.ChongZhi();
         _this.y = -5000;
         _this.x = -5000;
      }
   }
}

