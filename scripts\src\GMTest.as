package src
{
   import flash.display.MovieClip;
   import flash.events.KeyboardEvent;
   import flash.events.Event;
   
   public class GMTest extends MovieClip
   {
      public function GMTest()
      {
         super();
         addEventListener(Event.ADDED_TO_STAGE, onAddedToStage);
      }
      
      private function onAddedToStage(e:Event):void
      {
         removeEventListener(Event.ADDED_TO_STAGE, onAddedToStage);
         
         // 测试GM系统初始化
         trace("GMTest: 开始测试GM系统");
         
         // 手动初始化GM系统
         try {
            GM.xxj();
            trace("GMTest: GM系统初始化成功");
         } catch(error:Error) {
            trace("GMTest: GM系统初始化失败 - " + error.message);
         }
         
         // 添加键盘监听测试
         stage.addEventListener(KeyboardEvent.KEY_DOWN, onKeyDown);
         trace("GMTest: 键盘监听已添加，按 ` 键测试GM界面");
      }
      
      private function onKeyDown(e:KeyboardEvent):void
      {
         trace("GMTest: 按键检测 - keyCode: " + e.keyCode);
         
         if(e.keyCode == 192) { // ` 键
            trace("GMTest: 检测到 ` 键，尝试打开GM界面");
            
            try {
               if(!GM.cuteGMPanel) {
                  trace("GMTest: 创建新的CuteGMPanel");
                  GM.cuteGMPanel = new CuteGMPanel();
                  stage.addChild(GM.cuteGMPanel);
               } else {
                  trace("GMTest: CuteGMPanel已存在，切换显示状态");
               }
               
               GM.cuteGMPanel.toggle();
               trace("GMTest: GM界面操作完成");
            } catch(error:Error) {
               trace("GMTest: GM界面操作失败 - " + error.message);
            }
         }
      }
   }
}
