package src.npc
{
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   
   public class 药水商人界面 extends MovieClip
   {
      private static var only:药水商人界面;
      
      public var buy_btn:SimpleButton;
      
      public var close1:SimpleButton;
      
      public var close2:SimpleButton;
      
      public var temp_btn:SimpleButton;
      
      public var temp2_btn:SimpleButton;
      
      public var getObj_mc:MovieClip;
      
      public var song_btn:SimpleButton;
      
      public var close:SimpleButton;
      
      public var jieMian2:MovieClip;
      
      public var ling_btn:SimpleButton;
      
      public var quxiao_btn:SimpleButton;
      
      public var A_mc:MovieClip;
      
      public var numUP_btn:SimpleButton;
      
      public var numDOWN_btn:SimpleButton;
      
      public var jindutiao_mc:MovieClip;
      
      public var qinMiDu_txt:TextField;
      
      public function 药水商人界面()
      {
         super();
         this.buy_btn.addEventListener(MouseEvent.CLICK,this.BUY);
         this.close1.addEventListener(MouseEvent.CLICK,this.CloseXX);
         this.close2.addEventListener(MouseEvent.CLICK,this.CloseXX);
         this.temp_btn.addEventListener(MouseEvent.CLICK,this.getObj_mc_Open);
         this.temp2_btn.addEventListener(MouseEvent.CLICK,this.jieMian2_Open);
         this.getObj_mc.song_btn.addEventListener(MouseEvent.CLICK,this.GetNpc_Obj);
         this.getObj_mc.close.addEventListener(MouseEvent.CLICK,this.getObj_mc_Close);
         this.jieMian2.ling_btn.addEventListener(MouseEvent.CLICK,this.GetPlayer_Obj);
         this.jieMian2.quxiao_btn.addEventListener(MouseEvent.CLICK,this.jieMian2_Close);
         this.getObj_mc.A_mc.numUP_btn.addEventListener(MouseEvent.CLICK,this.SongNumUP);
         this.getObj_mc.A_mc.numDOWN_btn.addEventListener(MouseEvent.CLICK,this.SongNumDOWN);
      }
      
      public static function Open(param1:int = 0, param2:int = 0) : *
      {
         var _loc3_:Class = null;
         var _loc4_:Number = 0;
         var _loc5_:MovieClip = null;
         var _loc6_:int = 0;
         MusicBox.MusicPlay2("m2");
         if(!only)
         {
            _loc3_ = All_Npc.loadData.getClass("src.npc.药水商人界面") as Class;
            only = new _loc3_();
            _loc4_ = 1;
            while(_loc4_ < 8)
            {
               _loc5_ = new Shop_picNEW();
               _loc6_ = int(only.jieMian2["s1_" + _loc4_].getChildIndex(only.jieMian2["s1_" + _loc4_].pic_xx));
               _loc5_.x = only.jieMian2["s1_" + _loc4_].pic_xx.x;
               _loc5_.y = only.jieMian2["s1_" + _loc4_].pic_xx.y;
               _loc5_.name = "pic_xx";
               only.jieMian2["s1_" + _loc4_].removeChild(only.jieMian2["s1_" + _loc4_].pic_xx);
               only.jieMian2["s1_" + _loc4_].pic_xx = _loc5_;
               only.jieMian2["s1_" + _loc4_].addChild(_loc5_);
               only.jieMian2["s1_" + _loc4_].setChildIndex(_loc5_,_loc6_);
               _loc4_++;
            }
         }
         Main._stage.addChild(only);
         only.x = param1;
         only.y = param2;
         only.visible = true;
         only.ShowJinDu();
         only.xiaoLiDaiNum();
         only.LingQuYN();
      }
      
      public static function Close() : *
      {
         if(only)
         {
            only.x = 5000;
            only.y = 5000;
            only.visible = false;
         }
      }
      
      private function GetNpc_Obj(param1:*) : *
      {
         this.getObj_mc_Open();
         this.Use_xiaoLiDaiNum(int(this.getObj_mc.A_mc._txt.text));
         this.ShowJinDu();
         TaskData.isOk();
      }
      
      private function GetPlayer_Obj(param1:*) : *
      {
         All_Npc.NpcGetPlayerObj(3);
         this.LingQuYN();
         this.ShowJinDu();
      }
      
      private function BUY(param1:*) : *
      {
         SuppliesShopPanel.open();
         药水商人界面.Close();
      }
      
      private function CloseXX(param1:*) : *
      {
         Close();
      }
      
      private function ShowJinDu() : *
      {
         this.qinMiDu_txt.text = InitData.qinMiDu_Arr[3].getValue() + "/" + InitData.qinMiDu_Max_Arr[3].getValue();
         if(InitData.qinMiDu_Arr[3].getValue() <= InitData.qinMiDu_Max_Arr[3].getValue())
         {
            this.jindutiao_mc.XX_mc.scaleX = InitData.qinMiDu_Arr[3].getValue() / InitData.qinMiDu_Max_Arr[3].getValue();
         }
         else
         {
            this.jindutiao_mc.XX_mc.scaleX = 1;
         }
      }
      
      private function getObj_mc_Open(param1:* = null) : *
      {
         this.getObj_mc.x = 0;
         this.getObj_mc.y = 0;
         this.getObj_mc.visible = true;
         this.jieMian2_Close();
      }
      
      private function getObj_mc_Close(param1:* = null) : *
      {
         this.getObj_mc.x = 5000;
         this.getObj_mc.y = 5000;
         this.getObj_mc.visible = false;
      }
      
      private function jieMian2_Open(param1:* = null) : *
      {
         this.jieMian2.x = 242;
         this.jieMian2.y = 166;
         this.jieMian2.visible = true;
         this.getObj_mc_Close();
         this.ShowPicAndNum();
         if(InitData.qinMiDu_Arr[3].getValue() < InitData.qinMiDu_Max_Arr[3].getValue())
         {
            this.jieMian2.ling_btn.visible = false;
         }
         else if(this.LingQuYN())
         {
            this.jieMian2.ling_btn.visible = true;
         }
      }
      
      private function jieMian2_Close(param1:* = null) : *
      {
         this.jieMian2.x = 5000;
         this.jieMian2.y = 5000;
         this.jieMian2.visible = false;
      }
      
      private function SongNumUP(param1:* = null) : *
      {
         if(int(this.getObj_mc.A_mc._txt.text) < 20)
         {
            this.getObj_mc.A_mc._txt.text = int(this.getObj_mc.A_mc._txt.text) + 1;
         }
         else
         {
            this.getObj_mc.A_mc._txt.text = 1;
         }
      }
      
      private function SongNumDOWN(param1:* = null) : *
      {
         if(int(this.getObj_mc.A_mc._txt.text) > 1)
         {
            this.getObj_mc.A_mc._txt.text = int(this.getObj_mc.A_mc._txt.text) - 1;
         }
         else
         {
            this.getObj_mc.A_mc._txt.text = 20;
         }
      }
      
      private function xiaoLiDaiNum() : int
      {
         var _loc1_:int = int(Main.player1.getBag().getOtherobjNum(63102));
         var _loc2_:int = 0;
         if(Main.P1P2)
         {
            _loc2_ = int(Main.player2.getBag().getOtherobjNum(63102));
         }
         var _loc3_:int = _loc1_ + _loc2_;
         if(_loc3_ >= 1)
         {
            this.temp_btn.visible = true;
         }
         return _loc3_;
      }
      
      private function Use_xiaoLiDaiNum(param1:int) : *
      {
         var _loc5_:int = 0;
         var _loc2_:int = int(Main.player1.getBag().getOtherobjNum(63102));
         var _loc3_:int = 0;
         if(Main.P1P2)
         {
            _loc3_ = int(Main.player2.getBag().getOtherobjNum(63102));
         }
         var _loc4_:int = _loc2_ + _loc3_;
         if(_loc4_ >= param1)
         {
            if(_loc2_ <= param1)
            {
               Main.player1.getBag().getAndUseOtherobj(63102,_loc2_);
               if(Main.P1P2)
               {
                  Main.player2.getBag().getAndUseOtherobj(63102,param1 - _loc2_);
               }
            }
            else
            {
               Main.player1.getBag().getAndUseOtherobj(63102,param1);
            }
            _loc5_ = param1 * 3;
            InitData.qinMiDu_Arr[3].setValue(InitData.qinMiDu_Arr[3].getValue() + _loc5_);
         }
         else
         {
            NewMC.Open("文字提示",this,480,290,30,0,true,2,"小礼袋不足");
         }
      }
      
      private function LingQuYN() : Boolean
      {
         if(InitData.qinMiDu_Time[3].getValue() >= Main.serverTime.getValue())
         {
            this.jieMian2.ling_btn.visible = false;
            return false;
         }
         return true;
      }
      
      private function ShowPicAndNum() : *
      {
         var _loc1_:Array = All_Npc.get_AllObj_Pic_And_Num(3);
         var _loc2_:* = InitData.qinMiDu_num[3].getValue() % 7;
         if(_loc2_ == 0)
         {
            _loc2_ = 7;
         }
         var _loc3_:int = 1;
         while(_loc3_ < 8)
         {
            this.jieMian2["s1_" + _loc3_].pic_xx.gotoAndStop(_loc1_[_loc2_][1]);
            this.jieMian2["s1_" + _loc3_].howNum.text = "";
            if(_loc3_ < 2)
            {
               this.jieMian2["_txt" + _loc3_].text = "x " + _loc1_[_loc2_][0];
            }
            _loc2_++;
            if(_loc2_ > 7)
            {
               _loc2_ = 1;
            }
            _loc3_++;
         }
      }
   }
}

