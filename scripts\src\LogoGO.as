package src
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class <PERSON>goG<PERSON> extends MovieClip
   {
      public static var LogoGONum:int;
      
      internal var time:int = 0;
      
      public function LogoGO()
      {
         super();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
      }
      
      private function onENTER_FRAME(param1:*) : *
      {
         ++this.time;
         gotoAndStop(5);
         if(this.time > 35)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            removeEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
            gotoAndPlay(33);
            LogoGONum = 1;
         }
      }
      
      private function onMOUSE_MOVE(param1:*) : *
      {
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         removeEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         gotoAndPlay(6);
         LogoGONum = 2;
      }
   }
}

