package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   
   public class promptSlot
   {
      private static var _slotArr:Array = [];
      
      private static var arrNum:* = 2;
      
      public function promptSlot()
      {
         super();
      }
      
      public static function creatDeleteSlot() : promptSlot
      {
         var _loc1_:promptSlot = new promptSlot();
         creatSlotArr();
         return _loc1_;
      }
      
      private static function creatSlotArr() : void
      {
         var _loc1_:Number = 0;
         while(_loc1_ < arrNum)
         {
            _slotArr[_loc1_] = -1;
            _loc1_++;
         }
      }
      
      public function addToSlot(param1:*, param2:*) : void
      {
         _slotArr[param2] = param1;
      }
      
      public function delSlot(param1:Number) : void
      {
         if(_slotArr[param1] != 1)
         {
            _slotArr[param1] = -1;
         }
      }
      
      public function getProp(param1:uint) : *
      {
         return _slotArr[param1];
      }
      
      public function getPropGem(param1:uint) : Boolean
      {
         if(_slotArr[param1] is Gem)
         {
            return true;
         }
         return false;
      }
      
      public function getPropEquip(param1:uint) : Boolean
      {
         if(_slotArr[param1] is Equip)
         {
            return true;
         }
         return false;
      }
   }
}

