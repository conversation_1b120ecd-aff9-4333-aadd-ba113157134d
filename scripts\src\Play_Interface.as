package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.fanpaiPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.jiangliPanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.setTransfer.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import com.hotpoint.braveManIII.views.temaiPanel.*;
   import com.hotpoint.braveManIII.views.tuijianPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class Play_Interface extends MovieClip
   {
      public static var interfaceX:Play_Interface;
      
      public static var SaveLoadMC:MovieClip;
      
      public static var Xfile_YN:Boolean;
      
      public static var allTooltip:MovieClip;
      
      public static var BossLifeYN:Boolean;
      
      public static var bossIS:Enemy;
      
      public static var SixOneOK:Boolean = false;
      
      public static var templianJiNum:Array = [0,0];
      
      public static const data:Class = Play_Interface_data;
      
      public static var cz_Open1:Boolean = true;
      
      private static var LunTan10Num_now:int = 0;
      
      public static var buChangHeCeng2:Boolean = false;
      
      public static var huaZhi:uint = 1;
      
      private static var OpenYN:Boolean = false;
      
      private static var OpenYN2:Boolean = false;
      
      private static var showTimeNum:uint = 60;
      
      private static var showTimeNum2:uint = 60;
      
      public var myXml:XML;
      
      private var saveTime:int = 0;
      
      private var infoArr0:Array;
      
      private var infoArr1:Array;
      
      private var 滚动提示time:int = 135;
      
      public function Play_Interface()
      {
         var _loc1_:fpsBox = null;
         this.myXml = new XML();
         this.infoArr0 = ["找“技能导师-奥古斯汀”可以学习各系武器的技能噢","去挑战关卡时，记得检查一下药品是否足够。如果不够，可以找“妮蒂亚”购买。","收集到第一块女神碎片后，村庄的左边会开启通往女神像地图的入口。","修复第一块女神碎片后，会在“技能导师”背后开启通往“神秘之地”的入口","装备属性低的话，可以找“装备商人-道格拉斯”进行装备强化，宝石合成、宝石镶嵌来提升。","2个“强化石”可以合成1个“精良的强化石”，用“精良的强化石”强化装备得到的属性会更高","2个“低级属性石”可以合成一个“中级属性石”，“中级属性石”会有两条属性噢。","“强化石碎片”可以强化史诗级以下的技能石，2个“强化石碎片”可以合成一个1级强化石。","到达25级时，可以找“技能导师”进行转职，转职后可学习4个新的转职技能。","在背包里“鼠标双击”或“拖动”装备可以进行穿戴，右手武器需要拖动武器到右手武器栏才能替换。","2段跳可以在“技能导师”的“通用技能”里习得。","“菜单”栏里可以进行“快捷键设置”“返回城镇”等操作。","如果游戏过程中比较卡，可以通过降低画质提升流畅性。","在仓库中可以进行1P和2P的物品交换。"];
         this.infoArr1 = ["注意观察怪物的发招动作，并采取相应的行动进行躲避，可以让你更轻松的过关","按“上+攻击”和“下+攻击”可以发动各职业的2种特有攻击方式","多使用技能攻击怪物，可以更快更安全的过关","按2次“A(←)”或者“D(→)”可以跑动，跑动中按“攻击”可以进行“跑攻”","注入“技能石”后，打怪时能量条会开始蓄气，满了之后按“N”可以发动怪物技能，2P按“PageDown”发动"];
         super();
         load_mc.visible = false;
         SaveLoadMC = new 游戏初始化();
         addChild(SaveLoadMC);
         SaveLoadMC.visible = false;
         interfaceX = this;
         if(Main.tiaoShiYN)
         {
            _loc1_ = new fpsBox();
            _loc1_.x = 0;
            _loc1_.y = 0;
            addChild(_loc1_);
         }
         info_mc.mouseEnabled = false;
         info_mc.mouseChildren = false;
         this.初始化技能图标();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         _Move_mc2.CW_mc.addEventListener(MouseEvent.MOUSE_DOWN,this.CW面板);
         _Move_mc2.Xbag_mc.addEventListener(MouseEvent.CLICK,this.bag_OPEN);
         _Move_mc2.Xfile_mc.addEventListener(MouseEvent.CLICK,this.File_OPEN);
         _Move_mc2.Xshop_mc.addEventListener(MouseEvent.CLICK,this.shop_OPEN);
         file_mc.addEventListener(MouseEvent.MOUSE_MOVE,this.画质提示开启);
         file_mc.addEventListener(MouseEvent.MOUSE_OUT,this.画质提示关闭);
         file_mc.A.addEventListener(MouseEvent.CLICK,this.画质);
         file_mc.C.addEventListener(MouseEvent.CLICK,this.画质);
         file_mc.A.gotoAndStop(1);
         file_mc.C.gotoAndStop(2);
         file_mc.addEventListener(MouseEvent.ROLL_OUT,this.系统菜单关闭);
         file_mc.visible = false;
         file_mc.gameStop_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.游戏暂停);
         _Move_mc.renWuXX_mc.addEventListener(MouseEvent.MOUSE_DOWN,this.任务面板);
         zb_1.addEventListener(MouseEvent.MOUSE_OVER,this.showOpen);
         zb_1.addEventListener(MouseEvent.MOUSE_OUT,this.showClose);
         zb_2.addEventListener(MouseEvent.MOUSE_OVER,this.showOpen2);
         zb_2.addEventListener(MouseEvent.MOUSE_OUT,this.showClose);
         _Move_mc.card_mc.addEventListener(MouseEvent.MOUSE_DOWN,this.卡片面板);
         _Move_mc.xingLing_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.xingLing_Open);
         _Move_mc.vip_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.vip_Open);
         _Move_mc.QianDao_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.QianDaoFun);
         _Move_mc.jh_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.jh_Open);
         _Move_mc.paiHang_Btn.addEventListener(MouseEvent.MOUSE_DOWN,this.PaiHang_Open);
         _Move_mc.linghun_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.linghun_Open);
         _Move_mc.gongHui_btn.addEventListener(MouseEvent.MOUSE_DOWN,GongHui_Open);
         _Move_mc.yueka_btn.addEventListener(MouseEvent.MOUSE_DOWN,yueKaOpen);
         _Move_mc.jihua_mc.mouseEnabled = false;
         _Move_mc.jihua_mc.mouseChildren = false;
         TiShiShow(false);
         this.HuaZhiInit();
         _Move_mc.addEventListener(MouseEvent.MOUSE_MOVE,this.XOpen);
         _Move_mc.addEventListener(MouseEvent.MOUSE_OUT,this.XClose);
         _Move_mc2.addEventListener(MouseEvent.MOUSE_MOVE,this.XOpen2);
         _Move_mc2.addEventListener(MouseEvent.MOUSE_OUT,this.XClose2);
         zhuanZhi_btn.addEventListener(MouseEvent.CLICK,this.转职面板);
         Play1_mc["yuhuo1"].addEventListener(MouseEvent.MOUSE_OVER,this.tipOpen);
         Play1_mc["yuhuo1"].addEventListener(MouseEvent.MOUSE_OUT,this.tipClose);
         Play2_mc["yuhuo2"].addEventListener(MouseEvent.MOUSE_OVER,this.tipOpen);
         Play2_mc["yuhuo2"].addEventListener(MouseEvent.MOUSE_OUT,this.tipClose);
         yinCang_btn.addEventListener(MouseEvent.CLICK,this.YingCang1P2P);
         _Move_mc.tuijian_btn.addEventListener(MouseEvent.CLICK,this.TuiJianShop);
         _Move_mc.caiYao_btn.addEventListener(MouseEvent.CLICK,this.caiYao_Fun);
         jlNum_mc1.addEventListener(MouseEvent.MOUSE_MOVE,this.jlNum_mcShow1);
         jlNum_mc2.addEventListener(MouseEvent.MOUSE_MOVE,this.jlNum_mcShow2);
         jlNum_mc1.addEventListener(MouseEvent.MOUSE_OUT,this.jlNum_mc_out1);
         jlNum_mc2.addEventListener(MouseEvent.MOUSE_OUT,this.jlNum_mc_out2);
         libao10_btn.addEventListener(MouseEvent.CLICK,this.libao10);
         lunTan10_btn.addEventListener(MouseEvent.CLICK,this.lunTan10);
         huiTie8_btn.addEventListener(MouseEvent.CLICK,this.huiTie8);
         qingrenjie_btn.addEventListener(MouseEvent.CLICK,this.qingrenjie);
         if(!Main.P1P2)
         {
            goGame_btn.x = 836;
            goGame_btn.y = 470;
            lunTan10_btn.x = 750;
            lunTan10_btn.y = 470;
         }
         goGame_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.goGameXX);
      }
      
      private static function HuoDong51(param1:*) : *
      {
         FiveOne_Interface.Open();
      }
      
      private static function ChunJie_Open(param1:*) : *
      {
         ChunJiePanel.open();
      }
      
      private static function chongZhi_Open(param1:*) : *
      {
         ChongZhi_Interface.Open();
      }
      
      private static function chongZhi_Open1(param1:*) : *
      {
         ChongZhi_Interface2.Open(1);
      }
      
      private static function chongZhi_Open2(param1:*) : *
      {
         ChongZhi_Interface2.Open(2);
      }
      
      private static function chongZhi_Open3(param1:*) : *
      {
         ChongZhi_Interface2.Open(3);
      }
      
      private static function chongZhi_Open4(param1:*) : *
      {
         ChongZhi_Interface2.Open(4);
      }
      
      private static function chongZhi_Open5(param1:*) : *
      {
         ChongZhi_Interface2.Open(5);
      }
      
      private static function chongZhi_Open6(param1:*) : *
      {
         ChongZhi_Interface2.Open(6);
      }
      
      private static function onCongZhiXXXX(param1:*) : *
      {
         ChongZhi_Interface4.Open();
      }
      
      private static function teMai_Open(param1:*) : *
      {
         TeMaiPanel.open();
      }
      
      private static function GongHui_Open(param1:*) : *
      {
         if(Main.gameNum.getValue() == 0)
         {
            GongHui_Interface.Open();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"只能在主城中开启");
         }
      }
      
      private static function Nian_Open(param1:*) : *
      {
         if(Main.gameNum.getValue() == 0)
         {
            NewYear_Interface.Open();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"只能在主城中开启");
         }
      }
      
      private static function dengji_Open(param1:*) : *
      {
         JiangLiPanel.open();
      }
      
      public static function yueKaOpen(param1:*) : *
      {
         YueKa_Interface.Open();
      }
      
      public static function WeiBoFaFang() : *
      {
         interfaceX.ShowWeiBoFaFang();
      }
      
      public static function LunTan10_Show() : *
      {
         var _loc1_:int = 0;
         TiaoShi.txtShow("奖励" + Main.HuiTie8Num);
         if(Main.HuiTie8Num > 0)
         {
            _loc1_ = int(Main.HuiTie8Num);
            while(_loc1_ > 0)
            {
               if(Main.HuiTie8Arr[_loc1_] == 0)
               {
                  interfaceX.huiTie8_btn.gotoAndStop(_loc1_);
                  interfaceX.huiTie8_btn.visible = true;
                  LunTan10Num_now = _loc1_;
                  return;
               }
               _loc1_--;
            }
         }
         interfaceX.huiTie8_btn.visible = false;
      }
      
      private static function HuoDong10_Fun(param1:*) : *
      {
         if(Main.serverTime.getValue() < 20131219)
         {
            NewMC.Open("文字提示",Main._stage,470,400,30,0,true,2,"活动还未开始, 开放时间:12月19日~12月31日");
            return;
         }
         FanPaiPanel.open();
      }
      
      private static function zhuanPan_Fun(param1:*) : *
      {
         ZhuanPan.Open();
      }
      
      public static function Open(param1:int = 0, param2:int = 0) : *
      {
         var _loc3_:Date = null;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         if(!interfaceX)
         {
            interfaceX = new Play_Interface();
         }
         interfaceX.初始化技能图标();
         Main._this.addChild(interfaceX);
         interfaceX.xiari1.gotoAndStop(1);
         interfaceX.xiari2.gotoAndStop(1);
         if(Boolean(Main.player1.getEquipSlot().getEquipFromSlot(7)) && Main.player1.getEquipSlot().getEquipFromSlot(7).getFrame() == 447)
         {
            interfaceX.xiari1.visible = true;
            if(Main.player_1.jianCDnum < 11)
            {
               interfaceX.xiari1.gotoAndStop(Main.player_1.jianCDnum);
            }
            else
            {
               interfaceX.xiari1.gotoAndPlay(Main.player_1.jianCDnum);
            }
         }
         else
         {
            interfaceX.xiari1.visible = false;
         }
         if(Main.P1P2 && Main.player2.getEquipSlot().getEquipFromSlot(7) && Main.player2.getEquipSlot().getEquipFromSlot(7).getFrame() == 447)
         {
            interfaceX.xiari2.visible = true;
            if(Main.player_2.jianCDnum < 11)
            {
               interfaceX.xiari2.gotoAndStop(Main.player_2.jianCDnum);
            }
            else
            {
               interfaceX.xiari2.gotoAndPlay(Main.player_2.jianCDnum);
            }
         }
         else
         {
            interfaceX.xiari2.visible = false;
         }
         interfaceX.zhuanZhi_Fun();
         interfaceX.buCang_btn.visible = false;
         interfaceX.shanshuo_mc.mouseChildren = interfaceX.shanshuo_mc.mouseEnabled = interfaceX.shanshuo_mc.visible = false;
         interfaceX.libao10_btn.visible = false;
         interfaceX.dengji_btn.visible = false;
         interfaceX.huiTie8_btn.visible = false;
         interfaceX.shanshuo_mc.visible = false;
         interfaceX.lunTan10_btn.visible = false;
         interfaceX.dengji_btn.addEventListener(MouseEvent.MOUSE_DOWN,dengji_Open);
         interfaceX.chongZhi_btn.visible = false;
         interfaceX.ChongZhiFuLi_btn.visible = false;
         interfaceX.chongZhi_btn1.visible = false;
         interfaceX.chongZhi_btn2.visible = false;
         interfaceX.chongZhi_btn3.visible = false;
         interfaceX.chongZhi_btn4.visible = false;
         interfaceX.chongZhi_btn5.visible = false;
         interfaceX.chongZhi_btn6.visible = false;
         interfaceX.zhuanPan_btn.visible = false;
         interfaceX.teMai_btn.visible = false;
         interfaceX.Nian_btn.visible = false;
         interfaceX.qingrenjie_btn.visible = false;
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() != 1)
         {
            interfaceX.libao10_btn.visible = true;
            _loc3_ = new Date();
            _loc4_ = Number(_loc3_.getHours());
            _loc5_ = Number(_loc3_.getMinutes());
            _loc6_ = _loc4_ * 60 + _loc5_;
            _loc7_ = int(Main.serverTime.getValue());
            if(!buChangHeCeng2)
            {
               interfaceX.buCang_btn.visible = true;
               interfaceX.buCang_btn.addEventListener(MouseEvent.MOUSE_DOWN,buCang_Open);
            }
            interfaceX.xiari1.visible = false;
            interfaceX.xiari2.visible = false;
            interfaceX.x51.visible = false;
            interfaceX.duWu_btn.visible = true;
            interfaceX.gameBox_btn.visible = true;
            interfaceX.teMai_btn.visible = false;
            interfaceX.dengji_btn.visible = true;
            interfaceX.lunTan10_btn.visible = true;
            interfaceX.chunJie_btn.visible = false;
            if(Main.serverTime.getValue() <= ChunJiePanel.overTime)
            {
               interfaceX.chunJie_btn.visible = true;
               interfaceX.chunJie_btn.addEventListener(MouseEvent.MOUSE_DOWN,ChunJie_Open);
            }
            if(Main.serverTime.getValue() <= NewYear_Interface.overTime)
            {
               interfaceX.Nian_btn.visible = true;
               interfaceX.Nian_btn.addEventListener(MouseEvent.MOUSE_DOWN,Nian_Open);
            }
            if(Main.serverTime.getValue() <= Panel_XianHua.overTime)
            {
               interfaceX.qingrenjie_btn.visible = true;
            }
            if(Main.serverTime.getValue() <= 20230716)
            {
               interfaceX.zhuanPan_btn.visible = true;
               interfaceX.zhuanPan_btn.addEventListener(MouseEvent.MOUSE_DOWN,zhuanPan_Fun);
            }
            if(Main.serverTime.getValue() <= 20230716)
            {
               interfaceX.chongZhi_btn.visible = true;
               interfaceX.chongZhi_btn.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open);
            }
            if(Main.serverTime.getValue() <= 20230813)
            {
               interfaceX.teMai_btn.visible = true;
               interfaceX.teMai_btn.addEventListener(MouseEvent.MOUSE_DOWN,teMai_Open);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface4.overTime)
            {
               interfaceX.ChongZhiFuLi_btn.visible = true;
               interfaceX.ChongZhiFuLi_btn.addEventListener(MouseEvent.MOUSE_DOWN,onCongZhiXXXX);
            }
            if(Main.serverTime.getValue() <= 20230212)
            {
               interfaceX.chongZhi_btn1.visible = true;
               interfaceX.chongZhi_btn1.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open1);
            }
            if(Main.serverTime.getValue() <= 20230731)
            {
               interfaceX.chongZhi_btn2.visible = true;
               interfaceX.chongZhi_btn2.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open2);
            }
            if(Main.serverTime.getValue() <= 20230813)
            {
               interfaceX.chongZhi_btn3.visible = true;
               interfaceX.chongZhi_btn3.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open3);
            }
            if(Main.serverTime.getValue() <= 20230101)
            {
               interfaceX.chongZhi_btn4.visible = true;
               interfaceX.chongZhi_btn4.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open4);
            }
            if(Main.serverTime.getValue() <= 20230205)
            {
               interfaceX.chongZhi_btn5.visible = true;
               interfaceX.chongZhi_btn5.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open5);
            }
            if(Main.serverTime.getValue() <= 20230716)
            {
               interfaceX.chongZhi_btn6.visible = true;
               interfaceX.chongZhi_btn6.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open6);
            }
         }
         else
         {
            interfaceX.x51.visible = false;
            interfaceX.duWu_btn.visible = false;
            interfaceX.gameBox_btn.visible = false;
            interfaceX.buCang_btn.visible = false;
            interfaceX.Nian_btn.visible = false;
            interfaceX.teMai_btn.visible = false;
            interfaceX.zhuanPan_btn.visible = false;
            interfaceX.chunJie_btn.visible = false;
            interfaceX.chongZhi_btn.visible = false;
            interfaceX.chongZhi_btn1.visible = false;
            interfaceX.chongZhi_btn2.visible = false;
            interfaceX.chongZhi_btn3.visible = false;
            interfaceX.chongZhi_btn4.visible = false;
            interfaceX.chongZhi_btn5.visible = false;
            interfaceX.chongZhi_btn6.visible = false;
            interfaceX.ChongZhiFuLi_btn.visible = false;
            interfaceX.x51.removeEventListener(MouseEvent.MOUSE_DOWN,HuoDong51);
            interfaceX.ChongZhiFuLi_btn.removeEventListener(MouseEvent.MOUSE_DOWN,onCongZhiXXXX);
            interfaceX.teMai_btn.removeEventListener(MouseEvent.MOUSE_DOWN,teMai_Open);
            interfaceX.chongZhi_btn.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open);
            interfaceX.chongZhi_btn1.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open1);
            interfaceX.chongZhi_btn2.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open2);
            interfaceX.chongZhi_btn3.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open3);
            interfaceX.chongZhi_btn4.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open4);
            interfaceX.chongZhi_btn5.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open5);
            interfaceX.chongZhi_btn6.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open6);
            interfaceX.HuoDong10_btn.removeEventListener(MouseEvent.MOUSE_DOWN,HuoDong10_Fun);
            interfaceX.Nian_btn.removeEventListener(MouseEvent.MOUSE_DOWN,Nian_Open);
            interfaceX.chunJie_btn.removeEventListener(MouseEvent.MOUSE_DOWN,ChunJie_Open);
            interfaceX.zhuanPan_btn.removeEventListener(MouseEvent.MOUSE_DOWN,zhuanPan_Fun);
         }
         interfaceX.black_guoDu_mc.gotoAndPlay(14);
         interfaceX.x = param1;
         interfaceX.y = param2;
         interfaceX.visible = true;
         BossLifeYN = false;
         ShowXX();
         ShowXX2();
         if(cz_Open1 && NewLoad.chongZhiData && Boolean(NewLoad.chongZhiData.hasClass("mc1")))
         {
            cz_Open1 = false;
         }
      }
      
      private static function buCang_Open(param1:*) : *
      {
         if(Main.player1.getBag().backOtherBagNum() > 0)
         {
            buChangHeCeng2 = true;
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63377));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点赞礼包领取成功!");
            interfaceX.buCang_btn.visible = false;
            interfaceX.buCang_btn.y = -5000;
            interfaceX.buCang_btn.x = -5000;
            Main.Save();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包其他栏空间不足");
         }
      }
      
      public static function getBuChang() : *
      {
         if(SixOneOK)
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63342));
            SixOneOK = false;
            SaveLoadMC.visible = false;
            Main.Save();
         }
      }
      
      private static function LongZou(param1:*) : *
      {
         ChongZhi_Interface3.Open();
      }
      
      public static function Close() : *
      {
         if(!interfaceX)
         {
            interfaceX = new Play_Interface();
         }
         xx = 5000;
         interfaceX.y = 5000;
         interfaceX.visible = false;
         BossLifeYN = false;
      }
      
      private static function ShowXX() : *
      {
         interfaceX._Move_mc.x = 0;
         showTimeNum = 60;
      }
      
      private static function ShowXX2() : *
      {
         interfaceX._Move_mc2.y = 503;
         showTimeNum2 = 60;
      }
      
      public static function WhoIsBoss(param1:Enemy) : *
      {
         bossIS = param1;
      }
      
      public static function jihuaTiShi(param1:Boolean = true) : *
      {
         if(!interfaceX)
         {
            return;
         }
         if(param1)
         {
            interfaceX._Move_mc.jihua_mc.visible = true;
         }
         else
         {
            interfaceX._Move_mc.jihua_mc.visible = false;
         }
      }
      
      public static function TiShiShow(param1:Boolean = true, param2:uint = 0) : *
      {
         var _loc3_:Class = null;
         var _loc4_:MovieClip = null;
         if(!interfaceX)
         {
            return;
         }
         if(param1)
         {
            interfaceX._Move_mc.RenWuTiShi_mc.visible = true;
            TaskPanel.TiShi_wanCeng();
         }
         else
         {
            interfaceX._Move_mc.RenWuTiShi_mc.visible = false;
            if(param2 == 110097)
            {
               TiaoShi.txtShow("过场.............");
               _loc3_ = SelMap.loadData.getClass("src.tool.XX_MC") as Class;
               _loc4_ = new _loc3_();
               Main._stage.addChild(_loc4_);
            }
         }
      }
      
      private function goGameXX(param1:*) : *
      {
         if(Main.gameNum2.getValue() == 4)
         {
            SelMap.Open(0,0,3,3);
         }
         else
         {
            SelMap.Open();
         }
      }
      
      private function DuWu(param1:*) : *
      {
         DuWu_Interface.Open();
      }
      
      private function gameBox(param1:*) : *
      {
         var _loc2_:URLRequest = new URLRequest("http://news.4399.com/gonglue/ysdxy/gl/471540.html");
         navigateToURL(_loc2_,"_blank");
      }
      
      private function libao10(param1:*) : *
      {
         var _loc2_:URLRequest = new URLRequest("http://my.4399.com/jifen/yx-ysdxy");
         navigateToURL(_loc2_,"_blank");
      }
      
      private function lunTan10(param1:*) : *
      {
         var _loc2_:URLRequest = new URLRequest("http://my.4399.com/forums-mtag-tagid-81127.html");
         navigateToURL(_loc2_,"_blank");
      }
      
      private function jlNum_mcShow1(param1:*) : *
      {
         if(Boolean(Main.player_1.playerJL) && Main.player1.playerJL_Data.getSKILL3() > 0)
         {
            info_mc2._txt.text = SkillFactory.getSkillById(Main.player1.playerJL_Data.getSKILL3()).getIntroduction();
            info_mc2.visible = true;
            info_mc2.x = mouseX + 5;
            info_mc2.y = mouseY + 5;
         }
         else
         {
            info_mc2._txt.text = "召唤精灵无主动圣物或未召唤精灵";
            info_mc2.visible = true;
            info_mc2.x = mouseX + 5;
            info_mc2.y = mouseY + 5;
         }
      }
      
      private function jlNum_mcShow2(param1:*) : *
      {
         if(Boolean(Main.player_2.playerJL) && Main.player2.playerJL_Data.getSKILL3() > 0)
         {
            info_mc2._txt.text = SkillFactory.getSkillById(Main.player2.playerJL_Data.getSKILL3()).getIntroduction();
            info_mc2.visible = true;
            info_mc2.x = mouseX + 5;
            info_mc2.y = mouseY + 5;
         }
         else
         {
            info_mc2._txt.text = "召唤精灵无主动圣物或未召唤精灵";
            info_mc2.visible = true;
            info_mc2.x = mouseX + 5;
            info_mc2.y = mouseY + 5;
         }
      }
      
      private function jlNum_mc_out1(param1:*) : *
      {
         info_mc2.visible = false;
      }
      
      private function jlNum_mc_out2(param1:*) : *
      {
         info_mc2.visible = false;
      }
      
      private function reXue1(param1:*) : *
      {
      }
      
      private function reXue2(param1:*) : *
      {
      }
      
      private function PaiHang_Open(param1:*) : *
      {
         TiaoZhanPaiHang_Interface.Open();
      }
      
      private function linghun_Open(param1:*) : *
      {
         if(Main.LuoPanArr[0] == 1 && Main.LuoPanArr[1] == 1 && Main.LuoPanArr[2] == 1 && Main.LuoPanArr[3] == 1)
         {
            LingHunShi_Interface.Open();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"开启时空之门后才可使用");
         }
      }
      
      private function ShowWeiBoFaFang() : *
      {
         var _loc1_:XML = null;
         var _loc2_:String = null;
         var _loc3_:* = 0;
         var _loc4_:String = null;
         var _loc5_:String = null;
         if(TeShuHuoDong.TeShuHuoDongArr[19])
         {
            weiBo_btn.x = weiBo_btn.y = -5000;
         }
         else
         {
            this.myXml = XMLAsset.createXML(Play_Interface.data);
            for each(_loc1_ in this.myXml.奖励发放)
            {
               _loc2_ = String(_loc1_.用户账号);
               _loc3_ = uint(_loc1_.存档序号);
               _loc4_ = Main.logName.toLocaleLowerCase();
               _loc5_ = _loc2_.toLocaleLowerCase();
               if(_loc4_ == _loc5_ && Main.saveNum + 1 == _loc3_)
               {
                  weiBo_btn.x = 470;
                  weiBo_btn.y = 250;
                  weiBo_btn.addEventListener(MouseEvent.CLICK,this.WeiBoFaFang);
               }
            }
         }
      }
      
      private function WeiBoFaFang(param1:*) : *
      {
         var _loc2_:int = 60000 + InitData.BuyNum_0.getValue();
         if(TeShuHuoDong.TeShuHuoDongArr[19])
         {
            NewMC.Open("文字提示",this,470,420,30,0,true,2,"已经领取过");
         }
         else if(Main.player1.getBag().backOtherBagNum() < 2)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他类背包空间不足!");
         }
         else
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(_loc2_ + 3251));
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(_loc2_ + 3245));
            ++TeShuHuoDong.TeShuHuoDongArr[19];
            NewMC.Open("文字提示",this,470,420,90,0,true,2,"微博活动奖励领取成功!");
            weiBo_btn.x = weiBo_btn.y = -5000;
            weiBo_btn.removeEventListener(MouseEvent.CLICK,this.WeiBoFaFang);
         }
      }
      
      public function qingrenjie(param1:*) : *
      {
         Panel_XianHua.Open();
      }
      
      public function huiTie8(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         if(LunTan10Num_now == 1)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'击杀点礼包\' 成功");
               Main.HuiTie8Arr[1] = 1;
            }
         }
         else if(LunTan10Num_now == 2)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               _loc2_ = 0;
               while(_loc2_ < 5)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  _loc2_++;
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'封印的星灵财宝\' 成功");
               Main.HuiTie8Arr[2] = 1;
            }
         }
         else if(LunTan10Num_now == 3)
         {
            if(Main.player1.getBag().backOtherBagNum() < 5)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               _loc3_ = 0;
               while(_loc3_ < 5)
               {
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  _loc3_++;
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'复活药\' 成功");
               Main.HuiTie8Arr[3] = 1;
            }
         }
         else if(LunTan10Num_now == 4)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'星灵礼包\' 成功");
               Main.HuiTie8Arr[4] = 1;
            }
         }
         else if(LunTan10Num_now == 5)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               _loc3_ = 0;
               while(_loc3_ < 3)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63368));
                  _loc3_++;
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'轮回炼狱入场卷\' 成功");
               Main.HuiTie8Arr[5] = 1;
            }
         }
         else if(LunTan10Num_now == 6)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               _loc3_ = 0;
               while(_loc3_ < 15)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  _loc3_++;
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'洗练券\' 成功");
               Main.HuiTie8Arr[6] = 1;
            }
         }
         else if(LunTan10Num_now == 7)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               _loc4_ = 0;
               while(_loc4_ < 3)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
                  _loc4_++;
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'卓越4级强化石\' 成功");
               Main.HuiTie8Arr[7] = 1;
            }
         }
         else if(LunTan10Num_now == 8)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63331));
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'幽冥守卫宠物蛋\' 成功");
               Main.HuiTie8Arr[8] = 1;
            }
         }
         LunTan10_Show();
      }
      
      private function tipOpen(param1:*) : *
      {
         yhcs_txt.visible = true;
         yhcs_txt.x = mouseX + 5;
         yhcs_txt.y = mouseY + 5;
      }
      
      private function tipClose(param1:*) : *
      {
         yhcs_txt.visible = false;
      }
      
      private function QianDaoFun(param1:*) : *
      {
         QianDao.Open();
      }
      
      private function xingLing_Open(param1:*) : *
      {
         XingLing_Interface.Open();
      }
      
      private function vip_Open(param1:*) : *
      {
         Vip_Interface.Open();
      }
      
      private function jh_Open(param1:*) : *
      {
         JiHua_Interface.Open();
      }
      
      private function zbP1P2() : *
      {
         zb_1.visible = false;
         zb_2.visible = false;
         if(Main.Map0_YN2)
         {
            if(Main.player_1.visible)
            {
               zb_1.visible = true;
            }
            if(Boolean(Main.P1P2) && Boolean(Main.player_2.visible))
            {
               zb_2.visible = true;
            }
            if(Main.water.getValue() != 1)
            {
               zb_1.gotoAndStop(1);
               zb_2.gotoAndStop(1);
            }
            else
            {
               zb_1.gotoAndStop(2);
               zb_2.gotoAndStop(2);
            }
         }
      }
      
      private function showOpen(param1:*) : *
      {
         var _loc4_:int = 0;
         var _loc5_:SuitToolShow = null;
         var _loc6_:ItemsTooltip = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc2_:Number = 0;
         var _loc3_:int = 0;
         allTooltip = new MovieClip();
         addChild(allTooltip);
         allTooltip.x = mouseX;
         allTooltip.y = mouseY;
         if(Main.water.getValue() != 1)
         {
            _loc4_ = 0;
            while(_loc4_ < 4)
            {
               _loc5_ = new SuitToolShow();
               _loc6_ = new ItemsTooltip();
               _loc7_ = _loc4_;
               if(_loc7_ >= 2)
               {
                  _loc7_++;
               }
               if(Main.player1.getEquipSlot().getEquipFromSlot(_loc7_) != null)
               {
                  _loc2_ += Main.player1.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId();
                  _loc6_.equipTooltipSP(Main.player1.getEquipSlot().getEquipFromSlot(_loc7_),1);
                  allTooltip.addChild(_loc6_);
                  _loc6_.x = _loc3_ * 188;
                  _loc3_++;
               }
               if(_loc3_ == 4 && _loc2_ > 0)
               {
                  if(_loc2_ / 4 == Main.player1.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId())
                  {
                     _loc5_.suitTooltipShow(Main.player1.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId());
                     allTooltip.addChild(_loc5_);
                     _loc5_.x = _loc3_ * 188;
                  }
               }
               _loc4_++;
            }
         }
         else
         {
            _loc8_ = 8;
            while(_loc8_ < 12)
            {
               _loc5_ = new SuitToolShow();
               _loc6_ = new ItemsTooltip();
               _loc7_ = _loc8_;
               if(_loc7_ >= 10)
               {
                  _loc7_++;
               }
               if(Main.player1.getEquipSlot().getEquipFromSlot(_loc7_) != null)
               {
                  _loc2_ += Main.player1.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId();
                  _loc6_.equipTooltipSP(Main.player1.getEquipSlot().getEquipFromSlot(_loc7_),1);
                  allTooltip.addChild(_loc6_);
                  _loc6_.x = _loc3_ * 188;
                  _loc3_++;
               }
               if(_loc3_ == 4 && _loc2_ > 0)
               {
                  if(_loc2_ / 4 == Main.player1.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId())
                  {
                     _loc5_.suitTooltipShow(Main.player1.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId());
                     allTooltip.addChild(_loc5_);
                     _loc5_.x = _loc3_ * 188;
                  }
               }
               _loc8_++;
            }
         }
         allTooltip.visible = true;
      }
      
      private function showClose(param1:*) : *
      {
         allTooltip.visible = false;
      }
      
      private function showOpen2(param1:*) : *
      {
         var _loc4_:int = 0;
         var _loc5_:SuitToolShow = null;
         var _loc6_:ItemsTooltip = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc2_:Number = 0;
         var _loc3_:int = 1;
         allTooltip = new MovieClip();
         addChild(allTooltip);
         allTooltip.x = mouseX;
         allTooltip.y = mouseY;
         if(Main.water.getValue() != 1)
         {
            _loc4_ = 0;
            while(_loc4_ < 4)
            {
               _loc5_ = new SuitToolShow();
               _loc6_ = new ItemsTooltip();
               _loc7_ = _loc4_;
               if(_loc7_ >= 2)
               {
                  _loc7_++;
               }
               if(Main.player2.getEquipSlot().getEquipFromSlot(_loc7_) != null)
               {
                  _loc2_ += Main.player2.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId();
                  _loc6_.equipTooltipSP(Main.player2.getEquipSlot().getEquipFromSlot(_loc7_),1);
                  allTooltip.addChild(_loc6_);
                  _loc6_.x -= _loc3_ * 188;
                  _loc3_++;
               }
               if(_loc3_ == 5 && _loc2_ > 0)
               {
                  if(_loc2_ / 4 == Main.player2.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId())
                  {
                     _loc5_.suitTooltipShow(Main.player2.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId());
                     allTooltip.addChild(_loc5_);
                     _loc5_.x -= _loc3_ * 188;
                  }
               }
               _loc4_++;
            }
         }
         else
         {
            _loc8_ = 8;
            while(_loc8_ < 12)
            {
               _loc5_ = new SuitToolShow();
               _loc6_ = new ItemsTooltip();
               _loc7_ = _loc8_;
               if(_loc7_ >= 10)
               {
                  _loc7_++;
               }
               if(Main.player2.getEquipSlot().getEquipFromSlot(_loc7_) != null)
               {
                  _loc2_ += Main.player2.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId();
                  _loc6_.equipTooltipSP(Main.player2.getEquipSlot().getEquipFromSlot(_loc7_),1);
                  allTooltip.addChild(_loc6_);
                  _loc6_.x -= _loc3_ * 188;
                  _loc3_++;
               }
               if(_loc3_ == 5 && _loc2_ > 0)
               {
                  if(_loc2_ / 4 == Main.player2.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId())
                  {
                     _loc5_.suitTooltipShow(Main.player2.getEquipSlot().getEquipFromSlot(_loc7_).getSuitId());
                     allTooltip.addChild(_loc5_);
                     _loc5_.x -= _loc3_ * 188;
                  }
               }
               _loc8_++;
            }
         }
         allTooltip.visible = true;
      }
      
      public function HuaZhiInit() : *
      {
         file_mc.A.gotoAndStop(2);
         file_mc.C.gotoAndStop(2);
         if(huaZhi == 1)
         {
            Main._stage.quality = StageQuality.HIGH;
            file_mc.A.gotoAndStop(1);
         }
         else if(huaZhi == 2)
         {
            Main._stage.quality = StageQuality.LOW;
            file_mc.C.gotoAndStop(1);
         }
      }
      
      private function 卡片面板(param1:*) : *
      {
         CardPanel.open();
      }
      
      private function 游戏暂停(param1:*) : *
      {
         var _loc2_:GameStop = new GameStop();
      }
      
      private function onENTER_FRAME(param1:*) : *
      {
         this.Boss血条();
         this.玩家状态();
         this.连击计数();
         this.Cd计数();
         this.怪物技能计数();
         this.滚动提示();
         this.ShopObjTime_Show();
         this.Pk_mc();
         this.Go_mc();
         this.zbP1P2();
         this.zhuanZhi_Fun();
         if(showTimeNum > 0 && !Play_Interface.OpenYN && Main.gameNum.getValue() != 0)
         {
            --showTimeNum;
            if(interfaceX._Move_mc.x > -60)
            {
               interfaceX._Move_mc.x -= 2;
            }
         }
         if(showTimeNum2 > 0 && !Play_Interface.OpenYN2 && Main.gameNum.getValue() != 0)
         {
            --showTimeNum2;
            if(interfaceX._Move_mc2.y <= 564)
            {
               interfaceX._Move_mc2.y += 2;
            }
         }
         if(interfaceX._Move_mc.x <= -55)
         {
            interfaceX._Move_mc.vip_btn.visible = interfaceX._Move_mc.QianDao_btn.visible = interfaceX._Move_mc.paiHang_Btn.visible = interfaceX._Move_mc.tuijian_btn.visible = interfaceX._Move_mc.caiYao_btn.visible = interfaceX._Move_mc.linghun_btn.visible = interfaceX._Move_mc.gongHui_btn.visible = interfaceX._Move_mc.jihua_mc.visible = interfaceX._Move_mc.yueka_btn.visible = false;
         }
         else
         {
            interfaceX._Move_mc.vip_btn.visible = interfaceX._Move_mc.QianDao_btn.visible = interfaceX._Move_mc.paiHang_Btn.visible = interfaceX._Move_mc.tuijian_btn.visible = interfaceX._Move_mc.caiYao_btn.visible = interfaceX._Move_mc.linghun_btn.visible = interfaceX._Move_mc.gongHui_btn.visible = interfaceX._Move_mc.jihua_mc.visible = interfaceX._Move_mc.yueka_btn.visible = true;
         }
         this.NewBag();
         if(Boolean(Main.P1P2) && Main.gameNum.getValue() == 0)
         {
            yinCang_btn.visible = true;
         }
         else
         {
            yinCang_btn.visible = false;
         }
         if(Main.gameNum.getValue() == 0)
         {
            goGame_btn.visible = true;
         }
         else
         {
            goGame_btn.visible = false;
         }
      }
      
      private function YingCang1P2P(param1:*) : *
      {
         YinCang.Open();
      }
      
      private function TuiJianShop(param1:*) : *
      {
         TuiJianPanel.open();
      }
      
      private function caiYao_Fun(param1:*) : *
      {
         CaiYaoPanel.open();
      }
      
      private function QuanSouInfo() : *
      {
         if(Main.newPlay == 3)
         {
            openBag_mc.x = 380;
            openBag_mc.y = 465;
         }
         else
         {
            openBag_mc.y = -5000;
            openBag_mc.x = -5000;
         }
      }
      
      private function NewBag() : *
      {
         if(Main.newPlay == 3)
         {
            openBag_mc.x = 380;
            openBag_mc.y = 465;
         }
         else
         {
            openBag_mc.y = -5000;
            openBag_mc.x = -5000;
         }
      }
      
      private function XOpen(param1:*) : *
      {
         OpenYN = true;
         Play_Interface.ShowXX();
      }
      
      private function XClose(param1:*) : *
      {
         OpenYN = false;
      }
      
      private function XOpen2(param1:*) : *
      {
         OpenYN2 = true;
         Play_Interface.ShowXX2();
      }
      
      private function XClose2(param1:*) : *
      {
         OpenYN2 = false;
      }
      
      private function Go_mc() : *
      {
         if(!Main.world.stopYn && Main.gameNum.getValue() > 50 && Main.gameNum.getValue() < 81)
         {
            gogogo_mc.visible = true;
         }
         else
         {
            gogogo_mc.visible = false;
         }
      }
      
      private function Pk_mc() : *
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(PK_UI.PK_ing)
         {
            _PK_time_mc.visible = true;
            if(uint(PK_UI.Pk_timeNum / 27 % 60) < 10)
            {
               _PK_time_mc._time_txt.text = uint(PK_UI.Pk_timeNum / 27 / 60) + ":0" + uint(PK_UI.Pk_timeNum / 27 % 60);
            }
            else
            {
               _PK_time_mc._time_txt.text = uint(PK_UI.Pk_timeNum / 27 / 60) + ":" + uint(PK_UI.Pk_timeNum / 27 % 60);
            }
            _loc1_ = PK_UI.killNum70up.getValue() + PK_UI.killNum70down.getValue();
            _PK_time_mc._kill_txt.text = _loc1_;
            if(PK_UI.playerTime == 0)
            {
               _PK_time_mc.time3_mc.gotoAndStop("x0");
            }
            else
            {
               _loc2_ = (PK_UI.playerTime + 26) / 27;
               _PK_time_mc.time3_mc.gotoAndStop("x" + _loc2_);
            }
         }
         else
         {
            _PK_time_mc.visible = false;
         }
      }
      
      private function Boss血条() : *
      {
         if(Boolean(bossIS) && BossLifeYN)
         {
            BossLife_mc.visible = true;
            BossLife_mc.touXiang.gotoAndStop("a" + bossIS.id);
            if(bossIS.life.getValue() > 0)
            {
               BossLife_mc.life_mc.Start(bossIS);
            }
         }
         else
         {
            BossLife_mc.visible = false;
            BossLife_mc.life_mc.Stop();
         }
      }
      
      private function 怪物技能计数() : *
      {
         if(Boolean(Main.player1) && Boolean(Main.player_1))
         {
            if(this.Play1_mc.guaiWu.currentFrame != Main.player_1.energySlot.getEnergyPer(Main.player_1))
            {
               this.Play1_mc.guaiWu.gotoAndStop(Main.player_1.energySlot.getEnergyPer(Main.player_1));
            }
         }
         if(Main.P1P2 && Main.player2 && Boolean(Main.player_2))
         {
            if(this.Play2_mc.guaiWu.currentFrame != Main.player_2.energySlot.getEnergyPer(Main.player_2))
            {
               this.Play2_mc.guaiWu.gotoAndStop(Main.player_2.energySlot.getEnergyPer(Main.player_2));
            }
         }
      }
      
      private function 玩家状态() : *
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:int = 0;
         var _loc12_:int = 0;
         var _loc13_:int = 0;
         var _loc14_:int = 0;
         Play2_mc.head_mc.visible = false;
         if(Main.player_1)
         {
            _loc1_ = Math.round(Main.player_1.hp.getValue());
            _loc2_ = Math.round(Main.player_1.use_hp_Max.getValue());
            _loc3_ = int(Main.player_1.mp.getValue());
            _loc4_ = int(Main.player_1.use_mp_Max.getValue());
            _loc5_ = int(Main.player_1.data.getEXP());
            _loc6_ = int(Main.player_1.nextExp.getValue());
            _loc7_ = 100 - _loc1_ / _loc2_ * 100;
            _loc8_ = 100 - _loc3_ / _loc4_ * 100;
            _loc9_ = int(Main.player_1.data.getLevel());
            _loc10_ = Main.player_1.data.getEXP() / Main.player_1.nextExp.getValue() * 100;
            _loc11_ = int(Main.player_1.headFrame);
            if(_loc3_ == 1)
            {
               _loc3_ = 0;
            }
            Play1_mc.hp_txt.text = _loc1_ + "/" + _loc2_;
            Play1_mc.mp_txt.text = _loc3_ + "/" + _loc4_;
            Play1_mc.exp_txt.text = _loc5_ + "/" + _loc6_;
            Play1_mc.hp_mc.load_mc.scaleX = 1 - _loc7_ / 100;
            Play1_mc.mp_mc.load_mc.scaleX = 1 - _loc8_ / 100;
            this.showLV(_loc9_,1);
            Play1_mc.exp_mc.gotoAndStop(_loc10_);
            Play1_mc.head_mc.gotoAndStop(_loc11_);
            if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() != 1)
            {
               this.showShanShuo();
            }
            if(Main.player_1.zhongqiuState > 0)
            {
               Play1_mc["zhongqiu1"].visible = true;
            }
            else
            {
               Play1_mc["zhongqiu1"].visible = false;
            }
            if(Main.player_1.jianrenState > 0)
            {
               Play1_mc["jianren1"].visible = true;
            }
            else
            {
               Play1_mc["jianren1"].visible = false;
            }
            if(Main.player_1.shengmingState > 0)
            {
               Play1_mc["shengming1"].visible = true;
            }
            else
            {
               Play1_mc["shengming1"].visible = false;
            }
            if(Main.player1.buffNine[0] > 0)
            {
               Play1_mc["yuhuo1"].visible = true;
               this.showYHCS1();
            }
            else
            {
               Play1_mc["yuhuo1"].visible = false;
            }
            _loc12_ = 211;
            _loc13_ = 29;
            _loc14_ = 0;
            if(Play1_mc["zhongqiu1"].visible == true)
            {
               Play1_mc["zhongqiu1"].x = _loc12_ + _loc13_ * _loc14_;
               _loc14_++;
            }
            if(Play1_mc["jianren1"].visible == true)
            {
               Play1_mc["jianren1"].x = _loc12_ + _loc13_ * _loc14_;
               _loc14_++;
            }
            if(Play1_mc["shengming1"].visible == true)
            {
               Play1_mc["shengming1"].x = _loc12_ + _loc13_ * _loc14_;
               _loc14_++;
            }
            if(Play1_mc["yuhuo1"].visible == true)
            {
               Play1_mc["yuhuo1"].x = _loc12_ + _loc13_ * _loc14_;
               _loc14_++;
            }
         }
         if(Main.P1P2)
         {
            _loc1_ = Math.round(Main.player_2.hp.getValue());
            _loc2_ = Math.round(Main.player_2.use_hp_Max.getValue());
            _loc3_ = int(Main.player_2.mp.getValue());
            _loc4_ = int(Main.player_2.use_mp_Max.getValue());
            _loc5_ = int(Main.player_2.data.getEXP());
            _loc6_ = int(Main.player_2.nextExp.getValue());
            _loc7_ = 100 - _loc1_ / _loc2_ * 100;
            _loc8_ = 100 - _loc3_ / _loc4_ * 100;
            _loc9_ = int(Main.player_2.data.getLevel());
            _loc10_ = Main.player_2.data.getEXP() / Main.player_2.nextExp.getValue() * 100;
            _loc11_ = int(Main.player_2.headFrame);
            if(_loc3_ == 1)
            {
               _loc3_ = 0;
            }
            Play2_mc.hp_txt.text = _loc1_ + "/" + _loc2_;
            Play2_mc.mp_txt.text = _loc3_ + "/" + _loc4_;
            Play2_mc.exp_txt.text = _loc5_ + "/" + _loc6_;
            Play2_mc.hp_mc.load_mc.scaleX = 1 - _loc7_ / 100;
            Play2_mc.mp_mc.load_mc.scaleX = 1 - _loc8_ / 100;
            this.showLV(_loc9_,2);
            Play2_mc.exp_mc.gotoAndStop(_loc10_);
            Play2_mc.head_mc.gotoAndStop(_loc11_);
            Play2_mc.head_mc.visible = true;
            if(Main.player_2.zhongqiuState > 0)
            {
               Play2_mc["zhongqiu2"].visible = true;
            }
            else
            {
               Play2_mc["zhongqiu2"].visible = false;
            }
            if(Main.player_2.shengmingState > 0)
            {
               Play2_mc["shengming2"].visible = true;
            }
            else
            {
               Play2_mc["shengming2"].visible = false;
            }
            if(Main.player_2.jianrenState > 0)
            {
               Play2_mc["jianren2"].visible = true;
            }
            else
            {
               Play2_mc["jianren2"].visible = false;
            }
            if(Main.player2.buffNine[0] > 0)
            {
               Play2_mc["yuhuo2"].visible = true;
               this.showYHCS2();
            }
            else
            {
               Play2_mc["yuhuo2"].visible = false;
            }
            _loc12_ = -16;
            _loc13_ = 29;
            _loc14_ = 0;
            if(Play2_mc["zhongqiu2"].visible == true)
            {
               Play2_mc["zhongqiu2"].x = _loc12_ - _loc13_ * _loc14_;
               _loc14_++;
            }
            if(Play2_mc["jianren2"].visible == true)
            {
               Play2_mc["jianren2"].x = _loc12_ - _loc13_ * _loc14_;
               _loc14_++;
            }
            if(Play2_mc["shengming2"].visible == true)
            {
               Play2_mc["shengming2"].x = _loc12_ - _loc13_ * _loc14_;
               _loc14_++;
            }
            if(Play2_mc["yuhuo2"].visible == true)
            {
               Play2_mc["yuhuo2"].x = _loc12_ - _loc13_ * _loc14_;
               _loc14_++;
            }
         }
         Play1_mc.visible = Play2_mc.visible = true;
         jlNum_mc_X1.visible = jlNum_mc_X2.visible = true;
         jlNum_mc1.visible = jlNum_mc2.visible = true;
         jlNum_txt1.visible = jlNum_txt2.visible = true;
         if(!Main.player_1.visible)
         {
            Play1_mc.visible = false;
            jlNum_mc_X1.visible = false;
            jlNum_mc1.visible = false;
            jlNum_txt1.visible = false;
         }
         if(Boolean(Main.P1P2) && !Main.player_2.visible)
         {
            Play2_mc.visible = false;
            jlNum_mc_X2.visible = false;
            jlNum_mc2.visible = false;
            jlNum_txt2.visible = false;
         }
         if(!Main.P1P2)
         {
            Play2_mc.visible = false;
            jlNum_mc_X2.visible = false;
            jlNum_mc2.visible = false;
            jlNum_txt2.visible = false;
         }
      }
      
      private function showLV(param1:Number, param2:int) : *
      {
         var _loc3_:String = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         if(param1 < 10)
         {
            this["Play" + param2 + "_mc"]["level_1_mc"].gotoAndStop(param1);
            this["Play" + param2 + "_mc"]["level_2_mc"].visible = false;
         }
         else
         {
            this["Play" + param2 + "_mc"]["level_2_mc"].visible = true;
            _loc3_ = param1.toString();
            _loc4_ = int(_loc3_.substring(0,1));
            _loc5_ = int(_loc3_.substring(1,2));
            this["Play" + param2 + "_mc"]["level_1_mc"].gotoAndStop(_loc4_);
            if(_loc5_ == 0)
            {
               this["Play" + param2 + "_mc"]["level_2_mc"].gotoAndStop(10);
            }
            else
            {
               this["Play" + param2 + "_mc"]["level_2_mc"].gotoAndStop(_loc5_);
            }
         }
      }
      
      private function 连击计数() : *
      {
         if(Boolean(Main.player1) && Boolean(Main.player_1))
         {
            if(templianJiNum[0] != Main.player_1.lianJi.getValue() && Main.player_1.lianJi.getValue() != 0)
            {
               templianJiNum[0] = Main.player_1.lianJi.getValue();
               this.ShowLianJi(1,templianJiNum[0]);
            }
            if(Main.player_1.lianjiBool)
            {
               if(Main.player_1.lianJiTime.getValue() < 87)
               {
                  this["Play1_mc"]["LianJi_mc"].visible = true;
               }
               else
               {
                  this["Play1_mc"]["LianJi_mc"].visible = false;
               }
            }
            else if(Main.player_1.lianJiTime.getValue() < 60)
            {
               this["Play1_mc"]["LianJi_mc"].visible = true;
            }
            else
            {
               this["Play1_mc"]["LianJi_mc"].visible = false;
            }
         }
         if(Main.P1P2 && Main.player2 && Boolean(Main.player_2))
         {
            if(templianJiNum[1] != Main.player_2.lianJi.getValue() && Main.player_2.lianJi.getValue() != 0)
            {
               templianJiNum[1] = Main.player_2.lianJi.getValue();
               this.ShowLianJi(2,templianJiNum[1]);
            }
            if(Main.player_2.lianjiBool)
            {
               if(Main.player_2.lianJiTime.getValue() < 87)
               {
                  this["Play2_mc"]["LianJi_mc"].visible = true;
               }
               else
               {
                  this["Play2_mc"]["LianJi_mc"].visible = false;
               }
            }
            else if(Main.player_2.lianJiTime.getValue() < 60)
            {
               this["Play2_mc"]["LianJi_mc"].visible = true;
            }
            else
            {
               this["Play2_mc"]["LianJi_mc"].visible = false;
            }
         }
      }
      
      private function ShowLianJi(param1:int, param2:int) : *
      {
         var _loc5_:int = 0;
         this["Play" + param1 + "_mc"]["LianJi_mc"]["A_1"].visible = this["Play" + param1 + "_mc"]["LianJi_mc"]["A_2"].visible = this["Play" + param1 + "_mc"]["LianJi_mc"]["A_3"].visible = this["Play" + param1 + "_mc"]["LianJi_mc"]["A_4"].visible = false;
         var _loc3_:String = String(param2);
         var _loc4_:int = _loc3_.length;
         if(param2 <= 0)
         {
            return;
         }
         while(_loc4_)
         {
            _loc5_ = int(_loc3_.substr(_loc4_ - 1,1));
            this["Play" + param1 + "_mc"]["LianJi_mc"]["A_" + _loc4_].visible = true;
            if(_loc5_ == 0)
            {
               this["Play" + param1 + "_mc"]["LianJi_mc"]["A_" + _loc4_].gotoAndStop(10);
            }
            else
            {
               this["Play" + param1 + "_mc"]["LianJi_mc"]["A_" + _loc4_].gotoAndStop(_loc5_);
            }
            _loc4_--;
         }
      }
      
      public function GetCdStr(param1:Number = 1) : String
      {
         param1 = Number(Main["player" + param1].skinArr[Main["player" + param1].skinNum]);
         if(param1 == 0)
         {
            return "a";
         }
         if(param1 == 1)
         {
            return "b";
         }
         if(param1 == 2)
         {
            return "c";
         }
         if(param1 == 3)
         {
            return "k";
         }
         return undefined;
      }
      
      private function 初始化技能图标() : *
      {
         var _loc2_:String = null;
         var _loc3_:MovieClip = null;
         var _loc1_:* = 1;
         while(_loc1_ <= 2)
         {
            if(Boolean(Main["player" + _loc1_]) && Boolean(Main["player_" + _loc1_]))
            {
               _loc2_ = this.GetCdStr(_loc1_);
               i = 8;
               while(i < 16)
               {
                  _loc3_ = this["Play" + _loc1_ + "_mc"]["p_" + i];
                  _loc3_["p_mc"].gotoAndStop(_loc2_ + "" + i);
                  if(i > 11)
                  {
                     _loc3_["heiAnJN_mc"].visible = false;
                  }
                  ++i;
               }
            }
            _loc1_++;
         }
      }
      
      private function Cd计数() : *
      {
         var _loc2_:Player = null;
         var _loc3_:PlayerData = null;
         var _loc4_:String = null;
         var _loc5_:int = 0;
         var _loc6_:String = null;
         var _loc7_:int = 0;
         var _loc8_:MovieClip = null;
         var _loc9_:int = 0;
         var _loc10_:Supplies = null;
         var _loc11_:String = null;
         var _loc12_:MovieClip = null;
         this.初始化技能图标();
         var _loc1_:* = 1;
         while(_loc1_ <= 2)
         {
            if(Boolean(Main["player" + _loc1_]) && Boolean(Main["player_" + _loc1_]))
            {
               _loc2_ = Main["player_" + _loc1_];
               _loc3_ = Main["player" + _loc1_];
               _loc4_ = this.GetCdStr(_loc1_);
               _loc5_ = 8;
               while(_loc5_ < 16)
               {
                  _loc6_ = _loc4_ + String(_loc5_);
                  _loc7_ = _loc2_.selCD50(_loc6_);
                  _loc8_ = this["Play" + _loc1_ + "_mc"]["p_" + _loc5_];
                  _loc8_["X50"].gotoAndStop(_loc7_);
                  if(_loc5_ > 11)
                  {
                     _loc8_["heiAnJN_mc"].visible = false;
                  }
                  else
                  {
                     _loc9_ = _loc5_ - 7;
                     if(_loc3_.skinNum == 1)
                     {
                        _loc9_ += 4;
                     }
                     if(Boolean(_loc2_.heiAnJiNeng[_loc9_]) && _loc7_ < 50)
                     {
                        _loc8_.heiAnJN_mc.visible = true;
                     }
                     else
                     {
                        _loc8_.heiAnJN_mc.visible = false;
                     }
                  }
                  _loc5_++;
               }
               _loc5_ = 0;
               while(_loc5_ < 3)
               {
                  _loc10_ = _loc3_.getSuppliesSlot().getFromSuppliesSlot(_loc5_);
                  if(_loc10_)
                  {
                     _loc11_ = _loc10_.getName();
                     _loc7_ = _loc2_.sel_objCD50(_loc11_);
                     _loc12_ = this["Play" + _loc1_ + "_mc"]["obj_" + _loc5_];
                     _loc12_["X50"].gotoAndStop(_loc7_);
                     _loc12_["pic_mc"].gotoAndStop(_loc3_.getSuppliesSlot().getFromSuppliesSlot(_loc5_).getFrame());
                     _loc12_["num_txt"].text = _loc3_.getSuppliesSlot().getNumFromSuppliesSlot(_loc3_.getBag(),_loc5_);
                  }
                  _loc5_++;
               }
            }
            _loc1_++;
         }
      }
      
      private function bag_OPEN(param1:*) : *
      {
         ItemsPanel.OpenFrist = true;
         ItemsPanel.open();
      }
      
      private function shop_OPEN(param1:*) : *
      {
         Shop4399.Open();
      }
      
      private function 系统菜单关闭(param1:* = null) : *
      {
         file_mc.visible = false;
      }
      
      private function 画质(param1:MouseEvent) : *
      {
         file_mc.A.gotoAndStop(2);
         file_mc.C.gotoAndStop(2);
         if(param1.currentTarget.name == "A")
         {
            Main._stage.quality = StageQuality.HIGH;
            file_mc.A.gotoAndStop(1);
            huaZhi = 1;
         }
         else if(param1.currentTarget.name == "C")
         {
            Main._stage.quality = StageQuality.LOW;
            file_mc.C.gotoAndStop(1);
            huaZhi = 2;
         }
      }
      
      private function 转职面板(param1:*) : *
      {
         SetTransferPanel.open();
      }
      
      private function File_OPEN(param1:*) : *
      {
         if(file_mc.visible == false)
         {
            file_mc.visible = true;
            file_mc.key_mc.addEventListener(MouseEvent.CLICK,this.键位);
            file_mc.save_mc.addEventListener(MouseEvent.CLICK,this.保存);
            file_mc.city_mc.addEventListener(MouseEvent.CLICK,this.回城);
         }
         else
         {
            file_mc.visible = false;
         }
      }
      
      private function 键位(param1:*) : *
      {
         SetKeyPanel.open();
         this.系统菜单关闭();
      }
      
      private function 回城(param1:*) : *
      {
         Load.loadGameNum = 1;
         Load.loadGameNum2 = 1;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Main._this.Loading();
         Player.一起信春哥();
         this.系统菜单关闭();
         AchData.gkOk();
         TaskData.isOk();
         WinShow.All_0();
         PaiHang_Data.All_0();
      }
      
      private function 保存(param1:*) : *
      {
         if(this.saveTime <= 0)
         {
            Main.Save();
            this.saveTime = 800;
            this.系统菜单关闭();
            addEventListener(Event.ENTER_FRAME,this.onTime);
         }
      }
      
      private function onTime(param1:*) : *
      {
         if(this.saveTime > 0)
         {
            --this.saveTime;
         }
         else
         {
            removeEventListener(Event.ENTER_FRAME,this.onTime);
         }
         file_mc.time_txt.text = int(this.saveTime / 27);
      }
      
      private function 画质提示开启(param1:*) : *
      {
         画质提示_mc.y = -5000;
         画质提示_mc.x = -5000;
      }
      
      private function 画质提示关闭(param1:*) : *
      {
         画质提示_mc.y = -5000;
         画质提示_mc.x = -5000;
      }
      
      private function ShopObjTime_Show() : *
      {
         if(InitData.EXPxTime.getValue() > 0)
         {
            EXPxTime_mc.x = EXPxTime_mc.y = 0;
            EXPxTime_mc.t1_txt.text = this.TimeNum(InitData.EXPxTime.getValue());
         }
         else
         {
            EXPxTime_mc.x = EXPxTime_mc.y = 5000;
         }
         if(InitData.DOWNxTime.getValue() > 0)
         {
            DOWNxTime_mc.x = DOWNxTime_mc.y = 0;
            DOWNxTime_mc.t1_txt.text = this.TimeNum(InitData.DOWNxTime.getValue());
         }
         else
         {
            DOWNxTime_mc.x = DOWNxTime_mc.y = 5000;
         }
      }
      
      private function TimeNum(param1:int) : String
      {
         var _loc5_:String = null;
         var _loc6_:String = null;
         var _loc2_:int = param1 / 3600;
         var _loc3_:int = (param1 - _loc2_ * 3600) / 60;
         var _loc4_:int = param1 - _loc2_ * 3600 - _loc3_ * 60;
         if(_loc3_ < 10)
         {
            _loc5_ = "0" + _loc3_;
         }
         else
         {
            _loc5_ = _loc3_;
         }
         if(_loc4_ < 10)
         {
            _loc6_ = "0" + _loc4_;
         }
         else
         {
            _loc6_ = _loc4_;
         }
         return _loc2_ + ":" + _loc5_ + ":" + _loc6_;
      }
      
      private function 滚动提示() : *
      {
         var _loc1_:int = 0;
         ++this.滚动提示time;
         if(this.滚动提示time % 135 == 0)
         {
            if(Main.gameNum.getValue() == 0)
            {
               info_mc.x = info_mc.y = 0;
               _loc1_ = Math.random() * this.infoArr0.length;
               info_mc._txt.text = this.infoArr0[_loc1_];
            }
            else if(Main.gameNum.getValue() == 1 && Main.gameNum2.getValue() != 5)
            {
               info_mc.x = info_mc.y = 0;
               _loc1_ = Math.random() * this.infoArr1.length;
               info_mc._txt.text = this.infoArr1[_loc1_];
            }
            else
            {
               info_mc.x = info_mc.y = 5000;
            }
         }
      }
      
      private function 任务面板(param1:*) : *
      {
         TaskPanel.open();
      }
      
      private function CW面板(param1:*) : *
      {
         NewPetPanel.open();
      }
      
      public function zhuanZhi_Fun() : *
      {
         zhuanZhi_btn.visible = false;
         if(Main.gameNum.getValue() != 0)
         {
            return;
         }
         var _loc1_:Boolean = false;
         var _loc2_:Boolean = false;
         if(Main.P1P2)
         {
            if(!SetTransferPanel.tbo1() || !SetTransferPanel.tbo2())
            {
               _loc2_ = true;
            }
         }
         else if(!SetTransferPanel.tbo1())
         {
            _loc2_ = true;
         }
         if(Main.P1P2)
         {
            if(_loc2_)
            {
               if(Boolean(Main.player_1) && Boolean(Main.player_2))
               {
                  if(!SetTransferPanel.tbo1() && !SetTransferPanel.tbo2())
                  {
                     if(Main.player1.getLevel() >= 25 || Main.player2.getLevel() >= 25)
                     {
                        _loc1_ = true;
                     }
                  }
                  else if(Boolean(SetTransferPanel.tbo1()) && !SetTransferPanel.tbo2())
                  {
                     if(Main.player2.getLevel() >= 25)
                     {
                        _loc1_ = true;
                     }
                  }
                  else if(Boolean(SetTransferPanel.tbo2()) && !SetTransferPanel.tbo1())
                  {
                     if(Main.player1.getLevel() >= 25)
                     {
                        _loc1_ = true;
                     }
                  }
               }
            }
         }
         else if(_loc2_)
         {
            if(Main.player_1)
            {
               if(Main.player1.getLevel() >= 25)
               {
                  _loc1_ = true;
               }
            }
         }
         if(_loc1_ && Main.gameNum.getValue() == 0)
         {
            zhuanZhi_btn.visible = true;
         }
      }
      
      public function showYHCS1() : *
      {
         var _loc1_:int = 0;
         if(Main.player1.buffNine[0] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(1);
            _loc1_ = int(Main.player1.buffNine[0]);
            yhcs_txt.title_txt.text = "狱焰：一层";
            yhcs_txt._txt.text = "经验值获得15%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[1] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(2);
            _loc1_ = int(Main.player1.buffNine[1]);
            yhcs_txt.title_txt.text = "狱焰：二层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[2] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(3);
            _loc1_ = int(Main.player1.buffNine[2]);
            yhcs_txt.title_txt.text = "狱焰：三层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[3] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(4);
            _loc1_ = int(Main.player1.buffNine[3]);
            yhcs_txt.title_txt.text = "狱焰：四层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[4] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(5);
            _loc1_ = int(Main.player1.buffNine[4]);
            yhcs_txt.title_txt.text = "狱焰：五层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%   ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[5] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(6);
            _loc1_ = int(Main.player1.buffNine[5]);
            yhcs_txt.title_txt.text = "狱焰：六层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2   ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[6] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(7);
            _loc1_ = int(Main.player1.buffNine[6]);
            yhcs_txt.title_txt.text = "狱焰：七层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[7] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(8);
            _loc1_ = int(Main.player1.buffNine[7]);
            yhcs_txt.title_txt.text = "狱焰：八层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%   \n 魔抗+20%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[8] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(9);
            _loc1_ = int(Main.player1.buffNine[8]);
            yhcs_txt.title_txt.text = "狱焰：九层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%   \n 魔抗+5%   \n 破魔+5%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[9] > 0)
         {
            Play1_mc["yuhuo1"].gotoAndStop(10);
            _loc1_ = int(Main.player1.buffNine[9]);
            yhcs_txt.title_txt.text = "狱焰：十层";
            yhcs_txt._txt.text = "经验值获得30%   \n 防御力+20%   \n 魔法值+20%   \n 生命值+20%   \n 攻击力+20%    \n 移动速度+4     \n 暴击+30%   \n 魔抗+10%   \n 破魔+10% ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
            if(Main.player1.reBorn > 0)
            {
               yhcs_txt.time_txt.text += "\n重生冷却" + Math.floor(Main.player1.reBorn / 60) + "分" + Main.player1.reBorn % 60 + "秒";
            }
            yhcs_txt.cs_txt.visible = true;
         }
         else
         {
            yhcs_txt.cs_txt.visible = false;
         }
      }
      
      public function showShanShuo() : *
      {
         if(JiangLiPanel.LQtimes.getValue() == 0)
         {
            if(Main.player1.getLevel() >= 1)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         if(JiangLiPanel.LQtimes.getValue() == 1)
         {
            if(Main.player1.getLevel() >= 8)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 2)
         {
            if(Main.player1.getLevel() >= 15)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 3)
         {
            if(Main.player1.getLevel() >= 20)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 4)
         {
            if(Main.player1.getLevel() >= 25)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 5)
         {
            if(Main.player1.getLevel() >= 30)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 6)
         {
            if(Main.player1.getLevel() >= 35)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 7)
         {
            if(Main.player1.getLevel() >= 40)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 8)
         {
            if(Main.player1.getLevel() >= 45)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 9)
         {
            if(Main.player1.getLevel() >= 50)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 10)
         {
            if(Main.player1.getLevel() >= 55)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 11)
         {
            if(Main.player1.getLevel() >= 60)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 12)
         {
            if(Main.player1.getLevel() >= 65)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 13)
         {
            if(Main.player1.getLevel() >= 70)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 14)
         {
            interfaceX.shanshuo_mc.visible = false;
            interfaceX.dengji_btn.visible = false;
         }
      }
      
      public function showYHCS2() : *
      {
         var _loc1_:int = 0;
         if(Main.player2.buffNine[0] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(1);
            _loc1_ = int(Main.player2.buffNine[0]);
            yhcs_txt.title_txt.text = "狱焰：一层";
            yhcs_txt._txt.text = "经验值获得15%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[1] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(2);
            _loc1_ = int(Main.player2.buffNine[1]);
            yhcs_txt.title_txt.text = "狱焰：二层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[2] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(3);
            _loc1_ = int(Main.player2.buffNine[2]);
            yhcs_txt.title_txt.text = "狱焰：三层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[3] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(4);
            _loc1_ = int(Main.player2.buffNine[3]);
            yhcs_txt.title_txt.text = "狱焰：四层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[4] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(5);
            _loc1_ = int(Main.player2.buffNine[4]);
            yhcs_txt.title_txt.text = "狱焰：五层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%   ";
            yhcs_txt.time_txt.text += "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[5] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(6);
            _loc1_ = int(Main.player2.buffNine[5]);
            yhcs_txt.title_txt.text = "狱焰：六层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2   ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[6] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(7);
            _loc1_ = int(Main.player2.buffNine[6]);
            yhcs_txt.title_txt.text = "狱焰：七层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[7] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(8);
            _loc1_ = int(Main.player2.buffNine[7]);
            yhcs_txt.title_txt.text = "狱焰：八层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%   \n 魔抗+20%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[8] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(9);
            _loc1_ = int(Main.player2.buffNine[8]);
            yhcs_txt.title_txt.text = "狱焰：九层";
            yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%   \n 魔抗+5%   \n 破魔+5%  ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[9] > 0)
         {
            Play2_mc["yuhuo2"].gotoAndStop(10);
            _loc1_ = int(Main.player2.buffNine[9]);
            yhcs_txt.title_txt.text = "狱焰：十层";
            yhcs_txt._txt.text = "经验值获得30%   \n 防御力+20%   \n 魔法值+20%   \n 生命值+20%   \n 攻击力+20%    \n 移动速度+4     \n 暴击+30%   \n 魔抗+10%   \n 破魔+10% ";
            yhcs_txt.time_txt.text = "剩余" + Math.floor(_loc1_ / 3600) + "小时" + Math.floor(_loc1_ % 3600 / 60) + "分" + _loc1_ % 3600 % 60 + "秒";
            if(Main.player2.reBorn > 0)
            {
               yhcs_txt.time_txt.text += "\n重生冷却" + Math.floor(Main.player2.reBorn / 60) + "分" + Main.player2.reBorn % 60 + "秒";
            }
            yhcs_txt.cs_txt.visible = true;
         }
         else
         {
            yhcs_txt.cs_txt.visible = false;
         }
      }
   }
}

