package src
{
   import com.ByteArrayXX.*;
   import com.adobe.serialization.json.*;
   import com.hotpoint.braveManIII.views.elvesPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.makePanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.stampPanel.*;
   import com.hotpoint.braveManIII.views.wantedPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src._data.*;
   import src.tool.*;
   import unit4399.events.*;
   
   public class Api_4399_GongHui
   {
      public function Api_4399_GongHui()
      {
         super();
      }
      
      public static function Init(param1:Stage) : *
      {
         param1.addEventListener(UnionEvent.UNION_VISITOR_SUCCESS,onVisitorSuccess);
         param1.addEventListener(UnionEvent.UNION_MEMBER_SUCCESS,onMemberSuccess);
         param1.addEventListener(UnionEvent.UNION_GROW_SUCCESS,onGrowSuccess);
         param1.addEventListener(UnionEvent.UNION_MASTER_SUCCESS,onMasterSuccess);
         param1.addEventListener(UnionEvent.UNION_VARIABLES_SUCCESS,onVariablesSuccess);
         param1.addEventListener(UnionEvent.UNION_ROLE_SUCCESS,unionRoleSuccess);
         param1.addEventListener(UnionEvent.UNION_ERROR,unionCreateError);
      }
      
      public static function SelUserInfo() : *
      {
         var _loc1_:int = int(Main.saveNum);
         Main.serviceHold.getOwnUnion(_loc1_);
      }
      
      public static function AddGongHui(param1:String, param2:String) : *
      {
         var _loc3_:int = int(Main.saveNum);
         Main.serviceHold.unionCreate(_loc3_,param1,param2);
      }
      
      public static function getList(param1:int = 1) : *
      {
         var _loc2_:int = int(Main.saveNum);
         var _loc3_:int = int(GongHui_Interface.gongHuiNumMax);
         if(_loc3_ == 0)
         {
            _loc3_ = 900;
         }
         Main.serviceHold.getUnionList(_loc2_,param1,_loc3_);
      }
      
      public static function getBHCY_List(param1:int) : *
      {
         var _loc2_:int = int(Main.saveNum);
         Main.serviceHold.getUnionMembers(_loc2_,param1);
      }
      
      public static function upNum(param1:int = 15) : *
      {
         var _loc2_:int = int(Main.saveNum);
         Main.serviceHold.doVariable(_loc2_,param1);
      }
      
      public static function getNum(param1:Array) : *
      {
         var _loc2_:int = int(Main.saveNum);
         if(Main.serviceHold)
         {
            Main.serviceHold.getVariables(_loc2_,param1);
         }
      }
      
      public static function getSenHe(param1:int = 1, param2:int = 100) : *
      {
         var _loc3_:int = int(Main.saveNum);
         Main.serviceHold.getApplyList(_loc3_,param1,param2);
         TiaoShi.txtShow("获取待审核列表 请求\n");
      }
      
      public static function SenQing(param1:int, param2:String = "") : *
      {
         var _loc3_:int = int(Main.saveNum);
         Main.serviceHold.applyUnion(_loc3_,param1,param2);
      }
      
      public static function DuiHuanGX(param1:int) : *
      {
         var _loc2_:int = int(Main.saveNum);
         Main.serviceHold.doExchange(_loc2_,param1);
      }
      
      public static function setRW(param1:int) : *
      {
         var _loc2_:int = int(Main.saveNum);
         var _loc3_:String = String(param1);
         Main.serviceHold.doTask(_loc2_,_loc3_);
      }
      
      public static function XiaoHaoGX(param1:int) : *
      {
         var _loc2_:int = int(Main.saveNum);
         Main.serviceHold.usePersonalContribution(_loc2_,param1);
      }
      
      public static function XiaoHaoBanghui_GX(param1:int) : *
      {
         var _loc2_:int = int(Main.saveNum);
         Main.serviceHold.useUnionContribution(_loc2_,param1);
      }
      
      public static function InfoXX(param1:String, param2:int = 1, param3:int = 0, param4:int = 0, param5:int = 0) : *
      {
         var _loc6_:int = int(Main.saveNum);
         Main.serviceHold.setMemberExtra(_loc6_,param2,param1,param3,param4,param5);
      }
      
      public static function GH_InfoXX(param1:String, param2:int = 1) : *
      {
         var _loc3_:int = int(Main.saveNum);
         var _loc4_:int = int(GongHui_Interface.gongHui_ID);
         Main.serviceHold.setUnionExtra(_loc3_,param2,param1,_loc4_);
      }
      
      public static function SenHe(param1:int = 0, param2:int = 0, param3:int = 1) : *
      {
         var _loc4_:int = int(Main.saveNum);
         Main.serviceHold.auditMember(_loc4_,param1,param2,param3);
      }
      
      public static function get_QX(param1:int = 1, param2:int = 50) : *
      {
         Main.serviceHold.getRoleList(param1,param2);
      }
      
      public static function set_QX(param1:int, param2:int, param3:int) : *
      {
         var _loc4_:int = int(Main.saveNum);
         Main.serviceHold.setRole(_loc4_,param1,param2,param3);
      }
      
      public static function TiRen(param1:int = 0, param2:int = 0) : *
      {
         var _loc3_:int = int(Main.saveNum);
         Main.serviceHold.removeMember(_loc3_,param1,param2);
      }
      
      public static function ZhuanRang(param1:int = 0, param2:int = 0, param3:int = 0) : *
      {
         var _loc4_:int = int(Main.saveNum);
         Main.serviceHold.transferUnion(_loc4_,param1,param2,param3);
      }
      
      public static function TuiChu() : *
      {
         var _loc1_:int = int(Main.saveNum);
         Main.serviceHold.quitUion(_loc1_);
      }
      
      public static function JieSan(param1:int = 1) : *
      {
         var _loc2_:int = int(Main.saveNum);
         Main.serviceHold.dissolveUnion(_loc2_,param1);
      }
      
      private static function onVisitorSuccess(param1:UnionEvent) : *
      {
         var _loc3_:* = undefined;
         var _loc4_:String = null;
         var _loc5_:Object = null;
         var _loc6_:Array = null;
         var _loc2_:Object = param1.data;
         TiaoShi.txtShow("游客模块apiName:" + _loc2_.apiName + "\n");
         _loc3_ = _loc2_.data;
         switch(_loc2_.apiName)
         {
            case UnionEvent.UNI_API_BHCJ:
               TiaoShi.txtShow("帮会创建结果:" + String(_loc3_) + "\n");
               if(_loc3_ == true)
               {
                  Api_4399_GongHui.SelUserInfo();
               }
               break;
            case UnionEvent.UNI_API_BHLB:
               _loc4_ = _loc3_;
               _loc5_ = JSONs.decode(_loc4_,true);
               _loc6_ = _loc5_.unionList;
               TiaoShi.txtShow("公会总数:" + _loc5_.rowCount + "\n");
               GongHui_Interface.ListOk(_loc5_);
               break;
            case UnionEvent.UNI_API_BHSQ:
               if(Boolean(_loc3_))
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"申请提交成功, 请耐心等待审核结果");
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"申请提交失败了!");
               break;
            case UnionEvent.UNI_API_SSBH:
               _loc4_ = _loc3_;
               _loc5_ = JSONs.decode(_loc4_,true);
               TiaoShi.txtShow("当前用户 所属帮会信息:\n" + String(_loc3_) + "\n");
               GongHui_Interface.PlayerInfoOk(_loc5_);
         }
      }
      
      private static function onMemberSuccess(param1:UnionEvent) : *
      {
         var _loc3_:* = undefined;
         var _loc4_:String = null;
         var _loc5_:Array = null;
         var _loc2_:Object = param1.data;
         TiaoShi.txtShow("成员模块apiName:" + _loc2_.apiName + "\n");
         _loc3_ = _loc2_.data;
         switch(_loc2_.apiName)
         {
            case UnionEvent.UNI_API_BHMX:
               TiaoShi.txtShow("帮会明细结果:\n" + String(_loc3_) + "\n");
               GongHui_Interface.SenQingOpen();
               break;
            case UnionEvent.UNI_API_BHCY:
               TiaoShi.txtShow("帮会成员列表:\n" + String(_loc3_) + "\n");
               _loc4_ = _loc3_;
               _loc5_ = JSONs.decode(_loc4_,true);
               GongHui_Interface.CengYuanListOk(_loc5_);
               break;
            case UnionEvent.UNI_API_CYTZBG:
               TiaoShi.txtShow("成员扩展信息变更:\n" + Boolean(_loc3_) + "\n");
               break;
            case UnionEvent.UNI_API_BHTZBG:
               TiaoShi.txtShow("帮会扩展信息变更:\n" + Boolean(_loc3_) + "\n");
               break;
            case UnionEvent.UNI_API_BHRZ:
               TiaoShi.txtShow("帮会行为记录列表:\n" + String(_loc3_) + "\n");
               break;
            case UnionEvent.UNI_API_TCBH:
               TiaoShi.txtShow("退出帮会信息:\n" + Boolean(_loc3_) + "\n");
               break;
            case UnionEvent.UNI_API_XHGRGXD:
               TiaoShi.txtShow("消耗个人贡献点信息:\n" + int(_loc3_) + "\n");
               break;
            case UnionEvent.UNI_API_SZJS:
               TiaoShi.txtShow("设置角色权限信息:\n" + Boolean(_loc3_) + "\n");
         }
      }
      
      private static function onGrowSuccess(param1:UnionEvent) : *
      {
         var _loc2_:Object = param1.data;
         TiaoShi.txtShow("成长模块apiName:" + _loc2_.apiName + "\n");
         var _loc3_:* = _loc2_.data;
         switch(_loc2_.apiName)
         {
            case UnionEvent.UNI_API_BHRW:
               TiaoShi.txtShow("帮会任务结果:\n" + Boolean(_loc3_) + "\n");
               GongHui_Interface.gongHuiShow = true;
               SelUserInfo();
               Main.Save(false);
               break;
            case UnionEvent.UNI_API_BHDH:
               upNum(159);
               Api_4399_All.Money_sel();
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点券捐献成功, 公会经验+1, 贡献值+1");
               GongHui_Interface.gongHuiShow = true;
               SelUserInfo();
               Main.Save(false);
               TiaoShi.txtShow("帮会兑换信息:\n" + Boolean(_loc3_) + "\n");
               break;
            case UnionEvent.UNI_API_BHRWWC:
               TiaoShi.txtShow("帮会任务完成情况:\n" + String(_loc3_) + "\n");
         }
      }
      
      private static function onMasterSuccess(param1:UnionEvent) : *
      {
         var _loc3_:* = undefined;
         var _loc4_:String = null;
         var _loc5_:Object = null;
         var _loc2_:Object = param1.data;
         TiaoShi.txtShow("帮主模块apiName:" + _loc2_.apiName + "\n");
         _loc3_ = _loc2_.data;
         switch(_loc2_.apiName)
         {
            case UnionEvent.UNI_API_DSHLB:
               TiaoShi.txtShow("获取待审核列表:\n" + String(_loc3_) + "\n");
               _loc4_ = _loc3_;
               _loc5_ = JSONs.decode(_loc4_,true);
               GongHui_Interface.SenHeListOk(_loc5_);
               break;
            case UnionEvent.UNI_API_CYSH:
               TiaoShi.txtShow("成员审核信息:\n" + Boolean(_loc3_) + "\n");
               break;
            case UnionEvent.UNI_API_CYYC:
               TiaoShi.txtShow("移除成员信息:\n" + Boolean(_loc3_) + "\n");
               break;
            case UnionEvent.UNI_API_JSBH:
               TiaoShi.txtShow("解散帮会信息:\n" + String(_loc3_) + "\n");
         }
      }
      
      private static function onVariablesSuccess(param1:UnionEvent) : *
      {
         var _loc3_:* = undefined;
         var _loc4_:String = null;
         var _loc5_:Array = null;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc2_:Object = param1.data;
         TiaoShi.txtShow("公共变量模块apiName:" + _loc2_.apiName + "\n");
         _loc3_ = _loc2_.data;
         switch(_loc2_.apiName)
         {
            case UnionEvent.UNI_API_HQBL:
               TiaoShi.txtShow("公共变量列表:\n" + String(_loc3_) + "\n");
               _loc4_ = _loc3_;
               _loc5_ = JSONs.decode(_loc4_,true);
               if(_loc5_ != null)
               {
                  TiaoShi.txtShow("公共变量列表数组长度:" + _loc5_.length);
                  _loc6_ = 0;
                  _loc7_ = 0;
                  _loc8_ = 0;
                  _loc9_ = 0;
                  while(_loc9_ < _loc5_.length)
                  {
                     TiaoShi.txtShow(_loc9_ + " : " + _loc5_[_loc9_].id + ", " + _loc5_[_loc9_].value);
                     if(_loc5_[_loc9_].id == 57)
                     {
                        _loc6_ = int(_loc5_[_loc9_].value);
                     }
                     if(_loc5_[_loc9_].id == 58)
                     {
                        _loc7_ = int(_loc5_[_loc9_].value);
                     }
                     if(_loc5_[_loc9_].id == 137)
                     {
                        _loc8_ = int(_loc5_[_loc9_].value);
                     }
                     if(_loc5_[_loc9_].id == 157)
                     {
                        GongHui_Interface.xxArr[0] = _loc5_[_loc9_].value;
                     }
                     if(_loc5_[_loc9_].id == 158)
                     {
                        GongHui_Interface.xxArr[1] = _loc5_[_loc9_].value;
                     }
                     if(_loc5_[_loc9_].id == 159)
                     {
                        GongHui_Interface.xxArr[2] = _loc5_[_loc9_].value;
                        GongHui_Interface.HuoYue_Show();
                     }
                     if(_loc5_[_loc9_].id == 160)
                     {
                        GongHui_jiTan.jiTanLv_arr[0] = true;
                        GongHui_jiTan.jiTanLv_arr[1] = _loc5_[_loc9_].value;
                     }
                     if(_loc5_[_loc9_].id == 161)
                     {
                        GongHui_jiTan.jiTanLv_arr[2] = _loc5_[_loc9_].value;
                     }
                     if(_loc5_[_loc9_].id == 162)
                     {
                        GongHui_jiTan.jiTanLv_arr[3] = _loc5_[_loc9_].value;
                     }
                     if(_loc5_[_loc9_].id == 163)
                     {
                        GongHui_jiTan.jiTanLv_arr[4] = _loc5_[_loc9_].value;
                        Main.player_1.GetAllSkillCD();
                        if(Main.P1P2)
                        {
                           Main.player_2.GetAllSkillCD();
                        }
                     }
                     _loc9_++;
                  }
                  if(_loc6_ != 0)
                  {
                     GengXin.Go(_loc6_,_loc7_);
                  }
                  if(_loc8_ != 0)
                  {
                     _loc10_ = _loc8_ % 9;
                     Main.HuiTie8Num = _loc10_;
                     Play_Interface.LunTan10_Show();
                  }
                  break;
               }
               TiaoShi.txtShow("公会模式为沙盒模式!!");
               return;
               break;
            case UnionEvent.UNI_API_XGBL:
               TiaoShi.txtShow("公共变量修改信息:\n" + Boolean(_loc3_));
         }
      }
      
      private static function unionRoleSuccess(param1:UnionEvent) : void
      {
         var _loc2_:Object = param1.data;
         TiaoShi.txtShow("apiName:" + _loc2_.apiName + "\n");
         var _loc3_:* = _loc2_.data;
         switch(_loc2_.apiName)
         {
            case UnionEvent.UNI_API_JSQXLB:
               TiaoShi.txtShow("角色权限列表:\n" + String(_loc3_) + "\n");
         }
      }
      
      private static function unionCreateError(param1:UnionEvent) : *
      {
         var _loc2_:Array = new Array();
         _loc2_[10002] = "参数错误";
         _loc2_[10003] = "游戏未开通帮会API";
         _loc2_[10004] = "只有帮主有权限执行该操作";
         _loc2_[10005] = "用户未登陆";
         _loc2_[20001] = "当前点券不足";
         _loc2_[20002] = "点券不足";
         _loc2_[20003] = "扣款失败";
         _loc2_[20004] = "帮会名称已存在";
         _loc2_[20005] = "退出帮会后,24小时内不能申请或创建帮会";
         _loc2_[20006] = "超过申请数量上限";
         _loc2_[20007] = "该帮会的申请列表已满";
         _loc2_[20008] = "用户已经有帮会了";
         _loc2_[20009] = "已经申请过了";
         _loc2_[20010] = "用户还没有加入任何帮会";
         _loc2_[20011] = "不存在该帮会";
         _loc2_[20012] = "移除成员失败,用户不属于该帮会";
         _loc2_[20013] = "移除成员失败,帮主不能被移除";
         _loc2_[20014] = "审核失败,帮会成员已满 ";
         _loc2_[20015] = "编辑失败,只有帮主有该权限";
         _loc2_[20016] = "超过最大贡献值";
         _loc2_[20017] = "不存在该公共变量";
         _loc2_[20018] = "超过最大数量";
         _loc2_[20019] = "文本信息的字符数超过最大个数限制(1500)";
         _loc2_[20020] = "退出帮会后,24小时内不能申请加帮会";
         _loc2_[20021] = "没有兑换配置";
         _loc2_[20022] = "用户的申请信息已经过期";
         _loc2_[20023] = "帮会id错误";
         _loc2_[20024] = "已经申请过解散帮会了";
         _loc2_[20025] = "没有该任务";
         _loc2_[20026] = "用户不在审核列表中";
         _loc2_[20027] = "只有在加入帮会的24小时后才能进行贡献";
         _loc2_[20028] = "没有解散过帮会,不能进行取消解散";
         _loc2_[20029] = "公共变量未到生效时间";
         _loc2_[20030] = "账号不能变换存档加入同一个帮会";
         _loc2_[30001] = "数据库添加失败";
         _loc2_[30002] = "数据库删除失败";
         _loc2_[40001] = "特殊用户的type填写错误";
         _loc2_[40002] = "没有这个用户";
         TiaoShi.txtShow("帮会错误返回 eId:" + param1.data.eId + "  message:" + param1.data.msg + "\n");
         if(_loc2_[param1.data.eId])
         {
            if(param1.data.eId != 20016)
            {
               NewMC.Open("文字提示",Main._stage,480,300,40,0,true,1,_loc2_[param1.data.eId]);
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,300,40,0,true,1,"帮会接口未知错误:" + param1.data.eId);
         }
      }
   }
}

