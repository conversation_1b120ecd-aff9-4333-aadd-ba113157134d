package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.quest.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   public class obj extends MovieClip
   {
      internal var objX:*;
      
      public function obj(param1:*, param2:int, param3:int, param4:int = 0)
      {
         var _loc5_:int = 0;
         super();
         if(param1 is Equip)
         {
            this.objX = param1;
         }
         else if(param1 is Gem)
         {
            this.objX = param1;
         }
         else if(param1 is Quest)
         {
            this.objX = param1;
         }
         else if(param1 is Otherobj)
         {
            this.objX = param1;
         }
         else if(param1 is PetEquip)
         {
            this.objX = param1;
         }
         else if(param1 is Supplies)
         {
            this.objX = param1;
         }
         else
         {
            if(!(param1 is Object))
            {
               return;
            }
            this.objX = param1;
         }
         if(param4 == 0)
         {
            _loc5_ = int(param1.getFrame());
         }
         else
         {
            _loc5_ = param4;
         }
         this.x = param2;
         this.y = param3;
         Main.world.moveChild_Other.addChild(this);
         this.gotoAndStop(_loc5_);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
         mouseEnabled = false;
         mouseChildren = false;
      }
      
      private function onENTER_FRAME(param1:*) : *
      {
         var _loc3_:Player = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:Quest = null;
         var _loc11_:Number = NaN;
         var _loc12_:Number = NaN;
         var _loc2_:int = Player.All.length - 1;
         while(_loc2_ >= 0)
         {
            _loc3_ = Player.All[_loc2_];
            if(this.currentFrame == 360 && _loc3_.hit && this.hitTestObject(_loc3_.hit) && _loc3_.getKeyStatus("下",2))
            {
               _loc4_ = Math.random() * 4000 + 2000;
               _loc3_.MoneyUP(_loc4_);
               _loc5_ = this.x + Math.random() * 100 - 50;
               _loc6_ = this.y + Math.random() * 100 - 50;
               NewMC.Open("掉钱",Main.world.moveChild_Other,_loc5_,_loc6_,15,_loc4_,true);
               this.parent.removeChild(this);
               removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            }
            else if(this.currentFrame == 361 && _loc3_.hit && this.hitTestObject(_loc3_.hit) && _loc3_.getKeyStatus("下",2))
            {
               _loc7_ = Math.random() * 30 + 30;
               _loc3_.data.killPoint.setValue(_loc3_.data.killPoint.getValue() + _loc7_);
               _loc8_ = this.x + Math.random() * 100 - 50;
               _loc9_ = this.y + Math.random() * 100 - 50;
               NewMC.Open("击杀点",Main.world.moveChild_Other,_loc8_,_loc9_,20,_loc7_,true);
               this.parent.removeChild(this);
               removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            }
            else if(this.objX is Equip && _loc3_.hit && this.hitTestObject(_loc3_.hit) && _loc3_.getKeyStatus("下",2))
            {
               if(_loc3_.data.getBag().backequipBagNum() > 0)
               {
                  Player.All[_loc2_].data.getBag().addEquipBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is Gem && _loc3_.hit && this.hitTestObject(_loc3_.hit) && _loc3_.getKeyStatus("下",2))
            {
               if(_loc3_.data.getBag().backGemBagNum() > 0)
               {
                  Player.All[_loc2_].data.getBag().addGemBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is Otherobj && _loc3_.hit && this.hitTestObject(_loc3_.hit) && _loc3_.getKeyStatus("下",2))
            {
               if(_loc3_.data.getBag().backOtherBagNum() > 0)
               {
                  Player.All[_loc2_].data.getBag().addOtherobjBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is Supplies && _loc3_.hit && this.hitTestObject(_loc3_.hit) && _loc3_.getKeyStatus("下",2))
            {
               if(_loc3_.data.getBag().backSuppliesBagNum() > 0)
               {
                  (Player.All[_loc2_].data.getBag() as Bag).addSuppliesBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is PetEquip && _loc3_.hit && this.hitTestObject(_loc3_.hit) && _loc3_.getKeyStatus("下",2))
            {
               if(NewPetPanel.bag.backPetBagNum() > 0)
               {
                  NewPetPanel.bag.addPetBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is Quest && _loc3_.data.getBag().backQuestBagNum() > 0 && _loc3_.hit && this.hitTestObject(_loc3_.hit) && _loc3_.getKeyStatus("下",2))
            {
               _loc10_ = this.objX as Quest;
               _loc11_ = _loc10_.getFallLevel();
               if(_loc10_.getType() != 2)
               {
                  _loc12_ = 0;
                  if(_loc10_.getType() == 0)
                  {
                     _loc12_ = _loc10_.getGoodMaxNum();
                  }
                  else if(_loc10_.getType() == 1)
                  {
                     _loc12_ = _loc10_.getFallMax();
                  }
                  if(Player.All[_loc2_].data.getBag().fallQusetBag(_loc11_) < _loc12_)
                  {
                     Player.All[_loc2_].data.getBag().addQuestBag(this.objX);
                     this.parent.removeChild(this);
                     removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                     TaskData.addGoods(this.objX.getId());
                     TaskData.isOk();
                     if(Main.newPlay == 2)
                     {
                        Main.newPlay = 3;
                     }
                     return;
                  }
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
               }
               else if(Player.All[_loc2_].data.isTransferOk() == false)
               {
                  if(Player.All[_loc2_].data.getBag().fallQusetBag(_loc11_) < _loc10_.getFallMax())
                  {
                     Player.All[_loc2_].data.getBag().addQuestBag(this.objX);
                     this.parent.removeChild(this);
                     removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                     if(Main.newPlay == 2)
                     {
                        Main.newPlay = 3;
                     }
                     return;
                  }
               }
            }
            _loc2_--;
         }
         if(this.parent == Main.world.moveChild_Other && !JhitTestPoint.hitTestPoint(this,Main.world.MapData))
         {
            this.y += 10;
         }
      }
      
      private function onREMOVED_FROM_STAGE(param1:*) : *
      {
         stop();
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
   }
}

