package src
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   
   public class YueKa_Interface extends MovieClip
   {
      private static var loadData:ClassLoader;
      
      public static var _this:YueKa_Interface;
      
      private static var loadName:String = "yueKa.swf";
      
      private static var OpenYN:Boolean = false;
      
      public static var yueKaTime:Number = 0;
      
      public static var yueKaTime2:Number = 0;
      
      private var tooltip:ItemsTooltip;
      
      private var skin:MovieClip;
      
      public var objIdArr:Array = [0,63181,21221,33213,63107,63203,63147,63100,63210];
      
      public var objTypeArr:Array = [0,3,1,2,3,3,3,3,3];
      
      public var objNumArr:Array = [0,1,2,1,2,1,1,1,5];
      
      private var buyok:Boolean = false;
      
      public function YueKa_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.y = 0;
            _this.x = 0;
            _this.Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function Close(param1:* = null) : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.y = 5000;
            _this.x = 5000;
         }
      }
      
      private static function InitOpen() : *
      {
         var _loc1_:YueKa_Interface = new YueKa_Interface();
         Main._stage.addChild(_loc1_);
         OpenYN = true;
      }
      
      public static function GetBuy() : *
      {
         if(Boolean(_this) && Boolean(_this.buyok))
         {
            _this.buyok = false;
            NewMC.Open("文字提示",_this,400,400,30,0,true,2,"月卡购买成功");
            YueKa_Interface.yueKaTime = Main.serverDayNum + 30;
            _this.skin._BLACK_mc.visible = false;
            Main.Save2();
            _this.Show();
            _this.skin.buy_mc.visible = false;
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("Skin") as Class;
         this.skin = new _loc2_();
         addChild(this.skin);
         this.skin.Close_btn.addEventListener(MouseEvent.CLICK,Close);
         this.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,this.daoJuOver);
         this.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,this.daoJuOut);
         this.skin.buy_btn.addEventListener(MouseEvent.CLICK,this.BuyGo);
         this.skin.lingqu_btn.addEventListener(MouseEvent.CLICK,this.LingQu);
         this.skin.buy_mc.visible = false;
         this.skin.buy_mc.buyOk_btn.addEventListener(MouseEvent.CLICK,this.BuyGo2);
         this.skin.buy_mc.buyClose_btn.addEventListener(MouseEvent.CLICK,this.BuyClose);
         this.tooltip = new ItemsTooltip();
         this.addChild(this.tooltip);
         this.tooltip.visible = false;
         if(OpenYN)
         {
            Open();
         }
         else
         {
            Close();
         }
      }
      
      public function Show() : *
      {
         var _loc2_:* = undefined;
         var _loc3_:MovieClip = null;
         var _loc4_:int = 0;
         if(Main.serverDayNum == 0)
         {
            this.skin.buy_btn.visible = false;
            this.skin.day_txt.text = "数据读取中...";
         }
         else if(Main.serverDayNum < YueKa_Interface.yueKaTime)
         {
            this.skin.buy_btn.visible = false;
            _loc2_ = YueKa_Interface.yueKaTime - Main.serverDayNum;
            this.skin.day_txt.text = "剩余" + _loc2_ + "天";
         }
         else
         {
            this.skin.buy_btn.visible = true;
         }
         this.skin.lingqu_btn.visible = false;
         if(YueKa_Interface.yueKaTime == 0 || YueKa_Interface.yueKaTime < Main.serverDayNum || YueKa_Interface.yueKaTime2 < Main.serverDayNum)
         {
            this.skin.lingqu_btn.visible = true;
         }
         var _loc1_:Number = 1;
         while(_loc1_ <= 8)
         {
            _loc3_ = new Shop_picNEW();
            if(this.skin["n_" + _loc1_])
            {
               _loc4_ = int(this.skin["n_" + _loc1_].getChildIndex(this.skin["n_" + _loc1_].pic_xx));
               _loc3_.x = this.skin["n_" + _loc1_].pic_xx.x;
               _loc3_.y = this.skin["n_" + _loc1_].pic_xx.y;
               _loc3_.name = "pic_xx";
               _loc3_.gotoAndStop(this.getFrame(this.objTypeArr[_loc1_],this.objIdArr[_loc1_]));
               this.skin["n_" + _loc1_].removeChild(this.skin["n_" + _loc1_].pic_xx);
               this.skin["n_" + _loc1_].pic_xx = _loc3_;
               this.skin["n_" + _loc1_].addChild(_loc3_);
               this.skin["n_" + _loc1_].setChildIndex(_loc3_,_loc4_);
               this.skin["n_" + _loc1_].howNum.text = this.objNumArr[_loc1_];
            }
            _loc1_++;
         }
         this.skin._BLACK_mc.visible = false;
      }
      
      public function LingQu(param1:*) : *
      {
         if(Main.serverDayNum == 0)
         {
            NewMC.Open("文字提示",_this,480,450,45,0,true,2,"数据读取中,请稍后再试...");
            return;
         }
         if(YueKa_Interface.yueKaTime >= Main.serverDayNum && YueKa_Interface.yueKaTime2 < Main.serverDayNum)
         {
            this.lingQueOk();
         }
         else if(YueKa_Interface.yueKaTime == 0 || YueKa_Interface.yueKaTime > Main.serverDayNum)
         {
            NewMC.Open("文字提示",_this,480,450,45,0,true,2,"请先购买月卡");
         }
      }
      
      public function lingQueOk() : *
      {
         if(Main.player1.getBag().backOtherBagNum() < 6)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他类背包空间不足!");
            return;
         }
         if(Main.player1.getBag().backGemBagNum < 1)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"宝石类背包空间不足!");
            return;
         }
         if(Main.player1.getBag().backSuppliesBagNum < 1)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"消耗品背包空间不足!");
            return;
         }
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63107));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63107));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
         Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
         Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
         YueKa_Interface.yueKaTime2 = Main.serverDayNum;
         this.skin.lingqu_btn.visible = false;
         NewMC.Open("文字提示",_this,480,450,45,0,true,2,"今日物品已领取");
         Main.Save2();
      }
      
      private function BuyGo(param1:*) : *
      {
         var _loc2_:uint = uint(InitData.yueKa30.getValue());
         if(Shop4399.moneyAll.getValue() >= _loc2_)
         {
            this.skin.buy_mc.visible = true;
            return;
         }
         NewMC.Open("文字提示",this,480,450,45,0,true,2,"点券不足");
      }
      
      private function BuyGo2(param1:*) : *
      {
         this.buyok = true;
         this.skin._BLACK_mc.visible = true;
         Api_4399_All.BuyObj(InitData.yueKaID.getValue());
      }
      
      private function BuyClose(param1:*) : *
      {
         this.skin.buy_mc.visible = false;
      }
      
      public function getFrame(param1:Number, param2:Number) : Number
      {
         var _loc3_:Object = null;
         if(param1 == 0 || param1 == 4 || param1 == 5 || param1 == 6)
         {
            _loc3_ = EquipFactory.createEquipByID(param2);
            return (_loc3_ as Equip).getFrame();
         }
         if(param1 == 1)
         {
            _loc3_ = SuppliesFactory.getSuppliesById(param2);
            return (_loc3_ as Supplies).getFrame();
         }
         if(param1 == 2)
         {
            _loc3_ = GemFactory.creatGemById(param2);
            return (_loc3_ as Gem).getFrame();
         }
         if(param1 == 3)
         {
            _loc3_ = OtherFactory.creatOther(param2);
            return (_loc3_ as Otherobj).getFrame();
         }
         return 1;
      }
      
      private function addobj(param1:Number, param2:Number) : Object
      {
         var _loc3_:Object = null;
         if(param1 == 0 || param1 == 4 || param1 == 5 || param1 == 6)
         {
            _loc3_ = EquipFactory.createEquipByID(param2);
         }
         else if(param1 == 1)
         {
            _loc3_ = SuppliesFactory.getSuppliesById(param2);
         }
         else if(param1 == 2)
         {
            _loc3_ = GemFactory.creatGemById(param2);
         }
         else if(param1 == 3)
         {
            _loc3_ = OtherFactory.creatOther(param2);
         }
         return _loc3_;
      }
      
      private function daoJuOut(param1:DaoJuEvent) : void
      {
         this.tooltip.visible = false;
      }
      
      private function daoJuOver(param1:DaoJuEvent) : void
      {
         var _loc2_:String = param1.target.name.substr(0,2);
         var _loc3_:Number = Number(param1.target.name.substr(2,1));
         if(_loc2_ != "n_")
         {
            return;
         }
         this.tooltip.visible = true;
         this.tooltip.x = Main._stage.mouseX;
         this.tooltip.y = Main._stage.mouseY;
         var _loc4_:* = this.addobj(this.objTypeArr[_loc3_],this.objIdArr[_loc3_]);
         if(_loc4_ is Equip)
         {
            this.tooltip.equipTooltip(_loc4_);
         }
         else if(_loc4_ is Supplies)
         {
            this.tooltip.suppliesTooltip(_loc4_);
         }
         else if(_loc4_ is Gem)
         {
            this.tooltip.gemTooltip(_loc4_);
         }
         else if(_loc4_ is Otherobj)
         {
            this.tooltip.otherTooltip(_loc4_);
         }
      }
   }
}

