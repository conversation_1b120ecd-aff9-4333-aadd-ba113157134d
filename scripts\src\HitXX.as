package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.common.*;
   import flash.display.*;
   import flash.events.*;
   import src.other.*;
   import src.tool.*;
   
   public class HitXX extends MovieClip
   {
      public static var AllHitXX:Array = [];
      
      public static var baoJiUP:VT = VT.createVT();
      
      public var objArr:Array = [];
      
      public var gongJi_hp:Number;
      
      public var gongJi_hp_MAX:Number;
      
      public var gongJi_Ctr:Number;
      
      public var runArr:Array = [];
      
      public var cross:Boolean;
      
      public var 硬直:int;
      
      public var who:Object;
      
      public var getWho:Object;
      
      public var isCW:Boolean = false;
      
      public var _stage:MovieClip;
      
      public var RL:Boolean;
      
      public var times:int = 0;
      
      public var type:int = 0;
      
      public var space:int = 0;
      
      public var totalTime:int = 0;
      
      public var numValue:Number = 0;
      
      public var speedValue:int = 0;
      
      public var nameXX:String = "";
      
      public var flyName:String = "";
      
      public function HitXX()
      {
         super();
         this._stage = Main._this;
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public static function HitEnemy() : *
      {
         var _loc2_:int = 0;
         var _loc5_:HitXX = null;
         var _loc6_:Boolean = false;
         var _loc7_:Number = 0;
         var _loc8_:* = undefined;
         var _loc9_:int = 0;
         var _loc10_:Boolean = false;
         var _loc11_:int = 0;
         var _loc12_:Fly = null;
         var _loc13_:Enemy = null;
         var _loc14_:Player = null;
         var _loc15_:int = 0;
         var _loc16_:int = 0;
         var _loc17_:int = 0;
         var _loc18_:HitXX = null;
         var _loc19_:int = 0;
         var _loc20_:HitXX = null;
         var _loc21_:HitXX = null;
         var _loc22_:int = 0;
         var _loc1_:Number = Number(new Date().getTime());
         _loc2_ = 0;
         for(; _loc2_ < HitXX.AllHitXX.length; _loc2_++)
         {
            _loc5_ = HitXX.AllHitXX[_loc2_] as HitXX;
            if(_loc5_.parent is Fly)
            {
               (_loc5_.parent as Fly).bingDong = false;
               if((_loc5_.parent as Fly)._name == "Cw9_Fly3")
               {
                  (_loc5_.parent as Fly).bingDong = true;
               }
               if(!_loc5_.cross && _loc5_.parent.life != 0)
               {
                  _loc6_ = Boolean(JhitTestPoint.hitTestPoint(_loc5_,Main.world.MapData));
                  if(!_loc6_)
                  {
                     _loc7_ = 0;
                     while(_loc7_ < Main.world.numChildren)
                     {
                        _loc8_ = Main.world.getChildAt(_loc7_);
                        if(_loc8_ is Map && Boolean(JhitTestPoint.hitTestPoint(_loc5_,_loc8_.MapData)))
                        {
                           _loc6_ = true;
                           break;
                        }
                        _loc7_++;
                     }
                  }
                  if(_loc6_)
                  {
                     (_loc5_.parent as Fly).life = 0;
                     continue;
                  }
               }
            }
            if(_loc5_.who is Player)
            {
               if(Main.gameNum.getValue() == 0 && Boolean(Main.P1P2))
               {
                  _loc9_ = 0;
                  while(_loc9_ < Player.All.length)
                  {
                     if(_loc5_.who != Player.All[_loc9_])
                     {
                        _loc10_ = false;
                        _loc11_ = 0;
                        while(_loc11_ < _loc5_.objArr.length)
                        {
                           if(Player.All[_loc9_] == _loc5_.objArr[_loc11_])
                           {
                              _loc10_ = true;
                              break;
                           }
                           _loc11_++;
                        }
                        if(!_loc10_ && Player.All[_loc9_].hit && Player.All[_loc9_].hp.getValue() > 0 && _loc5_.hitTestObject(Player.All[_loc9_].hit))
                        {
                           if(BaoJi(_loc5_.who))
                           {
                              (Player.All[_loc9_] as Player).HpXX(_loc5_,true);
                           }
                           else
                           {
                              (Player.All[_loc9_] as Player).HpXX(_loc5_);
                           }
                           if((_loc5_.who as Player).skin.type >= 100)
                           {
                              new BuffEnemy(_loc5_,Player.All[_loc9_] as Player);
                           }
                           _loc5_.who.连击计数();
                           _loc5_.objArr[_loc5_.objArr.length] = Player.All[_loc9_];
                           MusicBox.ActMusicPlayX(_loc5_.who.data.skinArr[_loc5_.who.data.skinNum],_loc5_.who.skin.runType);
                           ACT.Play(_loc5_.who,Player.All[_loc9_]);
                           if(_loc5_.parent is Fly)
                           {
                              if((_loc5_.parent as Fly).life != -1 && (_loc5_.parent as Fly).life > 0)
                              {
                                 --(_loc5_.parent as Fly).life;
                              }
                           }
                        }
                     }
                     _loc9_++;
                  }
               }
               _loc9_ = 0;
               for(; _loc9_ < Enemy.All.length; _loc9_++)
               {
                  _loc10_ = false;
                  _loc11_ = 0;
                  while(_loc11_ < _loc5_.objArr.length)
                  {
                     if(Enemy.All[_loc9_] == _loc5_.objArr[_loc11_])
                     {
                        _loc10_ = true;
                        break;
                     }
                     _loc11_++;
                  }
                  if(Enemy.All[_loc9_].hit && Enemy.All[_loc9_].life.getValue() > 0 && !_loc10_ && _loc5_.hitTestObject(Enemy.All[_loc9_].hit))
                  {
                     if(_loc5_.parent is Fly)
                     {
                        _loc12_ = _loc5_.parent;
                        if(_loc12_.over2)
                        {
                           continue;
                        }
                        if((_loc5_.parent as Fly).life != -1 && (_loc5_.parent as Fly).life > 0)
                        {
                           --(_loc5_.parent as Fly).life;
                        }
                     }
                     if(BaoJi(_loc5_.who))
                     {
                        (Enemy.All[_loc9_] as Enemy).HpXX(_loc5_,true);
                     }
                     else
                     {
                        (Enemy.All[_loc9_] as Enemy).HpXX(_loc5_);
                     }
                     if((_loc5_.who as Player).skin.type >= 100)
                     {
                        new BuffEnemy(_loc5_,Enemy.All[_loc9_] as Enemy);
                     }
                     if(_loc5_.type >= 100 && _loc5_.parent is Fly)
                     {
                        new BuffEnemy(_loc5_,Enemy.All[_loc9_] as Enemy);
                     }
                     _loc5_.who.连击计数();
                     _loc5_.objArr.push(Enemy.All[_loc9_]);
                     if(_loc5_.parent is Fly && (_loc5_.parent as Fly)._name == "高能喷射")
                     {
                        ACT.Play(_loc5_.who,Enemy.All[_loc9_],"光束爆炸");
                     }
                     else
                     {
                        MusicBox.ActMusicPlayX(_loc5_.who.data.skinArr[_loc5_.who.data.skinNum],_loc5_.who.skin.runType);
                        ACT.Play(_loc5_.who,Enemy.All[_loc9_]);
                     }
                  }
               }
               _loc9_ = 0;
               while(_loc9_ < Player2.All.length)
               {
                  _loc10_ = false;
                  _loc11_ = 0;
                  while(_loc11_ < _loc5_.objArr.length)
                  {
                     if(Player2.All[_loc9_] == _loc5_.objArr[_loc11_])
                     {
                        _loc10_ = true;
                        break;
                     }
                     _loc11_++;
                  }
                  if(!_loc10_ && Player2.All[_loc9_].hit && Player2.All[_loc9_].hp.getValue() > 0 && _loc5_.hitTestObject(Player2.All[_loc9_].hit))
                  {
                     if(BaoJi(_loc5_.who))
                     {
                        (Player2.All[_loc9_] as Player2).HpXX(_loc5_,true);
                     }
                     else
                     {
                        (Player2.All[_loc9_] as Player2).HpXX(_loc5_);
                     }
                     if((_loc5_.who as Player).skin.type >= 100)
                     {
                        new BuffEnemy(_loc5_,Player2.All[_loc9_] as Player2);
                     }
                     _loc5_.who.连击计数();
                     _loc5_.objArr[_loc5_.objArr.length] = Player2.All[_loc9_];
                     MusicBox.ActMusicPlayX(_loc5_.who.data.skinArr[_loc5_.who.data.skinNum],_loc5_.who.skin.runType);
                     ACT.Play(_loc5_.who,Player2.All[_loc9_]);
                     if(_loc5_.parent is Fly)
                     {
                        if((_loc5_.parent as Fly).life != -1 && (_loc5_.parent as Fly).life > 0)
                        {
                           --(_loc5_.parent as Fly).life;
                        }
                     }
                  }
                  _loc9_++;
               }
               _loc9_ = 0;
               while(_loc9_ < OtherSkin.All.length)
               {
                  _loc10_ = false;
                  _loc11_ = 0;
                  while(_loc11_ < _loc5_.objArr.length)
                  {
                     if(OtherSkin.All[_loc9_] == _loc5_.objArr[_loc11_])
                     {
                        _loc10_ = true;
                        break;
                     }
                     _loc11_++;
                  }
                  if(OtherSkin.All[_loc9_].hit && !_loc10_ && _loc5_.hitTestObject(OtherSkin.All[_loc9_].hit) && (MapJG.step == 4 || MapJG.step == 13 || MapJG.step == 22))
                  {
                     if(MapJG.arr[OtherSkin.All[_loc9_].id - 1] - MapJG.attTimes == 1)
                     {
                        oskin = OtherSkin.All[_loc9_];
                        MapJG.attTimes = MapJG.arr[OtherSkin.All[_loc9_].id - 1];
                        OtherSkin.All[_loc9_].skin.gotoAndPlay(2);
                     }
                     if(MapJG.arr[OtherSkin.All[_loc9_].id - 1] - MapJG.attTimes > 1)
                     {
                        if(MapJG.step == 4)
                        {
                           MapJG.step = 6;
                           MapJG.timetemp = 0;
                        }
                        if(MapJG.step == 13)
                        {
                           MapJG.step = 15;
                           MapJG.timetemp = 0;
                        }
                        if(MapJG.step == 22)
                        {
                           MapJG.step = 24;
                           MapJG.timetemp = 0;
                        }
                        MapJG.attTimes = 0;
                     }
                  }
                  _loc9_++;
               }
            }
            else if(_loc5_.who is Enemy)
            {
               _loc13_ = _loc5_.who;
               if(Main.gameNum.getValue() == 3000)
               {
                  if(NpcYY._this && NpcYY._this.skin && _loc5_.hitTestObject(NpcYY._this.skin.hit))
                  {
                     NpcYY._this.Down();
                  }
               }
               _loc9_ = 0;
               while(_loc9_ < Player.All.length)
               {
                  _loc14_ = Player.All[_loc9_];
                  if(_loc14_.hp.getValue() > 0 && _loc14_.newLifeTime.getValue() <= InitData.BuyNum_0.getValue() && _loc14_.hit && _loc5_.hitTestObject(_loc14_.hit))
                  {
                     _loc10_ = false;
                     _loc11_ = 0;
                     while(_loc11_ < _loc5_.objArr.length)
                     {
                        if(_loc14_ == _loc5_.objArr[_loc11_])
                        {
                           _loc10_ = true;
                           break;
                        }
                        _loc11_++;
                     }
                     if(!_loc10_)
                     {
                        if(Main.gameNum.getValue() == 3000)
                        {
                           _loc14_.Hit3000(_loc13_.att_temp);
                        }
                        else if(!SanBi(_loc14_))
                        {
                           _loc14_.HpXX(_loc5_);
                           _loc13_.skin.MingZong(_loc14_);
                           if(_loc5_.type != 0)
                           {
                              new BuffEffect(_loc5_,_loc14_);
                           }
                        }
                        else
                        {
                           _loc15_ = _loc14_.x + Math.random() * 100 - 50;
                           _loc16_ = _loc14_.y + Math.random() * 100 - 150;
                           NewMC.Open("闪避",Main.world.moveChild_Other,_loc15_,_loc16_,15,0,true,2);
                        }
                        _loc5_.objArr[_loc5_.objArr.length] = _loc14_;
                        if(_loc5_.parent is Fly)
                        {
                           if((_loc5_.parent as Fly).life != -1 && (_loc5_.parent as Fly).life > 0)
                           {
                              --(_loc5_.parent as Fly).life;
                           }
                        }
                     }
                  }
                  _loc9_++;
               }
            }
            else if(_loc5_.who is ChongWu && _loc5_.getWho is Player)
            {
               _loc9_ = 0;
               while(_loc9_ < Enemy.All.length)
               {
                  _loc10_ = false;
                  _loc11_ = 0;
                  while(_loc11_ < _loc5_.objArr.length)
                  {
                     if(Enemy.All[_loc9_] == _loc5_.objArr[_loc11_])
                     {
                        _loc10_ = true;
                        break;
                     }
                     _loc11_++;
                  }
                  if(!_loc10_ && Enemy.All[_loc9_].hit && Enemy.All[_loc9_].life.getValue() > 0 && _loc5_.hitTestObject(Enemy.All[_loc9_].hit))
                  {
                     (Enemy.All[_loc9_] as Enemy).HpXX(_loc5_);
                     _loc5_.objArr[_loc5_.objArr.length] = Enemy.All[_loc9_];
                     ACT.Play2(0,Enemy.All[_loc9_]);
                     if(_loc5_.parent is Fly)
                     {
                        if((_loc5_.parent as Fly).bingDong)
                        {
                           if((_loc5_.parent as Fly)._name == "Cw9_Fly3")
                           {
                              _loc17_ = Math.random() * 100;
                              if(_loc17_ < 10)
                              {
                                 _loc18_ = new HitXX();
                                 _loc18_.type = 103;
                                 _loc18_.space = 54;
                                 _loc18_.totalTime = 54;
                                 _loc18_.numValue = 0;
                                 new BuffEnemy(_loc18_,Enemy.All[_loc9_] as Enemy);
                              }
                           }
                        }
                        if((_loc5_.parent as Fly)._name == "Cw33_Fly3")
                        {
                           ACT.Play(_loc5_.who,Enemy.All[_loc9_],"火眼金睛效果");
                        }
                        if((_loc5_.parent as Fly)._name == "Cw31_Fly1")
                        {
                           _loc19_ = Math.random() * 100;
                           if(_loc19_ < 50)
                           {
                              _loc20_ = new HitXX();
                              _loc20_.type = 507;
                              _loc20_.totalTime = 81;
                              _loc20_.space = 81;
                              _loc20_.numValue = 2;
                              new BuffEnemy(_loc20_,Enemy.All[_loc2_]);
                           }
                        }
                        if((_loc5_.parent as Fly)._name == "Cw31_Fly2")
                        {
                           _loc21_ = new HitXX();
                           _loc21_.type = 507;
                           _loc21_.totalTime = 81;
                           _loc21_.space = 81;
                           _loc21_.numValue = 2;
                           new BuffEnemy(_loc21_,Enemy.All[_loc2_]);
                        }
                        if((_loc5_.parent as Fly).life != -1 && (_loc5_.parent as Fly).life > 0)
                        {
                           --(_loc5_.parent as Fly).life;
                        }
                     }
                  }
                  _loc9_++;
               }
            }
            else if(_loc5_.who is Player2)
            {
               _loc9_ = 0;
               while(_loc9_ < Player.All.length)
               {
                  _loc10_ = false;
                  _loc11_ = 0;
                  while(_loc11_ < _loc5_.objArr.length)
                  {
                     if(Player.All[_loc9_] == _loc5_.objArr[_loc11_])
                     {
                        _loc10_ = true;
                        break;
                     }
                     _loc11_++;
                  }
                  if(Player.All[_loc9_].hit && (Player.All[_loc9_] as Player).hp.getValue() > 0 && !_loc10_ && _loc5_.hitTestObject(Player.All[_loc9_].hit))
                  {
                     if(!SanBi(Player.All[_loc9_]))
                     {
                        if(BaoJi(_loc5_.who))
                        {
                           (Player.All[_loc9_] as Player).HpXX(_loc5_,true);
                        }
                        else
                        {
                           (Player.All[_loc9_] as Player).HpXX(_loc5_);
                        }
                     }
                     else
                     {
                        _loc15_ = (Player.All[_loc9_] as Player).x + Math.random() * 100 - 50;
                        _loc16_ = (Player.All[_loc9_] as Player).y + Math.random() * 100 - 150;
                        NewMC.Open("闪避",Main.world.moveChild_Other,_loc15_,_loc16_,15,0,true,2);
                     }
                     _loc5_.objArr[_loc5_.objArr.length] = Player.All[_loc9_];
                     if(_loc5_.parent is Fly)
                     {
                        if((_loc5_.parent as Fly).life != -1 && (_loc5_.parent as Fly).life > 0)
                        {
                           --(_loc5_.parent as Fly).life;
                        }
                     }
                  }
                  _loc9_++;
               }
            }
         }
         var _loc3_:int = Enemy.All.length - 1;
         while(_loc3_ >= 0)
         {
            if(Enemy.All[_loc3_] is Enemy && (Enemy.All[_loc3_] as Enemy).life.getValue() <= 0)
            {
               Enemy.All.splice(_loc3_,1);
            }
            _loc3_--;
         }
         _loc2_ = Player.All.length - 1;
         while(_loc2_ >= 0)
         {
            _loc22_ = int((Player.All[_loc2_] as Player).hp.getValue());
            if(Boolean(Player.All[_loc2_]) && _loc22_ <= 0)
            {
               Player.All.splice(_loc2_,1);
            }
            _loc2_--;
         }
         var _loc4_:Number = Number(new Date().getTime());
         TiaoShi.tempTime3 = _loc4_ - _loc1_;
      }
      
      private static function SanBi(param1:*) : Boolean
      {
         var _loc2_:Number = Number(InitData.Temp0.getValue());
         if(PK_UI.PK_ing)
         {
            _loc2_ = int(param1.use_sanbi.getValue() * InitData.Temp01.getValue() * InitData.Temp01.getValue() * InitData.Temp01.getValue());
         }
         else
         {
            _loc2_ = int(param1.use_sanbi.getValue() * InitData.Temp01.getValue() * InitData.Temp01.getValue());
         }
         _loc2_ = Math.pow(_loc2_,InitData.Temp8.getValue());
         var _loc3_:int = Math.random() * InitData.Temp100.getValue();
         if(_loc2_ > _loc3_)
         {
            return true;
         }
         return false;
      }
      
      private static function BaoJi(param1:*) : Boolean
      {
         var _loc2_:int = int(param1.use_baoji.getValue() / InitData.Temp7.getValue() * InitData.Temp01.getValue()) + baoJiUP.getValue();
         _loc2_ = Math.pow(_loc2_,InitData.Temp8.getValue());
         var _loc3_:int = Math.random() * InitData.Temp100.getValue();
         if(_loc2_ > _loc3_)
         {
            return true;
         }
         return false;
      }
      
      private function onADDED_TO_STAGE(param1:*) : *
      {
         var _loc3_:int = 0;
         if(AllHitXX)
         {
            _loc3_ = 0;
            while(_loc3_ < AllHitXX.length)
            {
               if(AllHitXX[_loc3_] == this)
               {
                  return;
               }
               _loc3_++;
            }
         }
         var _loc2_:MovieClip = this.parent as MovieClip;
         while(_loc2_ != this._stage)
         {
            if(_loc2_ is FlyPerson)
            {
               this.硬直 = _loc2_.硬直;
               this.times = _loc2_.attTimes;
               this.who = this.getWho = _loc2_.who;
               if(this.who is ChongWu)
               {
                  this.isCW = true;
                  this.getWho = (_loc2_.who as ChongWu).who;
               }
               this.RL = _loc2_.RL;
               this.cross = _loc2_.cross;
               this.gongJi_hp = _loc2_.gongJi_hp;
               this.gongJi_hp_MAX = _loc2_.gongJi_hp_MAX;
               this.runArr = [_loc2_.runX,_loc2_.runY,_loc2_.runTime];
               this.type = _loc2_.type;
               this.space = _loc2_.space;
               this.totalTime = _loc2_.totalTime;
               this.numValue = _loc2_.numValue;
               AllHitXX.push(this);
               return;
            }
            if(_loc2_ is Fly)
            {
               this.nameXX = _loc2_.nameXX;
               this.硬直 = _loc2_.硬直;
               this.times = _loc2_.attTimes;
               this.who = this.getWho = _loc2_.who;
               if(this.who is ChongWu)
               {
                  this.isCW = true;
                  this.getWho = (_loc2_.who as ChongWu).who;
               }
               this.RL = _loc2_.RL;
               this.cross = _loc2_.cross;
               this.gongJi_hp = _loc2_.gongJi_hp;
               this.gongJi_hp_MAX = _loc2_.gongJi_hp_MAX;
               this.gongJi_Ctr = this.gongJi_hp;
               this.runArr = [_loc2_.runX,_loc2_.runY,_loc2_.runTime];
               this.type = (_loc2_ as Fly).type;
               this.space = (_loc2_ as Fly).space;
               this.totalTime = (_loc2_ as Fly).totalTime;
               this.numValue = (_loc2_ as Fly).numValue;
               AllHitXX.push(this);
               return;
            }
            if(_loc2_ is Skin_WuQi && _loc2_.parent is Player)
            {
               this.who = _loc2_.parent;
               this.RL = (this.who as Player).RL;
               this.gongJi_hp = this.who.skin.hpXX.getValue();
               this.gongJi_hp_MAX = this.who.skin.hpMax;
               if(this.who.skin.hpX >= this.who.skin.hpXX.getValue() + 1)
               {
                  SaveXX.Save(2,this.who.skin.hpX,true,false,false);
                  return;
               }
               this.硬直 = this.who.skin.硬直;
               this.times = this.who.skin.attTimes;
               this.runArr = [this.who.skin.runX,this.who.skin.runY,this.who.skin.runTime];
               AllHitXX.push(this);
               return;
            }
            if(_loc2_ is Skin_WuQi && _loc2_.parent is Player2)
            {
               this.who = _loc2_.parent;
               this.RL = (this.who as Player2).RL;
               this.gongJi_hp = this.who.skin.hpXX.getValue();
               this.gongJi_hp_MAX = this.who.skin.hpMax;
               this.硬直 = this.who.skin.硬直;
               this.times = this.who.skin.attTimes;
               this.runArr = [this.who.skin.runX,this.who.skin.runY,this.who.skin.runTime];
               AllHitXX.push(this);
               return;
            }
            if(_loc2_ is EnemySkin)
            {
               this.gongJi_hp = _loc2_.hpX;
               this.硬直 = _loc2_.硬直;
               this.runArr = [_loc2_.runX,_loc2_.runY,_loc2_.runTime];
               this.times = (_loc2_ as EnemySkin).attTimes;
               this.type = (_loc2_ as EnemySkin).type;
               this.space = (_loc2_ as EnemySkin).space;
               this.totalTime = (_loc2_ as EnemySkin).totalTime;
               this.numValue = (_loc2_ as EnemySkin).numValue;
            }
            else if(_loc2_ is Enemy)
            {
               this.who = _loc2_;
               this.RL = (this.who as Enemy).RL;
               AllHitXX.push(this);
               return;
            }
            _loc2_ = _loc2_.parent as MovieClip;
         }
      }
      
      private function onREMOVED_FROM_STAGE(param1:*) : *
      {
         var _loc2_:int = 0;
         if(AllHitXX)
         {
            _loc2_ = 0;
            while(_loc2_ < AllHitXX.length)
            {
               if(AllHitXX[_loc2_] == this)
               {
                  AllHitXX.splice(_loc2_,1);
               }
               _loc2_++;
            }
         }
      }
   }
}

