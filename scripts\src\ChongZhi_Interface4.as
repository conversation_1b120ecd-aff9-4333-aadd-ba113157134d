package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class ChongZhi_Interface4 extends MovieClip
   {
      public static var loadData:ClassLoader;
      
      public static var skin:MovieClip;
      
      public static var box_MC:MovieClip;
      
      private static var tooltip:ItemsTooltip;
      
      private static var tooltipID:int;
      
      public static var varXX:int = 12;
      
      public static var overTime:int = 20230813;
      
      public static var time0:String = "2023-08-02|12:30:01";
      
      public static var time1:String = "2023-08-13|23:59:59";
      
      public static var timeStr:String = "活动时间: 8月2日-8月13日";
      
      public static var lingQu_new:Array = [];
      
      public static var selYN:Boolean = false;
      
      public static var HDpointNUM15:VT = VT.createVT(-1);
      
      public static var loadName:String = "ChongZhi_new_v1720.swf";
      
      public static var czArr:Array = [0,0,100,300,800,1400,2000];
      
      public static var objIdArr:Array = [[],[63181,63106,63105,21221],[63458,63106,63140,21221],[63459,33213,63140,21222],[63460,33213,63289,63463],[63461,63290,63464,63463],[63462,33615,63464,63463]];
      
      public static var objNumArr:Array = [[],[1,1,1,1],[1,2,1,3],[1,10,2,1],[1,10,15,1],[1,15,1,2],[1,1,1,3]];
      
      public static var objTypeArr:Array = [[],[3,3,3,1],[3,3,3,1],[3,2,3,1],[3,2,3,3],[3,3,3,3],[3,2,3,3]];
      
      public static var objIdArr2:Array = [];
      
      public static var objTypeArr2:Array = [];
      
      public static var id2xArr:Array = [];
      
      public function ChongZhi_Interface4()
      {
         super();
      }
      
      public static function Open() : *
      {
         if(!lingQu_new[0] || lingQu_new[0] != varXX)
         {
            lingQu_new = [varXX];
         }
         if(!selYN)
         {
            Api_4399_All.GetTotalRecharged(15);
            selYN = true;
         }
         if(!skin)
         {
            Loading();
            return;
         }
         skin.y = 0;
         skin.x = 0;
         skin.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,daoJuOver);
         skin.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,daoJuOut);
         skin.congzhi_btn.addEventListener(MouseEvent.CLICK,Open_CongZhi);
         skin.close_btn.addEventListener(MouseEvent.CLICK,Close);
         if(!tooltip)
         {
            tooltip = new ItemsTooltip();
         }
         skin.addChild(tooltip);
         tooltip.visible = false;
         Show();
      }
      
      public static function Open_CongZhi(param1:* = null) : *
      {
         selYN = false;
         var _loc2_:GameStop = new GameStop();
         Main.ChongZhi();
      }
      
      public static function Close(param1:* = null) : *
      {
         selYN = false;
         skin.y = -5000;
         skin.x = -5000;
         skin.removeEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,daoJuOver);
         skin.removeEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,daoJuOut);
         skin.close_btn.removeEventListener(MouseEvent.CLICK,Close);
      }
      
      public static function onENTER_FRAME(param1:*) : *
      {
      }
      
      public static function Show() : *
      {
         var _loc3_:int = 0;
         var _loc4_:MovieClip = null;
         var _loc5_:MC_Btn = null;
         var _loc6_:Array = null;
         var _loc7_:Array = null;
         var _loc8_:Array = null;
         var _loc9_:MovieClip = null;
         var _loc10_:MovieClip = null;
         var _loc11_:int = 0;
         var _loc12_:Number = NaN;
         var _loc13_:int = 0;
         skin.time_txt.text = timeStr;
         skin.jindu_mc._mc.scaleX = 1;
         skin.jindu_txt.text = "查询中...";
         var _loc1_:int = 1;
         while(_loc1_ <= 6)
         {
            skin["txt_" + _loc1_].text = "充" + czArr[_loc1_] + "点券";
            _loc4_ = skin["mc_" + _loc1_];
            _loc5_ = new MC_Btn(_loc4_.lingqu_btn,"onCLICK_CZ");
            _loc5_.lingQuNum = _loc1_;
            if(lingQu_new[_loc1_])
            {
               _loc5_.goTo(5);
            }
            _loc6_ = objIdArr[_loc1_];
            _loc7_ = objNumArr[_loc1_];
            _loc8_ = objTypeArr[_loc1_];
            _loc3_ = 0;
            while(_loc3_ < 4)
            {
               _loc9_ = new Shop_picNEW();
               if(_loc4_["n_" + _loc3_])
               {
                  _loc10_ = _loc4_["n_" + _loc3_];
                  _loc11_ = _loc10_.getChildIndex(_loc10_.pic_xx);
                  _loc9_.x = _loc10_.pic_xx.x;
                  _loc9_.y = _loc10_.pic_xx.y;
                  _loc9_.name = "pic_xx";
                  _loc9_.gotoAndStop(getFrame(_loc8_[_loc3_],_loc6_[_loc3_]));
                  _loc10_.removeChild(_loc10_.pic_xx);
                  _loc10_.pic_xx = _loc9_;
                  _loc10_.addChild(_loc9_);
                  _loc10_.setChildIndex(_loc9_,_loc11_);
                  _loc10_.howNum.text = _loc7_[_loc3_];
                  _loc10_.idX = _loc6_[_loc3_];
                  _loc10_.typeX = _loc8_[_loc3_];
                  _loc9_.scaleY = 0.9;
                  _loc9_.scaleX = 0.9;
               }
               _loc3_++;
            }
            _loc1_++;
         }
         var _loc2_:int = HDpointNUM15.getValue();
         if(_loc2_ >= 0)
         {
            _loc12_ = _loc2_ / 2000;
            if(_loc12_ > 2000)
            {
               _loc12_ = 1;
            }
            _loc13_ = _loc12_ * 100;
            skin.jindu_mc._mc.scaleX = _loc12_;
            skin.jindu_txt.text = _loc2_ + "/2000";
         }
         _loc3_ = 1;
         while(_loc3_ <= 6)
         {
            _loc4_ = skin["mc_" + _loc3_];
            _loc4_.lingqu_btn.visible = false;
            _loc4_.lingqu_btn.gotoAndStop(1);
            if(_loc2_ >= 0)
            {
               _loc4_.lingqu_btn.visible = true;
            }
            if(_loc2_ >= czArr[_loc3_])
            {
               _loc4_.lingqu_btn.gotoAndStop(2);
               if(lingQu_new[_loc3_])
               {
                  _loc4_.lingqu_btn.gotoAndStop(5);
               }
            }
            _loc3_++;
         }
      }
      
      public static function LingQu_CZ(param1:int) : *
      {
         var _loc5_:* = undefined;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc2_:int = int(Main.player1.getBag().backOtherBagNum());
         var _loc3_:int = int(Main.player1.getBag().backGemBagNum());
         var _loc4_:int = int(Main.player1.getBag().backSuppliesBagNum());
         if(_loc2_ < 4 || _loc3_ < 1 || _loc4_ < 1)
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
            return;
         }
         for(_loc5_ in objIdArr[param1])
         {
            _loc6_ = int(objIdArr[param1][_loc5_]);
            _loc7_ = int(objTypeArr[param1][_loc5_]);
            _loc8_ = int(objNumArr[param1][_loc5_]);
            _loc9_ = 0;
            while(_loc9_ < _loc8_)
            {
               if(_loc7_ == 1)
               {
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(_loc6_));
               }
               else if(_loc7_ == 2)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(_loc6_));
               }
               else if(_loc7_ == 3)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(_loc6_));
               }
               _loc9_++;
            }
         }
         lingQu_new[param1] = true;
         Show();
         NewMC.Open("文字提示",Main._stage,470,400,90,0,true,1,"奖励物品已放入背包");
         Main.Save();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("Ui_ChongZhi") as Class;
         skin = new _loc2_();
         Main._stage.addChild(skin);
         Open();
      }
      
      public static function setObjArr() : *
      {
         id2xArr[194] = 63458;
         id2xArr[195] = 63459;
         id2xArr[196] = 63460;
         id2xArr[197] = 63461;
         id2xArr[198] = 63462;
         id2xArr[199] = 63463;
         objIdArr2[194] = [100815,100817,100819,100821,100823,100825,100827,100829,100831];
         objIdArr2[195] = [100816,100818,100820,100822,100824,100826,100828,100830,100832];
         objIdArr2[196] = [63453,63300,63309,63327,63376];
         objIdArr2[197] = [63465,63296,63369,63193,63295,63335,63294];
         objIdArr2[198] = [63209,63251,63266,63267,63280,63291,63328,63339,63375,63386,63310];
         objIdArr2[199] = [63162,63169,63171,63183,63197,63207,63246,63257,63268,63273,63337,63340,63235,63457];
         objTypeArr2[194] = [8,8,8,8,8,8,8,8,8];
         objTypeArr2[195] = [8,8,8,8,8,8,8,8,8];
         objTypeArr2[196] = [3,3,3,3,3];
         objTypeArr2[197] = [3,3,3,3,3,3,3];
         objTypeArr2[198] = [3,3,3,3,3,3,3,3,3,3,3];
         objTypeArr2[199] = [3,3,3,3,3,3,3,3,3,3,3,3,3,3];
      }
      
      public static function openBoX(param1:int, param2:MovieClip, param3:PlayerData) : *
      {
         var _loc8_:MovieClip = null;
         var _loc9_:MovieClip = null;
         var _loc10_:int = 0;
         setObjArr();
         var _loc4_:Class = ItemsPanel.loadData.getClass("自选礼包") as Class;
         box_MC = new _loc4_();
         box_MC.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,daoJuOver);
         box_MC.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,daoJuOut);
         box_MC.close_btn.addEventListener(MouseEvent.CLICK,CloseBoX);
         box_MC.typeXX = param1;
         box_MC.lingQu_btn.addEventListener(MouseEvent.CLICK,LingQu_BoX);
         box_MC.pData = param3;
         if(!tooltip)
         {
            tooltip = new ItemsTooltip();
         }
         box_MC.addChild(tooltip);
         tooltip.visible = false;
         param2.addChild(box_MC);
         var _loc5_:Array = objIdArr2[param1];
         var _loc6_:Array = objTypeArr2[param1];
         var _loc7_:int = 0;
         while(_loc7_ <= 13)
         {
            _loc8_ = box_MC["n_" + _loc7_];
            _loc8_.idX = _loc5_[_loc7_];
            _loc8_.typeX = _loc6_[_loc7_];
            if(_loc5_[_loc7_])
            {
               _loc9_ = new Shop_picNEW();
               _loc10_ = _loc8_.getChildIndex(_loc8_.pic_xx);
               _loc8_.addEventListener(MouseEvent.CLICK,xuanze);
               _loc9_.x = _loc8_.pic_xx.x - 3;
               _loc9_.y = _loc8_.pic_xx.y - 3;
               _loc9_.name = "pic_xx";
               _loc9_.gotoAndStop(getFrame(_loc6_[_loc7_],_loc5_[_loc7_]));
               _loc8_.removeChild(_loc8_.pic_xx);
               _loc8_.pic_xx = _loc9_;
               _loc8_.addChild(_loc9_);
               _loc8_.setChildIndex(_loc9_,_loc10_);
               _loc8_.howNum.text = "";
               if(param1 == 199)
               {
                  _loc8_.howNum.text = "20";
               }
            }
            else
            {
               _loc8_.visible = false;
            }
            _loc7_++;
         }
      }
      
      public static function LingQu_BoX(param1:MouseEvent) : *
      {
         var _loc2_:PlayerData = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:int = 0;
         var _loc12_:int = 0;
         if(param1.target.name == "lingQu_btn")
         {
            _loc2_ = param1.target.parent.pData;
            if(!_loc2_)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"出错了, 请稍后再试");
               CloseBoX();
               return;
            }
            _loc3_ = int(param1.target.parent.typeXX);
            _loc4_ = int(param1.target.parent.selNum);
            _loc5_ = int(objIdArr2[_loc3_][_loc4_]);
            _loc6_ = int(objTypeArr2[_loc3_][_loc4_]);
            _loc7_ = int(_loc2_.getBag().backSuppliesBagNum());
            _loc8_ = int(_loc2_.getBag().backGemBagNum());
            _loc9_ = int(_loc2_.getBag().backOtherBagNum());
            _loc10_ = int(_loc2_.getBag().backequipBagNum());
            if(_loc6_ == 1 && _loc7_ < 1)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包 消耗栏空间不足");
               return;
            }
            if(_loc6_ == 2 && _loc8_ < 1)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包 宝石栏空间不足");
               return;
            }
            if(_loc10_ < 1 && (_loc6_ == 0 || _loc6_ == 4 || _loc6_ == 5 || _loc6_ == 6 || _loc6_ == 8 || _loc6_ == 9))
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包 武器栏空间不足");
               return;
            }
            _loc11_ = 1;
            if(_loc3_ == 199)
            {
               _loc11_ = 20;
            }
            _loc2_.getBag().delOtherById(id2xArr[_loc3_]);
            _loc12_ = 0;
            while(_loc12_ < _loc11_)
            {
               if(_loc6_ == 0 || _loc6_ == 4 || _loc6_ == 5 || _loc6_ == 6 || _loc6_ == 8 || _loc6_ == 9)
               {
                  _loc2_.getBag().addEquipBag(EquipFactory.createEquipByID(_loc5_));
               }
               else if(_loc6_ == 1)
               {
                  _loc2_.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(_loc5_));
               }
               else if(_loc6_ == 2)
               {
                  _loc2_.getBag().addGemBag(GemFactory.creatGemById(_loc5_));
               }
               else if(_loc6_ == 3)
               {
                  _loc2_.getBag().addOtherobjBag(OtherFactory.creatOther(_loc5_));
               }
               _loc12_++;
            }
            NewMC.Open("文字提示",Main._stage,400,400,60,0,true,1,"领取成功 物品已放入背包");
            BagItemsShow.otherobjShow();
            CloseBoX();
            Main.Save();
         }
      }
      
      public static function CloseBoX(param1:* = null) : *
      {
         box_MC.removeEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,daoJuOver);
         box_MC.removeEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,daoJuOut);
         box_MC.close_btn.removeEventListener(MouseEvent.CLICK,CloseBoX);
         if(box_MC.parent)
         {
            box_MC.parent.removeChild(box_MC);
         }
         box_MC.visible = false;
      }
      
      public static function xuanze(param1:*) : *
      {
         var _loc2_:MovieClip = param1.target as MovieClip;
         var _loc3_:uint = uint(_loc2_.name.substr(2,2));
         box_MC["select"].x = _loc2_.x - 3;
         box_MC["select"].y = _loc2_.y - 3;
         box_MC.selNum = _loc3_;
      }
      
      public static function openBoX_200(param1:PlayerData) : *
      {
         if(NewPetPanel.bag.backPetBagNum() <= 0)
         {
            NewMC.Open("文字提示",Main._stage,480,360,30,0,true,2,"宠物背包已满!");
            return;
         }
         var _loc2_:Array = [17005,17010,17015,17020,17025,17030,17035,17040,17045,17050,17055,17060,17065,17070,17075,17080,17085,17090,17095,17100,17105,17110];
         var _loc3_:int = Math.random() * _loc2_.length;
         var _loc4_:int = int(_loc2_[_loc3_]);
         NewPetPanel.bag.addPetBag(PetEquip.creatPetEquip(_loc4_));
         param1.getBag().delOtherById(63464);
         BagItemsShow.otherobjShow();
         NewMC.Open("文字提示",Main._stage,480,360,30,0,true,2,"神权利器已放入宠物背包");
         Main.Save();
      }
      
      public static function getBoX_200() : PetEquip
      {
         var _loc1_:Array = [17005,17010,17015,17020,17025,17030,17035,17040,17045,17050,17055,17060,17065,17070,17075,17080,17085,17090,17095,17100,17105,17110];
         var _loc2_:int = Math.random() * _loc1_.length;
         var _loc3_:int = int(_loc1_[_loc2_]);
         return PetEquip.creatPetEquip(_loc3_);
      }
      
      public static function getFrame(param1:Number, param2:Number) : Number
      {
         var _loc3_:Object = null;
         if(param1 == 0 || param1 == 4 || param1 == 5 || param1 == 6 || param1 == 8 || param1 == 9)
         {
            _loc3_ = EquipFactory.createEquipByID(param2);
            return (_loc3_ as Equip).getFrame();
         }
         if(param1 == 1)
         {
            _loc3_ = SuppliesFactory.getSuppliesById(param2);
            return (_loc3_ as Supplies).getFrame();
         }
         if(param1 == 2)
         {
            _loc3_ = GemFactory.creatGemById(param2);
            return (_loc3_ as Gem).getFrame();
         }
         if(param1 == 3)
         {
            _loc3_ = OtherFactory.creatOther(param2);
            return (_loc3_ as Otherobj).getFrame();
         }
         return 1;
      }
      
      private static function addobj(param1:Number, param2:Number) : Object
      {
         var _loc3_:Object = null;
         if(param1 == 0 || param1 == 4 || param1 == 5 || param1 == 6 || param1 == 8 || param1 == 9)
         {
            _loc3_ = EquipFactory.createEquipByID(param2);
         }
         else if(param1 == 1)
         {
            _loc3_ = SuppliesFactory.getSuppliesById(param2);
         }
         else if(param1 == 2)
         {
            _loc3_ = GemFactory.creatGemById(param2);
         }
         else if(param1 == 3)
         {
            _loc3_ = OtherFactory.creatOther(param2);
         }
         return _loc3_;
      }
      
      private static function daoJuOut(param1:DaoJuEvent) : void
      {
         tooltip.visible = false;
      }
      
      private static function daoJuOver(param1:DaoJuEvent) : void
      {
         var _loc2_:String = param1.target.name.substr(0,2);
         var _loc3_:Number = Number(param1.target.name.substr(2,1));
         if(_loc2_ != "n_")
         {
            return;
         }
         tooltip.visible = true;
         tooltip.x = Main._stage.mouseX;
         tooltip.y = Main._stage.mouseY;
         if(tooltip.x > 600)
         {
            tooltip.x = Main._stage.mouseX - 200;
         }
         var _loc4_:int = int(param1.target.idX);
         var _loc5_:int = int(param1.target.typeX);
         var _loc6_:* = addobj(_loc5_,_loc4_);
         if(_loc6_ is Equip)
         {
            tooltip.equipTooltip(_loc6_);
         }
         else if(_loc6_ is Supplies)
         {
            tooltip.suppliesTooltip(_loc6_);
         }
         else if(_loc6_ is Gem)
         {
            tooltip.gemTooltip(_loc6_);
         }
         else if(_loc6_ is Otherobj)
         {
            tooltip.otherTooltip(_loc6_);
         }
      }
   }
}

