package com.hotpoint.braveManIII.repository.probability
{
   import com.hotpoint.braveManIII.Tool.*;
   import src.*;
   import src.tool.*;
   
   public class GemProbabilityFactory
   {
      public static var isProbabilityOk:Boolean;
      
      public static var probabilityArr:Array = [];
      
      public static var probabilityData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function GemProbabilityFactory()
      {
         super();
      }
      
      public static function creatProbabilltyData() : void
      {
         myXml = XMLAsset.createXML(InData.GemProbabiltyData);
         var _loc1_:GemProbabilityFactory = new GemProbabilityFactory();
         _loc1_.creatLoard();
      }
      
      public static function getProbabilltyByFallId(param1:Number) : EquipBaseProbabilityData
      {
         var _loc3_:EquipBaseProbabilityData = null;
         var _loc2_:EquipBaseProbabilityData = null;
         for each(_loc3_ in probabilityData)
         {
            if(_loc3_.getfallLevel() == param1)
            {
               TiaoShi.txtShow("data.getfallLevel() = ?? " + _loc3_.getfallLevel());
               TiaoShi.txtShow("data._goldArr = ?? " + _loc3_._goldArr);
               _loc2_ = _loc3_;
            }
         }
         if(_loc2_ == null)
         {
            TiaoShi.txtShow("找不到此掉落等级!!");
         }
         return _loc2_;
      }
      
      public static function getPorbabillty(param1:Number, param2:Number) : Number
      {
         return getProbabilltyByFallId(param1).getProbabil(param2);
      }
      
      public static function getGold(param1:Number, param2:Number) : Number
      {
         return getProbabilltyByFallId(param1).getGold(param2);
      }
      
      public function EquipProbabilityFactory() : *
      {
      }
      
      private function creatLoard() : *
      {
         this.xmlLoaded();
      }
      
      internal function xmlLoaded() : *
      {
         var _loc1_:XML = null;
         var _loc2_:Number = NaN;
         var _loc3_:XMLList = null;
         var _loc4_:XMLList = null;
         var _loc5_:Array = null;
         var _loc6_:Array = null;
         var _loc7_:EquipBaseProbabilityData = null;
         for each(_loc1_ in myXml.强化成功率)
         {
            _loc2_ = Number(_loc1_.掉落等级);
            _loc3_ = _loc1_.成功率;
            _loc4_ = _loc1_.金币;
            _loc5_ = [];
            _loc6_ = [];
            _loc5_.push(_loc3_.强化等级一);
            _loc5_.push(_loc3_.强化等级二);
            _loc5_.push(_loc3_.强化等级三);
            _loc5_.push(_loc3_.强化等级四);
            _loc5_.push(_loc3_.强化等级五);
            _loc6_.push(_loc4_.强化等级一);
            _loc6_.push(_loc4_.强化等级二);
            _loc6_.push(_loc4_.强化等级三);
            _loc6_.push(_loc4_.强化等级四);
            _loc6_.push(_loc4_.强化等级五);
            _loc7_ = EquipBaseProbabilityData.creatProbabilityData(_loc2_,_loc5_,_loc6_);
            probabilityData.push(_loc7_);
         }
         isProbabilityOk = true;
      }
   }
}

