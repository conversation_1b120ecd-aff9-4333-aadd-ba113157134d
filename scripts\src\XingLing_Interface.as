package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src._data.*;
   import src.tool.*;
   
   public class XingLing_Interface extends MovieClip
   {
      private static var loadData:ClassLoader;
      
      private static var _this:XingLing_Interface;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "XLUP_v972.swf";
      
      public static var selNumMax:uint = 12;
      
      public static var sel_LV_NumMax:uint = 10;
      
      public static var UP_Fun_YES_go:Boolean = false;
      
      public static var UP_Fun_YES_go2:Boolean = false;
      
      public static var UP_Fun_YES_go3:Boolean = false;
      
      public static var upOK:Boolean = false;
      
      public static var saveOK:Boolean = false;
      
      private var skin:MovieClip;
      
      private var xuQiuObjArr:Array = [0,63162,63169,63171,63183,63197,63207,63246,63257,63268,63273,63337,63340];
      
      private var xArr:Array = ["","巨蟹座能源: ","双鱼座能源: ","水瓶座能源: ","射手座能源: ","白羊座能源: ","金牛座能源: ","天蝎座能源: ","狮子座能源: ","处女座能源: ","天秤座能源: ","双子座能源: ","魔羯座能源: "];
      
      private var selNum:VT = VT.createVT(1);
      
      private var sel_LV_Num:VT = VT.createVT(1);
      
      private var up_Money_1:Boolean = false;
      
      private var up_Money_2:Boolean = false;
      
      internal var time:uint = 0;
      
      public function XingLing_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      private static function InitOpen() : *
      {
         var _loc1_:XingLing_Interface = new XingLing_Interface();
         Main._stage.addChild(_loc1_);
         OpenYN = true;
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.y = 0;
            _this.x = 0;
            _this.Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.y = 5000;
            _this.x = 5000;
         }
      }
      
      public static function UP_Fun_YES() : *
      {
         var _loc1_:* = 0;
         if(UP_Fun_YES_go)
         {
            UP_Fun_YES_go = false;
            _loc1_ = uint(XingLingFactory.UP(_this.selNum.getValue(),_this.sel_LV_Num.getValue()));
            _this.skin._BLACK_mc.visible = false;
            _this.Show();
            _this.ShowSanSuo(_loc1_);
         }
      }
      
      public static function UP2_Fun_YES() : *
      {
         var _loc1_:Array = null;
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         var _loc4_:* = 0;
         if(UP_Fun_YES_go3)
         {
            UP_Fun_YES_go3 = false;
            XingLingFactory.UP2(_this.selNum.getValue(),_this.sel_LV_Num.getValue());
            _loc1_ = XingLingFactory.Get_LV(_this.selNum.getValue(),_this.sel_LV_Num.getValue());
            _loc2_ = uint(_loc1_[0]);
            _loc3_ = uint(_loc1_[1]);
            _loc4_ = _this.sel_LV_Num.getValue() + 1;
            if(_loc3_ == 10 && _loc4_ <= sel_LV_NumMax)
            {
               _this.sel_LV_Num.setValue(_loc4_);
               _this.up_Money_2 = false;
               _this.up_Money_1 = false;
            }
            _this.skin._BLACK_mc.visible = false;
            _this.Show();
         }
      }
      
      public static function UpPinZhi_Fun_YES() : *
      {
         if(UP_Fun_YES_go2)
         {
            upOK = XingLingFactory.QiangHua(_this.selNum.getValue(),_this.sel_LV_Num.getValue());
            UP_Fun_YES_go2 = false;
            _this.ShowSanSuo(0);
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("Skin") as Class;
         this.skin = new _loc2_();
         this.skin.addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addChild(this.skin);
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function Close(param1:* = null) : *
      {
         this.visible = false;
         this.y = 5000;
         this.x = 5000;
      }
      
      private function onADDED_TO_STAGE(param1:*) : *
      {
         var _loc2_:Number = 0;
         this.skin._BLACK_mc.visible = false;
         this.skin._jinJieXuQiu_mc.visible = false;
         this.skin._jinJieXuQiu_mc.close_btn.addEventListener(MouseEvent.CLICK,this.JinJieClose);
         this.skin._jinJieXuQiu_mc.ok_btn.addEventListener(MouseEvent.CLICK,this.JinJieOK);
         this.skin._jinJieXuQiu_mc.dianQuan_btn.addEventListener(MouseEvent.CLICK,this.JinJieOK2);
         this.skin._jinJieXuQiu_mc.dianQuan_btn.addEventListener(MouseEvent.MOUSE_MOVE,this.JinJieOK2_MOUSE_MOVE);
         this.skin._jinJieXuQiu_mc.dianQuan_btn.addEventListener(MouseEvent.MOUSE_OUT,this.JinJieOK2_MOUSE_OUT);
         this.skin.close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         _loc2_ = 1;
         while(_loc2_ <= selNumMax)
         {
            this.skin["sel_btn_" + _loc2_].addEventListener(MouseEvent.CLICK,this.SEL_XingZuo_FUN);
            _loc2_++;
         }
         _loc2_ = 1;
         while(_loc2_ <= sel_LV_NumMax)
         {
            this.skin["sel2_" + _loc2_].visible = false;
            this.skin["sel2_" + _loc2_].mouseChildren = false;
            this.skin["sel2_" + _loc2_].addEventListener(MouseEvent.CLICK,this.SEL_LV_FUN);
            this.skin["sel2_" + _loc2_].addEventListener(MouseEvent.MOUSE_MOVE,this.SEL_LV_FUN_MOUSE_MOVE);
            this.skin["sel2_" + _loc2_].addEventListener(MouseEvent.MOUSE_OUT,this.SEL_LV_FUN_MOUSE_OUT);
            _loc2_++;
         }
         this.skin["up_btn"].addEventListener(MouseEvent.CLICK,this.UP_Fun);
         this.skin["up2_btn"].addEventListener(MouseEvent.CLICK,this.UP_Fun2);
         this.skin["A_mc"].addEventListener(MouseEvent.CLICK,this.Sel_Money_A);
         this.skin["B_mc"].addEventListener(MouseEvent.CLICK,this.Sel_Money_B);
         this.skin["C_mc"].addEventListener(MouseEvent.CLICK,this.Sel_Money_C);
         this.skin["qiangHua_btn"].addEventListener(MouseEvent.CLICK,this.QiangHua_Fun);
      }
      
      private function Sel_Money_A(param1:* = null) : *
      {
         this.up_Money_2 = true;
         this.up_Money_1 = true;
         this.skin["A_mc"]["yes_mc"].visible = true;
         this.skin["B_mc"]["yes_mc"].visible = false;
         this.skin["C_mc"]["yes_mc"].visible = false;
      }
      
      private function Sel_Money_B(param1:* = null) : *
      {
         this.up_Money_2 = false;
         this.up_Money_1 = false;
         this.skin["A_mc"]["yes_mc"].visible = false;
         this.skin["B_mc"]["yes_mc"].visible = true;
         this.skin["C_mc"]["yes_mc"].visible = true;
      }
      
      private function Sel_Money_C(param1:* = null) : *
      {
         this.up_Money_2 = false;
         this.up_Money_1 = false;
         this.skin["A_mc"]["yes_mc"].visible = false;
         this.skin["B_mc"]["yes_mc"].visible = true;
         this.skin["C_mc"]["yes_mc"].visible = true;
      }
      
      private function SelObj(param1:uint) : *
      {
         if(param1 == 10)
         {
            this.skin["B_mc"].visible = false;
            this.skin["C_mc"].visible = true;
         }
         else
         {
            this.skin["B_mc"].visible = true;
            this.skin["C_mc"].visible = false;
         }
         if(Boolean(this.up_Money_1) || Boolean(this.up_Money_2))
         {
            this.Sel_Money_A();
         }
         else
         {
            this.Sel_Money_B();
            this.Sel_Money_C();
         }
      }
      
      private function Show() : *
      {
         var _loc1_:Number = 0;
         var _loc8_:* = 0;
         if(Main.guanKa[this.selNum.getValue() + 17] > 0)
         {
            this.skin["back_mc"].visible = false;
         }
         else
         {
            this.skin["back_mc"].visible = true;
         }
         _loc1_ = 1;
         while(_loc1_ <= selNumMax)
         {
            this.skin["sel_" + _loc1_].visible = false;
            if(_loc1_ == this.selNum.getValue())
            {
               this.skin["sel_" + _loc1_].visible = true;
            }
            if(Main.guanKa[_loc1_ + 17] > 0)
            {
               this.skin["no_Sel_" + _loc1_].visible = false;
            }
            _loc1_++;
         }
         var _loc2_:Array = XingLingFactory.Get_LV_Data_Str(this.selNum.getValue(),this.sel_LV_Num.getValue());
         var _loc3_:Array = XingLingFactory.Get_LV(this.selNum.getValue(),this.sel_LV_Num.getValue());
         var _loc4_:uint = _loc3_[0] + 1;
         var _loc5_:uint = uint(_loc3_[1]);
         this.skin["qiangHua_btn"].visible = false;
         if(_loc5_ == 10)
         {
            this.skin["up_btn"].visible = false;
            _loc8_ = uint(XingLingFactory.Get_one_color(this.selNum.getValue(),this.sel_LV_Num.getValue()));
            if(_loc8_ < 5 || Boolean(Main.isVip()) && _loc8_ < 6)
            {
               this.skin["qiangHua_btn"].visible = true;
            }
         }
         else
         {
            this.skin["up_btn"].visible = true;
         }
         _loc1_ = 1;
         while(_loc1_ <= 10)
         {
            this.skin["shuXing_" + _loc1_].text = _loc2_[_loc1_];
            this.TextColor(this.skin["shuXing_" + _loc1_],_loc4_);
            if(_loc5_ >= _loc1_)
            {
               this.skin["shuXing_mc_" + _loc1_].gotoAndStop(_loc4_);
            }
            else
            {
               this.skin["shuXing_mc_" + _loc1_].gotoAndStop(1);
            }
            _loc1_++;
         }
         var _loc6_:uint = uint(XingLingFactory.Get_LV2(this.selNum.getValue()));
         var _loc7_:Array = XingLingFactory.Get_LV_color(this.selNum.getValue());
         _loc1_ = 1;
         while(_loc1_ <= sel_LV_NumMax)
         {
            this.skin["sel2_" + _loc1_]["sel_this"].visible = false;
            if(_loc1_ <= _loc6_)
            {
               this.skin["sel2_" + _loc1_].visible = true;
            }
            else
            {
               this.skin["sel2_" + _loc1_].visible = false;
            }
            this.skin["sel2_" + _loc1_].gotoAndStop(_loc7_[_loc1_] - 1);
            _loc1_++;
         }
         this.skin["sel2_" + this.sel_LV_Num.getValue()]["sel_this"].visible = true;
         this.skin["up2_btn"].visible = false;
         if(this.sel_LV_Num.getValue() + 1 <= sel_LV_NumMax && _loc7_[this.sel_LV_Num.getValue() + 1] <= 1)
         {
            this.skin["up2_btn"].visible = true;
         }
         this.XuQiuShow(_loc5_);
         this.SelObj(_loc5_);
         this.ShowTolal_Data();
      }
      
      private function XuQiuShow(param1:uint) : *
      {
         var _loc2_:String = null;
         var _loc3_:Array = null;
         var _loc4_:Array = null;
         var _loc5_:* = undefined;
         var _loc6_:* = 0;
         _loc2_ = this.xArr[this.selNum.getValue()];
         if(param1 == 10)
         {
            _loc3_ = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
            this.skin["C_mc"]._txt.text = "星源方块:1/" + this.getXuQiuNum_Other(63235);
            this.skin["A_mc"]._txt.text = "点券:9/" + Shop4399.moneyAll.getValue();
         }
         else
         {
            _loc4_ = XingLingFactory.Sel_XuQiu_up1(this.selNum.getValue());
            _loc5_ = this.xuQiuObjArr[this.selNum.getValue()];
            _loc6_ = uint(_loc4_[1]);
            if(Main.isVip())
            {
               _loc6_ /= 2;
            }
            this.skin["B_mc"]._txt.text = _loc2_ + _loc4_[0] + "/" + this.getXuQiuNum_Other(_loc5_) + "  击杀点:" + _loc6_ + "/" + this.getXuQiuNumKill_min();
            this.skin["A_mc"]._txt.text = "点券:" + _loc4_[2] + "/" + Shop4399.moneyAll.getValue();
         }
      }
      
      private function TextColor(param1:*, param2:uint) : *
      {
         if(param2 == 3)
         {
            TextTool.ColorX(param1,"0x66FF00");
         }
         else if(param2 == 4)
         {
            TextTool.ColorX(param1,"0x3399FF");
         }
         else if(param2 == 5)
         {
            TextTool.ColorX(param1,"0xFF3399");
         }
         else if(param2 == 6)
         {
            TextTool.ColorX(param1,"0xFFCC00");
         }
         else if(param2 == 7)
         {
            TextTool.ColorX(param1,"0xFF0000");
         }
      }
      
      private function ShowTolal_Data() : *
      {
         var _loc1_:Array = XingLingFactory.GetTolal_DataStr();
         var _loc2_:Number = 1;
         while(_loc2_ <= 8)
         {
            this.skin["shuXingNum_" + _loc2_].text = _loc1_[_loc2_];
            this.skin["shuXingNum_2" + _loc2_].text = _loc1_[_loc2_ + 8];
            this.skin["jinSe_txt"].text = "已点亮" + XingLingFactory.dianLiangNum * 10 + "颗";
            this.skin["jindu"]["_mc"].scaleX = XingLingFactory.dianLiangNumX;
            _loc2_++;
         }
      }
      
      private function ShowSanSuo(param1:uint = 1) : *
      {
         var _loc2_:Number = 0;
         if(param1 == -1)
         {
            _loc2_ = 1;
            while(_loc2_ <= 10)
            {
               this.skin["shuXing_mc_" + _loc2_]["show_mc"].gotoAndStop(1);
               _loc2_++;
            }
         }
         else if(param1 == 0)
         {
            _loc2_ = 1;
            while(_loc2_ <= 10)
            {
               this.skin["shuXing_mc_" + _loc2_]["show_mc"].gotoAndPlay(2);
               _loc2_++;
            }
         }
         else
         {
            this.skin["shuXing_mc_" + param1]["show_mc"].gotoAndPlay(2);
         }
      }
      
      private function SEL_XingZuo_FUN(param1:MouseEvent) : *
      {
         var _loc2_:uint = uint((param1.target.name as String).substr(8,2));
         this.selNum.setValue(_loc2_);
         this.sel_LV_Num.setValue(1);
         _this.up_Money_2 = false;
         _this.up_Money_1 = false;
         this.Show();
      }
      
      private function SEL_LV_FUN(param1:MouseEvent) : *
      {
         var _loc2_:Number = 1;
         while(_loc2_ <= sel_LV_NumMax)
         {
            this.skin["sel2_" + _loc2_]["sel_this"].visible = false;
            _loc2_++;
         }
         var _loc3_:uint = uint((param1.target.name as String).substr(5,2));
         this.sel_LV_Num.setValue(_loc3_);
         this.skin["sel2_" + _loc3_]["sel_this"].visible = true;
         this.Show();
      }
      
      private function SEL_LV_FUN_MOUSE_OUT(param1:MouseEvent) : *
      {
         var _loc2_:uint = uint((param1.target.name as String).substr(5,2));
         if(this.sel_LV_Num.getValue() != _loc2_)
         {
            this.skin["sel2_" + _loc2_]["sel_this"].visible = false;
         }
      }
      
      private function SEL_LV_FUN_MOUSE_MOVE(param1:MouseEvent) : *
      {
         var _loc2_:uint = uint((param1.target.name as String).substr(5,2));
         this.skin["sel2_" + _loc2_]["sel_this"].visible = true;
      }
      
      private function UP_Fun(param1:MouseEvent) : *
      {
         var _loc3_:* = undefined;
         var _loc4_:Array = null;
         var _loc5_:* = 0;
         var _loc2_:Array = XingLingFactory.Sel_XuQiu_up1(this.selNum.getValue());
         if(this.up_Money_1)
         {
            if(Shop4399.moneyAll.getValue() >= _loc2_[2])
            {
               this.skin._BLACK_mc.visible = true;
               Api_4399_All.BuyObj(_loc2_[3]);
               UP_Fun_YES_go = true;
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"点券不足");
            }
         }
         else
         {
            _loc3_ = this.xuQiuObjArr[this.selNum.getValue()];
            _loc4_ = this.XuQiuNum_Other(_loc3_,_loc2_[0]);
            _loc5_ = uint(_loc2_[1]);
            if(Main.isVip())
            {
               _loc5_ /= 2;
            }
            if(_loc4_[0] != 0 && Boolean(this.XuQiuNum_kill(_loc5_)))
            {
               this.KouChuObj(_loc4_);
               this.KouChukill(_loc5_);
               UP_Fun_YES_go = true;
               UP_Fun_YES();
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"材料不足,可选择点券升级");
            }
         }
      }
      
      private function UP_Fun2(param1:MouseEvent) : *
      {
         var _loc3_:String = null;
         this.skin._jinJieXuQiu_mc.visible = true;
         this.skin._jinJieXuQiu_mc.dianQuan_mc.visible = false;
         var _loc2_:Array = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
         _loc3_ = this.xArr[this.selNum.getValue()];
         var _loc4_:* = this.xuQiuObjArr[this.selNum.getValue()];
         this.skin._jinJieXuQiu_mc.xuQiu_num1.text = _loc3_ + _loc2_[0] + "/" + this.getXuQiuNum_Other(_loc4_);
         this.skin._jinJieXuQiu_mc.xuQiu_num2.text = "圣光水晶: " + _loc2_[1] + "/" + this.getXuQiuNum_Other(63138);
      }
      
      private function JinJieClose(param1:MouseEvent) : *
      {
         this.skin._jinJieXuQiu_mc.visible = false;
      }
      
      private function JinJieOK(param1:MouseEvent) : *
      {
         this.skin._jinJieXuQiu_mc.visible = false;
         var _loc2_:* = this.xuQiuObjArr[this.selNum.getValue()];
         var _loc3_:Array = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
         var _loc4_:Array = this.XuQiuNum_Other(_loc2_,_loc3_[0]);
         var _loc5_:Array = this.XuQiuNum_Other(63138,_loc3_[1]);
         if(_loc4_[0] != 0 && _loc5_[0] != 0)
         {
            this.KouChuObj(_loc4_);
            this.KouChuObj(_loc5_);
            UP_Fun_YES_go3 = true;
            UP2_Fun_YES();
         }
         else
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"材料不足");
         }
      }
      
      private function JinJieOK2(param1:MouseEvent) : *
      {
         this.skin._jinJieXuQiu_mc.visible = false;
         var _loc2_:* = this.xuQiuObjArr[this.selNum.getValue()];
         var _loc3_:Array = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
         var _loc4_:uint = uint(_loc3_[2]);
         if(Shop4399.moneyAll.getValue() >= _loc3_[2])
         {
            this.skin._BLACK_mc.visible = true;
            Api_4399_All.BuyObj(_loc3_[3]);
            UP_Fun_YES_go3 = true;
         }
         else
         {
            NewMC.Open("文字提示",this,400,400,90,0,true,2,"点券不足");
         }
      }
      
      private function JinJieOK2_MOUSE_MOVE(param1:*) : *
      {
         this.skin._jinJieXuQiu_mc.dianQuan_mc.visible = true;
         var _loc2_:* = this.xuQiuObjArr[this.selNum.getValue()];
         var _loc3_:Array = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
         var _loc4_:uint = uint(_loc3_[2]);
         this.skin._jinJieXuQiu_mc.dianQuan_mc.dianQuan_txt.text = "需消耗" + _loc4_ + "点券";
      }
      
      private function JinJieOK2_MOUSE_OUT(param1:*) : *
      {
         this.skin._jinJieXuQiu_mc.dianQuan_mc.visible = false;
      }
      
      private function QiangHua_Fun(param1:MouseEvent) : *
      {
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         var _loc4_:Array = null;
         this.skin["qiangHua_btn"].visible = false;
         this.skin["_BLACK_mc"].visible = true;
         if(this.up_Money_1)
         {
            _loc2_ = uint(InitData.xingLing_num9.getValue());
            _loc3_ = uint(InitData.xingLing_ID55.getValue());
            if(Shop4399.moneyAll.getValue() >= _loc2_)
            {
               this.skin._BLACK_mc.visible = true;
               Api_4399_All.BuyObj(_loc3_);
               UP_Fun_YES_go2 = true;
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"点券不足");
               this.skin["qiangHua_btn"].visible = true;
               this.skin._BLACK_mc.visible = false;
            }
         }
         else
         {
            _loc4_ = this.XuQiuNum_Other(63235,InitData.BuyNum_1.getValue());
            if(_loc4_[0] != 0)
            {
               this.KouChuObj(_loc4_);
               UP_Fun_YES_go2 = true;
               UpPinZhi_Fun_YES();
               Main.Save();
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"材料不足,可选择点券升级");
               this.skin["qiangHua_btn"].visible = true;
               this.skin._BLACK_mc.visible = false;
            }
         }
         addEventListener(Event.ENTER_FRAME,this.onTime);
      }
      
      private function getXuQiuNumKill_min() : uint
      {
         var _loc1_:* = 0;
         _loc1_ = uint(Main.player1.killPoint.getValue());
         if(Boolean(Main.P1P2) && _loc1_ > Main.player2.killPoint.getValue())
         {
            _loc1_ = uint(Main.player2.killPoint.getValue());
         }
         return _loc1_;
      }
      
      private function getXuQiuNum_Other(param1:uint) : uint
      {
         var _loc2_:* = 0;
         _loc2_ += Main.player1.getBag().getOtherobjNum(param1);
         if(Main.P1P2)
         {
            _loc2_ += Main.player2.getBag().getOtherobjNum(param1);
         }
         return _loc2_;
      }
      
      private function XuQiuNum_Other(param1:uint, param2:uint) : Array
      {
         var _loc3_:* = 0;
         var _loc5_:* = 0;
         if(Main.player1.getBag().getOtherobjNum(param1) >= param2)
         {
            return [param1,param2,0];
         }
         if(Main.P1P2)
         {
            _loc3_ = uint(Main.player1.getBag().getOtherobjNum(param1));
            _loc5_ = param2 - Main.player1.getBag().getOtherobjNum(param1);
            if(Main.player2.getBag().getOtherobjNum(param1) >= _loc5_)
            {
               return [param1,_loc3_,_loc5_];
            }
            return [0];
         }
         return [0];
      }
      
      private function KouChuObj(param1:Array) : *
      {
         if(param1[1] > 0)
         {
            Main.player1.getBag().delOtherById(param1[0],param1[1]);
         }
         if(param1[2] > 0)
         {
            Main.player2.getBag().delOtherById(param1[0],param1[2]);
         }
      }
      
      private function XuQiuNum_kill(param1:uint) : Boolean
      {
         if(!Main.P1P2 && Main.player1.killPoint.getValue() >= param1)
         {
            return true;
         }
         if(Boolean(Main.P1P2) && Main.player1.killPoint.getValue() >= param1 && Main.player2.killPoint.getValue() >= param1)
         {
            return true;
         }
         return false;
      }
      
      private function KouChukill(param1:uint) : *
      {
         Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - param1);
         if(Main.P1P2)
         {
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - param1);
         }
      }
      
      private function onTime(param1:*) : *
      {
         var _loc2_:* = 0;
         ++this.time;
         if(this.time > 50 && saveOK)
         {
            this.skin._BLACK_mc.visible = false;
            saveOK = false;
            this.time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onTime);
            _loc2_ = uint(XingLingFactory.Get_one_color(this.selNum.getValue(),this.sel_LV_Num.getValue()));
            if(upOK)
            {
               NewMC.Open("文字提示",_this,400,400,30,0,true,2,"强化成功!");
            }
            else
            {
               NewMC.Open("文字提示",_this,400,400,30,0,true,2,"强化失败!");
            }
            this.Show();
         }
      }
   }
}

