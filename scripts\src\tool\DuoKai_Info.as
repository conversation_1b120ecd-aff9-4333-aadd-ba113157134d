package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   
   public class DuoKai_Info extends MovieClip
   {
      private static var _this:DuoKai_Info;
      
      public static var _noSave:Boolean = false;
      
      public function DuoKai_Info()
      {
         super();
      }
      
      public static function Open(param1:int = 1, param2:String = "") : *
      {
         var _loc3_:* = new DuoKai_Info();
         Main._stage.addChild(_loc3_);
         _loc3_.gotoAndStop(param1);
         _noSave = true;
         _loc3_._txt.text = "uid:" + Main.userId + "/" + param2;
         var _loc4_:uint = uint(setTimeout(TimeFun,2000));
      }
      
      public static function TimeFun() : void
      {
         Main._stage.frameRate = 0;
      }
   }
}

