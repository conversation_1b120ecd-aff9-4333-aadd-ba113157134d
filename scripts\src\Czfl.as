package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   
   public class Czfl extends MovieClip
   {
      public static var _this:Czfl;
      
      public function Czfl()
      {
         super();
         this["close_btn"].addEventListener(MouseEvent.CLICK,this.onclose);
         this["go_btn"].addEventListener(MouseEvent.CLICK,this.OpenWEB);
      }
      
      public static function Open() : *
      {
         if(!_this)
         {
            _this = new Czfl();
         }
         Main._this.addChild(_this);
         _this.y = 0;
         _this.x = 0;
      }
      
      public function onclose(param1:*) : *
      {
         this.visible = false;
         this.y = -5000;
         this.x = -5000;
      }
      
      public function OpenWEB(param1:*) : *
      {
         Main.ChongZhi();
         _this.y = -5000;
         _this.x = -5000;
      }
   }
}

