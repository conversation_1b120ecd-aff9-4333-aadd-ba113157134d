package src
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class SixOne_Interface extends MovieClip
   {
      public static var loadData:ClassLoader;
      
      private static var _this:SixOne_Interface;
      
      public static var guankaNum:int;
      
      private static var name:String;
      
      public static var banben:int = 1;
      
      public static var banben_save:int = 0;
      
      public static var dayTimes2021:VT = VT.createVT(0);
      
      public static var nowdate2021:VT = VT.createVT(0);
      
      public static var okTimes2021:VT = VT.createVT(0);
      
      public static var state2021:VT = VT.createVT(0);
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "HD61_v2.swf";
      
      private static var allguanka:Array = [];
      
      public static var booltemp:Boolean = false;
      
      private static var nameArr:Array = [[1,"落月之原"],[2,"落月之森"],[3,"冰雪废墟"],[4,"死亡流沙"],[5,"万年雪山"],[6,"废弃都市"],[7,"火山的噩梦"],[8,"堕落城堡"],[9,"幽灵船"],[10,"机械城"],[11,"雪狼巢穴"],[12,"火之祭坛"],[13,"暗影城"],[14,"暗夜遗迹"],[15,"机械试验场"],[16,"熔岩城堡"],[51,"艾尔之海"],[52,"安塔利亚"],[53,"阿肯色"],[54,"雅利安"],[55,"波塞笛亚"]];
      
      private static var timetemp:int = 0;
      
      private static var jlArr:Array = [[1,21221],[2,33314],[2,33510],[3,63181],[3,63210],[3,63235],[3,63102],[3,63203],[3,63155],[3,63100]];
      
      private var skin:MovieClip;
      
      private var tishi:MovieClip;
      
      public function SixOne_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      private static function InitOpen() : *
      {
         var _loc1_:SixOne_Interface = new SixOne_Interface();
         Main._stage.addChild(_loc1_);
         OpenYN = true;
      }
      
      public static function Open() : *
      {
         if(banben_save != banben)
         {
            banben_save = banben;
            dayTimes2021 = VT.createVT(0);
            nowdate2021 = VT.createVT(0);
            okTimes2021 = VT.createVT(0);
            state2021 = VT.createVT(0);
         }
         if(_this)
         {
            _this.visible = true;
            _this.skin.visible = true;
            _this.tishi.visible = false;
            _this.y = 0;
            _this.x = 0;
            showAll();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.skin.visible = false;
         }
      }
      
      private static function guanka_rdm() : int
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         allguanka = [];
         _loc2_ = 1;
         while(_loc2_ <= 16)
         {
            allguanka.push(Main.guanKa[_loc2_]);
            _loc2_++;
         }
         _loc2_ = 51;
         while(_loc2_ <= 54)
         {
            allguanka.push(Main.guanKa[_loc2_]);
            _loc2_++;
         }
         for(_loc2_ in allguanka)
         {
            if(allguanka[_loc2_] != 0)
            {
               _loc1_++;
            }
         }
         _loc3_ = Math.floor(Math.random() * _loc1_);
         if(_loc3_ > 15)
         {
            _loc3_ += 34;
         }
         return _loc3_ + 1;
      }
      
      private static function getName(param1:int) : String
      {
         var _loc2_:* = undefined;
         for(_loc2_ in nameArr)
         {
            if(nameArr[_loc2_][0] == param1)
            {
               return nameArr[_loc2_][1];
            }
         }
         return undefined;
      }
      
      private static function showAll() : *
      {
         if(state2021.getValue() != 1)
         {
            guankaNum = guanka_rdm();
            name = getName(guankaNum);
         }
         if(nowdate2021.getValue() < Main.serverTime.getValue())
         {
            nowdate2021 = Main.serverTime;
            dayTimes2021.setValue(0);
         }
         if(dayTimes2021.getValue() < 10)
         {
            _this.skin["ok_btn"].visible = true;
            _this.skin["no_btn"].visible = true;
            if(state2021.getValue() == 0 || state2021.getValue() == 3)
            {
               if(dayTimes2021.getValue() == 0)
               {
                  _this.skin["txt_1"].text = "哇哇哇，好激动哦~我看到了真正的勇士了！你愿意带着我去探险吗？只要你能带我去探险，并教我战斗技巧，我就把我收藏的好东西给你。在我来这里玩的这段时间内，若能带我探险的次数超过80次的话，到时我将会把我最珍贵的宝贝给你哦~是我最珍贵的哦！嘿嘿。";
                  _this.skin["txt_2"].text = "我今天还想去" + (10 - dayTimes2021.getValue()) + "个地方，带我到" + name + "冒险吧，回来后我会送你一个好东西哦。  " + okTimes2021.getValue();
               }
               else
               {
                  _this.skin["txt_1"].text = "哈哈哈哈，真是太好玩太刺激了，你能带我去下一个地点玩玩么?";
                  _this.skin["txt_2"].text = "我今天还想去" + (10 - dayTimes2021.getValue()) + "个地方，带我到" + name + "冒险吧，回来后我会送你一个好东西哦。  " + okTimes2021.getValue();
               }
            }
            else if(state2021.getValue() == 1)
            {
               _this.skin["txt_1"].text = "刚才去的那里根本不是任务地点，你还是带我去任务地点吧。";
               _this.skin["txt_2"].text = "我今天还想去" + (10 - dayTimes2021.getValue()) + "个地方，带我到" + name + "冒险吧，回来后我会送你一个好东西哦。  " + okTimes2021.getValue();
            }
            else if(state2021.getValue() == 2)
            {
               _this.skin["txt_1"].text = "你根本就不是勇士，太差劲了！跟着你去冒险真不安全……你还是带我去别的地方玩吧。";
               _this.skin["txt_2"].text = "我今天还想去" + (10 - dayTimes2021.getValue()) + "个地方，带我到" + name + "冒险吧，回来后我会送你一个好东西哦。  " + okTimes2021.getValue();
            }
         }
         if(dayTimes2021.getValue() == 10 && state2021.getValue() == 3)
         {
            _this.skin["txt_1"].text = "咩哈哈哈，今天玩的好开心阿。谢谢你阿，再多带我玩几天，我就把我身后的这个东西送给你。";
            _this.skin["txt_2"].text = "今天玩够啦，明天再来吧！  " + okTimes2021.getValue();
            _this.skin["ok_btn"].visible = true;
            _this.skin["no_btn"].visible = false;
         }
         if(dayTimes2021.getValue() == 10 && state2021.getValue() != 3)
         {
            _this.skin["txt_1"].text = "咩哈哈哈，今天玩的好开心阿。谢谢你阿，再多带我玩几天，我就把我身后的这个东西送给你。";
            _this.skin["txt_2"].text = "今天玩够啦，明天再来吧！   " + okTimes2021.getValue();
            _this.skin["ok_btn"].visible = false;
            _this.skin["no_btn"].visible = false;
         }
      }
      
      private static function jianbian(param1:*) : *
      {
         ++timetemp;
         _this.tishi.alpha -= 0.01;
         if(timetemp > 100)
         {
            timetemp = 0;
            _this.tishi.visible = false;
            _this.skin.removeEventListener(Event.ENTER_FRAME,jianbian);
         }
      }
      
      private static function getRDM(param1:Player) : *
      {
         param1.data.getBag().addOtherobjBag(OtherFactory.creatOther(63342));
         _this.tishi["jl2"].gotoAndStop(OtherFactory.creatOther(63342).getFrame());
         NewMC.Open("文字提示",Main._stage,480,450,45,0,true,2,"获得\'随机宠物礼包\'");
      }
      
      private static function okgo(param1:*) : *
      {
         var _loc5_:Object = null;
         if(state2021.getValue() == 3)
         {
            _this.tishi["jl0"].gotoAndStop(0);
            _this.tishi["jl1"].gotoAndStop(0);
            _this.tishi["jl2"].gotoAndStop(0);
            _this.tishi["jl3"].gotoAndStop(0);
            _loc5_ = getJiangLi();
            if(_loc5_ is Supplies)
            {
               if(!(Main.player1.getBag().backOtherBagNum() >= 3 && Main.player1.getBag().backSuppliesBagNum() >= 2 && Main.player1.getBag().backGemBagNum() >= 1))
               {
                  NewMC.Open("文字提示",_this.skin,400,400,30,0,true,2,"消耗栏，宝石栏，其他栏各需要2格空间，请清理背包");
                  return;
               }
               if(okTimes2021.getValue() == 80 && Boolean(Main.P1P2))
               {
                  if(Main.player2.getBag().backOtherBagNum() < 1)
                  {
                     NewMC.Open("文字提示",_this.skin,400,400,30,0,true,2,"2P其他栏需要1格空间，请清理背包");
                     return;
                  }
                  getRDM(Main.player_2);
               }
               if(okTimes2021.getValue() == 100)
               {
                  getRDM(Main.player_1);
               }
               if(okTimes2021.getValue() % 10 == 0 && Boolean(okTimes2021.getValue()))
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63156).getFrame());
               }
               Main.player1.getBag().addSuppliesBag(_loc5_);
               _this.tishi.visible = true;
               _this.tishi.alpha = 1;
               _this.tishi["jl0"].gotoAndStop((_loc5_ as Supplies).getFrame());
               _this.skin.addEventListener(Event.ENTER_FRAME,jianbian);
               state2021.setValue(0);
               Main.Save();
            }
            if(_loc5_ is Gem)
            {
               if(!(Main.player1.getBag().backOtherBagNum() >= 3 && Main.player1.getBag().backSuppliesBagNum() >= 2 && Main.player1.getBag().backGemBagNum() >= 1))
               {
                  NewMC.Open("文字提示",_this.skin,400,400,30,0,true,2,"消耗栏，宝石栏，其他栏各需要2格空间，请清理背包");
                  return;
               }
               if(okTimes2021.getValue() == 80 && Boolean(Main.P1P2))
               {
                  if(Main.player2.getBag().backOtherBagNum() < 1)
                  {
                     NewMC.Open("文字提示",_this.skin,400,400,30,0,true,2,"2P其他栏需要1格空间，请清理背包");
                     return;
                  }
                  getRDM(Main.player_2);
               }
               if(okTimes2021.getValue() == 100)
               {
                  getRDM(Main.player_1);
               }
               if(okTimes2021.getValue() % 10 == 0 && Boolean(okTimes2021.getValue()))
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63156).getFrame());
               }
               Main.player1.getBag().addGemBag(_loc5_);
               _this.tishi.visible = true;
               _this.tishi.alpha = 1;
               _this.tishi["jl0"].gotoAndStop((_loc5_ as Gem).getFrame());
               _this.skin.addEventListener(Event.ENTER_FRAME,jianbian);
               state2021.setValue(0);
               Main.Save();
            }
            if(_loc5_ is Otherobj)
            {
               if(!(Main.player1.getBag().backOtherBagNum() >= 3 && Main.player1.getBag().backSuppliesBagNum() >= 2 && Main.player1.getBag().backGemBagNum() >= 1))
               {
                  NewMC.Open("文字提示",_this.skin,400,400,30,0,true,2,"消耗栏，宝石栏，其他栏各需要2格空间，请清理背包");
                  return;
               }
               if(okTimes2021.getValue() == 80 && Boolean(Main.P1P2))
               {
                  if(Main.player2.getBag().backOtherBagNum() < 1)
                  {
                     NewMC.Open("文字提示",_this.skin,400,400,30,0,true,2,"2P其他栏需要1格空间，请清理背包");
                     return;
                  }
                  getRDM(Main.player_2);
               }
               if(okTimes2021.getValue() == 100)
               {
                  getRDM(Main.player_1);
               }
               if(okTimes2021.getValue() % 10 == 0 && Boolean(okTimes2021.getValue()))
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63156));
                  _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63156).getFrame());
               }
               Main.player1.getBag().addOtherobjBag(_loc5_);
               _this.tishi.visible = true;
               _this.tishi.alpha = 1;
               _this.tishi["jl0"].gotoAndStop((_loc5_ as Otherobj).getFrame());
               _this.skin.addEventListener(Event.ENTER_FRAME,jianbian);
               state2021.setValue(0);
               Main.Save();
            }
         }
         if(dayTimes2021.getValue() == 10)
         {
            CloseX();
            return;
         }
         if(state2021.getValue() == 0 || state2021.getValue() == 2)
         {
            state2021.setValue(1);
         }
         Main.ysbtn.visible = false;
         Player.boolYS = true;
         Main.player_1.OpenYS();
         CloseX();
      }
      
      public static function getJiangLi() : Object
      {
         var _loc1_:int = Math.floor(Math.random() * jlArr.length);
         if(jlArr[_loc1_][0] == 1)
         {
            return SuppliesFactory.getSuppliesById(jlArr[_loc1_][1]);
         }
         if(jlArr[_loc1_][0] == 2)
         {
            return GemFactory.creatGemById(jlArr[_loc1_][1]);
         }
         if(jlArr[_loc1_][0] == 3)
         {
            return OtherFactory.creatOther(jlArr[_loc1_][1]);
         }
         return undefined;
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("ysPanel") as Class;
         this.skin = new _loc2_();
         var _loc3_:Class = loadData.getClass("TiShi") as Class;
         this.tishi = new _loc3_();
         this.skin.addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addChild(this.skin);
         addChild(this.tishi);
         booltemp = true;
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function Close(param1:*) : *
      {
         this.visible = false;
         this.y = 5000;
         this.x = 5000;
      }
      
      private function onADDED_TO_STAGE(param1:*) : *
      {
         _this.skin["ok_btn"].addEventListener(MouseEvent.CLICK,okgo);
         _this.skin["no_btn"].addEventListener(MouseEvent.CLICK,this.Close);
         _this.skin["close_btn"].addEventListener(MouseEvent.CLICK,this.Close);
         _this.tishi["jl0"].stop();
         _this.tishi["jl1"].stop();
         _this.tishi["jl2"].stop();
         _this.tishi["jl3"].stop();
         _this.tishi.visible = false;
      }
   }
}

