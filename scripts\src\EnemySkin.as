package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class EnemySkin extends MovieClip
   {
      public static var EnemySkinXmlArr:Array = new Array();
      
      public var who:Enemy;
      
      public var EnemySkinXml:XML = new XML();
      
      public var thisXML:XML = new XML();
      
      public var id:int;
      
      public var frame:int;
      
      public var runOver:Boolean;
      
      public var continuous:Boolean;
      
      public var stopRun:Boolean;
      
      public var gravity:int;
      
      public var moveYN:Boolean;
      
      public var continuousTime:int;
      
      public var hpX:int;
      
      public var runX:int;
      
      public var runY:int;
      
      public var runTime:int;
      
      public var 硬直:Number;
      
      public var 被攻击硬直:Number;
      
      public var attTimes:int;
      
      public var type:int = 0;
      
      public var space:int;
      
      public var totalTime:int;
      
      public var numValue:int;
      
      public var lexAdd:int = 1;
      
      public var runType:String = "";
      
      public var moveArr:Array = [];
      
      public function EnemySkin()
      {
         super();
         this.GoTo("站立");
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.otherggg();
      }
      
      public function otherggg() : *
      {
      }
      
      public function Over() : *
      {
         this.gotoAndStop(1);
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function boss14() : *
      {
      }
      
      public function onENTER_FRAME(param1:*) : *
      {
         this.otherXXX();
         this.boss14();
         if(this.currentFrame == this.totalFrames)
         {
            stop();
            (this.parent as Enemy).Dead();
            return;
         }
         this.GoToPlay();
      }
      
      public function otherXXX() : *
      {
      }
      
      public function MingZong(param1:Player) : *
      {
      }
      
      public function GoToPlay() : *
      {
         if(this.continuousTime > 0)
         {
            --this.continuousTime;
         }
         else if(this.runType == "被打")
         {
            this.GoTo("站立");
         }
         if(this.currentLabel == "死亡")
         {
            if(this.runType == "被打")
            {
               this.gotoAndPlay("被打");
            }
            else if(this.runType != "死亡")
            {
               this.GoTo("站立");
            }
         }
         this.isRunOver();
         if(this.runOver)
         {
            if(this.continuous || this.continuousTime > 0)
            {
               gotoAndPlay(this.runType);
               this.runOver = false;
               this.frame = 0;
            }
            else
            {
               this.GoTo("站立");
            }
         }
         else
         {
            ++this.frame;
         }
         if(this.runType == "站立" || this.runType == "移动" || this.runType == "跳跃" || this.runType == "被打" || this.runType == "死亡")
         {
            this.InitFrame();
         }
         else
         {
            this.FindFrame();
         }
      }
      
      private function InitFrame() : *
      {
         this.continuous = true;
         this.stopRun = true;
         this.gravity = (this.parent as Enemy).gravityXX;
         this.被攻击硬直 = (this.parent as Enemy).硬直.getValue();
      }
      
      private function FindFrame() : *
      {
         var _loc2_:* = undefined;
         var _loc3_:int = 0;
         var _loc4_:String = null;
         var _loc5_:String = null;
         var _loc6_:int = 0;
         this.moveArr = null;
         var _loc1_:String = getQualifiedClassName(this);
         for(_loc2_ in this.EnemySkinXml.怪物攻击)
         {
            _loc3_ = int(this.EnemySkinXml.怪物攻击[_loc2_].ID);
            _loc4_ = String(this.EnemySkinXml.怪物攻击[_loc2_].名称);
            _loc5_ = String(this.EnemySkinXml.怪物攻击[_loc2_].帧标签);
            _loc6_ = int(String(this.EnemySkinXml.怪物攻击[_loc2_].当前帧));
            if(this.id == _loc3_ && this.runType == _loc5_)
            {
               this.continuous = false;
               this.stopRun = false;
               this.gravity = int(this.EnemySkinXml.怪物攻击[_loc2_].重力调整);
               this.moveArr = [int(this.EnemySkinXml.怪物攻击[_loc2_].移动参数.X),int(this.EnemySkinXml.怪物攻击[_loc2_].移动参数.Y),int(this.EnemySkinXml.怪物攻击[_loc2_].移动参数.持续)];
               this.hpX = int(this.EnemySkinXml.怪物攻击[_loc2_].伤害.hp) * this.parent.攻击力.getValue() * this.lexAdd;
               this.runX = int(this.EnemySkinXml.怪物攻击[_loc2_].伤害.震退);
               this.runY = int(this.EnemySkinXml.怪物攻击[_loc2_].伤害.挑高);
               this.runTime = int(this.EnemySkinXml.怪物攻击[_loc2_].伤害.持续);
               this.硬直 = int(this.EnemySkinXml.怪物攻击[_loc2_].特效.硬直);
               this.被攻击硬直 = int(this.EnemySkinXml.怪物攻击[_loc2_].被攻击硬直);
               this.attTimes = int(this.EnemySkinXml.怪物攻击[_loc2_].特效.攻击次数);
               this.type = int(this.EnemySkinXml.怪物攻击[_loc2_].特效.类型);
               this.space = int(this.EnemySkinXml.怪物攻击[_loc2_].特效.间隔);
               this.totalTime = int(this.EnemySkinXml.怪物攻击[_loc2_].特效.总计时);
               this.numValue = int(this.EnemySkinXml.怪物攻击[_loc2_].特效.数值);
               return;
            }
         }
      }
      
      public function isRunOver() : *
      {
         if(this.currentLabel != this.runType)
         {
            this.runOver = true;
         }
         else
         {
            this.runOver = false;
         }
      }
      
      public function GoTo(param1:String, param2:int = 0) : *
      {
         this.otherGoTo(param1);
         if(param1 == "死亡" && this.runType != param1)
         {
            this.runType = "死亡";
            gotoAndPlay(this.runType);
            return;
         }
         if(param1 == "被打")
         {
            this.runType = param1;
            this.runOver = false;
            this.stopRun = false;
            gotoAndPlay(this.runType);
         }
         if((this.runOver || this.stopRun) && this.runType != param1)
         {
            this.runType = param1;
            this.runOver = false;
            gotoAndPlay(this.runType);
            this.frame = 0;
         }
         this.continuousTime = param2;
      }
      
      public function otherGoTo(param1:String) : *
      {
      }
      
      public function 震动(param1:int = 4) : *
      {
         if(Main.world)
         {
            Main.world.Quake(param1);
         }
      }
   }
}

