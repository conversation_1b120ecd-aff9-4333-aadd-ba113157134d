package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.utils.*;
   import src.other.*;
   import src.tool.*;
   
   public class Fly extends MovieClip
   {
      public static var FlyData:XML;
      
      public static var FlyData2:Object = new Object();
      
      public static var All:Array = [];
      
      public var isCleanOut:Boolean = true;
      
      public var life:int = -1;
      
      public var hit:HitXX;
      
      public var time:int = -1;
      
      public var continuous:Boolean;
      
      public var speedX:int;
      
      public var speedY:int;
      
      public var moveYN:Boolean = true;
      
      public var objArr:Array = [];
      
      public var gongJi_hp:Number;
      
      public var gongJi_hp_MAX:Number;
      
      public var runArr:Array = [];
      
      public var cross:Boolean = true;
      
      public var 硬直:int;
      
      public var runX:int;
      
      public var runY:int;
      
      public var runTime:int;
      
      public var who:Object;
      
      public var getWho:Object;
      
      public var _stage:MovieClip;
      
      public var RL:Boolean;
      
      public var over:Boolean;
      
      public var over2:Boolean;
      
      public var attTimes:int;
      
      public var type:int = 0;
      
      public var space:int;
      
      public var totalTime:int;
      
      public var numValue:int;
      
      public var _name:String;
      
      private var XXX:int;
      
      private var YYY:int;
      
      public var nameXX:String = "";
      
      public var bingDong:Boolean = false;
      
      public function Fly(param1:Boolean = false)
      {
         super();
         this._stage = Main._this;
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         if(param1)
         {
            return;
         }
         this.getData();
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         if(GameData.gameLV == 6 && Main.gameNum.getValue() != 999)
         {
            this.scaleY = 1.5;
            this.scaleX = 1.5;
         }
      }
      
      public static function getFlyNum(param1:String) : int
      {
         var _loc2_:int = 0;
         for(i in Fly.All)
         {
            if((Fly.All[i] as Fly)._name == param1)
            {
               _loc2_++;
            }
         }
         return _loc2_;
      }
      
      public function onADDED_TO_STAGE(param1:* = null) : *
      {
         var _loc2_:int = 0;
         var _loc4_:String = null;
         var _loc5_:int = 0;
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         _loc2_ = 0;
         while(_loc2_ < All.length)
         {
            if(All[_loc2_] == this)
            {
               return;
            }
            _loc2_++;
         }
         All[All.length] = this;
         var _loc3_:MovieClip = this;
         while(_loc3_ != this._stage)
         {
            if(_loc3_ is Skin_WuQi && _loc3_.parent is Player)
            {
               this.who = _loc3_.parent;
               this.RL = (this.who as Player).RL;
               this.硬直 = this.who.skin.硬直;
               this.gongJi_hp = this.who.skin.hpXX.getValue();
               this.gongJi_hp_MAX = this.who.skin.hpMax;
               this.attTimes = this.who.skin.attTimes;
               this.runX = this.who.skin.runX;
               this.runY = this.who.skin.runY;
               this.runTime = this.who.skin.runTime;
               break;
            }
            if(_loc3_ is Fly && _loc3_.parent is Fly)
            {
               this.who = _loc3_.parent.who;
               this.硬直 = _loc3_.parent.硬直;
               this.gongJi_hp = _loc3_.parent.gongJi_hp;
               this.attTimes = _loc3_.parent.attTimes;
               this.runX = _loc3_.parent.runX;
               this.runY = _loc3_.parent.runY;
               this.XXX = _loc3_.parent.x;
               this.YYY = _loc3_.parent.y;
               this.runTime = _loc3_.parent.runTime;
               break;
            }
            if(_loc3_ is Skin_WuQi && _loc3_.parent is Player2)
            {
               this.who = _loc3_.parent;
               this.RL = (this.who as Player2).RL;
               this.硬直 = this.who.skin.硬直;
               this.gongJi_hp = this.who.skin.hpXX.getValue();
               this.attTimes = this.who.skin.attTimes;
               this.runX = this.who.skin.runX;
               this.runY = this.who.skin.runY;
               this.runTime = this.who.skin.runTime;
               break;
            }
            if(_loc3_ is EnemySkin && _loc3_.parent is Enemy)
            {
               this.who = _loc3_.parent;
               this.RL = (this.who as Enemy).RL;
               this.硬直 = this.who.skin.硬直;
               this.attTimes = this.who.skin.attTimes;
               this.gongJi_hp = (_loc3_ as EnemySkin).hpX;
               this.runX = (_loc3_ as EnemySkin).runX;
               this.runY = (_loc3_ as EnemySkin).runY;
               this.runTime = (_loc3_ as EnemySkin).runTime;
               this.type = (_loc3_ as EnemySkin).type;
               this.space = (_loc3_ as EnemySkin).space;
               this.totalTime = (_loc3_ as EnemySkin).totalTime;
               this.numValue = (_loc3_ as EnemySkin).numValue;
               break;
            }
            if(_loc3_ is ChongWu)
            {
               this.RL = (_loc3_ as ChongWu).RL;
               this.who = _loc3_;
               for(_loc2_ in ChongWu.dataXml.宠物攻击)
               {
                  _loc4_ = String(ChongWu.dataXml.宠物攻击[_loc2_].名称);
                  if(getQualifiedClassName(this) == _loc4_)
                  {
                     this.moveYN = String(ChongWu.dataXml.宠物攻击[_loc2_].独立).toString() == "true" ? true : false;
                     this.life = int(ChongWu.dataXml.宠物攻击[_loc2_].HP);
                     this.continuous = String(ChongWu.dataXml.宠物攻击[_loc2_].循环).toString() == "true" ? true : false;
                     this.cross = String(ChongWu.dataXml.宠物攻击[_loc2_].穿越).toString() == "true" ? true : false;
                     this.time = int(ChongWu.dataXml.宠物攻击[_loc2_].时间);
                     this.speedX = int(ChongWu.dataXml.宠物攻击[_loc2_].移动x);
                     this.speedY = int(ChongWu.dataXml.宠物攻击[_loc2_].移动y);
                     this.gongJi_hp = Number(ChongWu.dataXml.宠物攻击[_loc2_].攻击HP) * (_loc3_ as ChongWu).who.gongji.getValue() * (100 + Math.random() * 2) / 100;
                     _loc5_ = int(Main.gameNum.getValue());
                     if(_loc5_ >= 1 && _loc5_ <= 9)
                     {
                        this.gongJi_hp_MAX = int(ChongWu.dataXml.宠物攻击[_loc2_].伤害限定);
                     }
                     else if(_loc5_ >= 10 && _loc5_ <= 16)
                     {
                        this.gongJi_hp_MAX = int(ChongWu.dataXml.宠物攻击[_loc2_].伤害限定2);
                     }
                     else if(_loc5_ >= 51 && _loc5_ <= 62)
                     {
                        this.gongJi_hp_MAX = int(ChongWu.dataXml.宠物攻击[_loc2_].伤害限定3);
                     }
                     else
                     {
                        this.gongJi_hp_MAX = int(ChongWu.dataXml.宠物攻击[_loc2_].伤害限定);
                     }
                     this.runArr = [int(ChongWu.dataXml.宠物攻击[_loc2_].震退),int(ChongWu.dataXml.宠物攻击[_loc2_].挑高),int(ChongWu.dataXml.宠物攻击[_loc2_].持续)];
                     break;
                  }
               }
               break;
            }
            _loc3_ = _loc3_.parent as MovieClip;
         }
         if(this.moveYN)
         {
            this.y += this.who.y;
            if(this.RL)
            {
               scaleX *= -1;
               this.x = this.who.x - this.x;
            }
            else
            {
               scaleX *= 1;
               this.x = this.who.x + this.x;
            }
            if(this.name == "R_mc")
            {
               this.RL = true;
               this.x = this.who.x + 120;
               this.scaleX = -1;
            }
            if(this.name == "L_mc")
            {
               this.RL = false;
               this.x = this.who.x - 120;
               this.scaleX = 1;
            }
            if(!(this is Boss108_FlyXX))
            {
               Main.world.moveChild_Other.addChild(this);
            }
            this.otherXXXXX();
         }
      }
      
      private function otherXXXXX() : *
      {
         var _loc1_:Player = null;
         var _loc2_:ChongWu = null;
         if(getQualifiedClassName(this) == "猎犬火焰")
         {
            this.y = this.YYY + 50;
            this.x = this.XXX;
         }
         if(getQualifiedClassName(this) == "宠3毒雾")
         {
            this.y = 0;
            this.scaleX = 1;
            this.x = this.who.x;
         }
         else if(getQualifiedClassName(this) == "宠3猫头鹰")
         {
            this.y = 500;
            this.x = this.who.x + Math.random() * 800 - 400;
         }
         else if(getQualifiedClassName(this) == "火焰之路" || getQualifiedClassName(this) == "寒冰之路")
         {
            Main.world.moveChild_Back.addChild(this);
         }
         else if(getQualifiedClassName(this) == "Cw29_Fly2")
         {
            _loc1_ = this.who.who;
            this.RL = _loc1_.RL;
            if(_loc1_.RL)
            {
               this.x = _loc1_.x;
               this.scaleX = 1;
            }
            else
            {
               this.x = _loc1_.x;
               this.scaleX = -1;
            }
         }
         else if(getQualifiedClassName(this) == "Cw29_Fly4")
         {
            _loc2_ = this.who;
            this.x = _loc2_.gongJiEnemy.x;
            this.y = _loc2_.gongJiEnemy.y;
         }
      }
      
      public function getData() : *
      {
         var _loc1_:* = getQualifiedClassName(this);
         this._name = _loc1_;
         if(Fly.FlyData2[_loc1_])
         {
            this.life = Fly.FlyData2[_loc1_][0];
            this.continuous = Fly.FlyData2[_loc1_][1];
            this.cross = Fly.FlyData2[_loc1_][2];
            this.time = Fly.FlyData2[_loc1_][3];
            this.speedX = Fly.FlyData2[_loc1_][4];
            this.speedY = Fly.FlyData2[_loc1_][5];
         }
      }
      
      public function onENTER_FRAME(param1:*) : *
      {
         if(getQualifiedClassName(this) == "Cw24_Fly2" || getQualifiedClassName(this) == "Cw24_Fly3")
         {
            this.x = this.who.x;
            this.y = this.who.y;
         }
         if(this.who is Player2 && (this.who as Player2).hp.getValue() == 0)
         {
            this.Dead();
            return;
         }
         if(this.over && this.currentLabel != "结束")
         {
            this.Dead();
            return;
         }
         if(!this.over && this.life == 0 && this.isCleanOut)
         {
            this.life = -1;
            gotoAndPlay("结束");
            this.continuous = false;
            this.over = true;
         }
         if(!this.over && this.time != -1)
         {
            --this.time;
            if(this.time == -1)
            {
               this.time = -1;
               gotoAndPlay("结束");
               this.continuous = false;
               this.over = true;
            }
         }
         if(this.continuous && this.currentLabel != "运行")
         {
            gotoAndPlay("运行");
         }
         else if(!this.continuous && this.currentLabel != "运行")
         {
            this.over = true;
         }
         if(this.over && this.currentLabel == "结束" && currentFrame == totalFrames)
         {
            this.over2 = true;
         }
         if(!this.over)
         {
            this.Move();
         }
         this.otherXX();
      }
      
      public function otherXX() : *
      {
         if(this._name == "转职2技能飞镖2" && this.over)
         {
            this.runX = this.runY = this.runTime = 0;
         }
         if(this._name == "暗器风暴3" && Boolean(this.who))
         {
            this.x = this.who.x;
            this.y = this.who.y;
         }
      }
      
      public function Move() : *
      {
         this.y += this.speedY;
         if(this.RL)
         {
            this.x += this.speedX;
         }
         else
         {
            this.x -= this.speedX;
         }
         if(this.x > Main.world._width + 2000 || this.x < -2000)
         {
            this.life = 0;
         }
      }
      
      public function Dead() : *
      {
         stop();
         for(i in All)
         {
            if(All[i] == this)
            {
               All.splice(i,1);
            }
         }
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         if(this.who is Player2 || this.who is Player || this.who is Enemy || this.who is ChongWu)
         {
            if(this.parent)
            {
               this.parent.removeChild(this);
            }
         }
      }
      
      public function Dead2() : *
      {
         stop();
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function 震动(param1:int = 4) : *
      {
         if(Main.world)
         {
            Main.world.Quake(param1);
         }
      }
   }
}

