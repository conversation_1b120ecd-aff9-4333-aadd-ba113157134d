package com.hotpoint.braveManIII.repository
{
   import com.hotpoint.braveManIII.Tool.*;
   import src.*;
   
   public class Zhufu2Factory
   {
      public static var allData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function Zhufu2Factory()
      {
         super();
      }
      
      public static function creatZhufu2Factory() : *
      {
         var _loc1_:Zhufu2Factory = new Zhufu2Factory();
         myXml = XMLAsset.createXML(Data2.zhuFu2);
         _loc1_.creatFactory();
      }
      
      private function creatFactory() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var _loc1_:XML = null;
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc10_:Number = NaN;
         var _loc11_:Number = NaN;
         var _loc12_:Number = NaN;
         var _loc13_:Number = NaN;
         var _loc14_:Number = NaN;
         var _loc15_:Array = null;
         for each(_loc1_ in myXml.祝福)
         {
            _loc2_ = Number(_loc1_.等级);
            _loc3_ = Number(_loc1_.成功率);
            _loc4_ = Number(_loc1_.材料1);
            _loc5_ = Number(_loc1_.材料2);
            _loc6_ = Number(_loc1_.材料3);
            _loc7_ = Number(_loc1_.材料4);
            _loc8_ = Number(_loc1_.攻击);
            _loc9_ = Number(_loc1_.防御);
            _loc10_ = Number(_loc1_.暴击);
            _loc11_ = Number(_loc1_.魔抗);
            _loc12_ = Number(_loc1_.破魔);
            _loc13_ = Number(_loc1_.生命);
            _loc14_ = Number(_loc1_.魔力);
            _loc15_ = [_loc2_,_loc3_,_loc4_,_loc5_,_loc6_,_loc7_,_loc8_,_loc9_,_loc10_,_loc11_,_loc12_,_loc13_,_loc14_];
            allData[_loc2_] = _loc15_;
         }
      }
   }
}

