package src
{
   import flash.display.MovieClip;
   import flash.events.*;
   
   public class MC_Btn extends MovieClip
   {
      public var skin:MovieClip;
      
      public var onCLICK_fun:Function;
      
      public var lingQuNum:int = 0;
      
      public function MC_Btn(param1:MovieClip, param2:String = "")
      {
         super();
         this.skin = param1;
         this.skin.gotoAndStop(1);
         this.skin.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.skin.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         if(param2 != "")
         {
            this.skin.addEventListener(MouseEvent.CLICK,this[param2]);
         }
         param1.mouseChildren = false;
         param1.buttonMode = true;
      }
      
      public function onMOUSE_MOVE(param1:MouseEvent) : *
      {
         if(this.skin.currentFrame == 2)
         {
            this.skin.gotoAndStop(3);
         }
      }
      
      public function onMOUSE_OUT(param1:MouseEvent) : *
      {
         if(this.skin.currentFrame == 3)
         {
            this.skin.gotoAndStop(2);
         }
      }
      
      public function onCLICK_CZ(param1:MouseEvent = null) : *
      {
         if(this.skin.currentFrame == 2 || this.skin.currentFrame == 3)
         {
            ChongZhi_Interface4.LingQu_CZ(this.lingQuNum);
         }
      }
      
      public function goTo(param1:int) : *
      {
         this.skin.gotoAndStop(param1);
      }
   }
}

