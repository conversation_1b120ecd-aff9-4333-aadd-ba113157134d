package src
{
   import com.hotpoint.braveManIII.models.common.VT;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.pet.Pet;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.elves.ElvesFactory;
   import com.hotpoint.braveManIII.repository.equip.EquipFactory;
   import com.hotpoint.braveManIII.repository.gem.GemFactory;
   import com.hotpoint.braveManIII.repository.other.OtherFactory;
   import com.hotpoint.braveManIII.repository.pet.PetFactory;
   import com.hotpoint.braveManIII.repository.supplies.SuppliesFactory;
   import com.hotpoint.braveManIII.repository.title.TitleFactory;
   import com.hotpoint.braveManIII.views.achPanel.AchData;
   import com.hotpoint.braveManIII.views.cardPanel.CardPanel;
   import com.hotpoint.braveManIII.views.petPanel.NewPetPanel;
   import com.hotpoint.braveManIII.views.storagePanel.StoragePanel;
   import com.hotpoint.braveManIII.views.taskPanel.TaskData;
   import com.hotpoint.braveManIII.views.caiyaoPanel.CaiYaoPanel;
   import com.hotpoint.braveManIII.views.skillPanel.SkillPanel;
   import com.hotpoint.braveManIII.repository.plan.PlanFactory;
   import com.hotpoint.braveManIII.models.plan.Plan;
   import com.hotpoint.braveManIII.repository.petEquip.PetEquipFactory;
   import src.tool.PaiHang_Data;
   import src.ShopKillPoint;
   import src.Panel_youling;
   import src.NewMC;
   import src.Main;
   import src.Enemy;
   import flash.display.MovieClip;
   
   public class CuteGMFunctions
   {
      // 角色相关功能
      public static function setPlayerLevel(player:int, level:int):void
      {
         try {
            if(player == 1) {
               Main.player1.level = VT.createVT(level);
               showCuteMessage("✨ 玩家1等级设置成功！等级：" + level, "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player2.level = VT.createVT(level);
               showCuteMessage("✨ 玩家2等级设置成功！等级：" + level, "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 设置等级失败：" + e.message, "error");
         }
      }
      
      public static function setPlayerGold(player:int, gold:int):void
      {
         try {
            if(player == 1) {
               Main.player1.gold.setValue(gold);
               showCuteMessage("💰 玩家1金币设置成功！金币：" + gold, "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player2.gold.setValue(gold);
               showCuteMessage("💰 玩家2金币设置成功！金币：" + gold, "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 设置金币失败：" + e.message, "error");
         }
      }
      
      public static function setPlayerExp(player:int, exp:int):void
      {
         try {
            if(player == 1) {
               Main.player1.exp.setValue(exp);
               showCuteMessage("⭐ 玩家1经验设置成功！经验：" + exp, "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player2.exp.setValue(exp);
               showCuteMessage("⭐ 玩家2经验设置成功！经验：" + exp, "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 设置经验失败：" + e.message, "error");
         }
      }
      
      public static function setPlayerSkillPoints(player:int, points:int):void
      {
         try {
            if(player == 1) {
               Main.player_1.data.points.setValue(points);
               showCuteMessage("🎯 玩家1技能点设置成功！技能点：" + points, "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player_2.data.points.setValue(points);
               showCuteMessage("🎯 玩家2技能点设置成功！技能点：" + points, "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 设置技能点失败：" + e.message, "error");
         }
      }
      
      // 装备相关功能
      public static function giveAllEquipments():void
      {
         try {
            var equipIds:Array = [
               11001, 11002, 11003, 11004, 11005,  // 武器
               12001, 12002, 12003, 12004, 12005,  // 头盔
               13001, 13002, 13003, 13004, 13005,  // 上衣
               14001, 14002, 14003, 14004, 14005,  // 下装
               15001, 15002, 15003, 15004, 15005   // 鞋子
            ];
            
            var count:int = 0;
            for(var i:int = 0; i < equipIds.length; i++) {
               var equip:Equip = EquipFactory.creatEquip(equipIds[i]);
               if(equip) {
                  equip.setReinforceLevel(15);  // 设置强化等级
                  if(Main.player1.getBag().addEquipBag(equip)) {
                     count++;
                  }
               }
            }
            
            showCuteMessage("⚔️ 一键装备完成！获得 " + count + " 件装备", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键装备失败：" + e.message, "error");
         }
      }
      
      // 宠物相关功能
      public static function giveAllPets():void
      {
         try {
            var petIds:Array = [
               73100, 73104, 73105, 73107, 73110, 73111, 73113, 73117,
               73118, 73120, 73123, 73126, 73130, 73133, 73134, 73137,
               73139, 73141, 73144, 73146
            ];
            
            var count:int = 0;
            for(var i:int = 0; i < petIds.length; i++) {
               var pet:Pet = PetFactory.creatPet(petIds[i]);
               if(pet) {
                  pet.wuxing = VT.createVT(4);      // 最高五行
                  pet.setLv(30);                    // 30级
                  pet.addLvLimit(10);               // 增加等级上限
                  pet.xingge = VT.createVT(3);      // 优秀性格
                  
                  if(Main.player1.getPetSlot().addPetSlot(pet)) {
                     count++;
                  }
               }
            }
            
            showCuteMessage("🐾 一键宠物完成！获得 " + count + " 只宠物", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键宠物失败：" + e.message, "error");
         }
      }
      
      public static function giveAllElves():void
      {
         try {
            var count:int = 0;
            for(var i:int = 1; i <= 9; i++) {
               var elves:* = ElvesFactory.creatElves(i);
               if(elves) {
                  elves.setAllPoint(678);           // 设置属性点
                  elves.setLevel(10);               // 设置等级
                  elves.blueEquipNum = VT.createVT(0);
                  elves.pinkEquipNum = VT.createVT(0);
                  elves.goldEquipNum = VT.createVT(0);
                  
                  if(Main.player1.getElvesSlot().addElvesSlot(elves)) {
                     count++;
                  }
               }
            }
            
            showCuteMessage("🧚 一键精灵完成！获得 " + count + " 个精灵", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键精灵失败：" + e.message, "error");
         }
      }
      
      // 道具相关功能
      public static function giveAllGems():void
      {
         try {
            var gemIds:Array = [
               63001, 63002, 63003, 63004, 63005,  // 各种宝石
               63101, 63102, 63103, 63104, 63105,
               63201, 63202, 63203, 63204, 63205
            ];
            
            var count:int = 0;
            for(var i:int = 0; i < gemIds.length; i++) {
               var gem:Gem = GemFactory.creatGemById(gemIds[i]);
               if(gem && Main.player1.getBag().addGemBag(gem)) {
                  count++;
               }
            }
            
            showCuteMessage("💎 一键宝石完成！获得 " + count + " 个宝石", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键宝石失败：" + e.message, "error");
         }
      }
      
      public static function giveAllSupplies():void
      {
         try {
            var suppliesIds:Array = [
               21001, 21002, 21003, 21004, 21005,  // 各种消耗品
               21101, 21102, 21103, 21104, 21105,
               21201, 21202, 21203, 21204, 21205
            ];
            
            var count:int = 0;
            for(var i:int = 0; i < suppliesIds.length; i++) {
               var supplies:Supplies = SuppliesFactory.creatSupplies(suppliesIds[i]);
               if(supplies) {
                  supplies.setTimes(99);  // 设置数量
                  if(Main.player1.getBag().addSuppliesBag(supplies)) {
                     count++;
                  }
               }
            }
            
            showCuteMessage("🧪 一键消耗品完成！获得 " + count + " 种消耗品", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键消耗品失败：" + e.message, "error");
         }
      }
      
      // 系统相关功能
      public static function unlockAllStages():void
      {
         try {
            for(var i:int = 0; i < 150; i++) {
               Main.guanKa[i] = 3;
            }
            showCuteMessage("🗝️ 所有关卡已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 解锁关卡失败：" + e.message, "error");
         }
      }
      
      public static function completeAllTasks():void
      {
         try {
            for(var i:int = 0; i < 120000; i++) {
               TaskData.setOk(i);
            }
            showCuteMessage("✅ 所有任务已完成！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 完成任务失败：" + e.message, "error");
         }
      }
      
      public static function unlockAllAchievements():void
      {
         try {
            for(var i:int = 0; i < 10000; i++) {
               AchData.setOk(i);
            }
            showCuteMessage("🏆 所有成就已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 解锁成就失败：" + e.message, "error");
         }
      }
      
      public static function clearBag():void
      {
         try {
            for(var i:int = 0; i < 50; i++) {
               Main.player1.getBag().equipBag[i] = null;
               Main.player1.getBag().suppliesBag[i] = null;
               Main.player1.getBag().gemBag[i] = null;
               Main.player1.getBag().otherobjBag[i] = null;
               Main.player1.getBag().questBag[i] = null;
            }
            showCuteMessage("🧹 背包已清空！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 清空背包失败：" + e.message, "error");
         }
      }
      
      public static function clearStorage():void
      {
         try {
            for(var i:int = 0; i < 35; i++) {
               StoragePanel.storage.equipStorage[i] = null;
               StoragePanel.storage.suppliesStorage[i] = null;
               StoragePanel.storage.gemStorage[i] = null;
               StoragePanel.storage.otherobjStorage[i] = null;
            }
            showCuteMessage("🏪 仓库已清空！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 清空仓库失败：" + e.message, "error");
         }
      }
      
      // 一键技能石
      public static function giveAllSkillGems():void
      {
         try {
            var skillGemIds:Array = [55104, 55114, 55119, 55124, 55129, 55134, 55139, 55144, 55149, 55154, 55159, 55164, 55169, 55174, 55179, 55184, 55189, 55194, 55199, 55204, 55209, 55214, 55219, 55224, 55229, 55234, 55239, 55244];

            for(var i:int = 0; i < skillGemIds.length; i++) {
               var gem:Gem = GemFactory.creatGemById(skillGemIds[i]);
               if(gem) {
                  Main.player_1.data.getBag().addGemBag(gem);
               }
            }
            showCuteMessage("💎 怪物+4技能石已放入背包！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键技能石失败：" + e.message, "error");
         }
      }

      // 一键徽章
      public static function giveAllBadges():void
      {
         try {
            var badgeIds:Array = [63164, 63163, 63168, 63166, 63167, 63165];

            for(var i:int = 0; i < badgeIds.length; i++) {
               Main.player1.getBadgeSlot().addToSlot(OtherFactory.creatOther(badgeIds[i]), i);
               if(Main.P1P2) {
                  Main.player2.getBadgeSlot().addToSlot(OtherFactory.creatOther(badgeIds[i]), i);
               }
            }
            showCuteMessage("🏅 一键徽章完成！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键徽章失败：" + e.message, "error");
         }
      }

      // 一键印章
      public static function giveAllStamps():void
      {
         try {
            var stampIds:Array = [34715, 34716, 34717, 34718, 34719, 34720, 34721, 34722, 34723, 34724, 34725, 34726, 34727, 34728, 34729, 34730, 34731, 34732, 34733, 34734, 34735, 34736, 34737, 34738, 34739, 34740, 34741, 34742, 34743, 34744, 34745, 34746, 34747, 34748, 34749, 34750, 34751, 34752, 34753, 34754, 34755, 34756, 34757, 34758];

            // 为P1设置印章
            for(var i:int = 0; i < 15; i++) {
               for(var j:int = 0; j < 4; j++) {
                  var stampId:int = stampIds[Math.floor(Math.random() * stampIds.length)];
                  var stamp:Gem = GemFactory.creatGemById(stampId);
                  if(stamp) {
                     Main.player1.getStampSlot()["setSlot" + (i+1)](stamp, j);
                  }
               }
            }

            // 为P2设置印章
            if(Main.P1P2) {
               for(var k:int = 0; k < 15; k++) {
                  for(var l:int = 0; l < 4; l++) {
                     var stampId2:int = stampIds[Math.floor(Math.random() * stampIds.length)];
                     var stamp2:Gem = GemFactory.creatGemById(stampId2);
                     if(stamp2) {
                        Main.player2.getStampSlot()["setSlot" + (k+1)](stamp2, l);
                     }
                  }
               }
            }

            showCuteMessage("🔖 一键印章完成！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键印章失败：" + e.message, "error");
         }
      }

      // 一键称号
      public static function giveAllTitles():void
      {
         try {
            for(var i:int = 0; i < 43; i++) {
               var title:* = TitleFactory.creatTitle(i);
               if(title) {
                  Main.player1.getTitleSlot().addToSlot(title);
                  if(Main.P1P2) {
                     var title2:* = TitleFactory.creatTitle(i);
                     if(title2) {
                        Main.player2.getTitleSlot().addToSlot(title2);
                     }
                  }
               }
            }
            showCuteMessage("👑 一键称号完成！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键称号失败：" + e.message, "error");
         }
      }

      // 一键宠物装备
      public static function giveAllPetEquipments():void
      {
         try {
            var petEquipIds:Array = [17080, 17075, 17070, 17065, 17060, 17055, 17050, 17045, 17040, 17035, 17030, 17025, 17020, 17015, 17010, 17005, 17000, 17090, 17085, 17095];

            for(var i:int = 0; i < petEquipIds.length; i++) {
               var petEquip:* = PetEquipFactory.creatPetEquip(petEquipIds[i]);
               if(petEquip) {
                  NewPetPanel.bag.addPetBag(petEquip);
               }
            }
            showCuteMessage("🎒 全部高品质宠物装备添加成功！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键宠物装备失败：" + e.message, "error");
         }
      }

      // 一键女神罗盘
      public static function fixGoddessCompass():void
      {
         try {
            Main.questArr = [[true,84110],[true,84111],[true,84112],[true,84113],[true,84114]];
            Main.LuoPanArr = [1,1,1,1];
            showCuteMessage("🧭 女神罗盘修复完成！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 女神罗盘修复失败：" + e.message, "error");
         }
      }

      // 职业相关功能
      public static function clearJob(player:int):void
      {
         try {
            if(player == 1) {
               Main.player1._transferArr[0] = false;
               Main.player1._transferArr[1] = false;
               Main.player1._transferArr[2] = false;
               Main.player1._transferArr[3] = false;
               showCuteMessage("🔄 P1职业已清除！", "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player2._transferArr[0] = false;
               Main.player2._transferArr[1] = false;
               Main.player2._transferArr[2] = false;
               Main.player2._transferArr[3] = false;
               showCuteMessage("🔄 P2职业已清除！", "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 清除职业失败：" + e.message, "error");
         }
      }

      public static function changeJob(player:int, jobType:int):void
      {
         try {
            var jobNames:Array = ["剑士", "法师", "格斗家", "刀客"];

            if(player == 1) {
               Main.player1._transferArr[jobType] = true;
               showCuteMessage("⚔️ P1转职" + jobNames[jobType] + "成功！", "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player2._transferArr[jobType] = true;
               showCuteMessage("⚔️ P2转职" + jobNames[jobType] + "成功！", "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 转职失败：" + e.message, "error");
         }
      }

      // 四职业技能
      public static function unlockAllJobSkills():void
      {
         try {
            Main.player1._pickSkillArr[0] = true;
            Main.player1._pickSkillArr[1] = true;
            Main.player1._pickSkillArr[2] = true;
            Main.player1._pickSkillArr[3] = true;
            Main.player1._skillArr = [["a1",1],["a2",1],["a3",1],["a4",1],["a5",1],["a6",1],["a7",1],["a8",9],["a9",9],["a10",9],["a11",9],["a12",9],["a13",9],["a14",9],["a15",9],["b1",1],["b2",1],["b3",1],["b4",1],["b5",1],["b6",1],["b7",1],["b8",9],["b9",9],["b10",9],["b11",9],["b12",9],["b13",9],["b14",9],["b15",9],["c1",1],["c2",1],["c3",1],["c4",1],["c5",1],["c6",1],["c7",1],["c8",9],["c9",9],["c10",9],["c11",9],["c12",9],["c13",9],["c14",9],["c15",9],["d1",1],["d2",5],["d3",5],["d4",5],["d5",5],["d6",5],["d7",5],["d8",5],["d9",0],["d10",0],["d11",0],["d12",0],["d13",0],["d14",0],["d15",0],["d16",0],["k1",1],["k2",1],["k3",1],["k4",1],["k5",1],["k6",1],["k7",1],["k8",9],["k9",9],["k10",9],["k11",9],["k12",9],["k13",9],["k14",9],["k15",9],["k16",5]];

            if(Main.P1P2) {
               Main.player2._pickSkillArr[0] = true;
               Main.player2._pickSkillArr[1] = true;
               Main.player2._pickSkillArr[2] = true;
               Main.player2._pickSkillArr[3] = true;
               Main.player2._skillArr = [["a1",1],["a2",1],["a3",1],["a4",1],["a5",1],["a6",1],["a7",1],["a8",9],["a9",9],["a10",9],["a11",9],["a12",9],["a13",9],["a14",9],["a15",9],["b1",1],["b2",1],["b3",1],["b4",1],["b5",1],["b6",1],["b7",1],["b8",9],["b9",9],["b10",9],["b11",9],["b12",9],["b13",9],["b14",9],["b15",9],["c1",1],["c2",1],["c3",1],["c4",1],["c5",1],["c6",1],["c7",1],["c8",9],["c9",9],["c10",9],["c11",9],["c12",9],["c13",9],["c14",9],["c15",9],["d1",1],["d2",5],["d3",5],["d4",5],["d5",5],["d6",5],["d7",5],["d8",5],["d9",0],["d10",0],["d11",0],["d12",0],["d13",0],["d14",0],["d15",0],["d16",0],["k1",1],["k2",1],["k3",1],["k4",1],["k5",1],["k6",1],["k7",1],["k8",9],["k9",9],["k10",9],["k11",9],["k12",9],["k13",9],["k14",9],["k15",9],["k16",5]];
            }

            showCuteMessage("🎯 四职业技能已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 解锁技能失败：" + e.message, "error");
         }
      }

      // 显示可爱的消息提示
      public static function showCuteMessage(message:String, type:String = "info"):void
      {
         var color:uint = 0x00CED1;  // 默认青色

         switch(type) {
            case "success":
               color = 0x98FB98;  // 绿色
               break;
            case "error":
               color = 0xFF6B6B;  // 红色
               break;
            case "warning":
               color = 0xFFD93D;  // 黄色
               break;
         }

         // 使用游戏原有的消息系统，但添加可爱的样式
         try {
            NewMC.Open("文字提示", Main._stage, 480, 300, 60, 0, true, 2, message);
         } catch(e:Error) {
            // 如果NewMC不可用，使用trace输出
            trace("GM消息: " + message);
         }
      }

      // 解锁功能
      public static function unlockElvesSlot():void
      {
         try {
            Main.player1.getElvesSlot().addSlotNum(100);
            if(Main.P1P2) {
               Main.player2.getElvesSlot().addSlotNum(100);
            }
            showCuteMessage("🧚 精灵槽已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 精灵槽解锁失败：" + e.message, "error");
         }
      }

      public static function unlockPetSlot():void
      {
         try {
            Main.player1.getPetSlot().addPetSlotNum(100);
            NewPetPanel.bag.setLimit3();
            if(Main.P1P2) {
               Main.player2.getPetSlot().addPetSlotNum(100);
               NewPetPanel.bag.setLimit3();
            }
            showCuteMessage("🐾 宠物栏已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 宠物栏解锁失败：" + e.message, "error");
         }
      }

      public static function unlockBagSlot():void
      {
         try {
            // 解锁装备栏
            for(var i:int = 0; i < 10; i++) {
               Main.player1.getBag().addLimitE();
               if(Main.P1P2) {
                  Main.player2.getBag().addLimitE();
               }
            }

            // 解锁消耗品栏
            for(var j:int = 0; j < 10; j++) {
               Main.player1.getBag().addLimitS();
               if(Main.P1P2) {
                  Main.player2.getBag().addLimitS();
               }
            }

            // 解锁宝石栏
            for(var k:int = 0; k < 10; k++) {
               Main.player1.getBag().addLimitG();
               if(Main.P1P2) {
                  Main.player2.getBag().addLimitG();
               }
            }

            // 解锁道具栏
            for(var l:int = 0; l < 10; l++) {
               Main.player1.getBag().addLimitO();
               if(Main.P1P2) {
                  Main.player2.getBag().addLimitO();
               }
            }

            showCuteMessage("🎒 背包已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 背包解锁失败：" + e.message, "error");
         }
      }

      public static function unlockStampSlot():void
      {
         try {
            for(var i:int = 0; i < 10; i++) {
               Main.player1.getStampSlot().setKeySlot11();
               if(Main.P1P2) {
                  Main.player2.getStampSlot().setKeySlot11();
               }
            }
            showCuteMessage("🔖 印章解锁成功！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 印章解锁失败：" + e.message, "error");
         }
      }

      public static function unlockSpecialShop():void
      {
         try {
            ShopKillPoint.KaiQiShopArr2[1] = true;
            ShopKillPoint.KaiQiShopArr2[2] = true;
            ShopKillPoint.KaiQiShopArr2[3] = true;
            ShopKillPoint.KaiQiShopArr2[4] = true;
            showCuteMessage("🛒 特殊购买栏已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 特殊购买栏解锁失败：" + e.message, "error");
         }
      }

      // 悬赏全满
      public static function fillAllBounties():void
      {
         try {
            var bountySlots:Array = [1, 4, 7, 10, 13, 16, 19, 22, 25, 28];
            for(var i:int = 0; i < bountySlots.length; i++) {
               Main.wts.getWantedTaskFromSlot(bountySlots[i]).addTimes(50);
            }
            showCuteMessage("💰 悬赏已全满成功！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 悬赏全满失败：" + e.message, "error");
         }
      }

      // 采药全满
      public static function fillAllHerbs():void
      {
         try {
            CaiYaoPanel.saveArr = [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1];
            showCuteMessage("🌿 药园采药已全满！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 采药全满失败：" + e.message, "error");
         }
      }

      // 计划全开
      public static function unlockAllPlans():void
      {
         try {
            for(var i:int = 0; i < 112; i++) {
               (PlanFactory.JiHuaData[i] as Plan).setState(1);
            }
            showCuteMessage("📋 计划已全开！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 计划全开失败：" + e.message, "error");
         }
      }

      // 重置技能
      public static function resetSkills():void
      {
         try {
            SkillPanel.open();
            SkillPanel.close();
            SkillPanel._instance.aginP1();
            Main.player_1.GetAllSkillCD();
            if(Main.P1P2) {
               SkillPanel._instance.aginP2();
               Main.player_2.GetAllSkillCD();
            }
            showCuteMessage("🔄 技能已重置！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 技能重置失败：" + e.message, "error");
         }
      }

      // 过检测
      public static function bypassDetection():void
      {
         try {
            Main.noSave = -43998785;
            Main.NoLog = 0;
            Main.NoLogInfo = new Array();
            for(var i:int = 1; i <= 15; i++) {
               Main.NoLogInfo[i] = null;
            }
            showCuteMessage("🔓 检测已过！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 过检测失败：" + e.message, "error");
         }
      }

      // 宝珠满级
      public static function maxBaoZhu():void
      {
         try {
            Panel_youling.lvArr = [100,100,100,100,100];
            Panel_youling.bzNumArr = [2147483647,2147483647,2147483647,2147483647,2147483647];
            showCuteMessage("💎 宝珠已满级！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 宝珠满级失败：" + e.message, "error");
         }
      }

      // 传送功能
      public static function teleportToSurface():void
      {
         try {
            Main.water = VT.createVT(1);
            showCuteMessage("🏠 地面主城传送成功，点击返回城镇即可！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 传送失败：" + e.message, "error");
         }
      }

      public static function teleportToUnderwater():void
      {
         try {
            Main.water.setValue(Math.random() * 999 + 1);
            showCuteMessage("🌊 海底主城传送成功，点击返回城镇即可！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 传送失败：" + e.message, "error");
         }
      }

      // 符石设置
      public static function setRuneStone(type:int, value:int):void
      {
         try {
            var runeNames:Array = ["勇者符石", "暗黑符石", "失落符石", "王者符石"];
            PaiHang_Data.jiFenArr[type].setValue(value);
            showCuteMessage("💫 自定义" + runeNames[type-1] + "成功！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 符石设置失败：" + e.message, "error");
         }
      }

      // 清空功能
      public static function clearPets():void
      {
         try {
            for(var i:int = 0; i < 50; i++) {
               Main.player1.getPetSlot().slot[i] = null;
               if(Main.P1P2) {
                  Main.player2.getPetSlot().slot[i] = null;
               }
            }
            showCuteMessage("🧹 所有宠物已清除！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 清空宠物失败：" + e.message, "error");
         }
      }

      public static function clearElves():void
      {
         try {
            for(var i:int = 0; i < 50; i++) {
               Main.player1.getElvesSlot().slot[i] = null;
               if(Main.P1P2) {
                  Main.player2.getElvesSlot().slot[i] = null;
               }
            }
            showCuteMessage("🧹 所有精灵已清除！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 清空精灵失败：" + e.message, "error");
         }
      }

      public static function clearTitles():void
      {
         try {
            for(var i:int = 0; i < 50; i++) {
               Main.player1.getTitleSlot().slot[i] = null;
               if(Main.P1P2) {
                  Main.player2.getTitleSlot().slot[i] = null;
               }
            }
            showCuteMessage("🧹 所有称号已清除！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 清空称号失败：" + e.message, "error");
         }
      }
   }
}
