package src
{
   import com.hotpoint.braveManIII.models.common.VT;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.pet.Pet;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.elves.ElvesFactory;
   import com.hotpoint.braveManIII.repository.equip.EquipFactory;
   import com.hotpoint.braveManIII.repository.gem.GemFactory;
   import com.hotpoint.braveManIII.repository.other.OtherFactory;
   import com.hotpoint.braveManIII.repository.pet.PetFactory;
   import com.hotpoint.braveManIII.repository.supplies.SuppliesFactory;
   import com.hotpoint.braveManIII.repository.title.TitleFactory;
   import com.hotpoint.braveManIII.views.achPanel.AchData;
   import com.hotpoint.braveManIII.views.cardPanel.CardPanel;
   import com.hotpoint.braveManIII.views.petPanel.NewPetPanel;
   import com.hotpoint.braveManIII.views.storagePanel.StoragePanel;
   import com.hotpoint.braveManIII.views.taskPanel.TaskData;
   import flash.display.MovieClip;
   
   public class CuteGMFunctions
   {
      // 角色相关功能
      public static function setPlayerLevel(player:int, level:int):void
      {
         try {
            if(player == 1) {
               Main.player1.level = VT.createVT(level);
               showCuteMessage("✨ 玩家1等级设置成功！等级：" + level, "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player2.level = VT.createVT(level);
               showCuteMessage("✨ 玩家2等级设置成功！等级：" + level, "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 设置等级失败：" + e.message, "error");
         }
      }
      
      public static function setPlayerGold(player:int, gold:int):void
      {
         try {
            if(player == 1) {
               Main.player1.gold.setValue(gold);
               showCuteMessage("💰 玩家1金币设置成功！金币：" + gold, "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player2.gold.setValue(gold);
               showCuteMessage("💰 玩家2金币设置成功！金币：" + gold, "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 设置金币失败：" + e.message, "error");
         }
      }
      
      public static function setPlayerExp(player:int, exp:int):void
      {
         try {
            if(player == 1) {
               Main.player1.exp.setValue(exp);
               showCuteMessage("⭐ 玩家1经验设置成功！经验：" + exp, "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player2.exp.setValue(exp);
               showCuteMessage("⭐ 玩家2经验设置成功！经验：" + exp, "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 设置经验失败：" + e.message, "error");
         }
      }
      
      public static function setPlayerSkillPoints(player:int, points:int):void
      {
         try {
            if(player == 1) {
               Main.player_1.data.points.setValue(points);
               showCuteMessage("🎯 玩家1技能点设置成功！技能点：" + points, "success");
            } else if(player == 2 && Main.P1P2) {
               Main.player_2.data.points.setValue(points);
               showCuteMessage("🎯 玩家2技能点设置成功！技能点：" + points, "success");
            } else {
               showCuteMessage("❌ 双人模式未开启！", "error");
            }
         } catch(e:Error) {
            showCuteMessage("❌ 设置技能点失败：" + e.message, "error");
         }
      }
      
      // 装备相关功能
      public static function giveAllEquipments():void
      {
         try {
            var equipIds:Array = [
               11001, 11002, 11003, 11004, 11005,  // 武器
               12001, 12002, 12003, 12004, 12005,  // 头盔
               13001, 13002, 13003, 13004, 13005,  // 上衣
               14001, 14002, 14003, 14004, 14005,  // 下装
               15001, 15002, 15003, 15004, 15005   // 鞋子
            ];
            
            var count:int = 0;
            for(var i:int = 0; i < equipIds.length; i++) {
               var equip:Equip = EquipFactory.creatEquip(equipIds[i]);
               if(equip) {
                  equip.setReinforceLevel(15);  // 设置强化等级
                  if(Main.player1.getBag().addEquipBag(equip)) {
                     count++;
                  }
               }
            }
            
            showCuteMessage("⚔️ 一键装备完成！获得 " + count + " 件装备", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键装备失败：" + e.message, "error");
         }
      }
      
      // 宠物相关功能
      public static function giveAllPets():void
      {
         try {
            var petIds:Array = [
               73100, 73104, 73105, 73107, 73110, 73111, 73113, 73117,
               73118, 73120, 73123, 73126, 73130, 73133, 73134, 73137,
               73139, 73141, 73144, 73146
            ];
            
            var count:int = 0;
            for(var i:int = 0; i < petIds.length; i++) {
               var pet:Pet = PetFactory.creatPet(petIds[i]);
               if(pet) {
                  pet.wuxing = VT.createVT(4);      // 最高五行
                  pet.setLv(30);                    // 30级
                  pet.addLvLimit(10);               // 增加等级上限
                  pet.xingge = VT.createVT(3);      // 优秀性格
                  
                  if(Main.player1.getPetSlot().addPetSlot(pet)) {
                     count++;
                  }
               }
            }
            
            showCuteMessage("🐾 一键宠物完成！获得 " + count + " 只宠物", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键宠物失败：" + e.message, "error");
         }
      }
      
      public static function giveAllElves():void
      {
         try {
            var count:int = 0;
            for(var i:int = 1; i <= 9; i++) {
               var elves:* = ElvesFactory.creatElves(i);
               if(elves) {
                  elves.setAllPoint(678);           // 设置属性点
                  elves.setLevel(10);               // 设置等级
                  elves.blueEquipNum = VT.createVT(0);
                  elves.pinkEquipNum = VT.createVT(0);
                  elves.goldEquipNum = VT.createVT(0);
                  
                  if(Main.player1.getElvesSlot().addElvesSlot(elves)) {
                     count++;
                  }
               }
            }
            
            showCuteMessage("🧚 一键精灵完成！获得 " + count + " 个精灵", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键精灵失败：" + e.message, "error");
         }
      }
      
      // 道具相关功能
      public static function giveAllGems():void
      {
         try {
            var gemIds:Array = [
               63001, 63002, 63003, 63004, 63005,  // 各种宝石
               63101, 63102, 63103, 63104, 63105,
               63201, 63202, 63203, 63204, 63205
            ];
            
            var count:int = 0;
            for(var i:int = 0; i < gemIds.length; i++) {
               var gem:Gem = GemFactory.creatGemById(gemIds[i]);
               if(gem && Main.player1.getBag().addGemBag(gem)) {
                  count++;
               }
            }
            
            showCuteMessage("💎 一键宝石完成！获得 " + count + " 个宝石", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键宝石失败：" + e.message, "error");
         }
      }
      
      public static function giveAllSupplies():void
      {
         try {
            var suppliesIds:Array = [
               21001, 21002, 21003, 21004, 21005,  // 各种消耗品
               21101, 21102, 21103, 21104, 21105,
               21201, 21202, 21203, 21204, 21205
            ];
            
            var count:int = 0;
            for(var i:int = 0; i < suppliesIds.length; i++) {
               var supplies:Supplies = SuppliesFactory.creatSupplies(suppliesIds[i]);
               if(supplies) {
                  supplies.setTimes(99);  // 设置数量
                  if(Main.player1.getBag().addSuppliesBag(supplies)) {
                     count++;
                  }
               }
            }
            
            showCuteMessage("🧪 一键消耗品完成！获得 " + count + " 种消耗品", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 一键消耗品失败：" + e.message, "error");
         }
      }
      
      // 系统相关功能
      public static function unlockAllStages():void
      {
         try {
            for(var i:int = 0; i < 150; i++) {
               Main.guanKa[i] = 3;
            }
            showCuteMessage("🗝️ 所有关卡已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 解锁关卡失败：" + e.message, "error");
         }
      }
      
      public static function completeAllTasks():void
      {
         try {
            for(var i:int = 0; i < 120000; i++) {
               TaskData.setOk(i);
            }
            showCuteMessage("✅ 所有任务已完成！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 完成任务失败：" + e.message, "error");
         }
      }
      
      public static function unlockAllAchievements():void
      {
         try {
            for(var i:int = 0; i < 10000; i++) {
               AchData.setOk(i);
            }
            showCuteMessage("🏆 所有成就已解锁！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 解锁成就失败：" + e.message, "error");
         }
      }
      
      public static function clearBag():void
      {
         try {
            for(var i:int = 0; i < 50; i++) {
               Main.player1.getBag().equipBag[i] = null;
               Main.player1.getBag().suppliesBag[i] = null;
               Main.player1.getBag().gemBag[i] = null;
               Main.player1.getBag().otherobjBag[i] = null;
               Main.player1.getBag().questBag[i] = null;
            }
            showCuteMessage("🧹 背包已清空！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 清空背包失败：" + e.message, "error");
         }
      }
      
      public static function clearStorage():void
      {
         try {
            for(var i:int = 0; i < 35; i++) {
               StoragePanel.storage.equipStorage[i] = null;
               StoragePanel.storage.suppliesStorage[i] = null;
               StoragePanel.storage.gemStorage[i] = null;
               StoragePanel.storage.otherobjStorage[i] = null;
            }
            showCuteMessage("🏪 仓库已清空！", "success");
         } catch(e:Error) {
            showCuteMessage("❌ 清空仓库失败：" + e.message, "error");
         }
      }
      
      // 显示可爱的消息提示
      public static function showCuteMessage(message:String, type:String = "info"):void
      {
         var color:uint = 0x00CED1;  // 默认青色

         switch(type) {
            case "success":
               color = 0x98FB98;  // 绿色
               break;
            case "error":
               color = 0xFF6B6B;  // 红色
               break;
            case "warning":
               color = 0xFFD93D;  // 黄色
               break;
         }

         // 使用游戏原有的消息系统，但添加可爱的样式
         try {
            NewMC.Open("文字提示", Main._stage, 480, 300, 60, 0, true, 2, message);
         } catch(e:Error) {
            // 如果NewMC不可用，使用trace输出
            trace("GM消息: " + message);
         }
      }
   }
}
