package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.elves.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.fanpaiPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.setProfession.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.utils.*;
   import src._data.*;
   import src.npc.*;
   import src.tool.*;
   
   public class YaoYuan extends MovieClip
   {
      public static var yaoArr:Array;
      
      public function YaoYuan()
      {
         super();
      }
      
      public static function Init() : *
      {
         var _loc1_:int = 0;
         if(!yaoArr)
         {
            yaoArr = new Array();
            _loc1_ = 0;
            while(_loc1_ < 4)
            {
               yaoArr[_loc1_] = VT.createVT();
               _loc1_++;
            }
         }
      }
   }
}

