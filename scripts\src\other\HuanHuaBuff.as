package src.other
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class HuanHuaBuff extends MovieClip
   {
      public static var h1:<PERSON>an<PERSON>uaBuff;
      
      public static var h2:<PERSON><PERSON><PERSON><PERSON>Buff;
      
      public var gjXX_num:Number = 0;
      
      public var buffTime:int = 0;
      
      public var who:Player;
      
      public function HuanHuaBuff()
      {
         super();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public static function Add_HuanHuaBuff(param1:Player) : *
      {
         var _loc2_:Equip = param1.data.getEquipSlot().getEquipFromSlot(2);
         var _loc3_:Equip = param1.data.getEquipSlot().getEquipFromSlot(5);
         if(_loc2_ && _loc2_.getHuanHua() == 5 || _loc3_ && _loc3_.getHuanHua() == 5)
         {
            if(param1 == Main.player_1)
            {
               if(!h1)
               {
                  h1 = new HuanHuaBuff();
                  h1.who = param1;
               }
               ++h1.gjXX_num;
            }
            else if(param1 == Main.player_2)
            {
               if(!h2)
               {
                  h2 = new HuanHuaBuff();
                  h2.who = param1;
               }
               ++h2.gjXX_num;
            }
         }
      }
      
      public function onENTER_FRAME(param1:*) : *
      {
         if(this.gjXX_num >= 8)
         {
            this.gjXX_num = 0;
            this.buffTime = 135;
         }
         if(this.buffTime > 0)
         {
            --this.buffTime;
            if(this.buffTime % 27 == 0)
            {
               if(this.who.hp.getValue() > 0)
               {
                  this.who.HpUp(1.6,2);
               }
            }
         }
      }
   }
}

