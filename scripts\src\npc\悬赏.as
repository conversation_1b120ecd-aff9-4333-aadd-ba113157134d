package src.npc
{
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import com.hotpoint.braveManIII.views.wantedPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   
   public class 悬赏 extends SimpleButton
   {
      public static var openXXX:Boolean;
      
      public static var _this:悬赏;
      
      public function 悬赏()
      {
         super();
         _this = this;
         addEventListener(MouseEvent.CLICK,this.Open);
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
      }
      
      public static function GOGO() : *
      {
         if(<PERSON><PERSON><PERSON>(_this) && !openXXX)
         {
            Main.world.moveChild.suo_mc.visible = true;
            Main.world.moveChild.suo_mc.gotoAndPlay(2);
            NewMC.Open("文字提示",Main._stage,480,400,45,0,true,2,"-= 悬赏任务开启! =-");
            openXXX = true;
         }
      }
      
      public function Open(param1:*) : *
      {
         if(Main.player1.getLevel() >= 50)
         {
            Main.allClosePanel();
            NewWantedPanel.open();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,45,0,true,2,"-= 悬赏任务50级后开启! =-");
         }
      }
      
      public function onADDED_TO_STAGE(param1:*) : *
      {
         Main.world.moveChild.suo_mc.mouseEnabled = false;
         Main.world.moveChild.suo_mc.mouseChildren = false;
         Main.world.moveChild.suo_mc.visible = true;
         if(Main.player1.getLevel() >= 50)
         {
            Main.world.moveChild.suo_mc.visible = false;
         }
      }
   }
}

