package src.tool
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   import src._data.*;
   
   public class Sel_NanDu extends MovieClip
   {
      public var NanDu1_mc:MovieClip;
      
      public var NanDu2_mc:MovieClip;
      
      public var NanDu3_mc:MovieClip;
      
      public var Star2:MovieClip;
      
      public var Star3:MovieClip;
      
      public var NanDu1_btn:SimpleButton;
      
      public var NanDu2_btn:SimpleButton;
      
      public var NanDu3_btn:SimpleButton;
      
      public var info_mc:MovieClip;
      
      public var info_txt:*;
      
      public var name_txt:*;
      
      public var t_txt:*;
      
      public var cj_0:*;
      
      public var cj_1:*;
      
      public var cj_2:*;
      
      public var cj_3:*;
      
      public var cj_4:*;
      
      public var cj_5:*;
      
      public var obj_1:*;
      
      public var obj_2:*;
      
      public var obj_3:*;
      
      public var obj_4:*;
      
      public var obj_5:*;
      
      public var obj_6:*;
      
      public var obj_7:*;
      
      public var obj_8:*;
      
      public var obj_9:*;
      
      public var obj_10:*;
      
      public var obj_11:*;
      
      public var obj_12:*;
      
      public var gmeName_txt:*;
      
      public var Close_btn:SimpleButton;
      
      public var type:uint = 1;
      
      private var selLV:uint = 1;
      
      public var GameNumNameArr:Array;
      
      public function Sel_NanDu()
      {
         var _loc2_:Shop_picNEW = null;
         this.GameNumNameArr = [[1,"落月之原"],[2,"落月之森"],[3,"冰雪废墟"],[4,"死亡流沙"],[5,"万年雪山"],[6,"废弃都市"],[7,"火山的噩梦"],[8,"堕落城堡"],[9,"幽灵船"],[10,"机械城"],[11,"雪狼巢穴"],[12,"火之祭坛"],[13,"暗影城"],[14,"暗夜遗迹"],[15,"机械试验场"],[16,"熔岩城堡"],[101,"神秘1"],[102,"神秘2"],[103,"神秘3"],[104,"神秘4"],[105,"神秘5"],[51,"艾尔之海"],[52,"安塔利亚"],[53,"阿肯色"],[54,"雅利安"],[55,"奥戈"],[56,"波塞笛亚"]];
         super();
         this.NanDu1_btn.addEventListener(MouseEvent.CLICK,this.NanDu1);
         this.NanDu2_btn.addEventListener(MouseEvent.CLICK,this.NanDu2);
         this.NanDu3_btn.addEventListener(MouseEvent.CLICK,this.NanDu3);
         this.NanDu1_btn.addEventListener(MouseEvent.ROLL_OVER,this.NanDu1_ROLL_OVER);
         this.NanDu2_btn.addEventListener(MouseEvent.ROLL_OVER,this.NanDu2_ROLL_OVER);
         this.NanDu3_btn.addEventListener(MouseEvent.ROLL_OVER,this.NanDu3_ROLL_OVER);
         var _loc1_:Number = 1;
         while(_loc1_ <= 12)
         {
            _loc2_ = new Shop_picNEW();
            this["obj_" + _loc1_] = _loc2_;
            addChild(_loc2_);
            _loc2_.x = 116.4 + (_loc1_ - 1) % 6 * 66.5;
            _loc2_.y = 376 + uint((_loc1_ - 1) / 6) * 69.5;
            this["obj_" + _loc1_].name = "obj_" + _loc1_;
            this["obj_" + _loc1_].addEventListener(MouseEvent.ROLL_OVER,this.SelObj);
            this["obj_" + _loc1_].addEventListener(MouseEvent.ROLL_OUT,this.SelObj_out);
            _loc1_++;
         }
         addChild(this.info_mc);
         this.Close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.info_mc.y = -2000;
         this.info_mc.x = -2000;
      }
      
      public function Open(param1:uint = 0, param2:uint = 0, param3:uint = 1) : *
      {
         this.info_mc.x = this.info_mc.y = -2000;
         this.type = param3;
         this.NanDu1_mc.gotoAndStop("d" + Main.gameNum.getValue());
         this.NanDu2_mc.gotoAndStop("d" + Main.gameNum.getValue());
         this.NanDu3_mc.gotoAndStop("d" + Main.gameNum.getValue());
         this.x = param1;
         this.y = param2;
         this.visible = true;
         this.NanDu2_btn.visible = this.NanDu3_btn.visible = false;
         TweenMax.to(this.NanDu2_mc,0,{"colorMatrixFilter":{"saturation":0}});
         TweenMax.to(this.NanDu3_mc,0,{"colorMatrixFilter":{"saturation":0}});
         TweenMax.to(this.Star2,0,{"colorMatrixFilter":{"saturation":0}});
         TweenMax.to(this.Star3,0,{"colorMatrixFilter":{"saturation":0}});
         if(Main.guanKa[Main.gameNum.getValue()] > 1)
         {
            this.NanDu2_btn.visible = true;
            TweenMax.to(this.NanDu2_mc,0,{"colorMatrixFilter":{"saturation":1}});
            TweenMax.to(this.Star2,0,{"colorMatrixFilter":{"saturation":1}});
         }
         if(Main.guanKa[Main.gameNum.getValue()] > 2)
         {
            this.NanDu3_btn.visible = true;
            TweenMax.to(this.NanDu3_mc,0,{"colorMatrixFilter":{"saturation":1}});
            TweenMax.to(this.Star3,0,{"colorMatrixFilter":{"saturation":1}});
         }
         this.NanDu1_ROLL_OVER();
         this.CengJiuShow();
      }
      
      public function Close(param1:* = null) : *
      {
         this.y = 5000;
         this.x = 5000;
         this.visible = false;
         Main._this.addChild(Play_Interface.interfaceX);
      }
      
      private function NanDu1(param1:*) : *
      {
         GameData.gameLV = 1;
         this.GameGo();
      }
      
      private function NanDu2(param1:*) : *
      {
         GameData.gameLV = 2;
         this.GameGo();
      }
      
      private function NanDu3(param1:*) : *
      {
         GameData.gameLV = 3;
         this.GameGo();
      }
      
      private function NanDu1_ROLL_OVER(param1:* = null) : *
      {
         this.selLV = 1;
         var _loc2_:Array = GaneObjFactory.GetNumArr(Main.gameNum.getValue(),1);
         var _loc3_:Number = 1;
         while(_loc3_ <= 12)
         {
            this["obj_" + _loc3_].gotoAndStop(_loc2_[_loc3_]);
            _loc3_++;
         }
      }
      
      private function NanDu2_ROLL_OVER(param1:*) : *
      {
         this.selLV = 2;
         var _loc2_:Array = GaneObjFactory.GetNumArr(Main.gameNum.getValue(),2);
         var _loc3_:Number = 1;
         while(_loc3_ <= 12)
         {
            this["obj_" + _loc3_].gotoAndStop(_loc2_[_loc3_]);
            _loc3_++;
         }
      }
      
      private function NanDu3_ROLL_OVER(param1:*) : *
      {
         this.selLV = 3;
         var _loc2_:Array = GaneObjFactory.GetNumArr(Main.gameNum.getValue(),3);
         var _loc3_:Number = 1;
         while(_loc3_ <= 12)
         {
            this["obj_" + _loc3_].gotoAndStop(_loc2_[_loc3_]);
            _loc3_++;
         }
      }
      
      private function SelObj(param1:MouseEvent) : *
      {
         var _loc2_:uint = uint((param1.target.name as String).substr(4,2));
         var _loc3_:Array = GaneObjFactory.GetInfo(Main.gameNum.getValue(),this.selLV,_loc2_);
         if(_loc3_[0] != 0)
         {
            this.info_mc.x = param1.target.x + 50;
            this.info_mc.y = param1.target.y - 5;
            this.info_mc.name_txt.text = _loc3_[0];
            this.info_mc.info_txt.text = _loc3_[1];
            if(_loc3_[2] == 2)
            {
               this.ColorX(this.info_mc.name_txt,"0x0066ff");
            }
            else if(_loc3_[2] == 3)
            {
               this.ColorX(this.info_mc.name_txt,"0xFF3399");
            }
            else if(_loc3_[2] == 4)
            {
               this.ColorX(this.info_mc.name_txt,"0xFF9933");
            }
         }
         else
         {
            this.info_mc.y = -2000;
            this.info_mc.x = -2000;
         }
      }
      
      private function SelObj_out(param1:MouseEvent) : *
      {
         this.info_mc.y = -2000;
         this.info_mc.x = -2000;
      }
      
      private function GameGo() : *
      {
         this.Close();
         SelMap.Close();
         Main.gameNum2.setValue(1);
         Main._this.Loading();
      }
      
      private function CengJiuShow() : *
      {
         var _loc1_:* = 0;
         this.gmeName_txt.text = "每日关卡成就";
         _loc1_ = 0;
         while(_loc1_ < this.GameNumNameArr.length)
         {
            if(Main.gameNum.getValue() == this.GameNumNameArr[_loc1_][0])
            {
               this.gmeName_txt.text = this.GameNumNameArr[_loc1_][1] + "每日关卡成就";
               break;
            }
            _loc1_++;
         }
         var _loc2_:Array = AchPanel.getStateByGkId(Main.gameNum.getValue());
         for(_loc1_ in _loc2_)
         {
            if(_loc2_[_loc1_] == 3)
            {
               this.ColorX(this["cj_" + _loc1_],"0x66FF00");
            }
            else
            {
               this.ColorX(this["cj_" + _loc1_],"0xFFFFFF");
            }
         }
      }
      
      private function ColorX(param1:*, param2:String) : *
      {
         var _loc3_:* = new TextFormat();
         _loc3_.color = param2;
         param1.setTextFormat(_loc3_);
      }
   }
}

