package src.tool
{
   import com.*;
   import com.ByteArrayXX.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.pet.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.plan.*;
   import com.hotpoint.braveManIII.models.task.*;
   import com.hotpoint.braveManIII.repository.chest.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.petEquip.*;
   import com.hotpoint.braveManIII.repository.plan.*;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.repository.quest.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.setProfession.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   import src._data.*;
   import src.other.*;
   
   public class TiaoShi extends MovieClip
   {
      public static var _this:TiaoShi;
      
      public static var Hpup:int;
      
      public static var tempVarTime:int;
      
      public static var tempVar:String;
      
      public static var qhXX:Boolean;
      
      public static var tempTimeArr:Array;
      
      public static var tempTime2:int;
      
      public static var tempTime3:int;
      
      private static var traceYN:Boolean = true;
      
      public static var chuanSongYN:Boolean = false;
      
      public static var XXX:Array = [];
      
      public static var tempNum:int = 0;
      
      public static var cwArr:Array = new Array();
      
      private var lunTan8Time:Timer;
      
      public function TiaoShi()
      {
         super();
         _this = this;
         if(!CeShi._this || !Main.tiaoShiYN)
         {
            this.y = -5000;
            this.x = -5000;
            this.visible = false;
            return;
         }
         if(Main.NoLog)
         {
            JieChu_txt.text = "已锁定";
         }
         else
         {
            JieChu_txt.text = "未锁定";
         }
         this.x = 916;
         this.y = 556;
         get_1_btn.addEventListener(MouseEvent.CLICK,this.GetP1Obj);
         get_2_btn.addEventListener(MouseEvent.CLICK,this.GetP2Obj);
         Op_btn.addEventListener(MouseEvent.CLICK,this.OP);
         KaiQi_btn.addEventListener(MouseEvent.CLICK,this.KaiQi);
         KaiQi_All_btn.addEventListener(MouseEvent.CLICK,this.KaiQiAll);
         P1_btn.addEventListener(MouseEvent.CLICK,this.P1_ZhuangTai);
         P2_btn.addEventListener(MouseEvent.CLICK,this.P2_ZhuangTai);
         tet_btn.addEventListener(MouseEvent.CLICK,this.Txt_show);
         sysTime_btn.addEventListener(MouseEvent.CLICK,this.sysTime);
         buy1.addEventListener(MouseEvent.CLICK,this.BuyOpenNum);
         buy2.addEventListener(MouseEvent.CLICK,this.BuyOpenNum);
         buy3.addEventListener(MouseEvent.CLICK,this.BuyOpenNum);
         buy4.addEventListener(MouseEvent.CLICK,this.BuyOpenNum);
         Q_buy1.addEventListener(MouseEvent.CLICK,this.Q_BuyOpenNum);
         name_btn.addEventListener(MouseEvent.CLICK,this.NewName);
         JieChu_btn.addEventListener(MouseEvent.CLICK,this.JieChu_Fun);
         ChongWu1_btn.addEventListener(MouseEvent.CLICK,this.CW_Fun1);
         ChongWu2_btn.addEventListener(MouseEvent.CLICK,this.CW_Fun2);
         ChongWu_close1_btn.addEventListener(MouseEvent.CLICK,this.CW_Fun1_C);
         ChongWu_close2_btn.addEventListener(MouseEvent.CLICK,this.CW_Fun2_C);
         other_btn.addEventListener(MouseEvent.CLICK,this.other_Fun);
         other2_btn.addEventListener(MouseEvent.CLICK,this.other_Fun2);
         X61_btn.addEventListener(MouseEvent.CLICK,this.XX61Fun);
         CJ_0_btn.addEventListener(MouseEvent.CLICK,this.CJ0_Fun);
         liBaoPoint_btn.addEventListener(MouseEvent.CLICK,this.SetLiBaoPoint);
         liBaoPoint2_btn.addEventListener(MouseEvent.CLICK,this.SetLiBaoPoint2);
         PaiHangTiJiao_btn.addEventListener(MouseEvent.CLICK,this.PaiHangTiJiao);
         Sel_btn.addEventListener(MouseEvent.CLICK,this.SEL_PaiHangTiJiao);
         NewData_btn.addEventListener(MouseEvent.CLICK,this.NewDataXXX);
         temp_btn.addEventListener(MouseEvent.CLICK,this.TempFun);
         QingKong_btn.addEventListener(MouseEvent.CLICK,this.QingKong_Fun);
         chunSong_btn.addEventListener(MouseEvent.CLICK,this.ChuanSong_Fun);
         GoGame_btn.addEventListener(MouseEvent.CLICK,this.GoGame_Fun);
         Map0_btn.addEventListener(MouseEvent.CLICK,this.Map0Fun);
         sysTime_txt.text = Main.serverTime.getValue();
         tiaozan_btn.addEventListener(MouseEvent.CLICK,this.TiaoZhan0);
         trace_btn.addEventListener(MouseEvent.CLICK,this.TraceFun);
      }
      
      public static function ShowPaiHang(param1:String = "") : *
      {
         if(Boolean(_this) && Boolean(_this.temp_txt))
         {
            _this.temp_txt.text += "\n回调信息:" + param1;
         }
      }
      
      public static function txtShow(param1:String) : *
      {
         if(_this && _this.temp_txt && Boolean(traceYN))
         {
            _this.temp_txt.text += "\n" + param1;
         }
      }
      
      private function TraceFun(param1:*) : *
      {
         if(traceYN)
         {
            traceYN = false;
         }
         else
         {
            traceYN = true;
         }
      }
      
      private function TiaoZhan0(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         for(_loc2_ in PaiHang_Data.sArr2)
         {
            if(PaiHang_Data.sArr2[_loc2_])
            {
               for(_loc3_ in PaiHang_Data.sArr2[_loc2_])
               {
                  if(PaiHang_Data.sArr2[_loc2_][_loc3_])
                  {
                     TiaoShi.txtShow("挑战模式成绩>>>" + _loc2_ + "," + _loc3_ + " = " + PaiHang_Data.sArr2[_loc2_][_loc3_].getValue());
                  }
               }
            }
         }
      }
      
      private function Map0Fun(param1:*) : *
      {
         if(Main.water.getValue() == 1)
         {
            Map0_txt.text = "海底";
            Main.water.setValue(Math.random() * 999 + 1);
         }
         else
         {
            Map0_txt.text = "地面";
            Main.water = VT.createVT(1);
         }
      }
      
      private function GoGame_Fun(param1:*) : *
      {
         GameData.winYN = false;
         Main.gameNum.setValue(uint(GuanKa1_txt.text));
         Main.gameNum2.setValue(uint(GuanKa2_txt.text));
         GameData.gameLV = uint(GuanKa3_txt.text);
         Main._this.Loading();
      }
      
      private function ChuanSong_Fun(param1:*) : *
      {
         if(chuanSongYN)
         {
            chuanSongYN = false;
         }
         else
         {
            chuanSongYN = true;
         }
      }
      
      private function QingKong_Fun(param1:*) : *
      {
         temp_txt.text = "";
      }
      
      private function OP(param1:*) : *
      {
         if(this.x != 0)
         {
            this.y = 0;
            this.x = 0;
         }
         else
         {
            this.x = 916;
            this.y = 556;
         }
      }
      
      private function GetP1Obj(param1:*) : *
      {
         var _loc2_:int = 0;
         if(get_1_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num1_txt.text))
            {
               Main.player_1.data.getBag().addEquipBag(EquipFactory.createEquipByID(int(get_1_txt.text)));
               _loc2_++;
            }
         }
         if(get_2_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num2_txt.text))
            {
               Main.player_1.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(int(get_2_txt.text)));
               _loc2_++;
            }
         }
         if(get_3_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num3_txt.text))
            {
               Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(get_3_txt.text)));
               _loc2_++;
            }
         }
         if(get_4_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num4_txt.text))
            {
               Main.player_1.data.getBag().addOtherobjBag(OtherFactory.creatOther(int(get_4_txt.text)));
               _loc2_++;
            }
         }
         if(get_5_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num5_txt.text))
            {
               Main.player_1.data.getBag().addQuestBag(QuestFactory.creatQust(int(get_5_txt.text)));
               _loc2_++;
            }
            TaskData.setOk(int(get_5_txt.text));
         }
      }
      
      private function GetP2Obj(param1:*) : *
      {
         var _loc2_:int = 0;
         if(!Main.P1P2)
         {
            return;
         }
         if(get_1_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num1_txt.text))
            {
               Main.player_2.data.getBag().addEquipBag(EquipFactory.createEquipByID(int(get_1_txt.text)));
               _loc2_++;
            }
         }
         if(get_2_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num1_txt.text))
            {
               Main.player_2.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(int(get_2_txt.text)));
               _loc2_++;
            }
         }
         if(get_3_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num1_txt.text))
            {
               Main.player_2.data.getBag().addGemBag(GemFactory.creatGemById(int(get_3_txt.text)));
               _loc2_++;
            }
         }
         if(get_4_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num1_txt.text))
            {
               Main.player_2.data.getBag().addOtherobjBag(OtherFactory.creatOther(int(get_4_txt.text)));
               _loc2_++;
            }
         }
         if(get_5_txt.text != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < int(get_num1_txt.text))
            {
               Main.player_2.data.getBag().addQuestBag(QuestFactory.creatQust(int(get_5_txt.text)));
               _loc2_++;
            }
            TaskData.setOk(int(get_5_txt.text));
         }
      }
      
      private function KaiQiAll(param1:*) : *
      {
         var _loc2_:int = 0;
         while(_loc2_ < 150)
         {
            Main.guanKa[_loc2_] = int(GuanKa3_txt.text);
            _loc2_++;
         }
      }
      
      private function KaiQi(param1:*) : *
      {
         Main.guanKa[int(GuanKa1_txt.text)] = int(GuanKa2_txt.text);
      }
      
      private function P1_ZhuangTai(param1:*) : *
      {
         if(int(p1_txt.text) != -1)
         {
            Main.player_1.data.setLevel(int(p1_txt.text));
         }
         if(int(p2_txt.text) != -1)
         {
            Main.player_1.data.killPoint.setValue(int(p2_txt.text));
         }
         if(int(p3_txt.text) != -1)
         {
            Main.player1.gold.setValue(int(p3_txt.text));
         }
         if(int(p4_txt.text) != -1)
         {
            Main.player_1.data.points.setValue(int(p4_txt.text));
         }
         if(int(p5_txt.text) != -1)
         {
            Enemy.gongJiPOWER.setValue(int(p5_txt.text));
         }
         if(int(p6_txt.text) != -1)
         {
            Main.player_1.fangYuPOWER.setValue(int(p6_txt.text));
         }
         if(int(p7_txt.text) != -1)
         {
            Main.player_1.ExpUP(int(p7_txt.text),2);
         }
         if(int(p8_txt.text) != -1)
         {
            HitXX.baoJiUP.setValue(int(p8_txt.text));
         }
         if(int(p9_txt.text) != -1)
         {
            Hpup = int(p9_txt.text);
         }
      }
      
      private function P2_ZhuangTai(param1:*) : *
      {
         if(!Main.P1P2)
         {
            return;
         }
         if(int(p1_txt.text) != -1)
         {
            Main.player_2.data.setLevel(int(p1_txt.text));
         }
         if(int(p2_txt.text) != -1)
         {
            Main.player_2.data.AddKillPoint(int(p2_txt.text));
         }
         if(int(p3_txt.text) != -1)
         {
            Main.player2.addGold(int(p3_txt.text));
         }
         if(int(p4_txt.text) != -1)
         {
            Main.player_2.data.addPoint(int(p4_txt.text));
         }
         if(int(p5_txt.text) != -1)
         {
            Enemy.gongJiPOWER.setValue(int(p5_txt.text));
         }
         if(int(p6_txt.text) != -1)
         {
            Main.player_2.fangYuPOWER.setValue(int(p6_txt.text));
         }
         if(int(p7_txt.text) != -1)
         {
            Main.player_2.ExpUP(int(p7_txt.text),2);
         }
         if(int(p8_txt.text) != -1)
         {
            HitXX.baoJiUP.setValue(int(p8_txt.text));
         }
         if(int(p9_txt.text) != -1)
         {
            Hpup = int(p9_txt.text);
         }
      }
      
      private function sysTime(param1:*) : *
      {
         if(int(sysTime_txt.text) != -1)
         {
            Main.serverTime.setValue(int(sysTime_txt.text));
         }
         QianDao.year = sysTime_txt.text.substr(0,4);
         QianDao.month = sysTime_txt.text.substr(4,2);
         QianDao.day = sysTime_txt.text.substr(6,2);
         Main.saveTimeX = int(sysTime_txt.text);
         txtShow("设置时间: " + Main.saveTimeX);
      }
      
      private function BuyOpenNum(param1:MouseEvent) : *
      {
         ShopKillPoint.KaiQiShopArr2[0] = 0;
         var _loc2_:int = int((param1.target.name as String).substr(3,1));
         if(ShopKillPoint.KaiQiShopArr2[_loc2_] == true)
         {
            ShopKillPoint.KaiQiShopArr2[_loc2_] = false;
            ShopKillPoint.shopX["shopX_" + _loc2_]["skin"].visible = true;
         }
         else
         {
            ShopKillPoint.KaiQiShopArr2[_loc2_] = true;
            ShopKillPoint.shopX["shopX_" + _loc2_]["skin"].visible = false;
         }
      }
      
      private function Q_BuyOpenNum(param1:MouseEvent) : *
      {
         if(Q_buy_txt.text == "1")
         {
            Main.player1.getStampSlot().setKeySlot11();
         }
         else if(Q_buy_txt.text == "2")
         {
            Main.player2.getStampSlot().setKeySlot11();
         }
         else if(Q_buy_txt.text == "0")
         {
            Main.player1.getStampSlot().setKeySlot11();
            Main.player2.getStampSlot().setKeySlot11();
         }
      }
      
      private function NewName(param1:*) : *
      {
         if(name_txt.text != 0)
         {
            Main.logName = name_txt.text;
            Main.logNameSave = "";
            Main.varX_old = 0;
         }
         if(var_txt.text != 0)
         {
            Main.varX = int(var_txt.text);
         }
      }
      
      private function JieChu_Fun(param1:*) : *
      {
         Main.noSave = -43998785;
         Main.NoLog = 0;
         Main.NoLogInfo = new Array();
      }
      
      private function CW_Fun1(param1:*) : *
      {
         Main.player_1.playerCW = null;
         Main.player1.getPetSlot().addPetSlot(PetFactory.creatPet(73101));
         Main.player1.getPetSlot().addPetSlot(PetFactory.creatPet(73500));
      }
      
      private function CW_Fun2(param1:*) : *
      {
         Main.player_2.playerCW = null;
         Main.player2.getPetSlot().addPetSlot(PetFactory.creatPet(73101));
         Main.player2.getPetSlot().addPetSlot(PetFactory.creatPet(73500));
      }
      
      private function CW_Fun1_C(param1:*) : *
      {
         if(Boolean(Main.player_1.playerCW) && Boolean(Main.player_1.playerCW.data.addExp(99999)))
         {
            NewMC.Open("宠物升级",Main.player_1.playerCW);
         }
         if(Boolean(Main.player_1.playerJL) && Boolean(Main.player_1.playerJL.data.addExp(99999)))
         {
            NewMC.Open("宠物升级",Main.player_1.playerCW);
         }
      }
      
      private function CW_Fun2_C(param1:*) : *
      {
         if(Main.player_2.playerCW.data.addExp(99999))
         {
            NewMC.Open("宠物升级",Main.player_2.playerCW);
         }
      }
      
      private function ShowInfo() : *
      {
         temp_txt.text += "Play_Interface.interfaceX._Move_mc.x = " + Play_Interface.interfaceX._Move_mc.x + "\n";
         temp_txt.text += "logName = " + Main.logName + "\n";
         temp_txt.text += "Uid = " + Main.userId + "\n";
         temp_txt.text += "当前版本:" + Main.varX + " ,存档版本:" + Main.varX_old + "\n";
         temp_txt.text += "存档日期 = " + TiaoShi.tempVarTime + "\n";
         if(Main.logNameSave == "" && Main.varX_old < 182 || Boolean(MD5contrast.contrast(Main.logName,Main.logNameSave,Main.saveNum)))
         {
            temp_txt.text += "存档:" + (int(Main.logNameSave.substr(5,1)) + 1) + ", sn:" + Main.logNameSave.substr(6,8) + "\n" + "验证信息: " + MD5contrast.contrastID + "\n";
         }
         else
         {
            temp_txt.text += "验证失败!!\n存档:" + (int(Main.logNameSave.substr(5,1)) + 1) + ", sn:" + Main.logNameSave.substr(6,8) + "\n" + "验证信息: 错误#" + MD5contrast.errorID + "\n";
         }
         if(Main.NoLog == 0)
         {
            temp_txt.text += "作弊检测:验证通过\n";
         }
         else if(Main.NoLog >= 2)
         {
            temp_txt.text += "\n>>>>>>>>>>>>>>>>>>>\n\n\n 作弊检测:已锁定 \n";
            if(Main.NoLogInfo[2])
            {
               temp_txt.text += "\n #2 修改攻击倍数" + Main.NoLogInfo[2] + "\n";
            }
            if(Main.NoLogInfo[3])
            {
               temp_txt.text += "\n #3 修改金币" + Main.NoLogInfo[3] + "\n";
            }
            if(Main.NoLogInfo[4])
            {
               temp_txt.text += "\n #4 物品堆叠上限" + Main.NoLogInfo[4] + "\n";
            }
            if(Main.NoLogInfo[5])
            {
               temp_txt.text += "\n #5 防复制" + Main.NoLogInfo[5] + "\n";
            }
            if(Main.NoLogInfo[7])
            {
               temp_txt.text += "\n #7 宝石类复制" + Main.NoLogInfo[7] + "\n";
            }
            if(Main.NoLogInfo[8])
            {
               temp_txt.text += "\n #8 其他类复制" + Main.NoLogInfo[8] + "\n";
            }
            if(Main.NoLogInfo[10])
            {
               temp_txt.text += "\n #10 修改技能表XML \n";
            }
            if(Main.NoLogInfo[11])
            {
               temp_txt.text += "\n #11 修改暴击闪避" + Main.NoLogInfo[11] + " \n";
            }
            if(Main.NoLogInfo[12] is int)
            {
               temp_txt.text += "\n #12 点券不足 领取礼包\n";
            }
            if(Main.NoLogInfo[13])
            {
               temp_txt.text += "\n #13 Vip称号\n";
            }
            if(Main.NoLogInfo[15])
            {
               temp_txt.text += "\n #15 跳关修改!\n";
            }
            temp_txt.text += "\n #Main.NoLog = " + Main.NoLog + ", NoLogInfo = " + Main.NoLogInfo;
            temp_txt.text += "\n\n\n>>>>>>>>>>>>>>>>>>>\n";
         }
         temp_txt.text += "\n已领取礼包 = " + Main.lingQueArr + ", length" + Main.lingQueArr.length;
         temp_txt.text += "\n已领取累计礼包 = " + Main.lingQueArr2 + ", length" + Main.lingQueArr2.length;
         temp_txt.text += "\n活动礼包领取 = " + TeShuHuoDong.TeShuHuoDongArr + ", length" + TeShuHuoDong.TeShuHuoDongArr.length + "\n";
         if(Shop4399.totalRecharged.getValue() != -2)
         {
            temp_txt.text += "\n累计充值:" + Shop4399.totalRecharged.getValue() + "点券";
            temp_txt.text += "\n礼包前充值:" + Shop_LiBao.HDpoint.getValue() + "点券";
            temp_txt.text += "\n活动赠送卷:" + TeShuHuoDong.TeShuHuoDongArr[0] + "点券";
         }
         else
         {
            temp_txt.text += "请稍候再试\n";
         }
         CheckTest.Testing();
         CheckTest.TestingOther();
         Main.wts.wantedTaskComplete();
         temp_txt.text += "\n Main.noSave = " + Main.noSave;
         temp_txt.text += "Shop4399.ShopArr.length = " + ShopFactory.GetObjData_AllNum();
      }
      
      private function XX61Fun(param1:*) : *
      {
         AchData.cjPoint_1.setValue(int(X61info_txt.text));
         AchData.cjPoint_2.setValue(int(X61_txt.text));
      }
      
      private function other_Fun(param1:*) : *
      {
         Main.lingQueArr = [];
         Main.lingQueArr2 = [];
         TeShuHuoDong.TeShuHuoDongArr = [];
      }
      
      private function other_Fun2(param1:*) : *
      {
      }
      
      private function CJ0_Fun(param1:*) : *
      {
         AchData.initAllAc();
      }
      
      private function SetLiBaoPoint(param1:*) : *
      {
         Shop_LiBao.HDpoint.setValue(int(liBaoPoint2_txt.text));
      }
      
      private function SetLiBaoPoint2(param1:*) : *
      {
         Shop4399.totalRecharged.setValue(int(liBaoPoint3_txt.text));
      }
      
      private function SEL_PaiHangTiJiao(param1:*) : *
      {
         PK_UI.Open();
      }
      
      private function PaiHangTiJiao(param1:*) : *
      {
         var _loc2_:Array = [];
         _loc2_[0] = new Object();
         _loc2_[0].rId = int(PaiHangID_txt.text);
         _loc2_[0].score = uint(PaiHangTiJiao_txt.text);
         Api_4399_All.SubmitScore(Main.saveNum,_loc2_);
      }
      
      private function NewDataXXX(param1:*) : *
      {
         TiaoShi.txtShow(TimeBox.TimeXX(this.CeShiMd5_PLayerData));
      }
      
      private function CeShiMd5_PLayerData() : *
      {
         TiaoShi.txtShow("Main.player1 - md5:" + MD5contrast.getObj_MD5String(Main.player1));
      }
      
      private function TempShow() : *
      {
         temp_txt.text = "";
         MD5contrast.getObj_MD5String(Main.player_1.data.getBag());
      }
      
      private function TempFun(param1:*) : *
      {
         TiaoShi.txtShow("怪物数据验证 md5:" + Data2.Enemy_md5_2);
         TiaoShi.txtShow("技能列表验证 md5:" + Data2.skill_md5_2);
         TiaoShi.txtShow("任务列表验证 md5:" + Data2.renWu_md5_2);
      }
      
      public function testXX() : *
      {
         var _loc1_:* = undefined;
         for(_loc1_ in PaiHang_Data.jiFenArr)
         {
            TiaoShi.txtShow("排行榜 >>>" + _loc1_ + ">" + PaiHang_Data.jiFenArr[_loc1_].getValue());
            if(PaiHang_Data.jiFenArr[_loc1_].getValue() < 0)
            {
               PaiHang_Data.jiFenArr[_loc1_] = VT.createVT(0);
            }
         }
      }
      
      private function Txt_show(param1:*) : *
      {
         var _loc6_:Array = null;
         var _loc7_:Class = null;
         var _loc8_:MovieClip = null;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:int = 0;
         var _loc12_:Enemy = null;
         var _loc13_:Array = null;
         var _loc14_:int = 0;
         var _loc15_:Array = null;
         var _loc16_:Array = null;
         temp_txt.text = "";
         var _loc2_:int = int(type_txt.text);
         var _loc3_:int = int(type2_txt.text);
         var _loc4_:int = int(type3_txt.text);
         var _loc5_:int = int(type4_txt.text);
         if(_loc2_ == 1)
         {
            this.ShowInfo();
            this.testXX();
         }
         else if(_loc2_ != 2)
         {
            if(_loc2_ == 3)
            {
               if(_loc3_ == 1)
               {
                  JinHuaPanel2.open(true);
               }
               else
               {
                  JinHuaPanel.open(true,_loc4_,_loc5_);
               }
            }
            else if(_loc2_ == 4)
            {
               YueKa_Interface.yueKaTime = Main.serverDayNum + 30;
            }
            else if(_loc2_ == 5)
            {
               _loc6_ = [15];
               Api_4399_GongHui.getNum(_loc6_);
            }
            else if(_loc2_ == 6)
            {
               NewPetPanel.tiaoshi();
            }
            else if(_loc2_ == 7)
            {
               NewPetPanel.testSkillxx();
            }
            else if(_loc2_ == 8)
            {
               Play_Interface.TiShiShow(false,110097);
            }
            else if(_loc2_ == 9)
            {
               NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(_loc3_));
            }
            else if(_loc2_ == 10)
            {
               Main.player1.getPetSlot().addPetSlot(PetFactory.creatPet(_loc3_));
            }
            else if(_loc2_ == 11)
            {
               Main.wts.getWantedTaskFromSlot(1).addTimes(50);
               Main.wts.getWantedTaskFromSlot(4).addTimes(50);
               Main.wts.getWantedTaskFromSlot(7).addTimes(50);
               Main.wts.getWantedTaskFromSlot(10).addTimes(50);
               Main.wts.getWantedTaskFromSlot(13).addTimes(50);
               Main.wts.getWantedTaskFromSlot(16).addTimes(50);
               Main.wts.getWantedTaskFromSlot(19).addTimes(50);
               Main.wts.getWantedTaskFromSlot(22).addTimes(50);
               Main.wts.getWantedTaskFromSlot(25).addTimes(50);
            }
            else if(_loc2_ == 12)
            {
               Yayale_LingQue.Open();
            }
            else if(_loc2_ == 13)
            {
               Yayale_LingQue.DuiHuanOpen(Main.player_1);
            }
            else if(_loc2_ == 14)
            {
               _loc6_ = [47];
               Api_4399_GongHui.getNum(_loc6_);
            }
            else if(_loc2_ == 15)
            {
               _loc6_ = [135];
               Api_4399_GongHui.getNum(_loc6_);
            }
            else if(_loc2_ == 16)
            {
               _loc6_ = [49];
               Api_4399_GongHui.getNum(_loc6_);
            }
            else if(_loc2_ == 17)
            {
               _loc6_ = [50];
               Api_4399_GongHui.getNum(_loc6_);
            }
            else if(_loc2_ == 18)
            {
               _loc6_ = [127,126];
               Api_4399_GongHui.getNum(_loc6_);
            }
            else if(_loc2_ == 20)
            {
               SixOne_Interface.okTimes2021.setValue(_loc3_);
               txtShow("六一活动完成次数:" + SixOne_Interface.okTimes2021.getValue());
            }
            else if(_loc2_ == 21)
            {
               _loc7_ = NewLoad.OtherData.getClass("_PK_UI") as Class;
               _loc8_ = new _loc7_();
               Main._stage.addChild(xx);
            }
            else if(_loc2_ == 22)
            {
               JiFenLingQue2.DengLuUp();
            }
            else if(_loc2_ == 23)
            {
               TiaoShi.txtShow("清除转职");
               _loc9_ = 0;
               _loc10_ = 0;
               while(_loc10_ < 3)
               {
                  if(Main.player1._transferArr[_loc10_])
                  {
                     _loc9_++;
                  }
                  _loc10_++;
               }
               if(_loc9_ > 1)
               {
                  TiaoShi.txtShow("清除转职---------> 1P");
                  SkillPanel.open();
                  SkillPanel.close();
                  SkillPanel._instance.aginP1();
                  _loc10_ = 0;
                  while(_loc10_ < 3)
                  {
                     if(Main.player1._transferArr[_loc10_])
                     {
                        Main.player1._transferArr[_loc10_] = false;
                     }
                     _loc10_++;
                  }
                  TiaoShi.txtShow("清除转职---------> 1P end");
               }
            }
            else if(_loc2_ == 24 && _loc3_ == 99)
            {
               GengXin.saveTime = "2014/07/10";
               Api_4399_GongHui.upNum(57);
               Api_4399_GongHui.upNum(58);
               Api_4399_GongHui.getNum([57,58]);
               GengXin.CaXun();
            }
            else if(_loc2_ == 25)
            {
               if(_loc3_ == 1)
               {
                  JiFenLingQue3.Post(true);
               }
               else
               {
                  JiFenLingQue3.Post();
               }
            }
            else if(_loc2_ == 26)
            {
               Main.player_1.playerJL.jiNengBD_ID = _loc3_;
               TiaoShi.txtShow("精灵被动技能 = " + _loc3_);
            }
            else if(_loc2_ == 27)
            {
               TiaoShi.txtShow("精灵主动技能 = " + int(Main.player_1.playerJL.jiNengZD_ID));
            }
            else if(_loc2_ == 28)
            {
               _loc9_ = int(TaskData.zhuXianNum());
               TiaoShi.txtShow("主线任务完成数量 = " + _loc9_);
            }
            else if(_loc2_ == 29)
            {
               SJ_Interface.Open();
            }
            else if(_loc2_ == 30)
            {
               _loc10_ = 0;
               while(_loc10_ <= 4)
               {
                  if(_loc10_ != 3)
                  {
                     TiaoShi.txtShow("i=" + _loc10_ + "\n");
                     if(PaiHang_Data.sArr2[_loc10_])
                     {
                        if(PaiHang_Data.sArr2[_loc10_].length > 0)
                        {
                           for(_loc11_ in PaiHang_Data.sArr2[_loc10_])
                           {
                              TiaoShi.txtShow(PaiHang_Data.sArr2[_loc10_][_loc11_].getValue() + ",");
                           }
                        }
                        TiaoShi.txtShow("\n");
                     }
                  }
                  _loc10_++;
               }
            }
            else if(_loc2_ == 31)
            {
               ZhuanPan.saveTime = 0;
               ZhuanPan.Open();
            }
            else if(_loc2_ == 32)
            {
               ZhuanPan.Num1time = 5;
            }
            else if(_loc2_ == 33)
            {
               Main.player1.getElvesSlot().addSlotNum(1);
            }
            else if(_loc2_ == 34)
            {
               Main.player1.getElvesSlot().getElvesFromSlot(_loc3_).delPinkEquip();
            }
            else if(_loc2_ == 35)
            {
               Main.player1.getElvesSlot().getElvesFromSlot(0).delPinkEquip();
            }
            else if(_loc2_ == 36)
            {
               PK_UI.xPk_Player_min = _loc3_;
            }
            else if(_loc2_ == 37)
            {
               PK_UI.jiFenArr[1].setValue(_loc3_);
            }
            else if(_loc2_ == 38)
            {
               PK_UI.Pk_timeNum = _loc3_;
            }
            else if(_loc2_ == 40)
            {
               TiaoShi.txtShow("调试#40 !!竞技场重新刷新玩家~~~~~~~~~~~~~~~~~~~~~");
               PK_UI.reAddOtherPlayer();
            }
            else if(_loc2_ == 41)
            {
               ChongZhi_Interface.HDpoint9.setValue(_loc3_);
               TiaoShi.txtShow("调试HDpoint9 = " + ChongZhi_Interface.HDpoint9.getValue());
            }
            else if(_loc2_ == 42)
            {
               TiaoShi.txtShow(PK_UI.jiFenArr[7]);
               ++PK_UI.jiFenArr[7];
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63300));
            }
            else if(_loc2_ == 43)
            {
               TiaoShi.txtShow("查询" + Api_4399_All.xxxArr.length + ":" + Api_4399_All.xxxArr);
            }
            else if(_loc2_ == 44)
            {
               Main.player1.getPetSlot().addPetSlot(Pet.creatPet(_loc3_));
               Main.player1.getPetSlot().getPetFromSlot(0).setLv(20);
            }
            else if(_loc2_ == 45)
            {
               Main.player1.getPetSlot().getPetFromSlot(0).tiaoshiSkill();
            }
            else if(_loc2_ == 46)
            {
               NewYear_Interface.addJuan();
            }
            else if(_loc2_ == 47)
            {
               NewYear_Interface.addKey();
               for(_loc10_ in Enemy.All)
               {
                  (Enemy.All[_loc10_] as Enemy).life.setValue(2);
               }
            }
            else if(_loc2_ == 48)
            {
               ChestFactory.getDataByPro(_loc3_);
            }
            else if(_loc2_ == 49)
            {
               Main.player_1.playerJL.jiNengZD_num = 100;
            }
            else if(_loc2_ == 50)
            {
               Main.player1.getPetSlot().addPetSlot(Pet.creatPet(73126));
               Main.player1.getPetSlot().getPetFromSlot(0).setLv(20);
               Main.player1.getPetSlot().getPetFromSlot(0).setSkill(0);
               Main.player1.getPetSlot().getPetFromSlot(0).setSkill(1);
               Main.player1.getPetSlot().getPetFromSlot(0).setSkill(2);
               Main.player1.getPetSlot().getPetFromSlot(0).setSkill(3);
               NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17110));
               NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17105));
               NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17100));
               NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17095));
            }
            else if(_loc2_ == 51)
            {
               FiveOne_Interface.arrAll[2] += 5;
               FiveOne_Interface.arrAll[3] += 5;
               FiveOne_Interface.arrAll[4] += 5;
               if(_loc2_ == 99)
               {
                  FiveOne_Interface.arrAll = [10,0,0,0,0,0,0,false,0];
               }
            }
            else if(_loc2_ == 52)
            {
               Main.player1.setEXP(-2147483645);
               TiaoShi.txtShow("-2147483645");
            }
            else if(_loc2_ == 53)
            {
               PK_UI.jiFenArr[0].setValue(999);
               txtShow("重置竞技场积分领取时间!");
            }
            else if(_loc2_ == 54)
            {
               PK_UI.jiFenArr[1].setValue(PK_UI.jiFenArr[1].getValue() + _loc3_);
            }
            else if(_loc2_ == 55)
            {
               TiaoShi.txtShow("技能石强化表长度 = " + GemProbabilityFactory.probabilityData.length);
            }
            else if(_loc2_ == 56)
            {
               if(qhXX)
               {
                  qhXX = false;
               }
               else
               {
                  qhXX = true;
               }
               TiaoShi.txtShow("强化XX = " + qhXX);
            }
            else if(_loc2_ == 57)
            {
               (PaiHang_Data.sArr2[_loc4_][_loc5_] as VT).setValue(_loc3_);
            }
            else if(_loc2_ == 58)
            {
               TiaoShi.txtShow("存档时间:" + Main.saveTimeX);
            }
            else if(_loc2_ == 61)
            {
               Main.HuiTie8Arr = [0,0,0,0,0,0,0,0,0,0];
               TiaoShi.txtShow("国庆活动领取奖励 清零!!!");
               Api_4399_GongHui.getNum([137]);
               TiaoShi.txtShow("国庆活动数值 查询~~~~~~~~~~~~~~~~~~~~~");
            }
            else if(_loc2_ == 62)
            {
               TiaoShi.txtShow("国庆活动数值 = " + Main.HuiTie8Arr);
            }
            else if(_loc2_ == 63)
            {
               if(_loc3_ == 2)
               {
                  Main.player_2.data.getPetSlot().addPetSlotNum(1);
               }
               else
               {
                  Main.player_1.data.getPetSlot().addPetSlotNum(1);
               }
            }
            else if(_loc2_ == 64)
            {
               TiaoShi.txtShow("当前剩余怪物数量:" + Enemy.All.length);
               for(_loc10_ in Enemy.All)
               {
                  _loc12_ = Enemy.All[_loc10_];
                  TiaoShi.txtShow(_loc10_ + "怪物ID:" + _loc12_.id + ", life:" + _loc12_.life.getValue() + ", x:" + _loc12_.x + ", y:" + _loc12_.y);
               }
            }
            else if(_loc2_ == 65)
            {
               Enemy.All = new Array();
               TiaoShi.txtShow("当前剩余怪物数量清空");
            }
            else if(_loc2_ == 66)
            {
               TiaoShi.txtShow("杀死所有怪:" + Enemy.All.length);
               for(_loc10_ in Enemy.All)
               {
                  _loc12_ = Enemy.All[_loc10_];
                  TiaoShi.txtShow(_loc10_ + "怪物ID:" + _loc12_.id + ", life:" + _loc12_.life.getValue() + ", x:" + _loc12_.x + ", y:" + _loc12_.y);
                  _loc12_.HpXX2(9999999999);
               }
            }
            else if(_loc2_ == 100)
            {
               GongHui_Interface.Open();
            }
            else if(_loc2_ == 101)
            {
               Api_4399_GongHui.getList(1,50);
            }
            else if(_loc2_ == 102)
            {
               Api_4399_GongHui.SelUserInfo();
            }
            else if(_loc2_ == 103)
            {
               Api_4399_GongHui.DuiHuanGX(5);
            }
            else if(_loc2_ == 104)
            {
               Api_4399_GongHui.XiaoHaoGX(5);
            }
            else if(_loc2_ == 105)
            {
               Api_4399_GongHui.setRW(67);
            }
            else if(_loc2_ == 106)
            {
               Api_4399_GongHui.getBHCY_List(360);
            }
            else if(_loc2_ == 107)
            {
               _loc10_ = 0;
               while(_loc10_ < _loc3_)
               {
                  (PlanFactory.JiHuaData[_loc10_] as Plan).setState(1);
                  _loc10_++;
               }
            }
            else if(_loc2_ == 108)
            {
               GongHui_Interface.InfoXX();
            }
            else if(_loc2_ == 109)
            {
               Api_4399_GongHui.get_QX();
            }
            else if(_loc2_ == 110)
            {
               Api_4399_GongHui.set_QX();
            }
            else if(_loc2_ == 111)
            {
               _loc13_ = GongHuiRenWu.isType(_loc3_);
               TiaoShi.txtShow("GongHuiRenWu.isType" + _loc3_ + " = " + _loc13_);
            }
            else if(_loc2_ == 112)
            {
               GongHuiRenWu.rwArr = null;
               GongHuiRenWu.SX(_loc3_);
            }
            else if(_loc2_ == 113)
            {
               GongHui_Interface.ListInfoPaiXu();
            }
            else if(_loc2_ == 114)
            {
               GongHui_Interface.xxArr[0] = _loc3_;
               GongHui_Interface.xxArr[1] = _loc4_;
               GongHui_Interface.xxArr[2] = _loc5_;
               TiaoShi.txtShow("GongHui_Interface.xxArr = " + GongHui_Interface.xxArr);
            }
            else if(_loc2_ == 115)
            {
               GongHuiTiaoZan.tzData = [0,[100,false],[100,false],[100,false]];
            }
            else if(_loc2_ == 116)
            {
               GongHui_jiTan.jiTanLv_arr[0] = true;
               GongHui_jiTan.jiTanLv_arr[1] = GongHui_jiTan.jiTanLv_arr[2] = GongHui_jiTan.jiTanLv_arr[3] = GongHui_jiTan.jiTanLv_arr[4] = _loc3_;
               TiaoShi.txtShow("公会祭坛技能等级 = " + GongHui_jiTan.jiTanLv_arr);
            }
            else if(_loc2_ == 117)
            {
               LingHunShi_Interface.pageNum = _loc3_;
               LingHunShi_Interface.Add_LHS(_loc3_);
            }
            else if(_loc2_ == 118)
            {
               LingHunShi_Interface.lhs_Data = null;
               LingHunShi_Interface.InitDataX();
            }
            else if(_loc2_ == 119)
            {
               _loc12_ = new Enemy(_loc3_);
               Main.world.moveChild_Enemy.addChild(_loc12_);
               _loc12_.x = 500;
               _loc12_.y = 380;
            }
            else if(_loc2_ == 120)
            {
               WantedTaskSlot.JLTimes10XX();
            }
            else if(_loc2_ == 121)
            {
               TiaoShi.txtShow("logo播放: " + LogoGO.LogoGONum);
            }
            else if(_loc2_ == 122)
            {
               if(MonsterSlot.x100)
               {
                  MonsterSlot.x100 = false;
                  TiaoShi.txtShow("图鉴掉落100% 关闭");
               }
               else
               {
                  MonsterSlot.x100 = true;
                  TiaoShi.txtShow("图鉴掉落100% 开启");
               }
            }
            else if(_loc2_ == 123)
            {
               ChunJiePanel.saveArr2_2022[2] += 10;
               ChunJiePanel.saveArr2_2022[3] += 10;
            }
            else if(_loc2_ == 124)
            {
               NewPetPanel.bag.addPetBag(PetEquip.creatPetEquip(_loc3_));
            }
            else if(_loc2_ == 125)
            {
               AchData.cjPoint_1.setValue(_loc3_);
               AchData.cjPoint_2.setValue(_loc3_);
            }
            else if(_loc2_ == 126)
            {
               ChunJiePanel.saveArr_2022[0] += 25;
            }
            else if(_loc2_ == 888)
            {
               _loc14_ = Main.serverTime.getValue() / 100;
               temp_txt.text = PaiHang_Data.newTime + "," + _loc14_ + "," + PaiHang_Data.sArr2.length;
            }
            else if(_loc2_ == 999)
            {
               TiaoShi.txtShow("原先键位1:" + Main.player1._keyArr);
               if(Main.player2)
               {
                  TiaoShi.txtShow("原先键位2:" + Main.player2._keyArr);
               }
               _loc15_ = [87,83,65,68,74,75,76,72,85,73,79,49,50,51,32,78];
               _loc16_ = [38,40,37,39,97,98,99,100,101,102,107,103,104,105,96,34];
               Main.player1._keyArr = com.DeepCopyUtil.clone(_loc15_);
               if(Main.player2)
               {
                  Main.player2._keyArr = com.DeepCopyUtil.clone(_loc16_);
               }
            }
            else
            {
               TiaoShi.txtShow("参数错误");
            }
         }
      }
      
      private function lunTan8Time_funXXX(param1:*) : *
      {
         Api_4399_GongHui.upNum(137);
         TiaoShi.txtShow("国庆活动数值 增加!");
         this.lunTan8Time = new Timer(2000,1);
         this.lunTan8Time.addEventListener(TimerEvent.TIMER,this.lunTan8Time_fun);
         this.lunTan8Time.start();
      }
      
      private function lunTan8Time_fun(param1:*) : *
      {
         Api_4399_GongHui.getNum([137]);
         TiaoShi.txtShow("国庆活动数值 查询~~~~~~~~~~~~~~~~~~~~~");
      }
   }
}

