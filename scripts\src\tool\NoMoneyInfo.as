package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   
   public class NoMoneyInfo extends MovieClip
   {
      public function NoMoneyInfo()
      {
         super();
         yes_btn.addEventListener(MouseEvent.CLICK,this.Close);
         addMoney_btn.addEventListener(MouseEvent.CLICK,this.Open_AddMoney2);
      }
      
      public function Open(param1:uint = 0) : *
      {
         this.visible = true;
         this.y = 0;
         this.x = 0;
         this._txt.text = "购买需要" + param1 + "点券";
      }
      
      public function Close(param1:* = null) : *
      {
         this.visible = false;
         this.y = 5000;
         this.x = 5000;
      }
      
      private function Open_AddMoney2(param1:* = null) : *
      {
         Main.ChongZhi();
         this.Close();
      }
   }
}

