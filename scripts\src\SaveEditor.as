package src
{
   import com.ByteArrayXX.Base64;
   import com.hotpoint.braveManIII.models.container.Bag;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.equip.EquipBaseAttrib;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.quest.Quest;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.equip.EquipFactory;
   import com.hotpoint.braveManIII.repository.gem.GemFactory;
   import com.hotpoint.braveManIII.repository.other.OtherFactory;
   import com.hotpoint.braveManIII.repository.quest.QuestFactory;
   import com.hotpoint.braveManIII.repository.supplies.SuppliesFactory;
   import flash.display.*;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.text.TextFieldType;
   import flash.text.TextFormat;
   import flash.utils.ByteArray;
   
   public class SaveEditor extends MovieClip
   {
      private var isVisible:Boolean = false;
      
      private var mainPanel:Sprite;
      
      private var currentPlayer:int = 1;
      
      private var currentCategory:int = 0;
      
      private var currentBagType:int = 1;
      
      private var itemSlots:Array = [];
      
      private var scrollY:int = 0;
      
      private var isDragging:Boolean = false;
      
      private var dragOffsetX:Number = 0;
      
      private var dragOffsetY:Number = 0;
      
      private var isResizing:Boolean = false;
      
      private var resizeHandle:Sprite;
      
      private var minWidth:int = 800;
      
      private var minHeight:int = 600;
      
      private var maxWidth:int = 1200;
      
      private var maxHeight:int = 800;
      
      private var currentWidth:int = 1000;
      
      private var currentHeight:int = 700;
      
      private var titleBar:Sprite;
      
      private var playerToggleBtn:Sprite;
      
      private var categoryButtons:Array = [];
      
      private var bagTypeButtons:Array = [];
      
      private var contentArea:Sprite;
      
      private var editPanel:Sprite;
      
      private var closeBtn:Sprite;
      
      private var selectedSlot:int = -1;
      
      private var editFields:Object = {};
      
      private var loadedSaveData:Object = null;
      
      private var tempPlayerData1:PlayerData = null;
      
      private var tempPlayerData2:PlayerData = null;
      
      public function SaveEditor()
      {
         super();
         this.initUI();
         this.initJavaScriptInterface();
      }
      
      private function initUI() : void
      {
         mainPanel = new Sprite();
         drawMainPanel();
         mainPanel.x = (Main._stageWidth - currentWidth) / 2;
         mainPanel.y = (Main._stageHeight - currentHeight) / 2;
         addChild(mainPanel);
         createTitleBar();
         createResizeHandle();
         setupEventListeners();
         var playerLabel:TextField = createTextField("选择玩家:",14,16777215);
         playerLabel.x = 20;
         playerLabel.y = 55;
         mainPanel.addChild(playerLabel);
         playerToggleBtn = createButton("玩家1",4486314,80);
         playerToggleBtn.x = 100;
         playerToggleBtn.y = 50;
         playerToggleBtn.addEventListener(MouseEvent.CLICK,togglePlayer);
         mainPanel.addChild(playerToggleBtn);
         var categories:Array = ["基本属性","背包编辑"];
         categoryButtons = [];
         var i:int = 0;
         while(i < categories.length)
         {
            var catBtn:Sprite = createButton(categories[i],i == 0 ? 4486314 : 6710886,100);
            catBtn.x = 200 + i * 110;
            catBtn.y = 50;
            catBtn.addEventListener(MouseEvent.CLICK,createCategoryHandler(i));
            categoryButtons.push(catBtn);
            mainPanel.addChild(catBtn);
            i++;
         }
         contentArea = new Sprite();
         contentArea.x = 20;
         contentArea.y = 90;
         mainPanel.addChild(contentArea);
         editPanel = new Sprite();
         editPanel.x = currentWidth * 0.5;
         editPanel.y = 90;
         mainPanel.addChild(editPanel);
         this.visible = false;
      }
      
      private function startDrag(e:MouseEvent) : void
      {
         isDragging = true;
         dragOffsetX = e.stageX - mainPanel.x;
         dragOffsetY = e.stageY - mainPanel.y;
      }
      
      private function stopDrag(e:MouseEvent) : void
      {
         isDragging = false;
      }
      
      private function onDrag(e:MouseEvent) : void
      {
         if(isDragging)
         {
            var newX:Number = e.stageX - dragOffsetX;
            var newY:Number = e.stageY - dragOffsetY;
            newX = Math.max(0,Math.min(newX,Main._stageWidth - currentWidth));
            newY = Math.max(0,Math.min(newY,Main._stageHeight - currentHeight));
            mainPanel.x = newX;
            mainPanel.y = newY;
         }
         else if(isResizing)
         {
            var newWidth:int = Math.max(minWidth,Math.min(maxWidth,e.stageX - mainPanel.x));
            var newHeight:int = Math.max(minHeight,Math.min(maxHeight,e.stageY - mainPanel.y));
            if(newWidth != currentWidth || newHeight != currentHeight)
            {
               currentWidth = newWidth;
               currentHeight = newHeight;
               redrawWindow();
            }
         }
      }
      
      private function drawMainPanel() : void
      {
         mainPanel.graphics.clear();
         mainPanel.graphics.beginFill(3355443,0.95);
         mainPanel.graphics.lineStyle(2,6710886);
         mainPanel.graphics.drawRoundRect(0,0,currentWidth,currentHeight,10,10);
         mainPanel.graphics.endFill();
      }
      
      private function createTitleBar() : void
      {
         titleBar = new Sprite();
         titleBar.graphics.beginFill(4473924,1);
         titleBar.graphics.drawRoundRect(0,0,currentWidth,40,10,10);
         titleBar.graphics.endFill();
         titleBar.buttonMode = true;
         titleBar.useHandCursor = true;
         mainPanel.addChild(titleBar);
         var title:TextField = createTextField("存档编辑器",18,16777215,true);
         title.x = 20;
         title.y = 10;
         title.mouseEnabled = false;
         titleBar.addChild(title);
         closeBtn = createButton("×",16729156,30,30);
         closeBtn.x = currentWidth - 40;
         closeBtn.y = 5;
         closeBtn.addEventListener(MouseEvent.CLICK,closeEditor);
         titleBar.addChild(closeBtn);
      }
      
      private function createResizeHandle() : void
      {
         resizeHandle = new Sprite();
         resizeHandle.graphics.beginFill(6710886,0.8);
         resizeHandle.graphics.moveTo(0,15);
         resizeHandle.graphics.lineTo(15,0);
         resizeHandle.graphics.lineTo(15,15);
         resizeHandle.graphics.lineTo(0,15);
         resizeHandle.graphics.endFill();
         resizeHandle.x = currentWidth - 15;
         resizeHandle.y = currentHeight - 15;
         resizeHandle.buttonMode = true;
         resizeHandle.useHandCursor = true;
         mainPanel.addChild(resizeHandle);
      }
      
      private function setupEventListeners() : void
      {
         titleBar.addEventListener(MouseEvent.MOUSE_DOWN,startDrag);
         resizeHandle.addEventListener(MouseEvent.MOUSE_DOWN,startResize);
         Main._stage.addEventListener(MouseEvent.MOUSE_UP,stopDragResize);
         Main._stage.addEventListener(MouseEvent.MOUSE_MOVE,onDrag);
      }
      
      private function startResize(e:MouseEvent) : void
      {
         isResizing = true;
         e.stopPropagation();
      }
      
      private function stopDragResize(e:MouseEvent) : void
      {
         isDragging = false;
         isResizing = false;
      }
      
      private function redrawWindow() : void
      {
         drawMainPanel();
         titleBar.graphics.clear();
         titleBar.graphics.beginFill(4473924,1);
         titleBar.graphics.drawRoundRect(0,0,currentWidth,40,10,10);
         titleBar.graphics.endFill();
         closeBtn.x = currentWidth - 40;
         resizeHandle.x = currentWidth - 15;
         resizeHandle.y = currentHeight - 15;
         editPanel.x = currentWidth * 0.5;
      }
      
      private function createTextField(text:String, size:int = 12, color:uint = 0, bold:Boolean = false) : TextField
      {
         var tf:TextField = new TextField();
         tf.text = text;
         tf.autoSize = TextFieldAutoSize.LEFT;
         tf.selectable = false;
         var format:TextFormat = new TextFormat();
         format.size = size;
         format.color = color;
         format.bold = bold;
         tf.setTextFormat(format);
         return tf;
      }
      
      private function createButton(text:String, color:uint = 6710886, width:int = 70, height:int = 25) : Sprite
      {
         var btn:Sprite = new Sprite();
         btn.graphics.beginFill(color);
         btn.graphics.lineStyle(1,8947848);
         btn.graphics.drawRoundRect(0,0,width,height,5);
         btn.graphics.endFill();
         var label:TextField = createTextField(text,12,16777215);
         label.x = (width - label.width) / 2;
         label.y = (height - label.height) / 2;
         label.mouseEnabled = false;
         btn.addChild(label);
         btn.buttonMode = true;
         btn.useHandCursor = true;
         return btn;
      }
      
      private function createCategoryHandler(category:int) : Function
      {
         return function(e:MouseEvent):void
         {
            currentCategory = category;
            updateCategoryButtons();
            refreshContent();
         };
      }
      
      private function updateCategoryButtons() : void
      {
         var i:int = 0;
         while(i < categoryButtons.length)
         {
            var btn:Sprite = categoryButtons[i];
            btn.graphics.clear();
            var color:uint = uint(i == currentCategory ? 4486314 : 6710886);
            btn.graphics.beginFill(color);
            btn.graphics.lineStyle(1,8947848);
            btn.graphics.drawRoundRect(0,0,100,25,5);
            btn.graphics.endFill();
            i++;
         }
      }
      
      private function refreshContent() : void
      {
         while(contentArea.numChildren > 0)
         {
            contentArea.removeChildAt(0);
         }
         if(currentCategory == 0)
         {
            showPlayerAttributes();
         }
         else if(currentCategory == 1)
         {
            showBagEditor();
         }
      }
      
      private function createBagTypeHandler(type:int) : Function
      {
         return function(e:MouseEvent):void
         {
            currentBagType = type;
            refreshItemList();
         };
      }
      
      public function show() : void
      {
         this.visible = true;
         isVisible = true;
         refreshContent();
      }
      
      private function showPlayerAttributes() : void
      {
         var playerData:PlayerData = getCurrentPlayerData();
         if(!playerData)
         {
            var errorText:TextField = createTextField("请先加载存档",14,16711680);
            errorText.x = 20;
            errorText.y = 20;
            contentArea.addChild(errorText);
            return;
         }
         var yPos:int = 20;
         var infoTitle:TextField = createTextField("基本信息",16,65535,true);
         infoTitle.x = 20;
         infoTitle.y = yPos;
         contentArea.addChild(infoTitle);
         yPos += 30;
         createAttributeEditor("等级",playerData.getLevel().toString(),"level",20,yPos);
         yPos += 35;
         createAttributeEditor("金币",playerData.getGold().toString(),"gold",20,yPos);
         yPos += 35;
         createAttributeEditor("经验值",playerData.getEXP().toString(),"exp",20,yPos);
         yPos += 35;
         createAttributeEditor("技能点",playerData.getPoints().toString(),"points",20,yPos);
         yPos += 35;
         createAttributeEditor("杀怪点数",playerData.getKillPoint().toString(),"killPoint",20,yPos);
         yPos += 35;
         var rebirthLabel:TextField = createTextField("转生状态:",12,16777215);
         rebirthLabel.x = 20;
         rebirthLabel.y = yPos;
         contentArea.addChild(rebirthLabel);
         var rebirthBtn:Sprite = createButton(playerData.isRebirth() ? "已转生" : "未转生",playerData.isRebirth() ? 43520 : 11141120,80);
         rebirthBtn.x = 120;
         rebirthBtn.y = yPos - 2;
         rebirthBtn.addEventListener(MouseEvent.CLICK,toggleRebirth);
         contentArea.addChild(rebirthBtn);
         yPos += 40;
         var player:Player = getCurrentPlayer();
         if(player)
         {
            createAttributeEditor("生命值",player.hp.getValue().toString(),"hp",20,yPos);
            yPos += 35;
            createAttributeEditor("魔法值",player.mp.getValue().toString(),"mp",20,yPos);
            yPos += 35;
         }
         var saveBtn:Sprite = createButton("保存修改",43520,100);
         saveBtn.x = 20;
         saveBtn.y = yPos + 20;
         saveBtn.addEventListener(MouseEvent.CLICK,savePlayerAttributes);
         contentArea.addChild(saveBtn);
      }
      
      private function createAttributeEditor(labelText:String, value:String, fieldName:String, x:int, y:int) : void
      {
         var label:TextField = createTextField(labelText + ":",12,16777215);
         label.x = x;
         label.y = y;
         contentArea.addChild(label);
         var input:TextField = createInputField(value);
         input.x = x + 100;
         input.y = y - 2;
         editFields[fieldName] = input;
         contentArea.addChild(input);
      }
      
      private function getCurrentPlayerData() : PlayerData
      {
         return currentPlayer == 1 ? Main.player1 : Main.player2;
      }
      
      private function getCurrentPlayer() : Player
      {
         return currentPlayer == 1 ? Main.player_1 : Main.player_2;
      }
      
      private function toggleRebirth(e:MouseEvent) : void
      {
         var playerData:PlayerData = getCurrentPlayerData();
         if(playerData)
         {
            var newState:Boolean = !playerData.isRebirth();
            playerData._rebirth = newState;
            var btn:Sprite = e.target as Sprite;
            var label:TextField = btn.getChildAt(0) as TextField;
            label.text = newState ? "已转生" : "未转生";
            btn.graphics.clear();
            btn.graphics.beginFill(newState ? 43520 : 11141120);
            btn.graphics.lineStyle(1,8947848);
            btn.graphics.drawRoundRect(0,0,80,25,5);
            btn.graphics.endFill();
            showMessage("转生状态已修改","success");
         }
      }
      
      private function savePlayerAttributes(e:MouseEvent) : void
      {
         var newKillPoint:int;
         var currentKillPoint:int;
         var diff:int;
         var newPoints:int;
         var currentPoints:int;
         var newHP:int;
         var newMP:int;
         var playerData:PlayerData = getCurrentPlayerData();
         var player:Player = getCurrentPlayer();
         if(!playerData)
         {
            showMessage("玩家数据不存在","error");
            return;
         }
         try
         {
            if(editFields["level"])
            {
               playerData.setLevel(parseInt(editFields["level"].text));
            }
            if(editFields["gold"])
            {
               playerData.SetGold(parseInt(editFields["gold"].text));
            }
            if(editFields["exp"])
            {
               playerData.setEXP(parseInt(editFields["exp"].text));
            }
            if(editFields["killPoint"])
            {
               newKillPoint = int(parseInt(editFields["killPoint"].text));
               currentKillPoint = playerData.getKillPoint();
               diff = newKillPoint - currentKillPoint;
               if(diff != 0)
               {
                  playerData.AddKillPoint(diff);
               }
            }
            if(editFields["points"])
            {
               newPoints = int(parseInt(editFields["points"].text));
               currentPoints = playerData.getPoints();
               playerData.delPoints(currentPoints);
               playerData.addPoint(newPoints);
            }
            if(player)
            {
               if(editFields["hp"])
               {
                  newHP = int(parseInt(editFields["hp"].text));
                  player.hp.setValue(newHP);
                  player.hpMax.setValue(newHP);
               }
               if(editFields["mp"])
               {
                  newMP = int(parseInt(editFields["mp"].text));
                  player.mp.setValue(newMP);
                  player.mpMax.setValue(newMP);
               }
            }
            showMessage("属性修改成功","success");
         }
         catch(error:Error)
         {
            showMessage("保存失败: " + error.message,"error");
         }
      }
      
      public function hide() : void
      {
         this.visible = false;
         isVisible = false;
      }
      
      public function toggle() : void
      {
         if(isVisible)
         {
            hide();
         }
         else
         {
            show();
         }
      }
      
      private function showBagEditor() : void
      {
         var bagTypes:Array = ["装备","消耗品","宝石","任务物品","其他物品"];
         bagTypeButtons = [];
         var i:int = 0;
         while(i < bagTypes.length)
         {
            var btn:Sprite = createButton(bagTypes[i],i == currentBagType - 1 ? 4486314 : 6710886,80);
            btn.x = i * 90;
            btn.y = 10;
            btn.addEventListener(MouseEvent.CLICK,createBagTypeHandler(i + 1));
            bagTypeButtons.push(btn);
            contentArea.addChild(btn);
            i++;
         }
         var itemListContainer:Sprite = new Sprite();
         itemListContainer.x = 0;
         itemListContainer.y = 50;
         contentArea.addChild(itemListContainer);
         refreshItemList(itemListContainer);
      }
      
      private function updateBagTypeButtons() : void
      {
         var i:int = 0;
         while(i < bagTypeButtons.length)
         {
            var btn:Sprite = bagTypeButtons[i];
            btn.graphics.clear();
            var color:uint = uint(i == currentBagType - 1 ? 4486314 : 6710886);
            btn.graphics.beginFill(color);
            btn.graphics.lineStyle(1,8947848);
            btn.graphics.drawRoundRect(0,0,80,25,5);
            btn.graphics.endFill();
            i++;
         }
      }
      
      private function refreshItemList(container:Sprite = null) : void
      {
         if(!container)
         {
            if(currentCategory == 1 && contentArea.numChildren > 0)
            {
               var i:int = 0;
               while(i < contentArea.numChildren)
               {
                  var child:DisplayObject = contentArea.getChildAt(i);
                  if(child.y == 50)
                  {
                     container = child as Sprite;
                     break;
                  }
                  i++;
               }
            }
            if(!container)
            {
               return;
            }
         }
         while(container.numChildren > 0)
         {
            container.removeChildAt(0);
         }
         updateBagTypeButtons();
         var bag:Bag = getCurrentBag();
         if(!bag)
         {
            var errorText:TextField = createTextField("请先加载存档",14,16711680);
            errorText.x = 10;
            errorText.y = 10;
            container.addChild(errorText);
            return;
         }
         var items:Array = getBagItems(bag);
         if(!items)
         {
            var nullText:TextField = createTextField("背包数据未初始化",14,16711680);
            nullText.x = 10;
            nullText.y = 10;
            container.addChild(nullText);
            return;
         }
         var j:int = 0;
         while(j < Math.min(items.length,48))
         {
            var item:* = items[j];
            var row:int = Math.floor(j / 6);
            var col:int = j % 6;
            var x:int = col * 65;
            var y:int = row * 65;
            createItemSlot(j,item,x,y,container);
            j++;
         }
      }
      
      private function getBagItems(bag:Bag) : Array
      {
         switch(currentBagType)
         {
            case 1:
               return bag.getEquipBag();
            case 2:
               return bag.getSuppliesBag();
            case 3:
               return bag.getGemBag();
            case 4:
               return bag.getQuestBag();
            case 5:
               return bag.getOtherobjBag();
            default:
               return null;
         }
      }
      
      private function closeEditor(e:MouseEvent) : void
      {
         hide();
      }
      
      private function togglePlayer(e:MouseEvent) : void
      {
         if(Main.P1P2)
         {
            currentPlayer = currentPlayer == 1 ? 2 : 1;
            var btn:Sprite = e.target as Sprite;
            var label:TextField = btn.getChildAt(0) as TextField;
            label.text = "玩家" + currentPlayer;
            refreshContent();
         }
         else
         {
            NewMC.Open("当前为单人模式",Main._stage);
         }
      }
      
      private function getCurrentBag() : Bag
      {
         var playerData:PlayerData = getCurrentPlayerData();
         return !!playerData ? playerData.getBag() : null;
      }
      
      private function createItemSlot(index:int, item:*, x:int, y:int, container:Sprite) : void
      {
         var itemName:String;
         var nameField:TextField;
         var itemInfo:String;
         var infoField:TextField;
         var editHint:TextField;
         var emptyLabel:TextField;
         var addHint:TextField;
         var slot:Sprite = new Sprite();
         var slotSize:int = 60;
         if(item != null)
         {
            slot.graphics.beginFill(4473924,0.8);
            slot.graphics.lineStyle(2,6710886);
         }
         else
         {
            slot.graphics.beginFill(2236962,0.5);
            slot.graphics.lineStyle(1,4473924,0.5);
         }
         slot.graphics.drawRoundRect(0,0,slotSize,slotSize,5);
         slot.graphics.endFill();
         slot.x = x;
         slot.y = y;
         if(item != null)
         {
            itemName = getItemName(item);
            nameField = createTextField(itemName,9,16777215);
            nameField.x = 2;
            nameField.y = 2;
            nameField.width = slotSize - 4;
            nameField.height = 12;
            nameField.wordWrap = true;
            slot.addChild(nameField);
            itemInfo = getItemInfo(item);
            infoField = createTextField(itemInfo,8,13421772);
            infoField.x = 2;
            infoField.y = 15;
            infoField.width = slotSize - 4;
            infoField.height = 30;
            infoField.wordWrap = true;
            slot.addChild(infoField);
            editHint = createTextField("点击编辑",7,65280);
            editHint.x = 2;
            editHint.y = slotSize - 12;
            slot.addChild(editHint);
         }
         else
         {
            emptyLabel = createTextField("空",12,6710886);
            emptyLabel.x = (slotSize - emptyLabel.width) / 2;
            emptyLabel.y = (slotSize - emptyLabel.height) / 2 - 5;
            slot.addChild(emptyLabel);
            addHint = createTextField("点击添加",8,43775);
            addHint.x = (slotSize - addHint.width) / 2;
            addHint.y = (slotSize - addHint.height) / 2 + 8;
            slot.addChild(addHint);
         }
         slot.buttonMode = true;
         slot.useHandCursor = true;
         if(item != null)
         {
            slot.addEventListener(MouseEvent.CLICK,createItemClickHandler(index,item));
         }
         else
         {
            slot.addEventListener(MouseEvent.CLICK,createEmptyClickHandler(index));
         }
         slot.addEventListener(MouseEvent.MOUSE_OVER,function(e:MouseEvent):void
         {
            slot.graphics.clear();
            if(item != null)
            {
               slot.graphics.beginFill(5592405,0.9);
               slot.graphics.lineStyle(2,65280);
            }
            else
            {
               slot.graphics.beginFill(3355443,0.7);
               slot.graphics.lineStyle(2,43775);
            }
            slot.graphics.drawRoundRect(0,0,slotSize,slotSize,5);
            slot.graphics.endFill();
         });
         slot.addEventListener(MouseEvent.MOUSE_OUT,function(e:MouseEvent):void
         {
            slot.graphics.clear();
            if(item != null)
            {
               slot.graphics.beginFill(4473924,0.8);
               slot.graphics.lineStyle(2,6710886);
            }
            else
            {
               slot.graphics.beginFill(2236962,0.5);
               slot.graphics.lineStyle(1,4473924,0.5);
            }
            slot.graphics.drawRoundRect(0,0,slotSize,slotSize,5);
            slot.graphics.endFill();
         });
         container.addChild(slot);
      }
      
      private function createInputField(defaultValue:String) : TextField
      {
         var field:TextField = new TextField();
         field.type = TextFieldType.INPUT;
         field.border = true;
         field.borderColor = 6710886;
         field.background = true;
         field.backgroundColor = 2236962;
         field.textColor = 16777215;
         field.width = 120;
         field.height = 20;
         field.text = defaultValue;
         var format:TextFormat = new TextFormat();
         format.size = 12;
         field.setTextFormat(format);
         return field;
      }
      
      private function getBagTypeName(type:int) : String
      {
         switch(type)
         {
            case 1:
               return "装备";
            case 2:
               return "消耗品";
            case 3:
               return "宝石";
            case 4:
               return "任务物品";
            case 5:
               return "其他物品";
            default:
               return "未知";
         }
      }
      
      private function getItemName(item:*) : String
      {
         if(!item)
         {
            return "空";
         }
         try
         {
            if(item is Equip)
            {
               return (item as Equip).getName();
            }
            if(item is Supplies)
            {
               return (item as Supplies).getName();
            }
            if(item is Gem)
            {
               return (item as Gem).getName();
            }
            if(item is Quest)
            {
               return (item as Quest).getName();
            }
            if(item is Otherobj)
            {
               return (item as Otherobj).getName();
            }
            return "未知物品";
         }
         catch(error:Error)
         {
            return "获取名称失败";
         }
      }
      
      private function getItemInfo(item:*) : String
      {
         var info:String;
         var equip:Equip;
         var supplies:Supplies;
         var gem:Gem;
         var quest:Quest;
         var other:Otherobj;
         if(!item)
         {
            return "";
         }
         try
         {
            info = "";
            if(item is Equip)
            {
               equip = item as Equip;
               info = "强化+" + equip.getReinforceLevel();
               if(equip.getGemSlotNum() > 0)
               {
                  info += "\n孔数:" + equip.getGemSlotNum();
               }
            }
            else if(item is Supplies)
            {
               supplies = item as Supplies;
               info = "数量:" + supplies.getTimes();
            }
            else if(item is Gem)
            {
               gem = item as Gem;
               info = "数量:" + gem.getTimes();
               info += "\n等级:" + gem.getStrengthenLevel();
            }
            else if(item is Quest)
            {
               quest = item as Quest;
               info = "数量:" + quest.getTimes();
            }
            else if(item is Otherobj)
            {
               other = item as Otherobj;
               info = "数量:" + other.getTimes();
            }
            return info;
         }
         catch(error:Error)
         {
            return "获取信息失败";
         }
      }
      
      private function createItemClickHandler(index:int, item:*) : Function
      {
         return function(e:MouseEvent):void
         {
            selectedSlot = index;
            trace("点击了物品，索引: " + index + ", 物品类型: " + (!!item ? getItemName(item) : "null"));
            showEditPanel(item);
         };
      }
      
      private function createEmptyClickHandler(index:int) : Function
      {
         return function(e:MouseEvent):void
         {
            selectedSlot = index;
            trace("点击了空槽位，索引: " + index);
            showAddItemPanel();
         };
      }
      
      private function cancelEdit(e:MouseEvent) : void
      {
         while(editPanel.numChildren > 0)
         {
            editPanel.removeChildAt(0);
         }
         editPanel.graphics.clear();
         selectedSlot = -1;
      }
      
      private function showEditPanel(item:*) : void
      {
         while(editPanel.numChildren > 0)
         {
            editPanel.removeChildAt(0);
         }
         editPanel.graphics.clear();
         editPanel.graphics.beginFill(2236962,0.9);
         editPanel.graphics.drawRoundRect(0,0,350,450,10);
         editPanel.graphics.endFill();
         editPanel.graphics.lineStyle(2,6710886);
         editPanel.graphics.drawRoundRect(0,0,350,450,10);
         var title:TextField = createTextField("编辑物品",16,16777215);
         title.x = 10;
         title.y = 10;
         editPanel.addChild(title);
         if(item is Equip)
         {
            showEquipEditPanel(item as Equip,40);
         }
         else if(item is Supplies)
         {
            showSuppliesEditPanel(item as Supplies,40);
         }
         else if(item is Gem)
         {
            showGemEditPanel(item as Gem,40);
         }
         else if(item is Otherobj)
         {
            showOtherobjEditPanel(item as Otherobj,40);
         }
         var deleteBtn:Sprite = createButton("删除",16711680);
         deleteBtn.x = 10;
         deleteBtn.y = 400;
         deleteBtn.addEventListener(MouseEvent.CLICK,deleteItem);
         editPanel.addChild(deleteBtn);
         var saveBtn:Sprite = createButton("保存",43520);
         saveBtn.x = 90;
         saveBtn.y = 400;
         saveBtn.addEventListener(MouseEvent.CLICK,saveChanges);
         editPanel.addChild(saveBtn);
         var cancelBtn:Sprite = createButton("取消",6710886);
         cancelBtn.x = 170;
         cancelBtn.y = 400;
         cancelBtn.addEventListener(MouseEvent.CLICK,cancelEdit);
         editPanel.addChild(cancelBtn);
      }
      
      private function showEquipEditPanel(equip:Equip, yPos:int) : void
      {
         var equipName:String;
         var nameLabel:TextField;
         var className:String;
         var classLabel:TextField;
         var descript:String;
         var descriptLabel:TextField;
         var errorLabel:TextField;
         var idLabel:TextField;
         var idField:TextField;
         var reinforceLabel:TextField;
         var reinforceField:TextField;
         var gemGridLabel:TextField;
         var gemGridField:TextField;
         try
         {
            equipName = equip.getName();
            if(equipName)
            {
               nameLabel = createTextField("装备名称: " + equipName,12,65535);
               nameLabel.x = 10;
               nameLabel.y = yPos;
               nameLabel.width = 200;
               editPanel.addChild(nameLabel);
               yPos += 25;
            }
            className = equip.getClassName();
            if(className)
            {
               classLabel = createTextField("装备类型: " + className,10,13421772);
               classLabel.x = 10;
               classLabel.y = yPos;
               classLabel.width = 200;
               editPanel.addChild(classLabel);
               yPos += 20;
            }
            descript = equip.getDescript();
            if(descript)
            {
               descriptLabel = createTextField("描述: " + descript,9,10066329);
               descriptLabel.x = 10;
               descriptLabel.y = yPos;
               descriptLabel.width = 200;
               descriptLabel.height = 40;
               descriptLabel.wordWrap = true;
               editPanel.addChild(descriptLabel);
               yPos += 45;
            }
         }
         catch(error:Error)
         {
            errorLabel = createTextField("装备信息获取失败",10,16711680);
            errorLabel.x = 10;
            errorLabel.y = yPos;
            editPanel.addChild(errorLabel);
            yPos += 25;
         }
         idLabel = createTextField("装备ID:",12,16777215);
         idLabel.x = 10;
         idLabel.y = yPos;
         editPanel.addChild(idLabel);
         idField = createInputField(equip.getBase().toString());
         idField.x = 80;
         idField.y = yPos;
         editFields["equipId"] = idField;
         editPanel.addChild(idField);
         yPos += 30;
         if(equip.getReinforceAttrib())
         {
            reinforceLabel = createTextField("强化等级:",12,16777215);
            reinforceLabel.x = 10;
            reinforceLabel.y = yPos;
            editPanel.addChild(reinforceLabel);
            reinforceField = createInputField(equip.getReinforceAttrib().getLevel().toString());
            reinforceField.x = 80;
            reinforceField.y = yPos;
            editFields["reinforceLevel"] = reinforceField;
            editPanel.addChild(reinforceField);
            yPos += 30;
         }
         gemGridLabel = createTextField("宝石孔数:",12,16777215);
         gemGridLabel.x = 10;
         gemGridLabel.y = yPos;
         editPanel.addChild(gemGridLabel);
         gemGridField = createInputField(equip.getGemGrid().toString());
         gemGridField.x = 80;
         gemGridField.y = yPos;
         editFields["gemGrid"] = gemGridField;
         editPanel.addChild(gemGridField);
      }
      
      private function showSuppliesEditPanel(supplies:Supplies, yPos:int) : void
      {
         var suppliesName:String;
         var nameLabel:TextField;
         var descript:String;
         var descriptLabel:TextField;
         var errorLabel:TextField;
         var idLabel:TextField;
         var idField:TextField;
         var countLabel:TextField;
         var countField:TextField;
         try
         {
            suppliesName = supplies.getName();
            if(suppliesName)
            {
               nameLabel = createTextField("物品名称: " + suppliesName,12,65535);
               nameLabel.x = 10;
               nameLabel.y = yPos;
               nameLabel.width = 200;
               editPanel.addChild(nameLabel);
               yPos += 25;
            }
            descript = supplies.getDescript();
            if(descript)
            {
               descriptLabel = createTextField("描述: " + descript,9,10066329);
               descriptLabel.x = 10;
               descriptLabel.y = yPos;
               descriptLabel.width = 200;
               descriptLabel.height = 40;
               descriptLabel.wordWrap = true;
               editPanel.addChild(descriptLabel);
               yPos += 45;
            }
         }
         catch(error:Error)
         {
            errorLabel = createTextField("物品信息获取失败",10,16711680);
            errorLabel.x = 10;
            errorLabel.y = yPos;
            editPanel.addChild(errorLabel);
            yPos += 25;
         }
         idLabel = createTextField("物品ID:",12,16777215);
         idLabel.x = 10;
         idLabel.y = yPos;
         editPanel.addChild(idLabel);
         idField = createInputField(supplies.getBase().toString());
         idField.x = 80;
         idField.y = yPos;
         editFields["suppliesId"] = idField;
         editPanel.addChild(idField);
         yPos += 30;
         countLabel = createTextField("数量:",12,16777215);
         countLabel.x = 10;
         countLabel.y = yPos;
         editPanel.addChild(countLabel);
         countField = createInputField(supplies.getTimes().toString());
         countField.x = 80;
         countField.y = yPos;
         editFields["suppliesCount"] = countField;
         editPanel.addChild(countField);
      }
      
      private function showGemEditPanel(gem:Gem, yPos:int) : void
      {
         var gemName:String;
         var nameLabel:TextField;
         var descript:String;
         var descriptLabel:TextField;
         var gemAttribs:Array;
         var attribLabel:TextField;
         var i:int;
         var attribText:TextField;
         var errorLabel:TextField;
         var idLabel:TextField;
         var idField:TextField;
         var countLabel:TextField;
         var countField:TextField;
         try
         {
            gemName = gem.getName();
            if(gemName)
            {
               nameLabel = createTextField("宝石名称: " + gemName,12,65535);
               nameLabel.x = 10;
               nameLabel.y = yPos;
               nameLabel.width = 200;
               editPanel.addChild(nameLabel);
               yPos += 25;
            }
            descript = gem.getDescript();
            if(descript)
            {
               descriptLabel = createTextField("描述: " + descript,9,10066329);
               descriptLabel.x = 10;
               descriptLabel.y = yPos;
               descriptLabel.width = 200;
               descriptLabel.height = 40;
               descriptLabel.wordWrap = true;
               editPanel.addChild(descriptLabel);
               yPos += 45;
            }
            gemAttribs = gem.showGemAttrib();
            if(gemAttribs && gemAttribs.length > 0)
            {
               attribLabel = createTextField("宝石属性:",10,65280);
               attribLabel.x = 10;
               attribLabel.y = yPos;
               editPanel.addChild(attribLabel);
               yPos += 20;
               i = 0;
               while(i < Math.min(gemAttribs.length,3))
               {
                  attribText = createTextField("• " + gemAttribs[i],9,13434828);
                  attribText.x = 15;
                  attribText.y = yPos;
                  attribText.width = 190;
                  editPanel.addChild(attribText);
                  yPos += 18;
                  i++;
               }
               yPos += 10;
            }
         }
         catch(error:Error)
         {
            errorLabel = createTextField("宝石信息获取失败",10,16711680);
            errorLabel.x = 10;
            errorLabel.y = yPos;
            editPanel.addChild(errorLabel);
            yPos += 25;
         }
         idLabel = createTextField("宝石ID:",12,16777215);
         idLabel.x = 10;
         idLabel.y = yPos;
         editPanel.addChild(idLabel);
         idField = createInputField(gem.getBase().toString());
         idField.x = 80;
         idField.y = yPos;
         editFields["gemId"] = idField;
         editPanel.addChild(idField);
         yPos += 30;
         countLabel = createTextField("数量:",12,16777215);
         countLabel.x = 10;
         countLabel.y = yPos;
         editPanel.addChild(countLabel);
         countField = createInputField(gem.getTimes().toString());
         countField.x = 80;
         countField.y = yPos;
         editFields["gemCount"] = countField;
         editPanel.addChild(countField);
      }
      
      private function showOtherobjEditPanel(otherobj:Otherobj, yPos:int) : void
      {
         var otherName:String;
         var nameLabel:TextField;
         var descript:String;
         var descriptLabel:TextField;
         var errorLabel:TextField;
         var idLabel:TextField;
         var idField:TextField;
         var countLabel:TextField;
         var countField:TextField;
         try
         {
            otherName = otherobj.getName();
            if(otherName)
            {
               nameLabel = createTextField("物品名称: " + otherName,12,65535);
               nameLabel.x = 10;
               nameLabel.y = yPos;
               nameLabel.width = 200;
               editPanel.addChild(nameLabel);
               yPos += 25;
            }
            descript = otherobj.getDescript();
            if(descript)
            {
               descriptLabel = createTextField("描述: " + descript,9,10066329);
               descriptLabel.x = 10;
               descriptLabel.y = yPos;
               descriptLabel.width = 200;
               descriptLabel.height = 40;
               descriptLabel.wordWrap = true;
               editPanel.addChild(descriptLabel);
               yPos += 45;
            }
         }
         catch(error:Error)
         {
            errorLabel = createTextField("物品信息获取失败",10,16711680);
            errorLabel.x = 10;
            errorLabel.y = yPos;
            editPanel.addChild(errorLabel);
            yPos += 25;
         }
         idLabel = createTextField("物品ID:",12,16777215);
         idLabel.x = 10;
         idLabel.y = yPos;
         editPanel.addChild(idLabel);
         idField = createInputField(otherobj.getBase().toString());
         idField.x = 80;
         idField.y = yPos;
         editFields["otherId"] = idField;
         editPanel.addChild(idField);
         yPos += 30;
         countLabel = createTextField("数量:",12,16777215);
         countLabel.x = 10;
         countLabel.y = yPos;
         editPanel.addChild(countLabel);
         countField = createInputField(otherobj.getTimes().toString());
         countField.x = 80;
         countField.y = yPos;
         editFields["otherCount"] = countField;
         editPanel.addChild(countField);
      }
      
      private function createItemClickHandler(index:int, item:*) : Function
      {
         return function(e:MouseEvent):void
         {
            selectedSlot = index;
            showEditPanel(item);
         };
      }
      
      private function createEmptyClickHandler(index:int) : Function
      {
         return function(e:MouseEvent):void
         {
            selectedSlot = index;
            showAddItemPanel();
         };
      }
      
      private function showEditPanel(item:*) : void
      {
         while(editPanel.numChildren > 0)
         {
            editPanel.removeChildAt(0);
         }
         editPanel.graphics.clear();
         editPanel.graphics.beginFill(2236962,0.9);
         editPanel.graphics.drawRoundRect(0,0,350,400,10);
         editPanel.graphics.endFill();
         editPanel.graphics.lineStyle(2,6710886);
         editPanel.graphics.drawRoundRect(0,0,350,400,10);
         var title:TextField = createTextField("编辑物品",16,16777215,true);
         title.x = 10;
         title.y = 10;
         editPanel.addChild(title);
         var yPos:int = 50;
         var nameLabel:TextField = createTextField("物品名称: " + getItemName(item),12,65535);
         nameLabel.x = 10;
         nameLabel.y = yPos;
         editPanel.addChild(nameLabel);
         yPos += 30;
         if(item is Equip)
         {
            showEquipEditOptions(item as Equip,yPos);
         }
         else if(item is Supplies || item is Gem || item is Quest || item is Otherobj)
         {
            showCountableItemEditOptions(item,yPos);
         }
      }
      
      private function showEquipEditOptions(equip:Equip, startY:int) : void
      {
         openDetailedEquipEditor(equip);
      }
      
      private function openDetailedEquipEditor(equip:Equip) : void
      {
         var mainPanel:Sprite;
         var title:TextField;
         var closeBtn:Sprite;
         var yPos:int;
         var infoLabel:TextField;
         var idLabel:TextField;
         var nameLabel:TextField;
         var attribTitle:TextField;
         var equipEditWindow:MovieClip = new MovieClip();
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0,0.7);
         bg.graphics.drawRect(0,0,Main._stageWidth,Main._stageHeight);
         bg.graphics.endFill();
         equipEditWindow.addChild(bg);
         mainPanel = new Sprite();
         mainPanel.graphics.beginFill(3355443,0.95);
         mainPanel.graphics.lineStyle(2,6710886);
         mainPanel.graphics.drawRoundRect(0,0,800,600,10,10);
         mainPanel.graphics.endFill();
         mainPanel.x = (Main._stageWidth - 800) / 2;
         mainPanel.y = (Main._stageHeight - 600) / 2;
         equipEditWindow.addChild(mainPanel);
         title = createTextField("⚔️ 装备编辑器 - " + equip.getName(),16,16777215,true);
         title.x = 20;
         title.y = 15;
         mainPanel.addChild(title);
         closeBtn = createButton("×",16729156,30,30);
         closeBtn.x = 760;
         closeBtn.y = 10;
         closeBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            if(Main._stage.contains(equipEditWindow))
            {
               Main._stage.removeChild(equipEditWindow);
            }
         });
         mainPanel.addChild(closeBtn);
         yPos = 50;
         infoLabel = createTextField("装备信息:",14,65535,true);
         infoLabel.x = 20;
         infoLabel.y = yPos;
         mainPanel.addChild(infoLabel);
         yPos += 25;
         idLabel = createTextField("ID: " + equip.getId(),12,16777215);
         idLabel.x = 20;
         idLabel.y = yPos;
         mainPanel.addChild(idLabel);
         nameLabel = createTextField("名称: " + equip.getName(),12,16777215);
         nameLabel.x = 200;
         nameLabel.y = yPos;
         mainPanel.addChild(nameLabel);
         yPos += 25;
         createEquipPropertyEditor(mainPanel,"强化等级:",equip.getReinforceLevel().toString(),"reinforce",20,yPos);
         yPos += 35;
         createEquipPropertyEditor(mainPanel,"宝石孔数:",equip.getGrid().toString(),"gemGrid",20,yPos);
         yPos += 45;
         attribTitle = createTextField("装备属性编辑:",14,65535,true);
         attribTitle.x = 20;
         attribTitle.y = yPos;
         mainPanel.addChild(attribTitle);
         yPos += 30;
         createEquipAttributeEditors(mainPanel,equip,yPos);
         Main._stage.addChild(equipEditWindow);
         Main._stage.setChildIndex(equipEditWindow,Main._stage.numChildren - 1);
      }
      
      private function createEquipPropertyEditor(parent:Sprite, labelText:String, value:String, fieldName:String, x:int, y:int) : void
      {
         var label:TextField = createTextField(labelText,12,16777215);
         label.x = x;
         label.y = y;
         parent.addChild(label);
         var input:TextField = createInputField(value);
         input.x = x + 100;
         input.y = y - 2;
         input.width = 80;
         editFields[fieldName] = input;
         parent.addChild(input);
         var applyBtn:Sprite = createButton("应用",4486314,60,20);
         applyBtn.x = x + 190;
         applyBtn.y = y - 2;
         applyBtn.addEventListener(MouseEvent.CLICK,createPropertyApplyHandler(fieldName));
         parent.addChild(applyBtn);
      }
      
      private function createEquipAttributeEditors(parent:Sprite, equip:Equip, startY:int) : void
      {
         var attrType:Object;
         var currentValue:Number;
         var j:int;
         var baseAttrib:EquipBaseAttrib;
         var attrX:int;
         var attrY:int;
         var maxBtn:Sprite;
         var maxAttribBtn:Sprite;
         var rerollBtn:Sprite;
         var yPos:int = startY;
         var attributeTypes:Array = [{
            "type":1,
            "name":"生命值",
            "color":16737894,
            "maxValue":9999
         },{
            "type":2,
            "name":"魔法值",
            "color":6711039,
            "maxValue":9999
         },{
            "type":3,
            "name":"攻击力",
            "color":16750950,
            "maxValue":999
         },{
            "type":4,
            "name":"防御力",
            "color":6750054,
            "maxValue":999
         },{
            "type":5,
            "name":"暴击",
            "color":16777062,
            "maxValue":100
         },{
            "type":6,
            "name":"闪避",
            "color":16738047,
            "maxValue":100
         },{
            "type":7,
            "name":"移动速度",
            "color":6750207,
            "maxValue":50
         },{
            "type":8,
            "name":"硬值",
            "color":10066329,
            "maxValue":999
         },{
            "type":9,
            "name":"魔抗",
            "color":10053375,
            "maxValue":999
         },{
            "type":10,
            "name":"破魔",
            "color":16764006,
            "maxValue":999
         },{
            "type":11,
            "name":"命中",
            "color":10092390,
            "maxValue":100
         },{
            "type":12,
            "name":"韧性",
            "color":6724095,
            "maxValue":100
         },{
            "type":13,
            "name":"格挡",
            "color":16751001,
            "maxValue":100
         },{
            "type":14,
            "name":"反击",
            "color":10092543,
            "maxValue":100
         },{
            "type":15,
            "name":"吸血",
            "color":16751103,
            "maxValue":50
         }];
         var baseAttribs:Array = equip.baseAttrib;
         var i:int = 0;
         while(i < attributeTypes.length)
         {
            attrType = attributeTypes[i];
            currentValue = 0;
            j = 0;
            while(j < baseAttribs.length)
            {
               baseAttrib = baseAttribs[j] as EquipBaseAttrib;
               if(baseAttrib && baseAttrib.getAttribType() == attrType.type)
               {
                  currentValue = baseAttrib.getValue();
                  break;
               }
               j++;
            }
            attrX = 20 + i % 3 * 250;
            attrY = yPos + Math.floor(i / 3) * 30;
            createAttributeEditor(parent,equip,attrType,currentValue,attrX,attrY);
            i++;
         }
         yPos += Math.ceil(attributeTypes.length / 3) * 30 + 20;
         maxBtn = createButton("满强化",4486314,80);
         maxBtn.x = 20;
         maxBtn.y = yPos;
         maxBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            setMaxReinforce(equip);
         });
         parent.addChild(maxBtn);
         maxAttribBtn = createButton("满属性",6728260,80);
         maxAttribBtn.x = 110;
         maxAttribBtn.y = yPos;
         maxAttribBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            setMaxAttributes(equip);
         });
         parent.addChild(maxAttribBtn);
         rerollBtn = createButton("重铸属性",16750950,80);
         rerollBtn.x = 200;
         rerollBtn.y = yPos;
         rerollBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            rerollEquipAttributes(equip);
         });
         parent.addChild(rerollBtn);
      }
      
      private function createAttributeEditor(parent:Sprite, equip:Equip, attrType:Object, currentValue:Number, x:int, y:int) : void
      {
         var nameLabel:TextField = createTextField(attrType.name + ":",10,attrType.color);
         nameLabel.x = x;
         nameLabel.y = y;
         nameLabel.width = 60;
         parent.addChild(nameLabel);
         var input:TextField = createInputField(currentValue.toString());
         input.x = x + 65;
         input.y = y;
         input.width = 70;
         input.height = 20;
         input.name = "attr_input_" + attrType.type;
         parent.addChild(input);
         var applyBtn:Sprite = createButton("应用",4486314,50,20);
         applyBtn.x = x + 145;
         applyBtn.y = y;
         applyBtn.addEventListener(MouseEvent.CLICK,createAttributeApplyHandler(equip,attrType.type,input));
         parent.addChild(applyBtn);
      }
      
      private function createAttributeApplyHandler(equip:Equip, attrType:int, inputField:TextField) : Function
      {
         return function(e:MouseEvent):void
         {
            var newValue:Number = Number(parseFloat(inputField.text));
            if(!isNaN(newValue) && newValue >= 0)
            {
               var baseAttribs:Array = equip.baseAttrib;
               var found:Boolean = false;
               var i:int = 0;
               while(i < baseAttribs.length)
               {
                  var baseAttrib:EquipBaseAttrib = baseAttribs[i] as EquipBaseAttrib;
                  if(baseAttrib && baseAttrib.getAttribType() == attrType)
                  {
                     if(newValue == 0)
                     {
                        baseAttribs.splice(i,1);
                        found = true;
                        break;
                     }
                     baseAttrib.setValue(newValue);
                     found = true;
                     break;
                  }
                  i++;
               }
               if(!found && newValue > 0)
               {
                  var newAttrib:EquipBaseAttrib = EquipBaseAttrib.creatEquipBaseAttrib(1,attrType,newValue);
                  baseAttribs.push(newAttrib);
               }
               var attrTypeName:String = getAttributeTypeName(attrType);
               if(newValue == 0)
               {
                  NewMC.Open(attrTypeName + "已删除",Main._stage);
               }
               else
               {
                  NewMC.Open(attrTypeName + "已设置为: " + newValue,Main._stage);
               }
            }
            else
            {
               NewMC.Open("请输入有效的数值(输入0可删除属性)!",Main._stage);
            }
         };
      }
      
      private function getAttributeTypeName(attrType:int) : String
      {
         switch(attrType)
         {
            case 1:
               return "生命值";
            case 2:
               return "魔法值";
            case 3:
               return "攻击力";
            case 4:
               return "防御力";
            case 5:
               return "暴击";
            case 6:
               return "闪避";
            case 7:
               return "移动速度";
            case 8:
               return "硬值";
            case 9:
               return "魔抗";
            case 10:
               return "破魔";
            case 11:
               return "命中";
            case 12:
               return "韧性";
            case 13:
               return "格挡";
            case 14:
               return "反击";
            case 15:
               return "吸血";
            default:
               return "未知属性";
         }
      }
      
      private function createPropertyApplyHandler(fieldName:String) : Function
      {
         return function(e:MouseEvent):void
         {
            var newValue:int;
            var bag:Bag;
            var item:*;
            var equip:Equip;
            var input:TextField = editFields[fieldName] as TextField;
            if(!input)
            {
               return;
            }
            newValue = int(parseInt(input.text));
            bag = getCurrentBag();
            if(!bag)
            {
               return;
            }
            item = null;
            switch(currentBagType)
            {
               case 1:
                  item = bag.getEquipFromBag(selectedSlot);
                  break;
               case 2:
                  item = bag.getSuppliesFromBag(selectedSlot);
                  break;
               case 3:
                  item = bag.getGemFromBag(selectedSlot);
                  break;
               case 4:
                  item = bag.getQuestFromBag(selectedSlot);
                  break;
               case 5:
                  item = bag.getOtherobjFromBag(selectedSlot);
            }
            if(!item || !(item is Equip))
            {
               return;
            }
            equip = item as Equip;
            try
            {
               switch(fieldName)
               {
                  case "reinforce":
                     if(newValue >= 0 && newValue <= 20)
                     {
                        equip.setReinforceLevel(newValue);
                        showMessage("强化等级已设置为: " + newValue,"success");
                     }
                     else
                     {
                        showMessage("强化等级范围: 0-20","warning");
                     }
                     break;
                  case "gemGrid":
                     if(newValue >= 0 && newValue <= 3)
                     {
                        equip.setGrid(newValue);
                        showMessage("宝石孔数已设置为: " + newValue,"success");
                     }
                     else
                     {
                        showMessage("宝石孔数范围: 0-3","warning");
                     }
               }
            }
            catch(error:Error)
            {
               NewMC.Open("设置失败: " + error.message,Main._stage);
            }
         };
      }
      
      private function setMaxReinforce(equip:Equip) : void
      {
         try
         {
            equip.setReinforceLevel(20);
            equip.setGrid(3);
            showMessage("装备已满强化!","success");
         }
         catch(error:Error)
         {
            showMessage("满强化失败: " + error.message,"error");
         }
      }
      
      private function setMaxAttributes(equip:Equip) : void
      {
         var baseAttribs:Array;
         var maxValues:Array;
         var i:int;
         var maxValue:Object;
         var found:Boolean;
         var j:int;
         var baseAttrib:EquipBaseAttrib;
         var newAttrib:EquipBaseAttrib;
         try
         {
            baseAttribs = equip.baseAttrib;
            maxValues = [{
               "type":1,
               "value":9999
            },{
               "type":2,
               "value":9999
            },{
               "type":3,
               "value":999
            },{
               "type":4,
               "value":999
            },{
               "type":5,
               "value":100
            },{
               "type":6,
               "value":100
            },{
               "type":7,
               "value":50
            },{
               "type":8,
               "value":999
            },{
               "type":9,
               "value":999
            },{
               "type":10,
               "value":999
            },{
               "type":11,
               "value":100
            },{
               "type":12,
               "value":100
            },{
               "type":13,
               "value":100
            },{
               "type":14,
               "value":100
            },{
               "type":15,
               "value":50
            }];
            i = 0;
            while(i < maxValues.length)
            {
               maxValue = maxValues[i];
               found = false;
               j = 0;
               while(j < baseAttribs.length)
               {
                  baseAttrib = baseAttribs[j] as EquipBaseAttrib;
                  if(baseAttrib && baseAttrib.getAttribType() == maxValue.type)
                  {
                     baseAttrib.setValue(maxValue.value);
                     found = true;
                     break;
                  }
                  j++;
               }
               if(!found)
               {
                  newAttrib = EquipBaseAttrib.creatEquipBaseAttrib(1,maxValue.type,maxValue.value);
                  baseAttribs.push(newAttrib);
               }
               i++;
            }
            NewMC.Open("装备属性已设置为满值!",Main._stage);
         }
         catch(error:Error)
         {
            NewMC.Open("满属性失败: " + error.message,Main._stage);
         }
      }
      
      private function rerollEquipAttributes(equip:Equip) : void
      {
         var bag:Bag;
         var newEquip:Equip;
         try
         {
            bag = getCurrentBag();
            if(!bag)
            {
               return;
            }
            newEquip = equip.reCreatEquip();
            bag.delEquip(selectedSlot);
            bag.addToEquipBag(newEquip,selectedSlot);
            NewMC.Open("装备属性已重铸!",Main._stage);
            refreshItemList();
         }
         catch(error:Error)
         {
            NewMC.Open("重铸失败: " + error.message,Main._stage);
         }
      }
      
      private function showCountableItemEditOptions(item:*, startY:int) : void
      {
         var countLabel:TextField;
         var countField:TextField;
         var saveBtn:Sprite;
         var deleteBtn:Sprite;
         var cancelBtn:Sprite;
         var yPos:int = startY;
         var currentCount:int = 1;
         try
         {
            if(item.hasOwnProperty("getTimes"))
            {
               currentCount = int(item.getTimes());
            }
         }
         catch(error:Error)
         {
            currentCount = 1;
         }
         countLabel = createTextField("数量:",12,16777215);
         countLabel.x = 10;
         countLabel.y = yPos;
         editPanel.addChild(countLabel);
         countField = createInputField(currentCount.toString());
         countField.x = 100;
         countField.y = yPos;
         editFields["count"] = countField;
         editPanel.addChild(countField);
         yPos += 50;
         saveBtn = createButton("保存",43520,80);
         saveBtn.x = 10;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK,saveCountableItemEdit);
         editPanel.addChild(saveBtn);
         deleteBtn = createButton("删除",16711680,80);
         deleteBtn.x = 100;
         deleteBtn.y = yPos;
         deleteBtn.addEventListener(MouseEvent.CLICK,deleteItem);
         editPanel.addChild(deleteBtn);
         cancelBtn = createButton("取消",6710886,80);
         cancelBtn.x = 190;
         cancelBtn.y = yPos;
         cancelBtn.addEventListener(MouseEvent.CLICK,cancelEdit);
         editPanel.addChild(cancelBtn);
      }
      
      private function showAddItemPanel() : void
      {
         while(editPanel.numChildren > 0)
         {
            editPanel.removeChildAt(0);
         }
         editPanel.graphics.clear();
         editPanel.graphics.beginFill(2236962,0.9);
         editPanel.graphics.drawRoundRect(0,0,350,300,10);
         editPanel.graphics.endFill();
         editPanel.graphics.lineStyle(2,6710886);
         editPanel.graphics.drawRoundRect(0,0,350,300,10);
         var title:TextField = createTextField("添加物品",16,16777215,true);
         title.x = 10;
         title.y = 10;
         editPanel.addChild(title);
         var yPos:int = 50;
         var idLabel:TextField = createTextField("物品ID:",12,16777215);
         idLabel.x = 10;
         idLabel.y = yPos;
         editPanel.addChild(idLabel);
         var idField:TextField = createInputField("");
         idField.x = 100;
         idField.y = yPos;
         editFields["newItemId"] = idField;
         editPanel.addChild(idField);
         yPos += 35;
         var countLabel:TextField = createTextField("数量:",12,16777215);
         countLabel.x = 10;
         countLabel.y = yPos;
         editPanel.addChild(countLabel);
         var countField:TextField = createInputField("1");
         countField.x = 100;
         countField.y = yPos;
         editFields["newItemCount"] = countField;
         editPanel.addChild(countField);
         yPos += 50;
         var addBtn:Sprite = createButton("添加",43520,80);
         addBtn.x = 10;
         addBtn.y = yPos;
         addBtn.addEventListener(MouseEvent.CLICK,addNewItem);
         editPanel.addChild(addBtn);
         var cancelBtn:Sprite = createButton("取消",6710886,80);
         cancelBtn.x = 100;
         cancelBtn.y = yPos;
         cancelBtn.addEventListener(MouseEvent.CLICK,cancelEdit);
         editPanel.addChild(cancelBtn);
      }
      
      private function deleteItem(e:MouseEvent) : void
      {
         var bag:Bag = getCurrentBag();
         switch(currentBagType)
         {
            case 1:
               bag.delEquip(selectedSlot);
               break;
            case 2:
               bag.delSupplies(selectedSlot);
               break;
            case 3:
               var gem:Gem = bag.getGemFromBag(selectedSlot);
               if(gem)
               {
                  bag.delGem(selectedSlot,gem.getTimes());
               }
               break;
            case 4:
               bag.delQuset(selectedSlot);
               break;
            case 5:
               var otherobj:Otherobj = bag.getOtherobjFromBag(selectedSlot);
               if(otherobj)
               {
                  bag.delOtherobj(selectedSlot,otherobj.getTimes());
                  break;
               }
         }
         refreshItemList();
         while(editPanel.numChildren > 0)
         {
            editPanel.removeChildAt(0);
         }
         NewMC.Open("删除成功",Main._stage);
      }
      
      private function saveChanges(e:MouseEvent) : void
      {
         var bag:Bag;
         var item:*;
         var modified:Boolean;
         var newEquipId:int;
         var newEquip:Equip;
         var reinforceLevel:int;
         var gemGrid:int;
         var newCount:int;
         var gemCount:int;
         var questCount:int;
         var otherCount:int;
         try
         {
            bag = getCurrentBag();
            item = null;
            switch(currentBagType)
            {
               case 1:
                  item = bag.getEquipFromBag(selectedSlot);
                  if(item)
                  {
                     modified = false;
                     if(editFields["equipId"])
                     {
                        newEquipId = int(parseInt(editFields["equipId"].text));
                        if(newEquipId > 0 && newEquipId != item.getBase())
                        {
                           newEquip = EquipFactory.createEquipByID(newEquipId);
                           if(newEquip)
                           {
                              if(item.getReinforceAttrib())
                              {
                                 newEquip.changeReinforce(item.getReinforceAttrib().getLevel(),item.getReinforceAttrib().getAttribType(),item.getReinforceAttrib().getAttribValues());
                              }
                              newEquip.setGemGrid(item.getGemGrid());
                              bag.delEquip(selectedSlot);
                              bag.addToEquipBag(newEquip,selectedSlot);
                              item = newEquip;
                              modified = true;
                           }
                        }
                     }
                     if(editFields["reinforceLevel"] && item.getReinforceAttrib())
                     {
                        reinforceLevel = int(parseInt(editFields["reinforceLevel"].text));
                        if(reinforceLevel != item.getReinforceAttrib().getLevel())
                        {
                           item.getReinforceAttrib().setLevel(reinforceLevel);
                           modified = true;
                        }
                     }
                     if(editFields["gemGrid"])
                     {
                        gemGrid = int(parseInt(editFields["gemGrid"].text));
                        if(gemGrid != item.getGemGrid())
                        {
                           item.setGemGrid(gemGrid);
                           modified = true;
                        }
                     }
                  }
                  break;
               case 2:
                  item = bag.getSuppliesFromBag(selectedSlot);
                  if(item && editFields["suppliesCount"])
                  {
                     newCount = int(parseInt(editFields["suppliesCount"].text));
                     if(newCount > 0)
                     {
                        item.setTimes(newCount);
                     }
                  }
                  break;
               case 3:
                  item = bag.getGemFromBag(selectedSlot);
                  if(item && editFields["gemCount"])
                  {
                     gemCount = int(parseInt(editFields["gemCount"].text));
                     if(gemCount > 0)
                     {
                        item.setTimes(gemCount);
                     }
                  }
                  break;
               case 4:
                  item = bag.getQuestFromBag(selectedSlot);
                  if(item && editFields["questCount"])
                  {
                     questCount = int(parseInt(editFields["questCount"].text));
                     if(questCount > 0)
                     {
                        item.setTimes(questCount);
                     }
                  }
                  break;
               case 5:
                  item = bag.getOtherobjFromBag(selectedSlot);
                  if(item && editFields["otherCount"])
                  {
                     otherCount = int(parseInt(editFields["otherCount"].text));
                     if(otherCount > 0)
                     {
                        item.setTimes(otherCount);
                     }
                     break;
                  }
            }
            refreshItemList();
            NewMC.Open("保存成功",Main._stage);
         }
         catch(error:Error)
         {
            NewMC.Open("保存失败: " + error.message,Main._stage);
         }
      }
      
      private function addNewItem(e:MouseEvent) : void
      {
         var itemId:int;
         var itemCount:int;
         var bag:Bag;
         var success:Boolean;
         var equip:Equip;
         var supplies:Supplies;
         var gem:Gem;
         var quest:Quest;
         var otherobj:Otherobj;
         try
         {
            itemId = int(parseInt(editFields["newItemId"].text));
            itemCount = int(parseInt(editFields["newItemCount"].text));
            if(itemId <= 0)
            {
               NewMC.Open("请输入有效的物品ID",Main._stage);
               return;
            }
            if(itemCount <= 0)
            {
               itemCount = 1;
            }
            bag = getCurrentBag();
            success = false;
            switch(currentBagType)
            {
               case 1:
                  equip = EquipFactory.createEquipByID(itemId);
                  if(equip)
                  {
                     success = bag.addToEquipBag(equip,selectedSlot);
                  }
                  break;
               case 2:
                  supplies = SuppliesFactory.getSuppliesById(itemId);
                  if(supplies)
                  {
                     supplies.setTimes(itemCount);
                     success = bag.addToSuppliesBag(supplies,selectedSlot);
                  }
                  break;
               case 3:
                  gem = GemFactory.creatGemById(itemId);
                  if(gem)
                  {
                     gem.setTimes(itemCount);
                     success = bag.addToGemBag(gem,selectedSlot);
                  }
                  break;
               case 4:
                  quest = QuestFactory.creatQust(itemId);
                  if(quest)
                  {
                     quest.setTimes(itemCount);
                     success = bag.addToQuestBag(quest,selectedSlot);
                  }
                  break;
               case 5:
                  otherobj = OtherFactory.creatOther(itemId);
                  if(otherobj)
                  {
                     success = bag.addToOtherobjBag(otherobj,selectedSlot);
                     break;
                  }
            }
            if(success)
            {
               refreshItemList();
               NewMC.Open("添加成功",Main._stage);
            }
            else
            {
               NewMC.Open("添加失败，请检查物品ID是否正确",Main._stage);
            }
         }
         catch(error:Error)
         {
            NewMC.Open("添加失败: " + error.message,Main._stage);
         }
      }
      
      private function saveEquipEdit(e:MouseEvent) : void
      {
         var items:Array;
         var equip:Equip;
         var newReinforce:int;
         var newGemSlot:int;
         var bag:Bag = getCurrentBag();
         if(!bag)
         {
            return;
         }
         items = getBagItems(bag);
         if(!items || selectedSlot < 0 || selectedSlot >= items.length)
         {
            return;
         }
         equip = items[selectedSlot] as Equip;
         if(!equip)
         {
            return;
         }
         try
         {
            if(editFields["reinforce"])
            {
               newReinforce = int(parseInt(editFields["reinforce"].text));
               equip.setReinforceLevel(newReinforce);
            }
            if(editFields["gemSlot"])
            {
               newGemSlot = int(parseInt(editFields["gemSlot"].text));
               equip.setGemSlotNum(newGemSlot);
            }
            showMessage("装备修改成功","success");
            cancelEdit(null);
            refreshContent();
         }
         catch(error:Error)
         {
            showMessage("保存失败: " + error.message,"error");
         }
      }
      
      private function saveCountableItemEdit(e:MouseEvent) : void
      {
         var items:Array;
         var item:*;
         var newCount:int;
         var bag:Bag = getCurrentBag();
         if(!bag)
         {
            return;
         }
         items = getBagItems(bag);
         if(!items || selectedSlot < 0 || selectedSlot >= items.length)
         {
            return;
         }
         item = items[selectedSlot];
         if(!item)
         {
            return;
         }
         try
         {
            if(editFields["count"])
            {
               newCount = int(parseInt(editFields["count"].text));
               if(item.hasOwnProperty("setTimes"))
               {
                  item.setTimes(newCount);
               }
            }
            showMessage("物品修改成功","success");
            cancelEdit(null);
            refreshContent();
         }
         catch(error:Error)
         {
            showMessage("保存失败: " + error.message,"error");
         }
      }
      
      private function deleteItem(e:MouseEvent) : void
      {
         var bag:Bag = getCurrentBag();
         if(!bag)
         {
            return;
         }
         try
         {
            switch(currentBagType)
            {
               case 1:
                  bag.delEquip(selectedSlot,1);
                  break;
               case 2:
                  bag.delSupplies(selectedSlot,1);
                  break;
               case 3:
                  bag.delGem(selectedSlot,1);
                  break;
               case 4:
                  bag.delQuest(selectedSlot,1);
                  break;
               case 5:
                  bag.delOtherobj(selectedSlot,1);
            }
            showMessage("物品删除成功","success");
            cancelEdit(null);
            refreshContent();
         }
         catch(error:Error)
         {
            showMessage("删除失败: " + error.message,"error");
         }
      }
      
      private function addNewItem(e:MouseEvent) : void
      {
         var itemId:int;
         var itemCount:int;
         var newEquip:Equip;
         var newSupplies:Supplies;
         var newGem:Gem;
         var newQuest:Quest;
         var newOther:Otherobj;
         var bag:Bag = getCurrentBag();
         if(!bag)
         {
            return;
         }
         try
         {
            itemId = int(parseInt(editFields["newItemId"].text));
            itemCount = int(parseInt(editFields["newItemCount"].text));
            switch(currentBagType)
            {
               case 1:
                  newEquip = EquipFactory.createEquipByID(itemId);
                  if(newEquip)
                  {
                     bag.addEquipBag(newEquip);
                     showMessage("装备添加成功","success");
                     break;
                  }
                  showMessage("无效的装备ID","error");
                  break;
               case 2:
                  newSupplies = SuppliesFactory.getSuppliesById(itemId);
                  if(newSupplies)
                  {
                     newSupplies.setTimes(itemCount);
                     bag.addSuppliesBag(newSupplies);
                     showMessage("消耗品添加成功","success");
                     break;
                  }
                  showMessage("无效的消耗品ID","error");
                  break;
               case 3:
                  newGem = GemFactory.creatGemById(itemId);
                  if(newGem)
                  {
                     newGem.setTimes(itemCount);
                     bag.addGemBag(newGem);
                     NewMC.Open("宝石添加成功",Main._stage);
                     break;
                  }
                  NewMC.Open("无效的宝石ID",Main._stage);
                  break;
               case 4:
                  newQuest = QuestFactory.getQuestById(itemId);
                  if(newQuest)
                  {
                     newQuest.setTimes(itemCount);
                     bag.addQuestBag(newQuest);
                     NewMC.Open("任务物品添加成功",Main._stage);
                     break;
                  }
                  NewMC.Open("无效的任务物品ID",Main._stage);
                  break;
               case 5:
                  newOther = OtherFactory.getOtherobjById(itemId);
                  if(newOther)
                  {
                     newOther.setTimes(itemCount);
                     bag.addOtherobjBag(newOther);
                     NewMC.Open("其他物品添加成功",Main._stage);
                     break;
                  }
                  NewMC.Open("无效的其他物品ID",Main._stage);
                  break;
            }
            cancelEdit(null);
            refreshContent();
         }
         catch(error:Error)
         {
            NewMC.Open("添加失败: " + error.message,Main._stage);
         }
      }
      
      private function cancelEdit(e:MouseEvent) : void
      {
         while(editPanel.numChildren > 0)
         {
            editPanel.removeChildAt(0);
         }
         editPanel.graphics.clear();
         editFields = {};
         selectedSlot = -1;
      }
      
      private function showMessage(message:String, type:String = "info") : void
      {
         var prefix:String = "";
         switch(type)
         {
            case "success":
               prefix = "✓ ";
               break;
            case "error":
               prefix = "✗ ";
               break;
            case "warning":
               prefix = "⚠ ";
               break;
            case "info":
            default:
               prefix = "ℹ ";
         }
         NewMC.Open(prefix + message,Main._stage);
         trace("[SaveEditor] " + type.toUpperCase() + ": " + message);
      }
      
      private function initJavaScriptInterface() : void
      {
         if(ExternalInterface.available)
         {
            try
            {
               ExternalInterface.addCallback("parseSaveFile",parseSaveFileFromJS);
               ExternalInterface.addCallback("exportSaveFile",exportSaveFileToJS);
               ExternalInterface.addCallback("getSaveData",getSaveDataForJS);
               ExternalInterface.addCallback("setSaveData",setSaveDataFromJS);
               ExternalInterface.addCallback("getPlayerAttributes",getPlayerAttributesForJS);
               ExternalInterface.addCallback("setPlayerAttributes",setPlayerAttributesFromJS);
               ExternalInterface.addCallback("getBagItems",getBagItemsForJS);
               ExternalInterface.addCallback("setBagItem",setBagItemFromJS);
               ExternalInterface.addCallback("addBagItem",addBagItemFromJS);
               ExternalInterface.addCallback("deleteBagItem",deleteBagItemFromJS);
               ExternalInterface.addCallback("getEquipDetails",getEquipDetailsForJS);
               ExternalInterface.addCallback("setEquipDetails",setEquipDetailsFromJS);
               trace("[SaveEditor] 离线存档编辑器JavaScript接口已注册");
            }
            catch(error:Error)
            {
               trace("[SaveEditor] JavaScript接口注册失败: " + error.message);
            }
         }
         else
         {
            trace("[SaveEditor] ExternalInterface不可用");
         }
      }
      
      public function parseSaveFileFromJS(saveFileContent:String) : Object
      {
         var byteArray:ByteArray;
         var saveObject:Object;
         var result:Object;
         try
         {
            trace("[SaveEditor] 开始解析存档文件，内容长度: " + saveFileContent.length);
            byteArray = Base64.decodeToByteArray(saveFileContent) as ByteArray;
            byteArray.position = 0;
            saveObject = byteArray.readObject();
            loadedSaveData = saveObject;
            if(saveObject["p1"])
            {
               tempPlayerData1 = saveObject["p1"] as PlayerData;
            }
            if(saveObject["p2"])
            {
               tempPlayerData2 = saveObject["p2"] as PlayerData;
            }
            result = {
               "success":true,
               "message":"存档解析成功",
               "hasPlayer1":tempPlayerData1 != null,
               "hasPlayer2":tempPlayerData2 != null,
               "gameVersion":saveObject["var"] || false,
               "saveTime":saveObject["saveTimeX"] || false
            };
            if(tempPlayerData1)
            {
               result.player1Info = {
                  "level":tempPlayerData1.getLevel(),
                  "gold":tempPlayerData1.getGold(),
                  "exp":tempPlayerData1.getEXP()
               };
            }
            if(tempPlayerData2)
            {
               result.player2Info = {
                  "level":tempPlayerData2.getLevel(),
                  "gold":tempPlayerData2.getGold(),
                  "exp":tempPlayerData2.getEXP()
               };
            }
            trace("[SaveEditor] 存档解析成功，玩家1: " + (tempPlayerData1 != null) + ", 玩家2: " + (tempPlayerData2 != null));
            return result;
         }
         catch(error:Error)
         {
            trace("[SaveEditor] 存档解析失败: " + error.message);
            return {
               "success":false,
               "error":"存档解析失败: " + error.message
            };
         }
      }
      
      public function exportSaveFileToJS() : Object
      {
         var byteArray:ByteArray;
         var saveFileContent:String;
         try
         {
            if(!loadedSaveData)
            {
               return {
                  "success":false,
                  "error":"没有加载的存档数据"
               };
            }
            if(tempPlayerData1)
            {
               loadedSaveData["p1"] = tempPlayerData1;
            }
            if(tempPlayerData2)
            {
               loadedSaveData["p2"] = tempPlayerData2;
            }
            byteArray = new ByteArray();
            byteArray.writeObject(loadedSaveData);
            byteArray.position = 0;
            saveFileContent = Base64.encodeByteArray(byteArray);
            trace("[SaveEditor] 存档导出成功，内容长度: " + saveFileContent.length);
            return {
               "success":true,
               "message":"存档导出成功",
               "saveFileContent":saveFileContent
            };
         }
         catch(error:Error)
         {
            trace("[SaveEditor] 存档导出失败: " + error.message);
            return {
               "success":false,
               "error":"存档导出失败: " + error.message
            };
         }
      }
      
      public function getSaveDataForJS(playerNum:int = 1) : Object
      {
         var playerData:PlayerData;
         var saveData:Object;
         try
         {
            playerData = playerNum == 1 ? tempPlayerData1 : tempPlayerData2;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家数据不存在，请先导入存档文件"
               };
            }
            saveData = {
               "success":true,
               "playerNum":playerNum,
               "level":playerData.getLevel(),
               "gold":playerData.getGold(),
               "exp":playerData.getEXP(),
               "points":playerData.getPoints(),
               "killPoint":playerData.getKillPoint(),
               "rebirth":playerData.isRebirth()
            };
            try
            {
               if(playerData.hasOwnProperty("hp") && playerData.hp)
               {
                  saveData.hp = playerData.hp.getValue();
               }
               if(playerData.hasOwnProperty("mp") && playerData.mp)
               {
                  saveData.mp = playerData.mp.getValue();
               }
            }
            catch(hpMpError:Error)
            {
               trace("[SaveEditor] HP/MP数据获取失败，这是正常的: " + hpMpError.message);
            }
            return saveData;
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":error.message
            };
         }
      }
      
      public function setSaveDataFromJS(data:Object) : Object
      {
         var playerNum:int;
         var playerData:PlayerData;
         var newPoints:int;
         var currentPoints:int;
         var newKillPoint:int;
         var currentKillPoint:int;
         var diff:int;
         var newHP:int;
         var newMP:int;
         try
         {
            playerNum = int(int(data.playerNum) || true);
            playerData = playerNum == 1 ? tempPlayerData1 : tempPlayerData2;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家数据不存在，请先导入存档文件"
               };
            }
            if(data.hasOwnProperty("level"))
            {
               playerData.setLevel(int(data.level));
            }
            if(data.hasOwnProperty("gold"))
            {
               playerData.SetGold(int(data.gold));
            }
            if(data.hasOwnProperty("exp"))
            {
               playerData.setEXP(int(data.exp));
            }
            if(data.hasOwnProperty("points"))
            {
               newPoints = int(data.points);
               currentPoints = playerData.getPoints();
               playerData.delPoints(currentPoints);
               playerData.addPoint(newPoints);
            }
            if(data.hasOwnProperty("killPoint"))
            {
               newKillPoint = int(data.killPoint);
               currentKillPoint = playerData.getKillPoint();
               diff = newKillPoint - currentKillPoint;
               if(diff != 0)
               {
                  playerData.AddKillPoint(diff);
               }
            }
            if(data.hasOwnProperty("rebirth"))
            {
               playerData._rebirth = Boolean(data.rebirth);
            }
            try
            {
               if(data.hasOwnProperty("hp") && playerData.hasOwnProperty("hp") && playerData.hp)
               {
                  newHP = int(data.hp);
                  playerData.hp.setValue(newHP);
                  if(playerData.hasOwnProperty("hpMax") && playerData.hpMax)
                  {
                     playerData.hpMax.setValue(newHP);
                  }
               }
               if(data.hasOwnProperty("mp") && playerData.hasOwnProperty("mp") && playerData.mp)
               {
                  newMP = int(data.mp);
                  playerData.mp.setValue(newMP);
                  if(playerData.hasOwnProperty("mpMax") && playerData.mpMax)
                  {
                     playerData.mpMax.setValue(newMP);
                  }
               }
            }
            catch(hpMpError:Error)
            {
               trace("[SaveEditor] HP/MP修改失败，这是正常的: " + hpMpError.message);
            }
            return {
               "success":true,
               "message":"存档数据修改成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":error.message
            };
         }
      }
      
      public function getPlayerAttributesForJS(playerNum:int = 1) : Object
      {
         return getSaveDataForJS(playerNum);
      }
      
      public function setPlayerAttributesFromJS(data:Object) : Object
      {
         return setSaveDataFromJS(data);
      }
      
      public function getBagItemsForJS(playerNum:int = 1, bagType:int = 1) : Object
      {
         var playerData:PlayerData;
         var bag:Bag;
         var items:Array;
         var itemsData:Array;
         var i:int;
         var item:*;
         var itemData:Object;
         var equip:Equip;
         var supplies:Supplies;
         var gem:Gem;
         var quest:Quest;
         var other:Otherobj;
         try
         {
            playerData = playerNum == 1 ? tempPlayerData1 : tempPlayerData2;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家数据不存在，请先导入存档文件"
               };
            }
            bag = playerData.getBag();
            if(!bag)
            {
               return {
                  "success":false,
                  "error":"背包数据不存在"
               };
            }
            items = getBagItems(bag,bagType);
            if(!items)
            {
               return {
                  "success":false,
                  "error":"背包数据未初始化"
               };
            }
            itemsData = [];
            i = 0;
            while(i < items.length)
            {
               item = items[i];
               if(item != null)
               {
                  itemData = {
                     "index":i,
                     "name":getItemName(item),
                     "info":getItemInfo(item),
                     "type":bagType
                  };
                  if(item is Equip)
                  {
                     equip = item as Equip;
                     itemData.id = equip.getBase();
                     itemData.reinforceLevel = equip.getReinforceLevel();
                     itemData.gemGrid = equip.getGemGrid();
                  }
                  else if(item is Supplies)
                  {
                     supplies = item as Supplies;
                     itemData.id = supplies.getBase();
                     itemData.count = supplies.getTimes();
                  }
                  else if(item is Gem)
                  {
                     gem = item as Gem;
                     itemData.id = gem.getBase();
                     itemData.count = gem.getTimes();
                  }
                  else if(item is Quest)
                  {
                     quest = item as Quest;
                     itemData.id = quest.getBase();
                     itemData.count = quest.getTimes();
                  }
                  else if(item is Otherobj)
                  {
                     other = item as Otherobj;
                     itemData.id = other.getBase();
                     itemData.count = other.getTimes();
                  }
                  itemsData.push(itemData);
               }
               else
               {
                  itemsData.push({
                     "index":i,
                     "name":"空",
                     "type":bagType,
                     "empty":true
                  });
               }
               i++;
            }
            return {
               "success":true,
               "playerNum":playerNum,
               "bagType":bagType,
               "items":itemsData
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":error.message
            };
         }
      }
      
      private function getBagItems(bag:Bag, bagType:int) : Array
      {
         switch(bagType)
         {
            case 1:
               return bag.getEquipBag();
            case 2:
               return bag.getSuppliesBag();
            case 3:
               return bag.getGemBag();
            case 4:
               return bag.getQuestBag();
            case 5:
               return bag.getOtherobjBag();
            default:
               return null;
         }
      }
      
      public function setBagItemFromJS(data:Object) : Object
      {
         var playerNum:int;
         var bagType:int;
         var index:int;
         var playerData:PlayerData;
         var bag:Bag;
         var items:Array;
         var item:*;
         var equip:Equip;
         try
         {
            playerNum = int(int(data.playerNum) || true);
            bagType = int(int(data.bagType) || true);
            index = int(int(data.index) || false);
            playerData = playerNum == 1 ? tempPlayerData1 : tempPlayerData2;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家数据不存在，请先导入存档文件"
               };
            }
            bag = playerData.getBag();
            if(!bag)
            {
               return {
                  "success":false,
                  "error":"背包数据不存在"
               };
            }
            items = getBagItems(bag,bagType);
            if(!items || index < 0 || index >= items.length)
            {
               return {
                  "success":false,
                  "error":"无效的物品索引"
               };
            }
            item = items[index];
            if(!item)
            {
               return {
                  "success":false,
                  "error":"物品不存在"
               };
            }
            switch(bagType)
            {
               case 1:
                  if(item is Equip)
                  {
                     equip = item as Equip;
                     if(data.hasOwnProperty("reinforceLevel"))
                     {
                        equip.getReinforceAttrib().setLevel(int(data.reinforceLevel));
                     }
                     if(data.hasOwnProperty("gemGrid"))
                     {
                        equip.setGemGrid(int(data.gemGrid));
                     }
                  }
                  break;
               case 2:
               case 3:
               case 4:
               case 5:
                  if(data.hasOwnProperty("count") && item.hasOwnProperty("setTimes"))
                  {
                     item.setTimes(int(data.count));
                     break;
                  }
            }
            return {
               "success":true,
               "message":"物品修改成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":error.message
            };
         }
      }
      
      public function addBagItemFromJS(data:Object) : Object
      {
         var playerNum:int;
         var bagType:int;
         var itemId:int;
         var itemCount:int;
         var index:int;
         var playerData:PlayerData;
         var bag:Bag;
         var success:Boolean;
         var newItem:*;
         try
         {
            playerNum = int(int(data.playerNum) || true);
            bagType = int(int(data.bagType) || true);
            itemId = int(int(data.itemId) || false);
            itemCount = int(int(data.itemCount) || true);
            index = int(int(data.index) || true);
            playerData = playerNum == 1 ? tempPlayerData1 : tempPlayerData2;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家数据不存在，请先导入存档文件"
               };
            }
            bag = playerData.getBag();
            if(!bag)
            {
               return {
                  "success":false,
                  "error":"背包数据不存在"
               };
            }
            if(itemId <= 0)
            {
               return {
                  "success":false,
                  "error":"无效的物品ID"
               };
            }
            success = false;
            newItem = null;
            switch(bagType)
            {
               case 1:
                  newItem = EquipFactory.createEquipByID(itemId);
                  if(newItem)
                  {
                     success = index >= 0 ? bag.addToEquipBag(newItem,index) : bag.addEquipBag(newItem);
                  }
                  break;
               case 2:
                  newItem = SuppliesFactory.getSuppliesById(itemId);
                  if(newItem)
                  {
                     newItem.setTimes(itemCount);
                     success = index >= 0 ? bag.addToSuppliesBag(newItem,index) : bag.addSuppliesBag(newItem);
                  }
                  break;
               case 3:
                  newItem = GemFactory.creatGemById(itemId);
                  if(newItem)
                  {
                     newItem.setTimes(itemCount);
                     success = index >= 0 ? bag.addToGemBag(newItem,index) : bag.addGemBag(newItem);
                  }
                  break;
               case 4:
                  newItem = QuestFactory.creatQust(itemId);
                  if(newItem)
                  {
                     newItem.setTimes(itemCount);
                     success = index >= 0 ? bag.addToQuestBag(newItem,index) : bag.addQuestBag(newItem);
                  }
                  break;
               case 5:
                  newItem = OtherFactory.creatOther(itemId);
                  if(newItem)
                  {
                     newItem.setTimes(itemCount);
                     success = index >= 0 ? bag.addToOtherobjBag(newItem,index) : bag.addOtherobjBag(newItem);
                     break;
                  }
            }
            if(success)
            {
               return {
                  "success":true,
                  "message":"物品添加成功"
               };
            }
            return {
               "success":false,
               "error":"物品添加失败，请检查物品ID是否正确"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":error.message
            };
         }
      }
      
      public function deleteBagItemFromJS(data:Object) : Object
      {
         var playerNum:int;
         var bagType:int;
         var index:int;
         var playerData:PlayerData;
         var bag:Bag;
         try
         {
            playerNum = int(int(data.playerNum) || true);
            bagType = int(int(data.bagType) || true);
            index = int(int(data.index) || false);
            playerData = playerNum == 1 ? tempPlayerData1 : tempPlayerData2;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家数据不存在，请先导入存档文件"
               };
            }
            bag = playerData.getBag();
            if(!bag)
            {
               return {
                  "success":false,
                  "error":"背包数据不存在"
               };
            }
            switch(bagType)
            {
               case 1:
                  bag.delEquip(index,1);
                  break;
               case 2:
                  bag.delSupplies(index,1);
                  break;
               case 3:
                  bag.delGem(index,1);
                  break;
               case 4:
                  bag.delQuest(index,1);
                  break;
               case 5:
                  bag.delOtherobj(index,1);
            }
            return {
               "success":true,
               "message":"物品删除成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":error.message
            };
         }
      }
      
      public function getEquipDetailsForJS(playerNum:int = 1, index:int = 0) : Object
      {
         var playerData:PlayerData;
         var bag:Bag;
         var equipBag:Array;
         var equip:Equip;
         var equipData:Object;
         var baseAttribs:Array;
         var i:int;
         var baseAttrib:EquipBaseAttrib;
         try
         {
            playerData = playerNum == 1 ? tempPlayerData1 : tempPlayerData2;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家数据不存在，请先导入存档文件"
               };
            }
            bag = playerData.getBag();
            if(!bag)
            {
               return {
                  "success":false,
                  "error":"背包数据不存在"
               };
            }
            equipBag = bag.getEquipBag();
            if(!equipBag || index < 0 || index >= equipBag.length)
            {
               return {
                  "success":false,
                  "error":"无效的装备索引"
               };
            }
            equip = equipBag[index] as Equip;
            if(!equip)
            {
               return {
                  "success":false,
                  "error":"装备不存在"
               };
            }
            equipData = {
               "success":true,
               "index":index,
               "id":equip.getBase(),
               "name":equip.getName(),
               "className":equip.getClassName(),
               "descript":equip.getDescript(),
               "reinforceLevel":equip.getReinforceLevel(),
               "gemGrid":equip.getGemGrid(),
               "attributes":[]
            };
            baseAttribs = equip.baseAttrib;
            i = 0;
            while(i < baseAttribs.length)
            {
               baseAttrib = baseAttribs[i] as EquipBaseAttrib;
               if(baseAttrib)
               {
                  equipData.attributes.push({
                     "type":baseAttrib.getAttribType(),
                     "value":baseAttrib.getValue()
                  });
               }
               i++;
            }
            return equipData;
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":error.message
            };
         }
      }
      
      public function setEquipDetailsFromJS(data:Object) : Object
      {
         var playerNum:int;
         var index:int;
         var playerData:PlayerData;
         var bag:Bag;
         var equipBag:Array;
         var equip:Equip;
         var attributes:Array;
         var baseAttribs:Array;
         var i:int;
         var attrData:Object;
         var attrType:int;
         var attrValue:Number;
         var j:int;
         var baseAttrib:EquipBaseAttrib;
         try
         {
            playerNum = int(int(data.playerNum) || true);
            index = int(int(data.index) || false);
            playerData = playerNum == 1 ? tempPlayerData1 : tempPlayerData2;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家数据不存在，请先导入存档文件"
               };
            }
            bag = playerData.getBag();
            if(!bag)
            {
               return {
                  "success":false,
                  "error":"背包数据不存在"
               };
            }
            equipBag = bag.getEquipBag();
            if(!equipBag || index < 0 || index >= equipBag.length)
            {
               return {
                  "success":false,
                  "error":"无效的装备索引"
               };
            }
            equip = equipBag[index] as Equip;
            if(!equip)
            {
               return {
                  "success":false,
                  "error":"装备不存在"
               };
            }
            if(data.hasOwnProperty("reinforceLevel"))
            {
               equip.getReinforceAttrib().setLevel(int(data.reinforceLevel));
            }
            if(data.hasOwnProperty("gemGrid"))
            {
               equip.setGemGrid(int(data.gemGrid));
            }
            if(data.hasOwnProperty("attributes") && data.attributes is Array)
            {
               attributes = data.attributes as Array;
               baseAttribs = equip.baseAttrib;
               i = 0;
               while(i < attributes.length)
               {
                  attrData = attributes[i];
                  if(attrData.hasOwnProperty("type") && attrData.hasOwnProperty("value"))
                  {
                     attrType = int(attrData.type);
                     attrValue = Number(attrData.value);
                     j = 0;
                     while(j < baseAttribs.length)
                     {
                        baseAttrib = baseAttribs[j] as EquipBaseAttrib;
                        if(baseAttrib && baseAttrib.getAttribType() == attrType)
                        {
                           baseAttrib.setValue(attrValue);
                           break;
                        }
                        j++;
                     }
                  }
                  i++;
               }
            }
            return {
               "success":true,
               "message":"装备修改成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":error.message
            };
         }
      }
   }
}

