package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.quest.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import src.*;
   import src.tool.*;
   
   public class Bag
   {
      private var _equipBag:Array = new Array();
      
      private var _suppliesBag:Array = new Array();
      
      private var _gemBag:Array = new Array();
      
      private var _questBag:Array = new Array();
      
      public var _otherobjBag:Array = new Array();
      
      private var _limitE:VT = VT.createVT(24);
      
      private var _limitS:VT = VT.createVT(24);
      
      private var _limitG:VT = VT.createVT(24);
      
      private var _limitO:VT = VT.createVT(24);
      
      public function Bag()
      {
         super();
      }
      
      public static function createBag() : Bag
      {
         var _loc1_:Bag = new Bag();
         var _loc2_:int = 0;
         while(_loc2_ < 48)
         {
            _loc1_._equipBag[_loc2_] = null;
            _loc1_._suppliesBag[_loc2_] = null;
            _loc1_._gemBag[_loc2_] = null;
            _loc1_._questBag[_loc2_] = null;
            _loc1_._otherobjBag[_loc2_] = null;
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function get equipBag() : Array
      {
         return this._equipBag;
      }
      
      public function set equipBag(param1:Array) : void
      {
         this._equipBag = param1;
      }
      
      public function get suppliesBag() : Array
      {
         return this._suppliesBag;
      }
      
      public function set suppliesBag(param1:Array) : void
      {
         this._suppliesBag = param1;
      }
      
      public function get gemBag() : Array
      {
         return this._gemBag;
      }
      
      public function set gemBag(param1:Array) : void
      {
         this._gemBag = param1;
      }
      
      public function get questBag() : Array
      {
         return this._questBag;
      }
      
      public function set questBag(param1:Array) : void
      {
         this._questBag = param1;
      }
      
      public function get otherobjBag() : Array
      {
         return this._otherobjBag;
      }
      
      public function set otherobjBag(param1:Array) : void
      {
         this._otherobjBag = param1;
      }
      
      public function get limitE() : VT
      {
         return this._limitE;
      }
      
      public function set limitE(param1:VT) : void
      {
         this._limitE = param1;
      }
      
      public function get limitS() : VT
      {
         return this._limitS;
      }
      
      public function set limitS(param1:VT) : void
      {
         this._limitS = param1;
      }
      
      public function get limitG() : VT
      {
         return this._limitG;
      }
      
      public function set limitG(param1:VT) : void
      {
         this._limitG = param1;
      }
      
      public function get limitO() : VT
      {
         return this._limitO;
      }
      
      public function set limitO(param1:VT) : void
      {
         this._limitO = param1;
      }
      
      public function getLimitE() : int
      {
         return this._limitE.getValue();
      }
      
      public function getLimitS() : int
      {
         return this._limitS.getValue();
      }
      
      public function getLimitG() : int
      {
         return this._limitG.getValue();
      }
      
      public function getLimitO() : int
      {
         return this._limitO.getValue();
      }
      
      public function addLimitE() : *
      {
         if(this._limitE.getValue() < 48)
         {
            this._limitE.setValue(this._limitE.getValue() + 4);
         }
      }
      
      public function addLimitS() : *
      {
         if(this._limitS.getValue() < 48)
         {
            this._limitS.setValue(this._limitS.getValue() + 4);
         }
      }
      
      public function addLimitG() : *
      {
         if(this._limitG.getValue() < 48)
         {
            this._limitG.setValue(this._limitG.getValue() + 4);
         }
      }
      
      public function addLimitO() : *
      {
         if(this._limitO.getValue() < 48)
         {
            this._limitO.setValue(this._limitO.getValue() + 4);
         }
      }
      
      public function zhengliBag() : *
      {
         var _loc2_:* = undefined;
         var _loc1_:* = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] == null)
            {
               _loc2_ = _loc1_ + 1;
               while(_loc2_ < this._limitE.getValue())
               {
                  if(this._equipBag[_loc2_] != null)
                  {
                     this._equipBag[_loc1_] = this._equipBag[_loc2_];
                     this._equipBag[_loc2_] = null;
                     break;
                  }
                  _loc2_++;
               }
            }
            _loc1_++;
         }
      }
      
      public function zhengliBagS() : *
      {
         var _loc2_:* = undefined;
         var _loc1_:* = 0;
         while(_loc1_ < this._limitS.getValue())
         {
            if(this._suppliesBag[_loc1_] == null)
            {
               _loc2_ = _loc1_ + 1;
               while(_loc2_ < this._limitS.getValue())
               {
                  if(this._suppliesBag[_loc2_] != null)
                  {
                     this._suppliesBag[_loc1_] = this._suppliesBag[_loc2_];
                     this._suppliesBag[_loc2_] = null;
                     break;
                  }
                  _loc2_++;
               }
            }
            _loc1_++;
         }
      }
      
      public function zhengliBagG() : *
      {
         var _loc2_:* = undefined;
         var _loc1_:* = 0;
         while(_loc1_ < this._limitG.getValue())
         {
            if(this._gemBag[_loc1_] == null)
            {
               _loc2_ = _loc1_ + 1;
               while(_loc2_ < this._limitG.getValue())
               {
                  if(this._gemBag[_loc2_] != null)
                  {
                     this._gemBag[_loc1_] = this._gemBag[_loc2_];
                     this._gemBag[_loc2_] = null;
                     break;
                  }
                  _loc2_++;
               }
            }
            _loc1_++;
         }
      }
      
      public function zhengliBagO() : *
      {
         var _loc2_:* = undefined;
         var _loc1_:* = 0;
         while(_loc1_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc1_] == null)
            {
               _loc2_ = _loc1_ + 1;
               while(_loc2_ < this._limitO.getValue())
               {
                  if(this._otherobjBag[_loc2_] != null)
                  {
                     this._otherobjBag[_loc1_] = this._otherobjBag[_loc2_];
                     this._otherobjBag[_loc2_] = null;
                     break;
                  }
                  _loc2_++;
               }
            }
            _loc1_++;
         }
      }
      
      public function getEquipBag() : Array
      {
         return this._equipBag.slice();
      }
      
      public function getEquipFromBag(param1:Number) : Equip
      {
         if(this._equipBag[param1] != null && this._equipBag[param1] is Equip)
         {
            return this._equipBag[param1];
         }
         return null;
      }
      
      public function getSuppliesBag() : Array
      {
         return this._suppliesBag.slice();
      }
      
      public function getSuppliesFromBag(param1:Number) : Supplies
      {
         if(this._suppliesBag[param1] != null)
         {
            return this._suppliesBag[param1];
         }
         return null;
      }
      
      public function getGemBag() : Array
      {
         return this._gemBag.slice();
      }
      
      public function getGemFromBag(param1:Number) : Gem
      {
         if(this._gemBag[param1] != null)
         {
            return this._gemBag[param1];
         }
         return null;
      }
      
      public function getQuestBag() : Array
      {
         return this._questBag.slice();
      }
      
      public function getQuestFromBag(param1:Number) : Quest
      {
         if(this._questBag[param1] != null)
         {
            return this._questBag[param1];
         }
         return null;
      }
      
      public function getOtherobjFromBag(param1:Number) : Otherobj
      {
         if(this._otherobjBag[param1] != null)
         {
            return this._otherobjBag[param1];
         }
         return null;
      }
      
      public function getOtherobjBag() : Array
      {
         return this._otherobjBag.slice();
      }
      
      public function addEquipBag(param1:Equip, param2:int = -1) : Boolean
      {
         if(param2 != -1)
         {
            if(this._equipBag[param2] == null)
            {
               this._equipBag[param2] = param1;
               return true;
            }
            return false;
         }
         var _loc3_:Number = 0;
         while(_loc3_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc3_] == null)
            {
               this._equipBag[_loc3_] = param1;
               return true;
            }
            _loc3_++;
         }
         return false;
      }
      
      public function addToEquipBag(param1:Equip, param2:Number) : Boolean
      {
         if(this._equipBag[param2] == null)
         {
            this._equipBag[param2] = param1;
            return true;
         }
         return false;
      }
      
      public function addSuppliesBag(param1:Supplies) : Boolean
      {
         var _loc2_:Number = 0;
         while(_loc2_ < this._limitS.getValue())
         {
            if(this._suppliesBag[_loc2_] == null)
            {
               this._suppliesBag[_loc2_] = param1;
               return true;
            }
            _loc2_++;
         }
         return false;
      }
      
      public function addToSuppliesBag(param1:Supplies, param2:Number) : Boolean
      {
         if(this._suppliesBag[param2] == null)
         {
            this._suppliesBag[param2] = param1;
            return true;
         }
         return false;
      }
      
      public function backequipBagNum() : uint
      {
         var _loc1_:Number = 0;
         var _loc2_:int = 0;
         while(_loc2_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc2_] == null)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function backQuestBagNum() : uint
      {
         var _loc1_:Number = 0;
         var _loc2_:int = 0;
         while(_loc2_ < 24)
         {
            if(this._questBag[_loc2_] == null)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function backSuppliesBagNum() : uint
      {
         var _loc1_:Number = 0;
         var _loc2_:int = 0;
         while(_loc2_ < this._limitS.getValue())
         {
            if(this._suppliesBag[_loc2_] == null)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function backGemBagNum() : uint
      {
         var _loc1_:Number = 0;
         var _loc2_:int = 0;
         while(_loc2_ < this._limitG.getValue())
         {
            if(this._gemBag[_loc2_] == null)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function canPutGemNum(param1:Number) : uint
      {
         var _loc7_:* = 0;
         var _loc2_:Gem = GemFactory.creatGemById(param1);
         var _loc3_:uint = _loc2_.getTimes();
         var _loc4_:uint = _loc2_.getPileLimit();
         var _loc5_:* = 0;
         var _loc6_:int = 0;
         while(_loc6_ < this._limitG.getValue())
         {
            if(this._gemBag[_loc6_] == null)
            {
               _loc5_ += _loc4_;
            }
            else if(_loc2_.compareGem(this._gemBag[_loc6_]) == true)
            {
               _loc7_ = _loc4_ - this._gemBag[_loc6_].getTimes();
               _loc5_ += _loc7_;
            }
            _loc6_++;
         }
         return _loc5_;
      }
      
      public function backOtherBagNum() : uint
      {
         var _loc1_:Number = 0;
         var _loc2_:int = 0;
         while(_loc2_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc2_] == null)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function canPutOtherNum(param1:Number) : uint
      {
         var _loc7_:* = 0;
         var _loc2_:Otherobj = OtherFactory.creatOther(param1);
         var _loc3_:uint = _loc2_.getTimes();
         var _loc4_:uint = _loc2_.getPileLimit();
         var _loc5_:* = 0;
         var _loc6_:int = 0;
         while(_loc6_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc6_] == null)
            {
               _loc5_ += _loc4_;
            }
            else if(_loc2_.compareOtherobj(this._otherobjBag[_loc6_]) == true)
            {
               _loc7_ = _loc4_ - this._otherobjBag[_loc6_].getTimes();
               _loc5_ += _loc7_;
            }
            _loc6_++;
         }
         return _loc5_;
      }
      
      public function isGemBagEmpty(param1:Gem) : Boolean
      {
         var _loc2_:Boolean = false;
         var _loc3_:int = 0;
         if(param1.getIsPile() == true)
         {
            _loc2_ = true;
            _loc3_ = 0;
            while(_loc3_ < this._limitG.getValue())
            {
               if(this._gemBag[_loc3_] == null)
               {
                  return true;
               }
               if(this._gemBag[_loc3_].compareGem(param1) == true)
               {
                  if(this._gemBag[_loc3_].getTimes() + param1.getTimes() < param1.getPileLimit())
                  {
                     return true;
                  }
                  if(_loc3_ == 23)
                  {
                     return false;
                  }
               }
               _loc3_++;
            }
         }
         else
         {
            _loc3_ = 0;
            while(_loc3_ < this._limitG.getValue())
            {
               if(this._gemBag[_loc3_] == null)
               {
                  return true;
               }
               _loc3_++;
            }
         }
         return false;
      }
      
      public function addGemBag(param1:Gem) : Boolean
      {
         var _loc2_:int = 0;
         var _loc3_:Boolean = false;
         var _loc4_:int = 0;
         if(param1.getIsPile() == true)
         {
            _loc2_ = -1;
            _loc3_ = true;
            _loc4_ = 0;
            while(_loc4_ < this._limitG.getValue())
            {
               if(this._gemBag[_loc4_] != null)
               {
                  if(this._gemBag[_loc4_].compareGem(param1) == true)
                  {
                     while(this._gemBag[_loc4_].getTimes() < param1.getPileLimit())
                     {
                        this._gemBag[_loc4_].addGem(1);
                        if(param1.useGem(1) == false)
                        {
                           break;
                        }
                     }
                     if(param1.getTimes() == 0)
                     {
                        break;
                     }
                  }
               }
               else if(_loc2_ == -1)
               {
                  _loc2_ = _loc4_;
               }
               _loc4_++;
            }
            if(param1.getTimes() > 0)
            {
               this._gemBag[_loc2_] = param1;
            }
         }
         else
         {
            _loc4_ = 0;
            while(_loc4_ < this._limitG.getValue())
            {
               if(this._gemBag[_loc4_] == null)
               {
                  this._gemBag[_loc4_] = param1;
                  return true;
               }
               _loc4_++;
            }
         }
         return false;
      }
      
      public function addToGemBag(param1:Gem, param2:Number) : Boolean
      {
         if(this._gemBag[param2] == null)
         {
            this._gemBag[param2] = param1;
            return true;
         }
         return false;
      }
      
      public function addToQuestBag(param1:Quest, param2:Number) : Boolean
      {
         if(this._questBag[param2] == null)
         {
            this._questBag[param2] = param1;
            return true;
         }
         return false;
      }
      
      public function fallQusetBag(param1:Number) : Number
      {
         var _loc2_:Number = 0;
         var _loc3_:Number = 0;
         while(_loc3_ < 24)
         {
            if(this._questBag[_loc3_] != null)
            {
               if((this._questBag[_loc3_] as Quest).compareById(param1))
               {
                  if(this._questBag[_loc3_].isMany() == true)
                  {
                     _loc2_ += this._questBag[_loc3_].getTimes();
                  }
                  else
                  {
                     _loc2_ += 1;
                  }
               }
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function clearQuest(param1:Number) : void
      {
         var _loc2_:Number = 0;
         while(_loc2_ < 24)
         {
            if(this._questBag[_loc2_] != null)
            {
               if((this._questBag[_loc2_] as Quest).getFallLevel() == param1)
               {
                  this._questBag[_loc2_] = null;
               }
            }
            _loc2_++;
         }
      }
      
      public function addOtherobjBag(param1:Otherobj) : Boolean
      {
         var _loc2_:int = 0;
         var _loc3_:Boolean = false;
         var _loc4_:int = 0;
         if(param1.isMany() == true)
         {
            _loc2_ = -1;
            _loc3_ = true;
            _loc4_ = 0;
            while(_loc4_ < this._limitO.getValue())
            {
               if(this._otherobjBag[_loc4_] != null)
               {
                  if(this._otherobjBag[_loc4_].compareOtherobj(param1) == true)
                  {
                     while(this._otherobjBag[_loc4_].getTimes() < param1.getPileLimit())
                     {
                        this._otherobjBag[_loc4_].addOtherobj(1);
                        if(param1.useOtherobj(1) == false)
                        {
                           break;
                        }
                     }
                     if(param1.getTimes() == 0)
                     {
                        break;
                     }
                  }
               }
               else if(_loc2_ == -1)
               {
                  _loc2_ = _loc4_;
               }
               _loc4_++;
            }
            if(param1.getTimes() > 0)
            {
               this._otherobjBag[_loc2_] = param1;
            }
         }
         else
         {
            _loc4_ = 0;
            while(_loc4_ < this._limitO.getValue())
            {
               if(this._otherobjBag[_loc4_] == null)
               {
                  this._otherobjBag[_loc4_] = param1;
                  return true;
               }
               _loc4_++;
            }
         }
         return false;
      }
      
      public function addQuestBag(param1:Quest) : Boolean
      {
         var _loc2_:int = 0;
         var _loc3_:Boolean = false;
         var _loc4_:int = 0;
         if(param1.isMany() == true)
         {
            _loc2_ = -1;
            _loc3_ = true;
            _loc4_ = 0;
            while(_loc4_ < 24)
            {
               if(this._questBag[_loc4_] != null)
               {
                  if(this._questBag[_loc4_].compareQuest(param1) == true)
                  {
                     while(this._questBag[_loc4_].getTimes() < param1.getPileLimit())
                     {
                        this._questBag[_loc4_].addQuest(1);
                        if(param1.useQuest(1) == false)
                        {
                           break;
                        }
                     }
                     if(param1.getTimes() == 0)
                     {
                        break;
                     }
                  }
               }
               else if(_loc2_ == -1)
               {
                  _loc2_ = _loc4_;
               }
               _loc4_++;
            }
            if(param1.getTimes() > 0)
            {
               this._questBag[_loc2_] = param1;
            }
         }
         else
         {
            _loc4_ = 0;
            while(_loc4_ < 24)
            {
               if(this._questBag[_loc4_] == null)
               {
                  this._questBag[_loc4_] = param1;
                  return true;
               }
               _loc4_++;
            }
         }
         return false;
      }
      
      public function addToOtherobjBag(param1:Otherobj, param2:Number) : Boolean
      {
         if(this._otherobjBag[param2] == null)
         {
            this._otherobjBag[param2] = param1;
            return true;
         }
         return false;
      }
      
      public function delEquip(param1:Number) : Equip
      {
         var _loc2_:Equip = null;
         if(this._equipBag[param1] != null)
         {
            _loc2_ = this._equipBag[param1];
            this._equipBag[param1] = null;
         }
         return _loc2_;
      }
      
      public function delSupplies(param1:Number) : Supplies
      {
         var _loc2_:Supplies = null;
         if(this._suppliesBag[param1] != null)
         {
            _loc2_ = this._suppliesBag[param1];
            this._suppliesBag[param1] = null;
         }
         return _loc2_;
      }
      
      public function delGem(param1:Number, param2:Number) : Gem
      {
         var _loc3_:Gem = null;
         _loc3_ = this._gemBag[param1];
         if(_loc3_.getIsPile() == true)
         {
            _loc3_.useGem(param2);
            if(_loc3_.getTimes() == 0)
            {
               this._gemBag[param1] = null;
            }
         }
         else
         {
            this._gemBag[param1] = null;
         }
         return _loc3_.cloneGem(param2);
      }
      
      public function delQuset(param1:Number) : Quest
      {
         var _loc2_:Quest = null;
         if(this._questBag[param1] != null)
         {
            _loc2_ = this._questBag[param1];
            this._questBag[param1] = null;
         }
         return _loc2_;
      }
      
      public function delOtherobj(param1:Number, param2:Number = 1) : Otherobj
      {
         var _loc3_:Otherobj = null;
         _loc3_ = this._otherobjBag[param1];
         if(_loc3_.isMany() == true)
         {
            _loc3_.useOtherobj(param2);
            if(_loc3_.getTimes() == 0)
            {
               this._otherobjBag[param1] = null;
            }
         }
         else
         {
            this._otherobjBag[param1] = null;
         }
         return _loc3_.cloneOtherobj(param2);
      }
      
      public function equipBagMove(param1:Number, param2:Number) : *
      {
         var _loc3_:Equip = null;
         if(this._equipBag[param2] == null)
         {
            this._equipBag[param2] = this._equipBag[param1];
            this._equipBag[param1] = null;
         }
         else
         {
            _loc3_ = this._equipBag[param2];
            this._equipBag[param2] = this._equipBag[param1];
            this._equipBag[param1] = _loc3_;
         }
      }
      
      public function suppliesBagMove(param1:Number, param2:Number) : *
      {
         var _loc3_:Supplies = null;
         if(this._suppliesBag[param2] == null)
         {
            this._suppliesBag[param2] = this._suppliesBag[param1];
            this._suppliesBag[param1] = null;
         }
         else
         {
            _loc3_ = this._suppliesBag[param2];
            this._suppliesBag[param2] = this._suppliesBag[param1];
            this._suppliesBag[param1] = _loc3_;
         }
      }
      
      public function gemBagMove(param1:Number, param2:Number) : *
      {
         var _loc3_:* = 0;
         var _loc4_:Gem = null;
         if(param1 != param2)
         {
            if(this._gemBag[param2] == null)
            {
               this._gemBag[param2] = this._gemBag[param1];
               this._gemBag[param1] = null;
            }
            else if(this._gemBag[param2].getIsPile() == true && this._gemBag[param2].compareGem(this._gemBag[param1]) == true)
            {
               if(this._gemBag[param2].getTimes() < this._gemBag[param2].getPileLimit())
               {
                  _loc3_ = this._gemBag[param2].getTimes() + this._gemBag[param1].getTimes();
                  if(_loc3_ > this._gemBag[param2].getPileLimit())
                  {
                     this._gemBag[param1].useGem(this._gemBag[param2].getPileLimit() - this._gemBag[param2].getTimes());
                     this._gemBag[param2].addGem(this._gemBag[param2].getPileLimit() - this._gemBag[param2].getTimes());
                  }
                  else
                  {
                     this._gemBag[param2].addGem(this._gemBag[param1].getTimes());
                     this._gemBag[param1] = null;
                  }
               }
            }
            else
            {
               _loc4_ = this._gemBag[param2];
               this._gemBag[param2] = this._gemBag[param1];
               this._gemBag[param1] = _loc4_;
            }
         }
      }
      
      public function questBagMove(param1:Number, param2:Number) : *
      {
         var _loc3_:Otherobj = null;
         if(this._questBag[param2] == null)
         {
            this._questBag[param2] = this._questBag[param1];
            this._questBag[param1] = null;
         }
         else
         {
            _loc3_ = this._questBag[param2];
            this._questBag[param2] = this._questBag[param1];
            this._questBag[param1] = _loc3_;
         }
      }
      
      public function otherobjBagMove(param1:Number, param2:Number) : *
      {
         var _loc3_:* = 0;
         var _loc4_:Otherobj = null;
         if(param1 != param2)
         {
            if(this._otherobjBag[param2] == null)
            {
               this._otherobjBag[param2] = this._otherobjBag[param1];
               this._otherobjBag[param1] = null;
            }
            else if(this._otherobjBag[param2].isMany() == true && this._otherobjBag[param2].compareOtherobj(this._otherobjBag[param1]) == true)
            {
               if(this._otherobjBag[param2].getTimes() < this._otherobjBag[param2].getPileLimit())
               {
                  _loc3_ = this._otherobjBag[param2].getTimes() + this._otherobjBag[param1].getTimes();
                  if(_loc3_ > this._otherobjBag[param2].getPileLimit())
                  {
                     this._otherobjBag[param1].useOtherobj(this._otherobjBag[param2].getPileLimit() - this._otherobjBag[param2].getTimes());
                     this._otherobjBag[param2].addOtherobj(this._otherobjBag[param2].getPileLimit() - this._otherobjBag[param2].getTimes());
                  }
                  else
                  {
                     this._otherobjBag[param2].addOtherobj(this._otherobjBag[param1].getTimes());
                     this._otherobjBag[param1] = null;
                  }
               }
            }
            else
            {
               _loc4_ = this._otherobjBag[param2];
               this._otherobjBag[param2] = this._otherobjBag[param1];
               this._otherobjBag[param1] = _loc4_;
            }
         }
      }
      
      public function isHaveOtherobj(param1:Number) : Boolean
      {
         var _loc2_:Number = 0;
         while(_loc2_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc2_] != null)
            {
               if((this._otherobjBag[_loc2_] as Otherobj).compareById(param1) == true)
               {
                  return true;
               }
            }
            _loc2_++;
         }
         return false;
      }
      
      public function isHaveEquip(param1:Number) : Boolean
      {
         var _loc2_:Number = 0;
         while(_loc2_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc2_] != null)
            {
               if((this._equipBag[_loc2_] as Equip).compareById(param1) == true)
               {
                  return true;
               }
            }
            _loc2_++;
         }
         return false;
      }
      
      public function isHaveSupplies(param1:Number) : Boolean
      {
         var _loc2_:Number = 0;
         while(_loc2_ < this._limitS.getValue())
         {
            if(this._suppliesBag[_loc2_] != null)
            {
               if((this._suppliesBag[_loc2_] as Supplies).compareById(param1) == true)
               {
                  return true;
               }
            }
            _loc2_++;
         }
         return false;
      }
      
      public function isHavesQuest(param1:Number) : Boolean
      {
         var _loc2_:Number = 0;
         while(_loc2_ < 24)
         {
            if(this._questBag[_loc2_] != null)
            {
               if((this._questBag[_loc2_] as Quest).compareById(param1) == true)
               {
                  return true;
               }
            }
            _loc2_++;
         }
         return false;
      }
      
      public function isHaveGem(param1:Number) : Boolean
      {
         var _loc2_:Number = 0;
         while(_loc2_ < this._limitG.getValue())
         {
            if(this._gemBag[_loc2_] != null)
            {
               if((this._gemBag[_loc2_] as Gem).compareById(param1) == true)
               {
                  return true;
               }
            }
            _loc2_++;
         }
         return false;
      }
      
      public function getOtherobjNum(param1:Number) : Number
      {
         var _loc2_:Number = 0;
         var _loc3_:Number = 0;
         while(_loc3_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc3_] != null)
            {
               if((this._otherobjBag[_loc3_] as Otherobj).compareById(param1))
               {
                  if(this._otherobjBag[_loc3_].isMany() == true)
                  {
                     _loc2_ += this._otherobjBag[_loc3_].getTimes();
                  }
                  else
                  {
                     _loc2_ += 1;
                  }
               }
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getGemNum(param1:Number) : Number
      {
         var _loc2_:Number = 0;
         var _loc3_:Number = 0;
         while(_loc3_ < this._limitG.getValue())
         {
            if(this._gemBag[_loc3_] != null)
            {
               if((this._gemBag[_loc3_] as Gem).compareById(param1))
               {
                  if(this._gemBag[_loc3_].getIsPile() == true)
                  {
                     _loc2_ += this._gemBag[_loc3_].getTimes();
                  }
                  else
                  {
                     _loc2_ += 1;
                  }
               }
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getEquipObjNum(param1:Number) : Number
      {
         var _loc2_:Number = 0;
         var _loc3_:Number = 0;
         while(_loc3_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc3_] != null)
            {
               if((this._equipBag[_loc3_] as Equip).compareById(param1))
               {
                  _loc2_ += 1;
               }
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getSupObjNum(param1:Number) : Number
      {
         var _loc2_:Number = 0;
         var _loc3_:Number = 0;
         while(_loc3_ < this._limitS.getValue())
         {
            if(this._suppliesBag[_loc3_] != null)
            {
               if((this._suppliesBag[_loc3_] as Supplies).compareById(param1))
               {
                  _loc2_ += 1;
               }
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function getAndUseOtherobj(param1:Number, param2:Number = 1) : void
      {
         var _loc4_:Number = 0;
         var _loc3_:Number = 0;
         while(_loc3_ < param2)
         {
            _loc4_ = 0;
            while(_loc4_ < this._limitO.getValue())
            {
               if(this._otherobjBag[_loc4_] != null)
               {
                  if((this._otherobjBag[_loc4_] as Otherobj).compareById(param1))
                  {
                     if(this._otherobjBag[_loc4_].isMany() == true)
                     {
                        this._otherobjBag[_loc4_].useOtherobj(1);
                        if(this._otherobjBag[_loc4_].getTimes() == 0)
                        {
                           this._otherobjBag[_loc4_] = null;
                        }
                        break;
                     }
                     this._otherobjBag[_loc4_] = null;
                     break;
                  }
               }
               _loc4_++;
            }
            _loc3_++;
         }
      }
      
      public function delGemById(param1:Number, param2:Number) : Array
      {
         var _loc5_:int = 0;
         var _loc3_:Array = [];
         var _loc4_:Number = 0;
         while(_loc4_ < this._limitG.getValue())
         {
            if(param2 <= 0)
            {
               break;
            }
            if(this._gemBag[_loc4_] != null)
            {
               if((this._gemBag[_loc4_] as Gem).compareById(param1))
               {
                  if(this._gemBag[_loc4_].getIsPile() == true)
                  {
                     _loc5_ = 1;
                     while(_loc5_ <= param2)
                     {
                        (this._gemBag[_loc4_] as Gem).useGem(1);
                        if((this._gemBag[_loc4_] as Gem).getTimes() == 0)
                        {
                           _loc3_.push((this._gemBag[_loc4_] as Gem).cloneGem(_loc5_));
                           param2 -= _loc5_;
                           this._gemBag[_loc4_] = null;
                           break;
                        }
                        if(_loc5_ == param2)
                        {
                           _loc3_.push((this._gemBag[_loc4_] as Gem).cloneGem(param2));
                           param2 = 0;
                           break;
                        }
                        _loc5_++;
                     }
                  }
                  else
                  {
                     param2--;
                     _loc3_.push((this._gemBag[_loc4_] as Gem).cloneGem(1));
                     this._gemBag[_loc4_] = null;
                  }
               }
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function delOtherById(param1:Number, param2:Number = 1) : Array
      {
         var _loc5_:int = 0;
         var _loc3_:Array = [];
         var _loc4_:Number = 0;
         while(_loc4_ < this._limitO.getValue())
         {
            if(param2 <= 0)
            {
               break;
            }
            if(this._otherobjBag[_loc4_] != null)
            {
               if((this._otherobjBag[_loc4_] as Otherobj).compareById(param1))
               {
                  if(this._otherobjBag[_loc4_].isMany() == true)
                  {
                     _loc5_ = 1;
                     while(_loc5_ <= param2)
                     {
                        (this._otherobjBag[_loc4_] as Otherobj).useOtherobj(1);
                        if((this._otherobjBag[_loc4_] as Otherobj).getTimes() == 0)
                        {
                           _loc3_.push((this._otherobjBag[_loc4_] as Otherobj).cloneOtherobj(_loc5_));
                           param2 -= _loc5_;
                           this._otherobjBag[_loc4_] = null;
                           break;
                        }
                        if(_loc5_ == param2)
                        {
                           _loc3_.push((this._otherobjBag[_loc4_] as Otherobj).cloneOtherobj(param2));
                           param2 = 0;
                           break;
                        }
                        _loc5_++;
                     }
                  }
                  else
                  {
                     param2--;
                     _loc3_.push((this._otherobjBag[_loc4_] as Otherobj).cloneOtherobj(1));
                     this._otherobjBag[_loc4_] = null;
                  }
               }
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function clearOther(param1:Number) : void
      {
         var _loc2_:Number = 0;
         while(_loc2_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc2_] != null)
            {
               if((this._otherobjBag[_loc2_] as Otherobj).getId() == param1)
               {
                  this._otherobjBag[_loc2_] = null;
               }
            }
            _loc2_++;
         }
      }
      
      public function getEquipById(param1:Number) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc5_] != null)
            {
               if((this._equipBag[_loc5_] as Equip).compareById(param1))
               {
                  _loc3_.push(this._equipBag[_loc5_]);
                  _loc4_.push(_loc5_);
               }
            }
            _loc5_++;
         }
         _loc2_.push(_loc3_,_loc4_);
         if(_loc3_.length < 1)
         {
            return null;
         }
         return _loc2_;
      }
      
      public function getEquipByIdTOW(param1:Number) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc5_] != null)
            {
               if(Boolean((this._equipBag[_loc5_] as Equip).compareById(param1)) && (this._equipBag[_loc5_] as Equip).getRemainingTime() > 0)
               {
                  _loc3_.push(this._equipBag[_loc5_]);
                  _loc4_.push(_loc5_);
               }
            }
            _loc5_++;
         }
         _loc2_.push(_loc3_,_loc4_);
         if(_loc3_.length < 1)
         {
            return null;
         }
         return _loc2_;
      }
      
      public function getOtherobjById(param1:Number) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc5_] != null)
            {
               if((this._otherobjBag[_loc5_] as Otherobj).compareById(param1))
               {
                  _loc3_.push(this._otherobjBag[_loc5_]);
                  _loc4_.push(_loc5_);
               }
            }
            _loc5_++;
         }
         _loc2_.push(_loc3_,_loc4_);
         if(_loc3_.length < 1)
         {
            return null;
         }
         return _loc2_;
      }
      
      public function getQuestById(param1:Number) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < 24)
         {
            if(this._questBag[_loc5_] != null)
            {
               if((this._questBag[_loc5_] as Quest).compareById(param1))
               {
                  _loc3_.push(this._questBag[_loc5_]);
                  _loc4_.push(_loc5_);
               }
            }
            _loc5_++;
         }
         _loc2_.push(_loc3_,_loc4_);
         if(_loc3_.length < 1)
         {
            return null;
         }
         return _loc2_;
      }
      
      public function getGemById(param1:Number) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < this._limitG.getValue())
         {
            if(this._gemBag[_loc5_] != null)
            {
               if((this._gemBag[_loc5_] as Gem).compareById(param1))
               {
                  _loc3_.push(this._gemBag[_loc5_]);
                  _loc4_.push(_loc5_);
               }
            }
            _loc5_++;
         }
         _loc2_.push(_loc3_,_loc4_);
         if(_loc3_.length < 1)
         {
            return null;
         }
         return _loc2_;
      }
      
      public function getSupById(param1:Number) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < this._limitS.getValue())
         {
            if(this._suppliesBag[_loc5_] != null)
            {
               if((this._suppliesBag[_loc5_] as Supplies).compareById(param1))
               {
                  _loc3_.push(this._suppliesBag[_loc5_]);
                  _loc4_.push(_loc5_);
               }
            }
            _loc5_++;
         }
         _loc2_.push(_loc3_,_loc4_);
         if(_loc3_.length < 1)
         {
            return null;
         }
         return _loc2_;
      }
      
      public function getGemByType(param1:Number) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < this._limitG.getValue())
         {
            if(this._gemBag[_loc5_] != null)
            {
               if((this._gemBag[_loc5_] as Gem).getType() == param1)
               {
                  _loc3_.push(this._gemBag[_loc5_]);
                  _loc4_.push(_loc5_);
               }
            }
            _loc5_++;
         }
         _loc2_.push(_loc3_,_loc4_);
         if(_loc3_.length < 1)
         {
            return null;
         }
         return _loc2_;
      }
      
      public function getOtherByType(param1:Number) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc5_] != null)
            {
               if((this._otherobjBag[_loc5_] as Otherobj).getType() == param1)
               {
                  _loc3_.push(this._otherobjBag[_loc5_]);
                  _loc4_.push(_loc5_);
               }
            }
            _loc5_++;
         }
         _loc2_.push(_loc3_,_loc4_);
         if(_loc3_.length < 1)
         {
            return null;
         }
         return _loc2_;
      }
      
      public function getEquipAndPoint() : Array
      {
         var _loc1_:Array = [];
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Number = 0;
         while(_loc4_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc4_] != null)
            {
               _loc2_.push(this._equipBag[_loc4_]);
               _loc3_.push(_loc4_);
            }
            _loc4_++;
         }
         _loc1_.push(_loc2_,_loc3_);
         if(_loc2_.length < 1)
         {
            return null;
         }
         return _loc1_;
      }
      
      public function delEquipById(param1:Number, param2:Number) : Array
      {
         var _loc5_:Number = 0;
         var _loc3_:Array = [];
         var _loc4_:Number = 0;
         if(param2 > 0)
         {
            _loc5_ = 0;
            while(_loc5_ < this._limitE.getValue())
            {
               if(this._equipBag[_loc5_] != null)
               {
                  if((this._equipBag[_loc5_] as Equip).compareById(param1))
                  {
                     _loc3_.push((this._equipBag[_loc5_] as Equip).getClone());
                     this._equipBag[_loc5_] = null;
                     if(++_loc4_ >= param2)
                     {
                        break;
                     }
                  }
               }
               _loc5_++;
            }
         }
         return _loc3_;
      }
      
      public function delSuppliesById(param1:Number, param2:Number) : Array
      {
         var _loc5_:Number = 0;
         var _loc3_:Array = [];
         var _loc4_:Number = 0;
         if(param2 > 0)
         {
            _loc5_ = 0;
            while(_loc5_ < this._limitS.getValue())
            {
               if(this._suppliesBag[_loc5_] != null)
               {
                  if((this._suppliesBag[_loc5_] as Supplies).compareById(param1))
                  {
                     _loc3_.push((this._suppliesBag[_loc5_] as Supplies).getClone());
                     this._suppliesBag[_loc5_] = null;
                     if(++_loc4_ >= param2)
                     {
                        break;
                     }
                  }
               }
               _loc5_++;
            }
         }
         return _loc3_;
      }
      
      public function cheatTesting() : *
      {
         if(Main.NoLogInfo[4])
         {
            return;
         }
         var _loc1_:Number = 0;
         while(_loc1_ < this._limitG.getValue())
         {
            if(this._gemBag[_loc1_] != null)
            {
               if((this._gemBag[_loc1_] as Gem).getTimes() >= 1000)
               {
                  SaveXX.Save(4,(this._gemBag[_loc1_] as Gem).getTimes());
                  break;
               }
            }
            if(this._otherobjBag[_loc1_] != null)
            {
               if((this._otherobjBag[_loc1_] as Otherobj).getTimes() >= 1000)
               {
                  SaveXX.Save(4,(this._otherobjBag[_loc1_] as Otherobj).getTimes());
                  break;
               }
            }
            _loc1_++;
         }
      }
      
      public function cheatEquip() : *
      {
         var _loc2_:* = 0;
         var _loc3_:Number = 0;
         var _loc1_:Number = 0;
         while(_loc1_ < 23)
         {
            _loc2_ = _loc1_ + 1;
            while(_loc2_ < this._limitE.getValue())
            {
               if(this._equipBag[_loc1_])
               {
                  if((this._equipBag[_loc1_] as Equip).testEquip(this._equipBag[_loc2_]))
                  {
                     this._equipBag[_loc1_] = null;
                  }
               }
               _loc2_++;
            }
            _loc3_ = 0;
            while(_loc3_ < 35)
            {
               if(Boolean(this._equipBag[_loc1_]) && Boolean(StoragePanel.storage.getEquipFromStorage(_loc3_)))
               {
                  if((this._equipBag[_loc1_] as Equip).testEquip(StoragePanel.storage.getEquipFromStorage(_loc3_)))
                  {
                     this._equipBag[_loc1_] = null;
                  }
               }
               _loc3_++;
            }
            _loc1_++;
         }
      }
      
      public function cheatGem() : *
      {
         var _loc2_:* = 0;
         var _loc3_:Number = 0;
         var _loc1_:Number = 0;
         while(_loc1_ < 23)
         {
            _loc2_ = _loc1_ + 1;
            while(_loc2_ < this._limitG.getValue())
            {
               if(this._gemBag[_loc1_])
               {
                  (this._gemBag[_loc1_] as Gem).testGem(this._gemBag[_loc2_]);
               }
               _loc2_++;
            }
            _loc3_ = 0;
            while(_loc3_ < 35)
            {
               if(Boolean(this._gemBag[_loc1_]) && Boolean(StoragePanel.storage.getGemFromStorage(_loc3_)))
               {
                  (this._gemBag[_loc1_] as Gem).testGem(StoragePanel.storage.getGemFromStorage(_loc3_));
               }
               _loc3_++;
            }
            _loc1_++;
         }
      }
      
      public function cheatOther() : *
      {
         var _loc2_:* = 0;
         var _loc3_:Number = 0;
         var _loc1_:Number = 0;
         while(_loc1_ < 23)
         {
            _loc2_ = _loc1_ + 1;
            while(_loc2_ < this._limitO.getValue())
            {
               if(this._otherobjBag[_loc1_])
               {
                  (this._otherobjBag[_loc1_] as Otherobj).testOtherobj(this._otherobjBag[_loc2_]);
               }
               _loc2_++;
            }
            _loc3_ = 0;
            while(_loc3_ < 35)
            {
               if(Boolean(this._otherobjBag[_loc1_]) && Boolean(StoragePanel.storage.getOtherobjFromStorage(_loc3_)))
               {
                  (this._otherobjBag[_loc1_] as Otherobj).testOtherobj(StoragePanel.storage.getOtherobjFromStorage(_loc3_));
               }
               _loc3_++;
            }
            _loc1_++;
         }
      }
      
      public function plan1_5() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getGrid() == 0)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan4_15() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getGrid() == 0)
               {
                  if((this._equipBag[_loc1_] as Equip).getGemSlot().getColor() == 3)
                  {
                     return true;
                  }
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function getEquipGold(param1:Number) : Boolean
      {
         var _loc2_:int = 0;
         while(_loc2_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc2_] != null)
            {
               if((this._equipBag[_loc2_] as Equip).getDressLevel() >= param1)
               {
                  if((this._equipBag[_loc2_] as Equip).getColor() >= 4)
                  {
                     return true;
                  }
               }
            }
            _loc2_++;
         }
         return false;
      }
      
      public function getWuQiGold(param1:Number) : Boolean
      {
         var _loc2_:int = 0;
         while(_loc2_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc2_] != null)
            {
               if((this._equipBag[_loc2_] as Equip).getDressLevel() >= param1)
               {
                  if((this._equipBag[_loc2_] as Equip).getColor() >= 4)
                  {
                     if((this._equipBag[_loc2_] as Equip).getPosition() >= 5 && (this._equipBag[_loc2_] as Equip).getPosition() <= 7)
                     {
                        return true;
                     }
                  }
               }
            }
            _loc2_++;
         }
         return false;
      }
      
      public function plan2_5(param1:Number) : Boolean
      {
         var _loc2_:int = 0;
         while(_loc2_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc2_] != null)
            {
               if((this._equipBag[_loc2_] as Equip).getReinforceLevel() >= param1)
               {
                  return true;
               }
            }
            _loc2_++;
         }
         return false;
      }
      
      public function plan4_16() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getNewSkill() > 0)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan5_7() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getStar() > 0)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan5_8() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getBlessAttrib())
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan5_12() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getPosition() == 8)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan5_13() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getPosition() == 9)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan5_14() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getColor() >= 4)
               {
                  if((this._equipBag[_loc1_] as Equip).getPosition() == 8)
                  {
                     return true;
                  }
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan5_15() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getColor() >= 4)
               {
                  if((this._equipBag[_loc1_] as Equip).getPosition() == 9)
                  {
                     return true;
                  }
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan6_10() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getId() >= 20001 && (this._equipBag[_loc1_] as Equip).getId() <= 20024)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan6_14() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitO.getValue())
         {
            if(this._otherobjBag[_loc1_] != null)
            {
               if((this._otherobjBag[_loc1_] as Otherobj).getId() >= 63163 && (this._otherobjBag[_loc1_] as Otherobj).getId() <= 63168)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan6_15() : int
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         while(_loc2_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc2_] != null)
            {
               if((this._equipBag[_loc2_] as Equip).getStar() >= 3)
               {
                  _loc1_++;
               }
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function plan6_7() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getSuitId() >= 26 && (this._equipBag[_loc1_] as Equip).getSuitId() <= 30)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan7_3() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc1_] != null)
            {
               if((this._equipBag[_loc1_] as Equip).getId() >= 20079 && (this._equipBag[_loc1_] as Equip).getId() <= 20084)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan7_4() : int
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         while(_loc2_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc2_] != null)
            {
               if((this._equipBag[_loc2_] as Equip).getStar() >= 4)
               {
                  _loc1_++;
               }
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function plan8_4() : int
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         while(_loc2_ < this._limitE.getValue())
         {
            if(this._equipBag[_loc2_] != null)
            {
               if((this._equipBag[_loc2_] as Equip).getStar() >= 5)
               {
                  _loc1_++;
               }
            }
            _loc2_++;
         }
         return _loc1_;
      }
   }
}

