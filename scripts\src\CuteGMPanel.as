package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.filters.*;
   import flash.geom.*;
   import com.greensock.TweenLite;
   import com.greensock.easing.Cubic;
   import src.CuteGMFunctions;
   import src.Main;
   import src.GM;
   import src.SaveEditor;
   import com.hotpoint.braveManIII.repository.other.OtherFactory;
   
   public class CuteGMPanel extends MovieClip
   {
      private var mainPanel:Sprite;
      private var titleBar:Sprite;
      private var contentArea:Sprite;
      private var tabContainer:Sprite;
      private var currentTab:int = 0;
      private var tabs:Array = [];
      private var tabContents:Array = [];
      private var isVisible:Boolean = false;
      
      // 可爱的颜色主题
      private static const PINK:uint = 0xFF69B4;
      private static const LIGHT_PINK:uint = 0xFFB6C1;
      private static const PURPLE:uint = 0x9370DB;
      private static const LIGHT_PURPLE:uint = 0xDDA0DD;
      private static const CYAN:uint = 0x00CED1;
      private static const LIGHT_CYAN:uint = 0xAFEEEE;
      private static const ORANGE:uint = 0xFF7F50;
      private static const YELLOW:uint = 0xFFD700;
      private static const GREEN:uint = 0x98FB98;
      
      public function CuteGMPanel()
      {
         super();
         this.initUI();
         this.visible = false;
      }
      
      private function initUI():void
      {
         // 创建主面板
         mainPanel = new Sprite();
         addChild(mainPanel);
         
         // 创建背景
         createBackground();
         
         // 创建标题栏
         createTitleBar();
         
         // 创建选项卡
         createTabs();
         
         // 创建内容区域
         createContentArea();
         
         // 创建各个功能面板
         createTabContents();
         
         // 设置位置
         mainPanel.x = (Main._stageWidth - 800) / 2;
         mainPanel.y = (Main._stageHeight - 600) / 2;
         
         // 添加拖拽功能
         setupDragging();
      }
      
      private function createBackground():void
      {
         var bg:Sprite = new Sprite();
         
         // 创建渐变背景
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(800, 600, Math.PI/4, 0, 0);
         
         var colors:Array = [0xFFE4E1, 0xF0F8FF, 0xE6E6FA];
         var alphas:Array = [0.95, 0.95, 0.95];
         var ratios:Array = [0, 128, 255];
         
         bg.graphics.beginGradientFill("linear", colors, alphas, ratios, matrix);
         bg.graphics.lineStyle(3, 0xFFFFFF, 0.8);
         bg.graphics.drawRoundRect(0, 0, 800, 600, 20, 20);
         bg.graphics.endFill();
         
         // 添加发光效果
         bg.filters = [
            new GlowFilter(PINK, 0.6, 15, 15, 1, 1, true),
            new DropShadowFilter(5, 45, 0x000000, 0.3, 10, 10, 1, 1)
         ];
         
         mainPanel.addChild(bg);
      }
      
      private function createTitleBar():void
      {
         titleBar = new Sprite();
         
         // 标题栏背景
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(800, 50, 0, 0, 0);
         
         titleBar.graphics.beginGradientFill("linear", [PINK, LIGHT_PINK], [1, 1], [0, 255], matrix);
         titleBar.graphics.drawRoundRect(0, 0, 800, 50, 20, 20);
         titleBar.graphics.endFill();
         
         // 标题文字
         var title:TextField = createTextField("✨ 可爱GM控制台 ✨", 18, 0xFFFFFF, true);
         title.x = 20;
         title.y = 15;
         titleBar.addChild(title);
         
         // 关闭按钮
         var closeBtn:Sprite = createCuteButton("✕", 0xFF6B6B, 30, 30);
         closeBtn.x = 760;
         closeBtn.y = 10;
         closeBtn.addEventListener(MouseEvent.CLICK, hide);
         titleBar.addChild(closeBtn);
         
         // 最小化按钮
         var minBtn:Sprite = createCuteButton("－", 0xFFD93D, 30, 30);
         minBtn.x = 720;
         minBtn.y = 10;
         minBtn.addEventListener(MouseEvent.CLICK, minimize);
         titleBar.addChild(minBtn);
         
         mainPanel.addChild(titleBar);
      }
      
      private function createTabs():void
      {
         tabContainer = new Sprite();
         tabContainer.y = 60;
         
         var tabNames:Array = ["角色", "装备", "宠物", "道具", "系统", "调试", "高级"];
         var tabColors:Array = [PINK, PURPLE, CYAN, ORANGE, YELLOW, GREEN, 0xFF1493];
         
         for(var i:int = 0; i < tabNames.length; i++)
         {
            var tab:Sprite = createTab(tabNames[i], tabColors[i], i);
            tab.x = i * 130 + 10;
            tabContainer.addChild(tab);
            tabs.push(tab);
         }
         
         mainPanel.addChild(tabContainer);
      }
      
      private function createTab(name:String, color:uint, index:int):Sprite
      {
         var tab:Sprite = new Sprite();
         
         // 标签背景
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(120, 40, Math.PI/2, 0, 0);
         
         tab.graphics.beginGradientFill("linear", [color, adjustBrightness(color, -0.2)], [1, 1], [0, 255], matrix);
         tab.graphics.lineStyle(2, 0xFFFFFF, 0.8);
         tab.graphics.drawRoundRect(0, 0, 120, 40, 15, 15);
         tab.graphics.endFill();
         
         // 标签文字
         var label:TextField = createTextField(name, 14, 0xFFFFFF, true);
         label.x = (120 - label.textWidth) / 2;
         label.y = 12;
         tab.addChild(label);
         
         tab.buttonMode = true;
         tab.useHandCursor = true;
         
         // 添加悬停效果
         tab.addEventListener(MouseEvent.MOUSE_OVER, function(e:MouseEvent):void {
            TweenLite.to(tab, 0.2, {scaleX: 1.05, scaleY: 1.05});
            tab.filters = [new GlowFilter(0xFFFFFF, 0.8, 8, 8, 2, 1)];
         });
         
         tab.addEventListener(MouseEvent.MOUSE_OUT, function(e:MouseEvent):void {
            if(currentTab != index) {
               TweenLite.to(tab, 0.2, {scaleX: 1.0, scaleY: 1.0});
               tab.filters = [];
            }
         });
         
         tab.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            selectTab(index);
         });
         
         return tab;
      }
      
      private function createContentArea():void
      {
         contentArea = new Sprite();
         contentArea.x = 10;
         contentArea.y = 110;
         
         // 内容区域背景
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0xFFFFFF, 0.9);
         bg.graphics.lineStyle(2, LIGHT_PINK, 0.8);
         bg.graphics.drawRoundRect(0, 0, 780, 480, 15, 15);
         bg.graphics.endFill();
         
         contentArea.addChild(bg);
         mainPanel.addChild(contentArea);
      }
      
      private function createTabContents():void
      {
         tabContents = [];
         
         // 创建各个选项卡的内容
         tabContents.push(createPlayerTab());    // 角色
         tabContents.push(createEquipTab());     // 装备
         tabContents.push(createPetTab());       // 宠物
         tabContents.push(createItemTab());      // 道具
         tabContents.push(createSystemTab());    // 系统
         tabContents.push(createDebugTab());     // 调试
         tabContents.push(createAdvancedTab());  // 高级
         
         // 默认显示第一个选项卡
         selectTab(0);
      }
      
      private function createPlayerTab():Sprite
      {
         var container:Sprite = new Sprite();

         // 标题
         var title:TextField = createTextField("🎀 角色属性管理 🎀", 16, PINK, true);
         title.x = 20;
         title.y = 20;
         container.addChild(title);

         // 等级设置
         var levelLabel:TextField = createTextField("等级:", 12, 0x333333);
         levelLabel.x = 30;
         levelLabel.y = 60;
         container.addChild(levelLabel);

         var levelInput:TextField = createInputField("99", 80, 25);
         levelInput.x = 80;
         levelInput.y = 55;
         container.addChild(levelInput);

         var levelBtn1:Sprite = createCuteButton("设置P1", PINK, 80, 25);
         levelBtn1.x = 180;
         levelBtn1.y = 55;
         levelBtn1.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerLevel(1, parseInt(levelInput.text));
         });
         container.addChild(levelBtn1);

         var levelBtn2:Sprite = createCuteButton("设置P2", LIGHT_PINK, 80, 25);
         levelBtn2.x = 270;
         levelBtn2.y = 55;
         levelBtn2.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerLevel(2, parseInt(levelInput.text));
         });
         container.addChild(levelBtn2);

         // 金币设置
         var goldLabel:TextField = createTextField("金币:", 12, 0x333333);
         goldLabel.x = 30;
         goldLabel.y = 100;
         container.addChild(goldLabel);

         var goldInput:TextField = createInputField("999999", 80, 25);
         goldInput.x = 80;
         goldInput.y = 95;
         container.addChild(goldInput);

         var goldBtn1:Sprite = createCuteButton("设置P1", PURPLE, 80, 25);
         goldBtn1.x = 180;
         goldBtn1.y = 95;
         goldBtn1.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerGold(1, parseInt(goldInput.text));
         });
         container.addChild(goldBtn1);

         var goldBtn2:Sprite = createCuteButton("设置P2", LIGHT_PURPLE, 80, 25);
         goldBtn2.x = 270;
         goldBtn2.y = 95;
         goldBtn2.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerGold(2, parseInt(goldInput.text));
         });
         container.addChild(goldBtn2);

         // 经验设置
         var expLabel:TextField = createTextField("经验:", 12, 0x333333);
         expLabel.x = 30;
         expLabel.y = 140;
         container.addChild(expLabel);

         var expInput:TextField = createInputField("999999", 80, 25);
         expInput.x = 80;
         expInput.y = 135;
         container.addChild(expInput);

         var expBtn1:Sprite = createCuteButton("设置P1", CYAN, 80, 25);
         expBtn1.x = 180;
         expBtn1.y = 135;
         expBtn1.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerExp(1, parseInt(expInput.text));
         });
         container.addChild(expBtn1);

         var expBtn2:Sprite = createCuteButton("设置P2", LIGHT_CYAN, 80, 25);
         expBtn2.x = 270;
         expBtn2.y = 135;
         expBtn2.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerExp(2, parseInt(expInput.text));
         });
         container.addChild(expBtn2);

         // 技能点设置
         var skillLabel:TextField = createTextField("技能点:", 12, 0x333333);
         skillLabel.x = 30;
         skillLabel.y = 180;
         container.addChild(skillLabel);

         var skillInput:TextField = createInputField("9999", 80, 25);
         skillInput.x = 80;
         skillInput.y = 175;
         container.addChild(skillInput);

         var skillBtn1:Sprite = createCuteButton("设置P1", ORANGE, 80, 25);
         skillBtn1.x = 180;
         skillBtn1.y = 175;
         skillBtn1.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerSkillPoints(1, parseInt(skillInput.text));
         });
         container.addChild(skillBtn1);

         var skillBtn2:Sprite = createCuteButton("设置P2", YELLOW, 80, 25);
         skillBtn2.x = 270;
         skillBtn2.y = 175;
         skillBtn2.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerSkillPoints(2, parseInt(skillInput.text));
         });
         container.addChild(skillBtn2);

         // 快捷按钮区域
         var quickLabel:TextField = createTextField("💫 快捷操作 💫", 14, PINK, true);
         quickLabel.x = 400;
         quickLabel.y = 60;
         container.addChild(quickLabel);

         var maxLevelBtn:Sprite = createCuteButton("满级P1", PINK, 100, 35);
         maxLevelBtn.x = 400;
         maxLevelBtn.y = 90;
         maxLevelBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerLevel(1, 99);
            CuteGMFunctions.setPlayerGold(1, 999999999);
            CuteGMFunctions.setPlayerExp(1, 999999999);
            CuteGMFunctions.setPlayerSkillPoints(1, 9999);
         });
         container.addChild(maxLevelBtn);

         var maxLevelBtn2:Sprite = createCuteButton("满级P2", LIGHT_PINK, 100, 35);
         maxLevelBtn2.x = 520;
         maxLevelBtn2.y = 90;
         maxLevelBtn2.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerLevel(2, 99);
            CuteGMFunctions.setPlayerGold(2, 999999999);
            CuteGMFunctions.setPlayerExp(2, 999999999);
            CuteGMFunctions.setPlayerSkillPoints(2, 9999);
         });
         container.addChild(maxLevelBtn2);

         return container;
      }
      
      private function createEquipTab():Sprite
      {
         var container:Sprite = new Sprite();

         var title:TextField = createTextField("⚔️ 装备管理 ⚔️", 16, PURPLE, true);
         title.x = 20;
         title.y = 20;
         container.addChild(title);

         var oneKeyEquip:Sprite = createCuteButton("一键神装", PURPLE, 120, 40);
         oneKeyEquip.x = 30;
         oneKeyEquip.y = 60;
         oneKeyEquip.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllEquipments();
         });
         container.addChild(oneKeyEquip);

         var clearBagBtn:Sprite = createCuteButton("清空背包", 0xFF6B6B, 120, 40);
         clearBagBtn.x = 170;
         clearBagBtn.y = 60;
         clearBagBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.clearBag();
         });
         container.addChild(clearBagBtn);

         var clearStorageBtn:Sprite = createCuteButton("清空仓库", 0xFF8C69, 120, 40);
         clearStorageBtn.x = 310;
         clearStorageBtn.y = 60;
         clearStorageBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.clearStorage();
         });
         container.addChild(clearStorageBtn);

         // 更多装备功能
         var moreEquipLabel:TextField = createTextField("✨ 更多装备功能 ✨", 14, PURPLE, true);
         moreEquipLabel.x = 30;
         moreEquipLabel.y = 120;
         container.addChild(moreEquipLabel);

         var skillGemsBtn:Sprite = createCuteButton("一键技能石", PURPLE, 120, 35);
         skillGemsBtn.x = 30;
         skillGemsBtn.y = 150;
         skillGemsBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllSkillGems();
         });
         container.addChild(skillGemsBtn);

         var badgesBtn:Sprite = createCuteButton("一键徽章", LIGHT_PURPLE, 120, 35);
         badgesBtn.x = 170;
         badgesBtn.y = 150;
         badgesBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllBadges();
         });
         container.addChild(badgesBtn);

         var stampsBtn:Sprite = createCuteButton("一键印章", PURPLE, 120, 35);
         stampsBtn.x = 310;
         stampsBtn.y = 150;
         stampsBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllStamps();
         });
         container.addChild(stampsBtn);

         return container;
      }
      
      private function createPetTab():Sprite
      {
         var container:Sprite = new Sprite();

         var title:TextField = createTextField("🐾 宠物管理 🐾", 16, CYAN, true);
         title.x = 20;
         title.y = 20;
         container.addChild(title);

         var oneKeyPet:Sprite = createCuteButton("一键宠物", CYAN, 120, 40);
         oneKeyPet.x = 30;
         oneKeyPet.y = 60;
         oneKeyPet.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllPets();
         });
         container.addChild(oneKeyPet);

         var oneKeyElves:Sprite = createCuteButton("一键精灵", LIGHT_CYAN, 120, 40);
         oneKeyElves.x = 170;
         oneKeyElves.y = 60;
         oneKeyElves.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllElves();
         });
         container.addChild(oneKeyElves);

         // 更多宠物功能
         var morePetLabel:TextField = createTextField("🎯 更多宠物功能 🎯", 14, CYAN, true);
         morePetLabel.x = 30;
         morePetLabel.y = 120;
         container.addChild(morePetLabel);

         var petEquipBtn:Sprite = createCuteButton("一键宠装", CYAN, 120, 35);
         petEquipBtn.x = 30;
         petEquipBtn.y = 150;
         petEquipBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllPetEquipments();
         });
         container.addChild(petEquipBtn);

         var titlesBtn:Sprite = createCuteButton("一键称号", LIGHT_CYAN, 120, 35);
         titlesBtn.x = 170;
         titlesBtn.y = 150;
         titlesBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllTitles();
         });
         container.addChild(titlesBtn);

         var clearPetsBtn:Sprite = createCuteButton("清空宠物", 0xFF6B6B, 120, 35);
         clearPetsBtn.x = 310;
         clearPetsBtn.y = 150;
         clearPetsBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.clearPets();
         });
         container.addChild(clearPetsBtn);

         var clearElvesBtn:Sprite = createCuteButton("清空精灵", 0xFF8C69, 120, 35);
         clearElvesBtn.x = 450;
         clearElvesBtn.y = 150;
         clearElvesBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.clearElves();
         });
         container.addChild(clearElvesBtn);

         return container;
      }
      
      private function createItemTab():Sprite
      {
         var container:Sprite = new Sprite();

         var title:TextField = createTextField("💎 道具管理 💎", 16, ORANGE, true);
         title.x = 20;
         title.y = 20;
         container.addChild(title);

         var oneKeyGems:Sprite = createCuteButton("一键宝石", ORANGE, 120, 40);
         oneKeyGems.x = 30;
         oneKeyGems.y = 60;
         oneKeyGems.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllGems();
         });
         container.addChild(oneKeyGems);

         var oneKeySupplies:Sprite = createCuteButton("一键消耗品", YELLOW, 120, 40);
         oneKeySupplies.x = 170;
         oneKeySupplies.y = 60;
         oneKeySupplies.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.giveAllSupplies();
         });
         container.addChild(oneKeySupplies);

         // 道具添加区域
         var addItemLabel:TextField = createTextField("➕ 添加道具 ➕", 14, ORANGE, true);
         addItemLabel.x = 30;
         addItemLabel.y = 120;
         container.addChild(addItemLabel);

         var itemIdLabel:TextField = createTextField("道具ID:", 12, 0x333333);
         itemIdLabel.x = 30;
         itemIdLabel.y = 155;
         container.addChild(itemIdLabel);

         var itemIdInput:TextField = createInputField("21001", 80, 25);
         itemIdInput.x = 90;
         itemIdInput.y = 150;
         container.addChild(itemIdInput);

         var itemCountLabel:TextField = createTextField("数量:", 12, 0x333333);
         itemCountLabel.x = 190;
         itemCountLabel.y = 155;
         container.addChild(itemCountLabel);

         var itemCountInput:TextField = createInputField("99", 60, 25);
         itemCountInput.x = 230;
         itemCountInput.y = 150;
         container.addChild(itemCountInput);

         var addItemBtn:Sprite = createCuteButton("添加", ORANGE, 80, 25);
         addItemBtn.x = 310;
         addItemBtn.y = 150;
         addItemBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            var itemId:int = parseInt(itemIdInput.text);
            var itemCount:int = parseInt(itemCountInput.text);
            if(itemId > 0 && itemCount > 0) {
               try {
                  var item:* = OtherFactory.creatOther(itemId);
                  if(item) {
                     for(var i:int = 0; i < itemCount; i++) {
                        Main.player1.getBag().addOtherobjBag(item);
                     }
                     CuteGMFunctions.showCuteMessage("➕ 道具添加成功！ID:" + itemId + " 数量:" + itemCount, "success");
                  } else {
                     CuteGMFunctions.showCuteMessage("❌ 无效的道具ID！", "error");
                  }
               } catch(e:Error) {
                  CuteGMFunctions.showCuteMessage("❌ 添加失败：" + e.message, "error");
               }
            } else {
               CuteGMFunctions.showCuteMessage("❌ 请输入有效的ID和数量！", "error");
            }
         });
         container.addChild(addItemBtn);

         return container;
      }
      
      private function createSystemTab():Sprite
      {
         var container:Sprite = new Sprite();

         var title:TextField = createTextField("⚙️ 系统设置 ⚙️", 16, YELLOW, true);
         title.x = 20;
         title.y = 20;
         container.addChild(title);

         var unlockStagesBtn:Sprite = createCuteButton("解锁关卡", YELLOW, 120, 40);
         unlockStagesBtn.x = 30;
         unlockStagesBtn.y = 60;
         unlockStagesBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockAllStages();
         });
         container.addChild(unlockStagesBtn);

         var completeTasksBtn:Sprite = createCuteButton("完成任务", GREEN, 120, 40);
         completeTasksBtn.x = 170;
         completeTasksBtn.y = 60;
         completeTasksBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.completeAllTasks();
         });
         container.addChild(completeTasksBtn);

         var unlockAchBtn:Sprite = createCuteButton("解锁成就", ORANGE, 120, 40);
         unlockAchBtn.x = 310;
         unlockAchBtn.y = 60;
         unlockAchBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockAllAchievements();
         });
         container.addChild(unlockAchBtn);

         // 游戏设置区域
         var gameSettingsLabel:TextField = createTextField("🎮 游戏设置 🎮", 14, YELLOW, true);
         gameSettingsLabel.x = 30;
         gameSettingsLabel.y = 120;
         container.addChild(gameSettingsLabel);

         var enableP2Btn:Sprite = createCuteButton("开启双人", PURPLE, 120, 35);
         enableP2Btn.x = 30;
         enableP2Btn.y = 150;
         enableP2Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            Main.P1P2 = true;
            CuteGMFunctions.showCuteMessage("👥 双人模式已开启！", "success");
         });
         container.addChild(enableP2Btn);

         var disableP2Btn:Sprite = createCuteButton("关闭双人", LIGHT_PURPLE, 120, 35);
         disableP2Btn.x = 170;
         disableP2Btn.y = 150;
         disableP2Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            Main.P1P2 = false;
            CuteGMFunctions.showCuteMessage("👤 单人模式已开启！", "success");
         });
         container.addChild(disableP2Btn);

         var saveGameBtn:Sprite = createCuteButton("保存游戏", CYAN, 120, 35);
         saveGameBtn.x = 310;
         saveGameBtn.y = 150;
         saveGameBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            Main.Save();
            CuteGMFunctions.showCuteMessage("💾 游戏已保存！", "success");
         });
         container.addChild(saveGameBtn);

         // 解锁功能区域
         var unlockLabel:TextField = createTextField("🔓 解锁功能 🔓", 14, YELLOW, true);
         unlockLabel.x = 30;
         unlockLabel.y = 200;
         container.addChild(unlockLabel);

         var unlockBagBtn:Sprite = createCuteButton("解锁背包", GREEN, 120, 35);
         unlockBagBtn.x = 30;
         unlockBagBtn.y = 230;
         unlockBagBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockBagSlot();
         });
         container.addChild(unlockBagBtn);

         var unlockPetSlotBtn:Sprite = createCuteButton("解锁宠物栏", CYAN, 120, 35);
         unlockPetSlotBtn.x = 170;
         unlockPetSlotBtn.y = 230;
         unlockPetSlotBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockPetSlot();
         });
         container.addChild(unlockPetSlotBtn);

         var unlockElvesBtn:Sprite = createCuteButton("解锁精灵槽", PURPLE, 120, 35);
         unlockElvesBtn.x = 310;
         unlockElvesBtn.y = 230;
         unlockElvesBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockElvesSlot();
         });
         container.addChild(unlockElvesBtn);

         var unlockStampBtn:Sprite = createCuteButton("解锁印章", ORANGE, 120, 35);
         unlockStampBtn.x = 450;
         unlockStampBtn.y = 230;
         unlockStampBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockStampSlot();
         });
         container.addChild(unlockStampBtn);

         // 更多系统功能
         var moreSystemLabel:TextField = createTextField("⚡ 更多功能 ⚡", 14, YELLOW, true);
         moreSystemLabel.x = 30;
         moreSystemLabel.y = 280;
         container.addChild(moreSystemLabel);

         var bountyBtn:Sprite = createCuteButton("悬赏全满", GREEN, 120, 35);
         bountyBtn.x = 30;
         bountyBtn.y = 310;
         bountyBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.fillAllBounties();
         });
         container.addChild(bountyBtn);

         var herbBtn:Sprite = createCuteButton("采药全满", CYAN, 120, 35);
         herbBtn.x = 170;
         herbBtn.y = 310;
         herbBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.fillAllHerbs();
         });
         container.addChild(herbBtn);

         var planBtn:Sprite = createCuteButton("计划全开", PURPLE, 120, 35);
         planBtn.x = 310;
         planBtn.y = 310;
         planBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockAllPlans();
         });
         container.addChild(planBtn);

         var skillsBtn:Sprite = createCuteButton("四职业技能", ORANGE, 120, 35);
         skillsBtn.x = 450;
         skillsBtn.y = 310;
         skillsBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockAllJobSkills();
         });
         container.addChild(skillsBtn);

         return container;
      }
      
      private function createDebugTab():Sprite
      {
         var container:Sprite = new Sprite();

         var title:TextField = createTextField("🔧 调试工具 🔧", 16, GREEN, true);
         title.x = 20;
         title.y = 20;
         container.addChild(title);

         var killAllBtn:Sprite = createCuteButton("秒杀敌人", 0xFF6B6B, 120, 40);
         killAllBtn.x = 30;
         killAllBtn.y = 60;
         killAllBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            // 秒杀所有敌人
            for(var i:* in Enemy.All) {
               var enemy:Enemy = Enemy.All[i];
               enemy.HpXX2(1410065407);
            }
            CuteGMFunctions.showCuteMessage("💀 所有敌人已被秒杀！", "success");
         });
         container.addChild(killAllBtn);

         var openSaveEditorBtn:Sprite = createCuteButton("存档编辑器", PURPLE, 120, 40);
         openSaveEditorBtn.x = 170;
         openSaveEditorBtn.y = 60;
         openSaveEditorBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            if(!GM.saveEditor) {
               GM.saveEditor = new SaveEditor();
               Main._stage.addChild(GM.saveEditor);
            }
            GM.saveEditor.toggle();
         });
         container.addChild(openSaveEditorBtn);

         var oldGMBtn:Sprite = createCuteButton("传统GM", 0x808080, 120, 40);
         oldGMBtn.x = 310;
         oldGMBtn.y = 60;
         oldGMBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            // 打开传统GM界面
            var gm:GM = new GM();
            var keyEvent:KeyboardEvent = new KeyboardEvent(KeyboardEvent.KEY_DOWN);
            keyEvent.keyCode = 220;  // \ 键
            gm.按键(keyEvent);
         });
         container.addChild(oldGMBtn);

         // 调试信息区域
         var debugInfoLabel:TextField = createTextField("� 调试信息 �", 14, GREEN, true);
         debugInfoLabel.x = 30;
         debugInfoLabel.y = 120;
         container.addChild(debugInfoLabel);

         var showInfoBtn:Sprite = createCuteButton("显示信息", GREEN, 120, 35);
         showInfoBtn.x = 30;
         showInfoBtn.y = 150;
         showInfoBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            var info:String = "🎮 游戏信息:\n";
            info += "玩家1等级: " + (Main.player1 ? Main.player1.level.getValue() : "未知") + "\n";
            info += "玩家1金币: " + (Main.player1 ? Main.player1.gold.getValue() : "未知") + "\n";
            info += "双人模式: " + (Main.P1P2 ? "开启" : "关闭") + "\n";
            info += "当前地图: " + (Main.gameNum ? Main.gameNum.getValue() : "未知");
            CuteGMFunctions.showCuteMessage(info, "info");
         });
         container.addChild(showInfoBtn);

         var resetSkillBtn:Sprite = createCuteButton("重置技能", CYAN, 120, 35);
         resetSkillBtn.x = 170;
         resetSkillBtn.y = 150;
         resetSkillBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.resetSkills();
         });
         container.addChild(resetSkillBtn);

         // 特殊功能区域
         var specialLabel:TextField = createTextField("🔮 特殊功能 🔮", 14, GREEN, true);
         specialLabel.x = 30;
         specialLabel.y = 200;
         container.addChild(specialLabel);

         var bypassBtn:Sprite = createCuteButton("过检测", 0xFF6B6B, 120, 35);
         bypassBtn.x = 30;
         bypassBtn.y = 230;
         bypassBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.bypassDetection();
         });
         container.addChild(bypassBtn);

         var baoZhuBtn:Sprite = createCuteButton("宝珠满级", PURPLE, 120, 35);
         baoZhuBtn.x = 170;
         baoZhuBtn.y = 230;
         baoZhuBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.maxBaoZhu();
         });
         container.addChild(baoZhuBtn);

         var compassBtn:Sprite = createCuteButton("女神罗盘", CYAN, 120, 35);
         compassBtn.x = 310;
         compassBtn.y = 230;
         compassBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.fixGoddessCompass();
         });
         container.addChild(compassBtn);

         // 传送功能
         var teleportLabel:TextField = createTextField("🚀 传送功能 🚀", 14, GREEN, true);
         teleportLabel.x = 30;
         teleportLabel.y = 280;
         container.addChild(teleportLabel);

         var surfaceBtn:Sprite = createCuteButton("地面主城", ORANGE, 120, 35);
         surfaceBtn.x = 30;
         surfaceBtn.y = 310;
         surfaceBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.teleportToSurface();
         });
         container.addChild(surfaceBtn);

         var underwaterBtn:Sprite = createCuteButton("海底主城", CYAN, 120, 35);
         underwaterBtn.x = 170;
         underwaterBtn.y = 310;
         underwaterBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.teleportToUnderwater();
         });
         container.addChild(underwaterBtn);

         // 职业功能
         var jobLabel:TextField = createTextField("⚔️ 职业功能 ⚔️", 14, GREEN, true);
         jobLabel.x = 30;
         jobLabel.y = 360;
         container.addChild(jobLabel);

         var clearJobBtn:Sprite = createCuteButton("清除职业", 0xFF6B6B, 100, 30);
         clearJobBtn.x = 30;
         clearJobBtn.y = 390;
         clearJobBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.clearJob(1);
         });
         container.addChild(clearJobBtn);

         var swordBtn:Sprite = createCuteButton("转职剑士", PURPLE, 100, 30);
         swordBtn.x = 140;
         swordBtn.y = 390;
         swordBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.changeJob(1, 0);
         });
         container.addChild(swordBtn);

         var mageBtn:Sprite = createCuteButton("转职法师", CYAN, 100, 30);
         mageBtn.x = 250;
         mageBtn.y = 390;
         mageBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.changeJob(1, 1);
         });
         container.addChild(mageBtn);

         var fighterBtn:Sprite = createCuteButton("转职格斗", ORANGE, 100, 30);
         fighterBtn.x = 360;
         fighterBtn.y = 390;
         fighterBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.changeJob(1, 2);
         });
         container.addChild(fighterBtn);

         var bladeBtn:Sprite = createCuteButton("转职刀客", GREEN, 100, 30);
         bladeBtn.x = 470;
         bladeBtn.y = 390;
         bladeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.changeJob(1, 3);
         });
         container.addChild(bladeBtn);

         return container;
      }

      private function createAdvancedTab():Sprite
      {
         var container:Sprite = new Sprite();

         var title:TextField = createTextField("🌟 高级功能 🌟", 16, 0xFF1493, true);
         title.x = 20;
         title.y = 20;
         container.addChild(title);

         // 符石设置区域
         var runeLabel:TextField = createTextField("💫 符石设置 💫", 14, 0xFF1493, true);
         runeLabel.x = 30;
         runeLabel.y = 60;
         container.addChild(runeLabel);

         var runeValueLabel:TextField = createTextField("符石数值:", 12, 0x333333);
         runeValueLabel.x = 30;
         runeValueLabel.y = 95;
         container.addChild(runeValueLabel);

         var runeValueInput:TextField = createInputField("999999", 100, 25);
         runeValueInput.x = 110;
         runeValueInput.y = 90;
         container.addChild(runeValueInput);

         var rune1Btn:Sprite = createCuteButton("勇者符石", 0xFF1493, 100, 30);
         rune1Btn.x = 230;
         rune1Btn.y = 90;
         rune1Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setRuneStone(1, parseInt(runeValueInput.text));
         });
         container.addChild(rune1Btn);

         var rune2Btn:Sprite = createCuteButton("暗黑符石", 0x8B008B, 100, 30);
         rune2Btn.x = 340;
         rune2Btn.y = 90;
         rune2Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setRuneStone(2, parseInt(runeValueInput.text));
         });
         container.addChild(rune2Btn);

         var rune3Btn:Sprite = createCuteButton("失落符石", 0x4169E1, 100, 30);
         rune3Btn.x = 450;
         rune3Btn.y = 90;
         rune3Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setRuneStone(3, parseInt(runeValueInput.text));
         });
         container.addChild(rune3Btn);

         var rune4Btn:Sprite = createCuteButton("王者符石", 0xFFD700, 100, 30);
         rune4Btn.x = 560;
         rune4Btn.y = 90;
         rune4Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setRuneStone(4, parseInt(runeValueInput.text));
         });
         container.addChild(rune4Btn);

         // 清空功能区域
         var clearLabel:TextField = createTextField("🧹 清空功能 🧹", 14, 0xFF1493, true);
         clearLabel.x = 30;
         clearLabel.y = 140;
         container.addChild(clearLabel);

         var clearTitlesBtn:Sprite = createCuteButton("清空称号", 0xFF6B6B, 120, 35);
         clearTitlesBtn.x = 30;
         clearTitlesBtn.y = 170;
         clearTitlesBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.clearTitles();
         });
         container.addChild(clearTitlesBtn);

         // 自定义输入区域
         var customLabel:TextField = createTextField("⚙️ 自定义设置 ⚙️", 14, 0xFF1493, true);
         customLabel.x = 30;
         customLabel.y = 220;
         container.addChild(customLabel);

         // 自定义等级
         var customLevelLabel:TextField = createTextField("等级:", 12, 0x333333);
         customLevelLabel.x = 30;
         customLevelLabel.y = 255;
         container.addChild(customLevelLabel);

         var customLevelInput:TextField = createInputField("99", 80, 25);
         customLevelInput.x = 70;
         customLevelInput.y = 250;
         container.addChild(customLevelInput);

         var setLevel1Btn:Sprite = createCuteButton("设置P1", 0xFF1493, 80, 25);
         setLevel1Btn.x = 160;
         setLevel1Btn.y = 250;
         setLevel1Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerLevel(1, parseInt(customLevelInput.text));
         });
         container.addChild(setLevel1Btn);

         var setLevel2Btn:Sprite = createCuteButton("设置P2", 0xDA70D6, 80, 25);
         setLevel2Btn.x = 250;
         setLevel2Btn.y = 250;
         setLevel2Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerLevel(2, parseInt(customLevelInput.text));
         });
         container.addChild(setLevel2Btn);

         // 自定义金币
         var customGoldLabel:TextField = createTextField("金币:", 12, 0x333333);
         customGoldLabel.x = 30;
         customGoldLabel.y = 295;
         container.addChild(customGoldLabel);

         var customGoldInput:TextField = createInputField("999999", 80, 25);
         customGoldInput.x = 70;
         customGoldInput.y = 290;
         container.addChild(customGoldInput);

         var setGold1Btn:Sprite = createCuteButton("设置P1", 0xFF1493, 80, 25);
         setGold1Btn.x = 160;
         setGold1Btn.y = 290;
         setGold1Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerGold(1, parseInt(customGoldInput.text));
         });
         container.addChild(setGold1Btn);

         var setGold2Btn:Sprite = createCuteButton("设置P2", 0xDA70D6, 80, 25);
         setGold2Btn.x = 250;
         setGold2Btn.y = 290;
         setGold2Btn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerGold(2, parseInt(customGoldInput.text));
         });
         container.addChild(setGold2Btn);

         // 一键全功能按钮
         var allInOneLabel:TextField = createTextField("🚀 一键全功能 🚀", 14, 0xFF1493, true);
         allInOneLabel.x = 400;
         allInOneLabel.y = 220;
         container.addChild(allInOneLabel);

         var godModeBtn:Sprite = createCuteButton("上帝模式", 0xFF1493, 150, 50);
         godModeBtn.x = 400;
         godModeBtn.y = 250;
         godModeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            // 执行所有一键功能
            CuteGMFunctions.setPlayerLevel(1, 99);
            CuteGMFunctions.setPlayerGold(1, 999999999);
            CuteGMFunctions.setPlayerExp(1, 999999999);
            CuteGMFunctions.setPlayerSkillPoints(1, 9999);
            CuteGMFunctions.giveAllEquipments();
            CuteGMFunctions.giveAllPets();
            CuteGMFunctions.giveAllElves();
            CuteGMFunctions.giveAllGems();
            CuteGMFunctions.giveAllSupplies();
            CuteGMFunctions.giveAllSkillGems();
            CuteGMFunctions.giveAllBadges();
            CuteGMFunctions.giveAllStamps();
            CuteGMFunctions.giveAllTitles();
            CuteGMFunctions.giveAllPetEquipments();
            CuteGMFunctions.unlockAllStages();
            CuteGMFunctions.completeAllTasks();
            CuteGMFunctions.unlockAllAchievements();
            CuteGMFunctions.unlockBagSlot();
            CuteGMFunctions.unlockPetSlot();
            CuteGMFunctions.unlockElvesSlot();
            CuteGMFunctions.unlockStampSlot();
            CuteGMFunctions.unlockAllJobSkills();
            CuteGMFunctions.showCuteMessage("🌟 上帝模式已激活！所有功能已开启！", "success");
         });
         container.addChild(godModeBtn);

         return container;
      }

      private function selectTab(index:int):void
      {
         if(currentTab == index) return;

         // 重置所有标签样式
         for(var i:int = 0; i < tabs.length; i++)
         {
            var tab:Sprite = tabs[i];
            if(i == index) {
               tab.scaleX = tab.scaleY = 1.05;
               tab.filters = [new GlowFilter(0xFFFFFF, 1.0, 10, 10, 2, 1)];
            } else {
               tab.scaleX = tab.scaleY = 1.0;
               tab.filters = [];
            }
         }

         // 隐藏当前内容
         if(tabContents[currentTab] && contentArea.contains(tabContents[currentTab]))
         {
            contentArea.removeChild(tabContents[currentTab]);
         }

         // 显示新内容
         currentTab = index;
         if(tabContents[currentTab])
         {
            tabContents[currentTab].x = 20;
            tabContents[currentTab].y = 20;
            contentArea.addChild(tabContents[currentTab]);
         }
      }

      private function createCuteButton(text:String, color:uint, width:int = 100, height:int = 30):Sprite
      {
         var btn:Sprite = new Sprite();

         // 创建渐变背景
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(width, height, Math.PI/2, 0, 0);

         var colors:Array = [color, adjustBrightness(color, -0.3)];
         var alphas:Array = [1, 1];
         var ratios:Array = [0, 255];

         btn.graphics.beginGradientFill("linear", colors, alphas, ratios, matrix);
         btn.graphics.lineStyle(2, 0xFFFFFF, 0.8);
         btn.graphics.drawRoundRect(0, 0, width, height, height/2, height/2);
         btn.graphics.endFill();

         // 添加内发光
         btn.graphics.lineStyle(1, adjustBrightness(color, 0.4), 0.6);
         btn.graphics.drawRoundRect(1, 1, width-2, height-2, (height-2)/2, (height-2)/2);

         // 按钮文字
         var label:TextField = createTextField(text, 12, 0xFFFFFF, true);
         label.x = (width - label.textWidth) / 2;
         label.y = (height - label.textHeight) / 2;
         btn.addChild(label);

         btn.buttonMode = true;
         btn.useHandCursor = true;

         // 悬停效果
         btn.addEventListener(MouseEvent.MOUSE_OVER, function(e:MouseEvent):void {
            TweenLite.to(btn, 0.2, {scaleX: 1.05, scaleY: 1.05});
            btn.filters = [new GlowFilter(0xFFFFFF, 0.8, 8, 8, 2, 1)];
         });

         btn.addEventListener(MouseEvent.MOUSE_OUT, function(e:MouseEvent):void {
            TweenLite.to(btn, 0.2, {scaleX: 1.0, scaleY: 1.0});
            btn.filters = [];
         });

         btn.addEventListener(MouseEvent.MOUSE_DOWN, function(e:MouseEvent):void {
            TweenLite.to(btn, 0.1, {scaleX: 0.95, scaleY: 0.95});
         });

         btn.addEventListener(MouseEvent.MOUSE_UP, function(e:MouseEvent):void {
            TweenLite.to(btn, 0.1, {scaleX: 1.05, scaleY: 1.05});
         });

         return btn;
      }

      private function createTextField(text:String, size:int = 12, color:uint = 0x000000, bold:Boolean = false):TextField
      {
         var tf:TextField = new TextField();
         tf.text = text;
         tf.autoSize = TextFieldAutoSize.LEFT;
         tf.selectable = false;
         tf.mouseEnabled = false;

         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = size;
         format.color = color;
         format.bold = bold;
         tf.setTextFormat(format);

         return tf;
      }

      private function createInputField(defaultText:String = "", width:int = 100, height:int = 25):TextField
      {
         var input:TextField = new TextField();
         input.type = TextFieldType.INPUT;
         input.border = true;
         input.borderColor = LIGHT_PINK;
         input.background = true;
         input.backgroundColor = 0xFFFFFF;
         input.width = width;
         input.height = height;
         input.text = defaultText;

         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = 12;
         format.color = 0x333333;
         input.setTextFormat(format);

         // 添加圆角效果（通过遮罩）
         var mask:Sprite = new Sprite();
         mask.graphics.beginFill(0x000000);
         mask.graphics.drawRoundRect(0, 0, width, height, 8, 8);
         mask.graphics.endFill();
         input.addChild(mask);
         input.mask = mask;

         return input;
      }

      private function adjustBrightness(color:uint, factor:Number):uint
      {
         var r:uint = (color >> 16) & 0xFF;
         var g:uint = (color >> 8) & 0xFF;
         var b:uint = color & 0xFF;

         if(factor > 0) {
            r = Math.min(255, r + (255 - r) * factor);
            g = Math.min(255, g + (255 - g) * factor);
            b = Math.min(255, b + (255 - b) * factor);
         } else {
            r = Math.max(0, r + r * factor);
            g = Math.max(0, g + g * factor);
            b = Math.max(0, b + b * factor);
         }

         return (r << 16) | (g << 8) | b;
      }

      private function setupDragging():void
      {
         var isDragging:Boolean = false;
         var dragOffsetX:Number = 0;
         var dragOffsetY:Number = 0;

         titleBar.addEventListener(MouseEvent.MOUSE_DOWN, function(e:MouseEvent):void {
            isDragging = true;
            dragOffsetX = e.stageX - mainPanel.x;
            dragOffsetY = e.stageY - mainPanel.y;
            Main._stage.addEventListener(MouseEvent.MOUSE_MOVE, onDrag);
            Main._stage.addEventListener(MouseEvent.MOUSE_UP, stopDrag);
         });

         function onDrag(e:MouseEvent):void {
            if(isDragging) {
               mainPanel.x = e.stageX - dragOffsetX;
               mainPanel.y = e.stageY - dragOffsetY;
            }
         }

         function stopDrag(e:MouseEvent):void {
            isDragging = false;
            Main._stage.removeEventListener(MouseEvent.MOUSE_MOVE, onDrag);
            Main._stage.removeEventListener(MouseEvent.MOUSE_UP, stopDrag);
         }
      }

      public function show():void
      {
         trace("CuteGMPanel.show() 被调用");

         if(!isVisible) {
            trace("设置界面为可见状态");
            isVisible = true;
            this.visible = true;

            // 可爱的出现动画
            this.alpha = 0;
            this.scaleX = this.scaleY = 0.5;

            trace("开始显示动画");
            TweenLite.to(this, 0.5, {
               alpha: 1,
               scaleX: 1,
               scaleY: 1,
               ease: Cubic.easeOut,
               onComplete: function():void {
                  trace("GM界面显示动画完成");
               }
            });
         } else {
            trace("界面已经是可见状态");
         }
      }

      public function hide(e:MouseEvent = null):void
      {
         if(isVisible) {
            isVisible = false;

            // 可爱的消失动画
            TweenLite.to(this, 0.3, {
               alpha: 0,
               scaleX: 0.5,
               scaleY: 0.5,
               ease: Cubic.easeIn,
               onComplete: function():void {
                  visible = false;
               }
            });
         }
      }

      public function toggle():void
      {
         trace("CuteGMPanel.toggle() 被调用，当前状态: " + (isVisible ? "显示" : "隐藏"));

         if(isVisible) {
            trace("隐藏GM界面");
            hide();
         } else {
            trace("显示GM界面");
            show();
         }
      }

      private function minimize(e:MouseEvent):void
      {
         // 最小化动画
         TweenLite.to(mainPanel, 0.3, {
            scaleY: 0.1,
            y: Main._stageHeight - 50,
            ease: Cubic.easeOut
         });

         // 3秒后自动恢复
         TweenLite.delayedCall(3, function():void {
            TweenLite.to(mainPanel, 0.3, {
               scaleY: 1,
               y: (Main._stageHeight - 600) / 2,
               ease: Cubic.easeOut
            });
         });
      }
   }
}
