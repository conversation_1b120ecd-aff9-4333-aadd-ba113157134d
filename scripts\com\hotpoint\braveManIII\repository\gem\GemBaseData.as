package com.hotpoint.braveManIII.repository.gem
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.gem.*;
   
   public class GemBaseData
   {
      private var _id:VT;
      
      private var _frame:VT;
      
      private var _className:String;
      
      private var _name:String;
      
      private var _times:VT;
      
      private var _type:VT;
      
      private var _isPile:Boolean;
      
      private var _isStrengthen:Boolean;
      
      private var _isCompound:Boolean;
      
      private var _dropLevel:VT;
      
      private var _useLevel:VT;
      
      private var _strengthenLevel:VT;
      
      private var _pileLimit:VT;
      
      private var _price:VT;
      
      private var _color:VT;
      
      private var _probability:VT;
      
      private var _descript:String;
      
      private var _addSkill:VT;
      
      private var _addAttrib:Array = [];
      
      public function GemBaseData()
      {
         super();
      }
      
      public static function cteateGemBaseData(param1:Number, param2:Number, param3:String, param4:String, param5:String, param6:Number, param7:int, param8:Boolean, param9:Boolean, param10:Boolean, param11:Number, param12:Number, param13:Number, param14:Number, param15:Number, param16:Number, param17:Number, param18:Array, param19:Number) : GemBaseData
      {
         var _loc20_:GemBaseData = new GemBaseData();
         _loc20_._id = VT.createVT(param1);
         _loc20_._frame = VT.createVT(param2);
         _loc20_._className = param4;
         _loc20_._name = param3;
         _loc20_._descript = param5;
         _loc20_._times = VT.createVT(param6);
         _loc20_._type = VT.createVT(param7);
         _loc20_._isPile = param8;
         _loc20_._isStrengthen = param9;
         _loc20_._isCompound = param10;
         _loc20_._dropLevel = VT.createVT(param11);
         _loc20_._useLevel = VT.createVT(param12);
         _loc20_._strengthenLevel = VT.createVT(param13);
         _loc20_._pileLimit = VT.createVT(param14);
         _loc20_._price = VT.createVT(param15);
         _loc20_._color = VT.createVT(param16);
         _loc20_._probability = VT.createVT(param17);
         _loc20_._addAttrib = param18;
         _loc20_._addSkill = VT.createVT(param19);
         return _loc20_;
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(param1:VT) : void
      {
         this._id = param1;
      }
      
      public function get frame() : VT
      {
         return this._frame;
      }
      
      public function set frame(param1:VT) : void
      {
         this._frame = param1;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function set name(param1:String) : void
      {
         this._name = param1;
      }
      
      public function get times() : VT
      {
         return this._times;
      }
      
      public function set times(param1:VT) : void
      {
         this._times = param1;
      }
      
      public function get type() : VT
      {
         return this._type;
      }
      
      public function set type(param1:VT) : void
      {
         this._type = param1;
      }
      
      public function get isPile() : Boolean
      {
         return this._isPile;
      }
      
      public function set isPile(param1:Boolean) : void
      {
         this._isPile = param1;
      }
      
      public function get isStrengthen() : Boolean
      {
         return this._isStrengthen;
      }
      
      public function set isStrengthen(param1:Boolean) : void
      {
         this._isStrengthen = param1;
      }
      
      public function get isCompound() : Boolean
      {
         return this._isCompound;
      }
      
      public function set isCompound(param1:Boolean) : void
      {
         this._isCompound = param1;
      }
      
      public function get dropLevel() : VT
      {
         return this._dropLevel;
      }
      
      public function set dropLevel(param1:VT) : void
      {
         this._dropLevel = param1;
      }
      
      public function get useLevel() : VT
      {
         return this._useLevel;
      }
      
      public function set useLevel(param1:VT) : void
      {
         this._useLevel = param1;
      }
      
      public function get pileLimit() : VT
      {
         return this._pileLimit;
      }
      
      public function set pileLimit(param1:VT) : void
      {
         this._pileLimit = param1;
      }
      
      public function get price() : VT
      {
         return this._price;
      }
      
      public function set price(param1:VT) : void
      {
         this._price = param1;
      }
      
      public function get color() : VT
      {
         return this._color;
      }
      
      public function set color(param1:VT) : void
      {
         this._color = param1;
      }
      
      public function get probability() : VT
      {
         return this._probability;
      }
      
      public function set probability(param1:VT) : void
      {
         this._probability = param1;
      }
      
      public function get descript() : String
      {
         return this._descript;
      }
      
      public function set descript(param1:String) : void
      {
         this._descript = param1;
      }
      
      public function get addSkill() : VT
      {
         return this._addSkill;
      }
      
      public function set addSkill(param1:VT) : void
      {
         this._addSkill = param1;
      }
      
      public function get addAttrib() : Array
      {
         return this._addAttrib;
      }
      
      public function set addAttrib(param1:Array) : void
      {
         this._addAttrib = param1;
      }
      
      public function get strengthenLevel() : VT
      {
         return this._strengthenLevel;
      }
      
      public function set strengthenLevel(param1:VT) : void
      {
         this._strengthenLevel = param1;
      }
      
      public function get className() : String
      {
         return this._className;
      }
      
      public function set className(param1:String) : void
      {
         this._className = param1;
      }
      
      public function getId() : Number
      {
         return this._id.getValue();
      }
      
      public function getFrame() : Number
      {
         return this._frame.getValue();
      }
      
      public function getName() : String
      {
         return this._name;
      }
      
      public function getClassName() : String
      {
         return this._className;
      }
      
      public function getIsStrengthen() : Boolean
      {
         return this._isStrengthen;
      }
      
      public function getDropLevel() : Number
      {
         return this._dropLevel.getValue();
      }
      
      public function getUseLevel() : Number
      {
         return this._useLevel.getValue();
      }
      
      public function getStrengthenLevel() : Number
      {
         return this._strengthenLevel.getValue();
      }
      
      public function getPrice() : Number
      {
         return this._price.getValue();
      }
      
      public function getIsCompound() : Boolean
      {
         return this._isCompound;
      }
      
      public function getColor() : Number
      {
         return this._color.getValue();
      }
      
      public function getDescript() : String
      {
         return this._descript;
      }
      
      public function getTimes() : Number
      {
         return this._times.getValue();
      }
      
      public function getType() : int
      {
         return this._type.getValue();
      }
      
      public function getIsPile() : Boolean
      {
         return this._isPile;
      }
      
      public function getPileLimit() : Number
      {
         return this._pileLimit.getValue();
      }
      
      public function getProbability() : Number
      {
         return this._probability.getValue();
      }
      
      public function getAddSkill() : Number
      {
         return this._addSkill.getValue();
      }
      
      private function getRandomType() : uint
      {
         var _loc1_:* = Math.random() * 100;
         if(_loc1_ >= 0 && _loc1_ < 16)
         {
            return 0;
         }
         if(_loc1_ >= 16 && _loc1_ < 33)
         {
            return 1;
         }
         if(_loc1_ >= 33 && _loc1_ < 50)
         {
            return 2;
         }
         if(_loc1_ >= 50 && _loc1_ < 67)
         {
            return 3;
         }
         if(_loc1_ >= 67 && _loc1_ < 84)
         {
            return 4;
         }
         return 5;
      }
      
      public function createGem() : Gem
      {
         var _loc1_:Array = null;
         var _loc2_:Array = null;
         var _loc3_:Attribute = null;
         var _loc4_:Attribute = null;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:Attribute = null;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:Attribute = null;
         var _loc12_:int = 0;
         var _loc13_:Attribute = null;
         if(this._type.getValue() == GemTypeConst.ATTRIBSTONE)
         {
            _loc1_ = [];
            _loc2_ = [];
            for each(_loc3_ in this._addAttrib)
            {
               _loc1_.push(_loc3_.getClone());
            }
            if(this._color.getValue() == 1 && _loc1_.length > 0)
            {
               if(this._isPile == true)
               {
                  _loc4_ = _loc1_[0];
                  _loc2_.push(_loc4_);
               }
               else
               {
                  _loc5_ = int(this.getRandomType());
                  _loc4_ = _loc1_[_loc5_] as Attribute;
                  _loc2_.push(_loc4_);
               }
            }
            if(this._color.getValue() == 2 && _loc1_.length > 0)
            {
               _loc6_ = 2;
               while(_loc6_ > 0)
               {
                  _loc7_ = int(this.getRandomType());
                  _loc8_ = _loc1_[_loc7_] as Attribute;
                  _loc2_.push(_loc8_);
                  _loc6_--;
               }
            }
            if(this._color.getValue() == 3 && _loc1_.length > 0)
            {
               _loc9_ = 3;
               while(_loc9_ > 0)
               {
                  _loc10_ = int(this.getRandomType());
                  _loc11_ = _loc1_[_loc10_] as Attribute;
                  _loc2_.push(_loc11_);
                  _loc9_--;
               }
            }
            if(this._color.getValue() == 4 && _loc1_.length > 0)
            {
               _loc12_ = 4;
               while(_loc12_ > 0)
               {
                  _loc13_ = this.getAttributeXXX(_loc1_);
                  _loc2_.push(_loc13_);
                  _loc12_--;
               }
            }
            return Gem.creatGem(this._id.getValue(),this._times.getValue(),_loc2_);
         }
         _loc2_ = [];
         return Gem.creatGem(this._id.getValue(),this._times.getValue(),_loc2_);
      }
      
      public function getAttributeXXX(param1:Array) : Attribute
      {
         var _loc3_:* = undefined;
         var _loc5_:Attribute = null;
         var _loc2_:Array = [];
         for(_loc3_ in param1)
         {
            _loc5_ = param1[_loc3_];
            if(_loc5_.getValue() != 0)
            {
               _loc2_.push(_loc5_);
            }
         }
         if(_loc2_.length <= 0)
         {
         }
         var _loc4_:int = Math.random() * _loc2_.length;
         return _loc2_[_loc4_];
      }
      
      public function composeGem(param1:Array) : Gem
      {
         return Gem.creatGem(this._id.getValue(),this._times.getValue(),param1);
      }
      
      public function upLevelGem() : Gem
      {
         return Gem.creatGem(this._id.getValue(),this._times.getValue(),[]);
      }
   }
}

