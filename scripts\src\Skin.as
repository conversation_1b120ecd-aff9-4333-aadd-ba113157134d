package src
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.skill.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.utils.*;
   import src.Skin.*;
   import src.other.*;
   import src.tool.*;
   
   public class Skin extends MovieClip
   {
      private static var FindFrameObj:Object;
      
      public static var XmlLoaded:Boolean = false;
      
      public static var PlayerXml:Array = new Array();
      
      public static var xishouWuDi:Boolean = false;
      
      public static var xishouHP:int = 0;
      
      private var myTimeTemp:int = 0;
      
      private var isDef:Boolean = true;
      
      private var fyValue:int;
      
      public var playX:MovieClip;
      
      public var skinNum:int;
      
      public var Xml:XML;
      
      public var frame:int = 1;
      
      public var runOver:Boolean;
      
      public var continuous:Boolean;
      
      public var stopRun:Boolean;
      
      public var gravity:int;
      
      public var moveYN:Boolean;
      
      public var 硬直:int;
      
      public var 被攻击硬直:int;
      
      public var 魔法消耗:int;
      
      public var 能量消耗:int;
      
      public var 技能冷却:int;
      
      public var 持续时间:int;
      
      public var 作用类型:int;
      
      public var 数值1:Number;
      
      public var 数值2:Number;
      
      public var 数值3:Number;
      
      public var 数值4:Number;
      
      public var 数值5:Number;
      
      public var hpX:Number;
      
      public var hpMax:Number;
      
      public var hpXX:VT = VT.createVT();
      
      public var runX:int;
      
      public var runY:int;
      
      public var runTime:int;
      
      public var timeNum:int = 0;
      
      public var continuousTime:int;
      
      public var chiXu:int;
      
      public var yingZhi:int;
      
      public var run_X:int;
      
      public var run_Y:int;
      
      public var attTimes:int;
      
      public var type:int = 0;
      
      public var space:int = 0;
      
      public var totalTime:int = 0;
      
      public var numValue:int = 0;
      
      public var showYN:Boolean = false;
      
      public var runType:String = "站立";
      
      public var runArr:Array = [];
      
      public var moveArr:Array = [];
      
      public var skillArr:Array = ["攻击1","攻击2","攻击3","攻击4","跑攻","上挑","下斩","技能1","技能2","技能3","技能4","转职技能1","转职技能2","转职技能3","转职技能4"];
      
      public var skill_Id_Arr:Array = [["a1","a2","a3","a4","a5","a6","a7","a8","a9","a10","a11","a12","a13","a14","a15"],["b1","b2","b3","b4","b5","b6","b7","b8","b9","b10","b11","b12","b13","b14","b15"],["c1","c2","c3","c4","c5","c6","c7","c8","c9","c10","c11","c12","c13","c14","c15"],["k1","k2","k3","k4","k5","k6","k7","k8","k9","k10","k11","k12","k13","k14","k15"]];
      
      private var runNum:int = 1;
      
      internal var skill_ID:int;
      
      internal var skill_Value:int;
      
      internal var nodead_C:Class = NewLoad.XiaoGuoData.getClass("undeath") as Class;
      
      internal var nodead:MovieClip = new this.nodead_C();
      
      internal var time_Temp_2:int = 0;
      
      internal var hp_5s:int = 0;
      
      internal var time_SGHL:int = 0;
      
      public var time_mc:MovieClip;
      
      internal var kk:HitXX = null;
      
      internal var time_Temp:int = 0;
      
      internal var fanBool:Boolean = false;
      
      internal var beiShu:Number;
      
      public function Skin()
      {
         super();
         mouseChildren = false;
         mouseEnabled = false;
         this.GoTo("站");
         addEventListener(Event.REMOVED_FROM_STAGE,this.Over);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function Stop() : *
      {
         gotoAndStop("站");
         removeEventListener(Event.REMOVED_FROM_STAGE,this.Over);
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function Over(param1:* = null) : *
      {
         gotoAndStop(1);
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function onENTER_FRAME(param1:*) : *
      {
         if(Boolean(this.playX) && !this.playX is Player2)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
         if(this.showYN)
         {
            this.Over();
            return;
         }
         if(this.timeNum > 0)
         {
            --this.timeNum;
         }
         if(this.continuousTime > 0)
         {
            --this.continuousTime;
         }
         else if(this.runType == "被打")
         {
            this.stopRun = true;
            this.GoTo("站");
         }
         this.GoToPlay();
         this.GoToPlay2();
      }
      
      public function GoToPlay2() : *
      {
         if(this.playX.skin_Z)
         {
            this.playX.skin_Z.gotoAndStop(this.currentFrame);
         }
         if(this.playX.skin_W)
         {
            this.playX.skin_W.gotoAndStop(this.currentFrame);
         }
         if(!(this.runType == "跳" || this.skinNum != 3 && this.runType == "站"))
         {
            if(this.playX.skin_Z2)
            {
               this.playX.skin_Z2.gotoAndStop(this.currentFrame);
            }
            if(this.playX.skin_Z3)
            {
               this.playX.skin_Z3.gotoAndStop(this.currentFrame);
            }
         }
      }
      
      private function GoToPlay() : *
      {
         this.isRunOver();
         if(this.runOver)
         {
            if(this.continuous || this.continuousTime > 0)
            {
               gotoAndPlay(this.runType);
               if(this.playX.skin_Z3)
               {
                  if(this.runType == "跳" || this.skinNum != 3 && this.runType == "站")
                  {
                     this.playX.skin_Z3.gotoAndStop(this.runType);
                  }
                  else
                  {
                     this.playX.skin_Z3.gotoAndPlay(this.runType);
                  }
               }
               if(this.playX.skin_Z2)
               {
                  if(this.runType == "跳" || this.skinNum != 3 && this.runType == "站")
                  {
                     this.playX.skin_Z2.gotoAndStop(this.runType);
                  }
                  else
                  {
                     this.playX.skin_Z2.gotoAndPlay(this.runType);
                  }
               }
               this.runOver = false;
               this.frame = 1;
            }
            else
            {
               this.GoTo("站");
            }
         }
         this.FindFrame();
         ++this.frame;
         if(this.playX.data.skinArr[this.playX.data.skinNum] == 2 && this.currentFrame == 295)
         {
            (this.playX as Player).noYingZhiTime = 216;
            TiaoShi.txtShow("拳手神拳傲世技能8秒无硬直buff");
         }
      }
      
      public function GoTo(param1:String, param2:int = 0, param3:Boolean = false) : *
      {
         var _loc4_:* = undefined;
         var _loc5_:* = undefined;
         var _loc6_:Player = null;
         var _loc7_:* = undefined;
         var _loc8_:int = 0;
         if(param3 == true)
         {
            this.stopRun = true;
         }
         if(param1 == "怪物技能")
         {
            this.enemySkill();
         }
         if(param1 == "被打")
         {
            this.runType = param1;
            this.runOver = false;
            this.stopRun = false;
            gotoAndPlay(this.runType);
         }
         if((this.runOver || this.stopRun) && this.runType != param1)
         {
            param1 = this.OtherGoTo(param1);
            if(this.CanGoTo(param1))
            {
               this.runType = param1;
               this.otherGoTo(this.runType);
               if(this.runType == "技能1" || this.runType == "技能2" || this.runType == "技能3" || this.runType == "技能4" || this.runType == "转职技能1" || this.runType == "转职技能2" || this.runType == "转职技能3" || this.runType == "转职技能4")
               {
                  if(this.playX is Player)
                  {
                     _loc6_ = this.playX;
                     Skill_All.Add_Skill_All(_loc6_);
                     Skill_lei.Add_Skill_lei(_loc6_);
                     HuanHuaBuff.Add_HuanHuaBuff(_loc6_);
                     if(_loc6_.guangQiu)
                     {
                        _loc6_.guangQiu.GongJiType();
                     }
                     Skill_guangDun.addToPlayer(_loc6_);
                     _loc6_.huiFuYinZangXX();
                     _loc6_.fly4004_3_YN = true;
                  }
               }
               if(this.runType == "转职技能1" && this.playX is Player)
               {
                  _loc6_ = this.playX;
                  if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 3)
                  {
                     _loc6_.RZJN();
                  }
               }
               _loc4_ = param1.substr(0,2);
               _loc5_ = param1.substr(2,1);
               if(_loc4_ == "技能" && (_loc5_ >= 1 && _loc5_ <= 4))
               {
                  _loc6_.HeiAnJiNengOk(_loc5_);
               }
               this.runOver = false;
               this.frame = 1;
               gotoAndPlay(this.runType);
               if(this.playX.skin_Z3)
               {
                  if(this.runType == "跳" || this.skinNum != 3 && this.runType == "站")
                  {
                     this.playX.skin_Z3.gotoAndStop(this.runType);
                  }
                  else
                  {
                     this.playX.skin_Z3.gotoAndPlay(this.runType);
                  }
               }
               if(this.playX.skin_Z2)
               {
                  if(this.runType == "跳" || this.skinNum != 3 && this.runType == "站")
                  {
                     this.playX.skin_Z2.gotoAndStop(this.runType);
                  }
                  else
                  {
                     this.playX.skin_Z2.gotoAndPlay(this.runType);
                  }
               }
               this.FindFrame();
               MusicBox.ActMusicPlay(this.playX.data.skinArr[this.playX.data.skinNum],this.runType);
            }
            else
            {
               _loc7_ = param1.substr(0,2);
               if(_loc7_ == "技能")
               {
                  _loc8_ = int(param1.substr(2,1));
                  if(_loc8_ >= 1 && _loc8_ <= 4)
                  {
                     (this.parent as Player).HeiAnJiNeng(_loc8_);
                  }
               }
            }
         }
         if(param2 >= 0)
         {
            this.continuousTime = param2;
         }
      }
      
      public function CanGoTo(param1:String) : Boolean
      {
         var _loc2_:Array = this.get_Skill_Id_and_LV(param1);
         if(_loc2_[1] == 0)
         {
            return false;
         }
         if(Boolean(this.parent) && Boolean(this.parent.CanSkill(_loc2_[0],_loc2_[1])))
         {
            if(this.playX is Player)
            {
               JingLing.ACT_Stop(this.playX,param1);
            }
            return true;
         }
         return false;
      }
      
      public function Get_StopRun() : Boolean
      {
         return this.stopRun;
      }
      
      private function FindFrame() : *
      {
         var _loc1_:* = undefined;
         var _loc2_:String = null;
         var _loc3_:int = 0;
         var _loc4_:Number = NaN;
         this.moveArr = null;
         this.hpXX.setValue(this.NowHPxx());
         for(_loc1_ in this.Xml.技能)
         {
            _loc2_ = String(this.Xml.技能[_loc1_].帧标签);
            _loc3_ = int(this.Xml.技能[_loc1_].当前帧);
            if(this.runType == _loc2_ && this.frame == _loc3_)
            {
               this.continuous = String(this.Xml.技能[_loc1_].循环).toString() == "true" || String(this.Xml.技能[_loc1_].循环).toString() == "TRUE" ? true : false;
               this.stopRun = String(this.Xml.技能[_loc1_].中断).toString() == "true" || String(this.Xml.技能[_loc1_].中断).toString() == "TRUE" ? true : false;
               this.moveYN = String(this.Xml.技能[_loc1_].移动).toString() == "true" || String(this.Xml.技能[_loc1_].移动).toString() == "TRUE" ? true : false;
               this.gravity = int(this.Xml.技能[_loc1_].重力调整);
               this.moveArr = [int(this.Xml.技能[_loc1_].移动参数.X),int(this.Xml.技能[_loc1_].移动参数.Y),int(this.Xml.技能[_loc1_].移动参数.持续)];
               _loc4_ = Number(this.NowHPxx());
               this.hpX = Number(this.Xml.技能[_loc1_].伤害.hp) * _loc4_;
               this.hpXX.setValue(this.hpX);
               this.被攻击硬直 = int(this.Xml.技能[_loc1_].被攻击硬直);
               this.硬直 = int(this.Xml.技能[_loc1_].硬直) + this.yingZhi;
               this.runX = int(this.Xml.技能[_loc1_].伤害.震退) + this.run_X;
               this.runY = int(this.Xml.技能[_loc1_].伤害.挑高) + this.run_Y;
               this.runTime = int(this.Xml.技能[_loc1_].伤害.持续) + this.chiXu;
               return;
            }
         }
      }
      
      public function get_Skill_Id_and_LV(param1:String = "") : Array
      {
         var _loc2_:* = undefined;
         var _loc3_:String = null;
         var _loc4_:int = 0;
         if(param1 == "")
         {
            param1 = this.runType;
         }
         for(_loc2_ in this.skillArr)
         {
            if(param1 == this.skillArr[_loc2_])
            {
               _loc3_ = this.skill_Id_Arr[this.skinNum][_loc2_];
               _loc4_ = int(this.parent.data.getSkillLevel(_loc3_));
               return [_loc3_,_loc4_];
            }
         }
         return [-1,-1];
      }
      
      private function NowHPxx() : Number
      {
         var _loc2_:* = undefined;
         var _loc3_:Number = NaN;
         var _loc4_:int = 0;
         var _loc1_:Array = this.get_Skill_Id_and_LV();
         if(_loc1_ == null || _loc1_[1] <= 0)
         {
            return 1;
         }
         if(Boolean(SkillFactory.skillAllDataArr2[_loc1_[0]]) && Boolean(SkillFactory.skillAllDataArr2[_loc1_[0]][_loc1_[1]]))
         {
            _loc2_ = (SkillFactory.skillAllDataArr2[_loc1_[0]][_loc1_[1]] as Skill).getSkillValueArray();
            _loc3_ = Number(_loc2_[0].getValue());
            this.attTimes = _loc2_[4].getValue();
            this.yingZhi = _loc2_[5].getValue();
            this.run_Y = _loc2_[6].getValue();
            this.run_X = _loc2_[7].getValue();
            this.chiXu = _loc2_[8].getValue();
            _loc4_ = int(Main.gameNum.getValue());
            if(_loc4_ >= 1 && _loc4_ <= 9)
            {
               this.hpMax = _loc2_[9].getValue();
            }
            else if(_loc4_ >= 10 && _loc4_ <= 16)
            {
               this.hpMax = _loc2_[10].getValue();
            }
            else if(_loc4_ >= 51 && _loc4_ <= 62)
            {
               this.hpMax = _loc2_[11].getValue();
            }
            else if(_loc4_ == 999)
            {
               this.hpMax = _loc2_[12].getValue();
            }
            else
            {
               this.hpMax = _loc2_[9].getValue();
            }
            return _loc3_;
         }
         return 0;
      }
      
      private function isRunOver() : *
      {
         if(this.currentLabel != this.runType)
         {
            this.runOver = true;
         }
         else
         {
            this.runOver = false;
         }
      }
      
      public function OtherGoTo(param1:String) : String
      {
         var _loc2_:String = null;
         if(this.timeNum <= 0)
         {
            this.runNum = 1;
         }
         if(this.timeNum > 0 && param1 == "攻击")
         {
            _loc2_ = param1 + this.runNum;
            if(this.runNum == 4)
            {
               this.runNum = 1;
            }
            else
            {
               ++this.runNum;
            }
            return _loc2_;
         }
         return param1;
      }
      
      public function otherGoTo(param1:String) : *
      {
      }
      
      private function enemySkill() : *
      {
         var _loc1_:String = null;
         var _loc6_:Class = null;
         var _loc7_:Number = NaN;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:Number = NaN;
         var _loc11_:int = 0;
         var _loc12_:int = 0;
         var _loc13_:Class = null;
         var _loc14_:Class = null;
         if(this.parent.energySlot.getEnergyPer(this.parent) != 100)
         {
            return;
         }
         if(this.parent.data.skinNum == 0)
         {
            this.parent.energySlot.energyLeftNum.setValue(0);
            if(this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(0) != null)
            {
               this.skill_ID = this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(0).getGemSkill();
               this.skill_Value = this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(0).getClassName();
            }
         }
         else
         {
            this.parent.energySlot.energyRightNum.setValue(0);
            if(this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(1) != null)
            {
               this.skill_ID = this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(1).getGemSkill();
               this.skill_Value = this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(1).getClassName();
            }
         }
         var _loc2_:Skill = SkillFactory.getSkillById(this.skill_ID);
         _loc1_ = _loc2_.getSkillName();
         var _loc3_:Array = _loc2_.getSkillValueArray();
         var _loc4_:Number = _loc2_.getSkillActOn();
         var _loc5_:int = int(Main.gameNum.getValue());
         TiaoShi.txtShow("技能石:" + _loc1_ + "," + this.skill_ID + "," + this.skill_Value + ",类型:" + _loc4_);
         if(_loc1_ == "眼镜蛇冲击")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("眼镜蛇冲击") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "双头狗冲击")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("双头狗冲击") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "双头狗冲击波")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("双头狗冲击波") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "蝎王地刺")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("蝎王地刺") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "雪女落冰")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[6].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[6].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("雪女落冰") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "金太郎斧劈")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("金太郎斧劈") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "生命复苏")
         {
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("生命复苏") as Class;
            this.parent.skin_W.addChild(new _loc6_());
            _loc7_ = Number(_loc3_[0].getValue());
            _loc8_ = int((this.parent as Player).use_hp_Max.getValue());
            _loc9_ = int(_loc8_ * _loc7_);
            (this.parent as Player).HpUp(_loc9_);
         }
         else if(_loc1_ == "法力再生")
         {
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("法力再生") as Class;
            this.parent.skin_W.addChild(new _loc6_());
            _loc10_ = Number(_loc3_[0].getValue());
            _loc11_ = int((this.parent as Player).use_mp_Max.getValue());
            _loc12_ = int(_loc11_ * _loc10_);
            (this.parent as Player).MpUp(_loc12_);
         }
         else if(_loc1_ == "熔岩魔天火")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("熔岩魔天火") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "死灵爆裂")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("死灵爆裂") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "鳄鱼旋风斩")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("鳄鱼旋风斩") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "狮王裂地斩")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("狮王裂地斩") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "杀戮狂魔波")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("狂魔波技能") as Class;
            _loc13_ = NewLoad.enemySkill[this.skill_Value].getClass("杀戮狂魔波") as Class;
            this.parent.skin_W.addChild(new _loc6_());
            this.parent.skin_W.addChild(new _loc13_());
         }
         else if(_loc1_ == "白狼滚石")
         {
            setTimeout(this.gunshi,1000);
            setTimeout(this.gunshi,1300);
            _loc13_ = NewLoad.enemySkill[this.skill_Value].getClass("白狼滚石") as Class;
            this.parent.skin_W.addChild(new _loc13_());
         }
         else if(_loc1_ == "白狼怒吼")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("白狼怒吼") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "暗黑风暴")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("暗黑风暴") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "真·暗黑风暴")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("暗黑风暴") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "黑洞")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("黑洞") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "魔轮转")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("魔轮转") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "真·魔轮转")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("魔轮转") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "缩小术")
         {
            this.type = _loc4_;
            this.hpX = _loc3_[0].getValue();
            this.numValue = _loc3_[1].getValue();
            this.space = _loc3_[2].getValue();
            this.totalTime = _loc3_[3].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("缩小术") as Class;
            _loc13_ = NewLoad.enemySkill[this.skill_Value].getClass("缩小吸引") as Class;
            this.parent.skin_W.addChild(new _loc6_());
            this.parent.skin_W.addChild(new _loc13_());
         }
         else if(_loc1_ == "雪女风雪")
         {
            this.type = _loc4_;
            this.hpX = _loc3_[0].getValue();
            this.numValue = _loc3_[1].getValue();
            this.space = _loc3_[2].getValue();
            this.totalTime = _loc3_[3].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            this.hpMax = _loc3_[9].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("雪女风雪") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "机械反弹")
         {
            this.beiShu = _loc3_[0].getValue();
            TweenMax.to(this,3,{"dropShadowFilter":{
               "color":16711680,
               "alpha":1,
               "blurX":18,
               "blurY":18
            }});
            this.addEventListener(Event.ENTER_FRAME,this.fanshang);
         }
         else if(_loc1_ == "无敌罩")
         {
            this.hpX = _loc3_[0].getValue();
            this.attTimes = _loc3_[4].getValue();
            this.硬直 = _loc3_[5].getValue();
            this.runY = _loc3_[6].getValue();
            this.runX = _loc3_[7].getValue();
            this.runTime = _loc3_[8].getValue();
            if(_loc5_ >= 1 && _loc5_ <= 9)
            {
               this.hpMax = _loc3_[9].getValue();
            }
            else if(_loc5_ >= 10 && _loc5_ <= 16)
            {
               this.hpMax = _loc3_[10].getValue();
            }
            else if(_loc5_ >= 51 && _loc5_ <= 62)
            {
               this.hpMax = _loc3_[11].getValue();
            }
            else
            {
               this.hpMax = _loc3_[9].getValue();
            }
            _loc6_ = NewLoad.enemySkill[this.skill_Value].getClass("无敌罩") as Class;
            this.parent.skin_W.addChild(new _loc6_());
         }
         else if(_loc1_ == "吸收护罩")
         {
            xishouHP = (this.parent as Player).use_hp_Max.getValue() * _loc3_[0].getValue();
            xishouWuDi = true;
            this.parent.addChild(this.nodead);
            this.time_Temp_2 = 0;
            this.addEventListener(Event.ENTER_FRAME,this.xishou);
            this.addEventListener(Event.REMOVED_FROM_STAGE,this.shanchu);
         }
         else if(_loc1_ == "时光回流")
         {
            _loc14_ = NewLoad.XiaoGuoData.getClass("时光回流") as Class;
            this.time_mc = new _loc14_();
            this.hp_5s = (this.parent as Player).hp.getValue();
            Main.world.moveChild_Other.addChild(this.time_mc);
            this.addEventListener(Event.ENTER_FRAME,this.shiguanghuiliu);
            this.addEventListener(Event.REMOVED_FROM_STAGE,this.shanchu2);
         }
      }
      
      public function shanchu(param1:*) : *
      {
         if(this.parent.contains(this.nodead))
         {
            this.parent.removeChild(this.nodead);
         }
         xishouWuDi = false;
         EnergySlot.energyBool = true;
         this.removeEventListener(Event.ENTER_FRAME,this.xishou);
      }
      
      public function xishou(param1:*) : *
      {
         EnergySlot.energyBool = false;
         ++this.time_Temp_2;
         if(xishouWuDi == false)
         {
            if(this.parent.contains(this.nodead))
            {
               this.parent.removeChild(this.nodead);
            }
            EnergySlot.energyBool = true;
            this.time_Temp_2 = 0;
            this.removeEventListener(Event.ENTER_FRAME,this.xishou);
            return;
         }
         if(this.time_Temp_2 > 216)
         {
            xishouWuDi = false;
            this.time_Temp_2 = 0;
            EnergySlot.energyBool = true;
            if(this.parent.contains(this.nodead))
            {
               this.parent.removeChild(this.nodead);
            }
            this.removeEventListener(Event.ENTER_FRAME,this.xishou);
            return;
         }
      }
      
      public function shanchu2(param1:*) : *
      {
         if(Main.world.moveChild_Other.contains(this.time_mc))
         {
            Main.world.moveChild_Other.removeChild(this.time_mc);
         }
         EnergySlot.energyBool = true;
         this.removeEventListener(Event.ENTER_FRAME,this.shanchu2);
      }
      
      private function shiguanghuiliu(param1:*) : *
      {
         var _loc2_:int = 0;
         ++this.time_SGHL;
         if(this.parent)
         {
            EnergySlot.energyBool = false;
            this.time_mc.x = (this.parent as Player).x;
            this.time_mc.y = (this.parent as Player).y;
            if((this.parent as Player).hp.getValue() <= 0)
            {
               if(Main.world.moveChild_Other.contains(this.time_mc))
               {
                  Main.world.moveChild_Other.removeChild(this.time_mc);
               }
               EnergySlot.energyBool = true;
               this.removeEventListener(Event.ENTER_FRAME,this.shiguanghuiliu);
            }
            if(this.time_SGHL > 147)
            {
               if((this.parent as Player).hp.getValue() > 0)
               {
                  _loc2_ = this.hp_5s - (this.parent as Player).hp.getValue();
                  (this.parent as Player).hp.setValue(this.hp_5s);
                  if(_loc2_ > 0)
                  {
                     NewMC.Open("回血效果",this.parent,0,0,0,_loc2_);
                  }
                  if(Main.world.moveChild_Other.contains(this.time_mc))
                  {
                     Main.world.moveChild_Other.removeChild(this.time_mc);
                  }
               }
               EnergySlot.energyBool = true;
               this.removeEventListener(Event.ENTER_FRAME,this.shiguanghuiliu);
               this.time_SGHL = 0;
            }
         }
         else
         {
            if(Main.world.moveChild_Other.contains(this.time_mc))
            {
               Main.world.moveChild_Other.removeChild(this.time_mc);
            }
            EnergySlot.energyBool = true;
            this.removeEventListener(Event.ENTER_FRAME,this.shiguanghuiliu);
            this.time_SGHL = 0;
         }
      }
      
      internal function fanshang(param1:*) : *
      {
         var _loc2_:* = undefined;
         this.fanBool = true;
         ++this.time_Temp;
         if(this.time_Temp > 400)
         {
            this.time_Temp = 0;
            this.fanBool = false;
            TweenMax.to(this,1,{"dropShadowFilter":{
               "color":16711680,
               "alpha":0
            }});
            this.removeEventListener(Event.ENTER_FRAME,this.fanshang);
         }
         if(this.fanBool == true)
         {
            for(_loc2_ in HitXX.AllHitXX)
            {
               if(Boolean(HitXX.AllHitXX[_loc2_]) && this.parent.hitXX == HitXX.AllHitXX[_loc2_])
               {
                  this.parent.hitXX.runArr = [0,0,0];
                  this.parent.hitXX.硬直 = 0;
                  this.parent.hitXX.times = 200;
                  this.parent.hitXX.gongJi_hp *= this.beiShu;
                  if(this.parent.hitXX != this.kk)
                  {
                     this.kk = this.parent.hitXX;
                     if((this.parent.hitXX.who as Enemy).life.getValue() > 0)
                     {
                        (this.parent.hitXX.who as Enemy).HpXX(this.parent.hitXX);
                     }
                  }
               }
            }
         }
      }
      
      public function gunshi() : *
      {
         var _loc1_:int = int(Main.gameNum.getValue());
         var _loc2_:Skill = SkillFactory.getSkillById(this.skill_ID);
         esName = _loc2_.getSkillName();
         var _loc3_:Array = _loc2_.getSkillValueArray();
         var _loc4_:Number = _loc2_.getSkillActOn();
         this.hpX = _loc3_[0].getValue();
         this.attTimes = _loc3_[4].getValue();
         this.硬直 = _loc3_[5].getValue();
         this.runY = _loc3_[6].getValue();
         this.runX = _loc3_[7].getValue();
         this.runTime = _loc3_[8].getValue();
         if(_loc1_ >= 1 && _loc1_ <= 9)
         {
            this.hpMax = _loc3_[6].getValue();
         }
         else if(_loc1_ >= 10 && _loc1_ <= 16)
         {
            this.hpMax = _loc3_[10].getValue();
         }
         else if(_loc1_ >= 51 && _loc1_ <= 62)
         {
            this.hpMax = _loc3_[11].getValue();
         }
         else
         {
            this.hpMax = _loc3_[12].getValue();
         }
         var _loc5_:Class = NewLoad.enemySkill[this.skill_Value].getClass("滚石") as Class;
         this.parent.skin_W.addChild(new _loc5_());
      }
      
      public function 震动(param1:int = 4) : *
      {
         if(Main.world)
         {
            Main.world.Quake(param1);
         }
      }
   }
}

