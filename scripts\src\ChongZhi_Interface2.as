package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class ChongZhi_Interface2 extends MovieClip
   {
      public static var _this:ChongZhi_Interface2;
      
      public static var openYn:Boolean = false;
      
      public static var buyOk:Boolean = false;
      
      public static var openNum:int = 0;
      
      public static var selYN:Boolean = false;
      
      public static var shopArr:Array = [[],["活动日期: 至2023年2月12日",599,287,14655,14661],["活动日期: 至2023年7月31日",599,287,14775,14781],["活动日期: 至2023年8月13日",599,287,100779,100785],["活动日期: 至2023年2月5日",499,265,14757,14763],["活动日期: 至2023年2月5日",799,288,100791,100797],["活动日期: 至2023年7月16日",599,289,100803,100809]];
      
      public var skin:MovieClip;
      
      public function ChongZhi_Interface2()
      {
         super();
         _this = this;
         var _loc1_:Class = NewLoad.chongZhiData.getClass("mc2") as Class;
         this.skin = new _loc1_();
         _this.XX_mc.addChild(this.skin);
         _this.XX_mc.x = 136;
         _this.XX_mc.y = 47;
         close_btn.addEventListener(MouseEvent.CLICK,Close);
         lingqu_btn.addEventListener(MouseEvent.CLICK,lingQuFun);
      }
      
      public static function Open(param1:int = 0) : *
      {
         openNum = param1;
         if(!_this)
         {
            _this = new ChongZhi_Interface2();
         }
         _this.addEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         _this.y = 0;
         _this.x = 0;
         _this.skin.gotoAndStop(openNum);
         _this.skin.time_txt.text = shopArr[openNum][0];
         Main._this.addChild(_this);
         Show();
      }
      
      public static function onENTER_FRAME(param1:*) : *
      {
         if(!openYn && Main.gameNum.getValue() == 0)
         {
            openYn = true;
            _this.y = 0;
            _this.x = 0;
         }
      }
      
      public static function Close(param1:* = null) : *
      {
         var _loc2_:ChongZhi_Interface2 = null;
         if(!_this)
         {
            _loc2_ = new ChongZhi_Interface2();
            Main._this.addChild(_loc2_);
         }
         _this.removeEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         _this.y = -5000;
         _this.x = -5000;
      }
      
      public static function lingQuFun(param1:* = null) : *
      {
         if(Main.player1.getBag().backequipBagNum() >= 2)
         {
            TiaoShi.txtShow("时装购买:" + openNum + ">>>>" + shopArr[openNum]);
            if(Shop4399.moneyAll.getValue() >= shopArr[openNum][1])
            {
               Api_4399_All.BuyObj(shopArr[openNum][2]);
               buyOk = true;
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
               buyOk = false;
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,请整理背包!");
         }
         Show();
      }
      
      public static function Buy_OK() : *
      {
         if(buyOk)
         {
            Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(shopArr[openNum][3]));
            Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(shopArr[openNum][4]));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功!");
            buyOk = false;
            Show();
         }
      }
      
      public static function Show() : *
      {
         _this.lingqu_btn.visible = false;
         _this.lingqu_btn.y = -5000;
         _this.lingqu_btn.x = -5000;
         if(!buyOk)
         {
            _this.lingqu_btn.visible = true;
            _this.lingqu_btn.x = 267;
            _this.lingqu_btn.y = 340;
         }
      }
   }
}

