package src
{
   import com.hotpoint.braveManIII.models.elves.Elves;
   import com.hotpoint.braveManIII.repository.elves.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.other.*;
   
   public class JingLing extends MovieClip
   {
      public var who:Player;
      
      public var data:Elves;
      
      public var RL:Boolean = true;
      
      public var skin:MovieClip;
      
      private var distanceX_Max:int = 900;
      
      private var distanceY_Max:int = 500;
      
      private var distanceX2:int = 100;
      
      private var distanceY2:int = 30;
      
      public var jiNengZD_ID:int = 1;
      
      public var jiNengBD_ID:int = 0;
      
      private var jiNengZD_Data:Array = [];
      
      private var jiNengBD_Data:Array = [];
      
      public var jiNengZD_num:int = 0;
      
      public var jiNengZD_Time:int = 0;
      
      public var runType:String = "移动";
      
      public var lianJiNumArr:Array = [0,0,0,"",0];
      
      private var timeX:int = 0;
      
      private var timeY:int = 0;
      
      private var MoveX:int = 2;
      
      private var MoveY:int = 2;
      
      private var BD1Time:int = 0;
      
      private var ZD1time:int;
      
      public var ZD1num:int = 0;
      
      private var ZD2Time:int = 0;
      
      private var ZD1012_Time:int = 0;
      
      private var ZD1014_Time:int = 0;
      
      private var ZD1016_Time:int = 0;
      
      private var ZD1018_Time:int = 0;
      
      private var shuiMC:MovieClip;
      
      public function JingLing(param1:Elves, param2:Player)
      {
         super();
         this.who = param2;
         this.who.data.playerJL_Data = this.data = param1;
         this.who.playerJL = this;
         this.addSkin();
         mouseChildren = mouseEnabled = false;
         this.getID();
      }
      
      public static function lianJiNumXX1010(param1:Player) : *
      {
         var _loc2_:JingLing = null;
         if(param1.playerJL)
         {
            _loc2_ = param1.playerJL;
            if(_loc2_.jiNengBD_ID == 1010)
            {
               _loc2_.lianJiNumArr[1] = param1.lianJi.getValue();
            }
         }
      }
      
      public static function ACT_Stop(param1:Player, param2:String) : *
      {
         var _loc3_:JingLing = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         if(param1.playerJL)
         {
            _loc3_ = param1.playerJL;
            if(_loc3_.jiNengBD_ID == 1010)
            {
               _loc4_ = int(_loc3_.jiNengBD_Data[0].getValue());
               _loc5_ = int(_loc3_.jiNengBD_Data[1].getValue());
               if(_loc3_.lianJiNumArr[0] == 0)
               {
                  if(_loc3_.lianJiNumArr[1] < _loc4_)
                  {
                     _loc3_.lianJiNumArr[2] = 0;
                  }
                  if(_loc3_.lianJiNumArr[1] - _loc3_.lianJiNumArr[2] >= _loc4_)
                  {
                     _loc3_.lianJiNumArr[0] = 1;
                  }
               }
               if(_loc3_.lianJiNumArr[0] == 1)
               {
                  if(param2.substr(0,2) == "攻击")
                  {
                     _loc3_.lianJiNumArr[3] = param2;
                     _loc3_.lianJiNumArr[2] = _loc3_.lianJiNumArr[1];
                     _loc3_.lianJiNumArr[0] = 2;
                     param1.hpXX2num = param1.use_gongji.getValue() * _loc5_;
                  }
               }
               if(_loc3_.lianJiNumArr[0] == 2)
               {
                  if(_loc3_.lianJiNumArr[3] != param2)
                  {
                     _loc3_.lianJiNumArr[3] = "";
                     _loc3_.lianJiNumArr[0] = 0;
                     param1.hpXX2num = 0;
                  }
               }
            }
            else
            {
               _loc3_.lianJiNumArr = [0,0,0,"",0];
               param1.hpXX2num = 0;
            }
         }
      }
      
      public static function QingChuLengQue() : *
      {
         if(Boolean(Main.player_1) && Boolean(Main.player_1.playerJL))
         {
            Main.player_1.playerJL.jiNengZD_num = 0;
         }
         if(Main.P1P2 && Main.player_2 && Boolean(Main.player_2.playerJL))
         {
            Main.player_2.playerJL.jiNengZD_num = 0;
         }
      }
      
      public function getID() : *
      {
         if(this.data.getSID3() > 0)
         {
            this.jiNengZD_ID = SkillFactory.getSkillById(this.data.getSKILL3()).getSkillActOn();
            this.jiNengZD_Data = SkillFactory.getSkillById(this.data.getSKILL3()).getSkillValueArray();
         }
         else
         {
            this.jiNengZD_ID = 0;
         }
         if(this.data.getSID2() > 0)
         {
            this.jiNengBD_ID = SkillFactory.getSkillById(this.data.getSKILL2()).getSkillActOn();
            this.jiNengBD_Data = SkillFactory.getSkillById(this.data.getSKILL2()).getSkillValueArray();
         }
         else
         {
            this.jiNengBD_ID = 0;
         }
      }
      
      public function InterfaceShow() : *
      {
         if(this.who == Main.player_1)
         {
            Play_Interface.interfaceX.jlNum_mc1.gotoAndStop("a" + this.jiNengZD_ID);
            Play_Interface.interfaceX.jlNum_txt1.text = this.jiNengZD_num + "%";
            if(this.jiNengZD_num >= 100)
            {
               Play_Interface.interfaceX.jlNum_mc_X1.gotoAndStop(2);
            }
            else
            {
               Play_Interface.interfaceX.jlNum_mc_X1.gotoAndStop(1);
            }
         }
         else if(Boolean(Main.P1P2) && this.who == Main.player_2)
         {
            Play_Interface.interfaceX.jlNum_mc2.gotoAndStop("a" + this.jiNengZD_ID);
            Play_Interface.interfaceX.jlNum_txt2.text = this.jiNengZD_num + "%";
            if(this.jiNengZD_num >= 100)
            {
               Play_Interface.interfaceX.jlNum_mc_X2.gotoAndStop(2);
            }
            else
            {
               Play_Interface.interfaceX.jlNum_mc_X2.gotoAndStop(1);
            }
         }
      }
      
      public function JingLingOPEN() : *
      {
         if(Boolean(this.who) && this.who.visible)
         {
            Main.world.moveChild_ChongWu.addChild(this);
            this.x = this.who.x + 110;
            this.y = this.who.y - 60;
            addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
      
      public function Close() : *
      {
         this.who.hpXX2num = 0;
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.parent.removeChild(this);
         this.who.playerJL = null;
         this.who.data.playerJL_Data = null;
      }
      
      public function addSkin() : *
      {
         var _loc1_:String = ElvesFactory.getClassName(this.data.getId());
         var _loc2_:Class = ChongWu.loadData.getClass(_loc1_) as Class;
         this.skin = new _loc2_();
         addChild(this.skin);
         if(this.who)
         {
            Main.world.moveChild_ChongWu.addChild(this);
            this.x = this.who.x + 110;
            this.y = this.who.y - 60;
            addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
      
      private function onENTER_FRAME(param1:*) : *
      {
         this.ShowPlay();
         this.Move();
         this.BD();
         --this.jiNengZD_Time;
         this.InterfaceShow();
      }
      
      public function JNnumUP(param1:int, param2:int, param3:MovieClip) : *
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:String = null;
         var _loc7_:GDpic = null;
         if(this.jiNengZD_ID != 0 && this.jiNengZD_Time <= 0)
         {
            _loc4_ = Math.random() * 100;
            if(_loc4_ < 50)
            {
               _loc5_ = Math.random() * 4 + 3;
               this.jiNengZD_num += _loc5_;
               if(this.jiNengZD_num > 100)
               {
                  this.jiNengZD_num = 100;
               }
               _loc6_ = "获得能量:" + _loc5_ + " 当前:" + this.jiNengZD_num;
               _loc7_ = new GDpic(param1,param2,param3,this.who);
            }
            else
            {
               this.jiNengZD_Time = 54;
            }
         }
      }
      
      public function ShowPlay() : *
      {
         if(this.skin)
         {
            if(this.skin.currentLabel != this.runType)
            {
               if(this.runType == "攻击")
               {
                  this.runType = "移动";
               }
               this.skin.gotoAndPlay(this.runType);
            }
         }
      }
      
      private function Move() : *
      {
         var _loc1_:int = this.x - this.who.x;
         var _loc2_:int = this.y - this.who.y + 70;
         if(Math.abs(_loc1_) > this.distanceX_Max || Math.abs(_loc2_) > this.distanceY_Max)
         {
            this.SearchPlayer();
         }
         var _loc3_:int = Math.abs(_loc1_) / 35 + 1;
         if(_loc1_ > this.distanceX2)
         {
            this.MoveX = -_loc3_;
            this.skin.scaleX = 1;
         }
         else if(_loc1_ < -this.distanceX2)
         {
            this.MoveX = _loc3_;
            this.skin.scaleX = -1;
         }
         if(this.MoveX > 0)
         {
            this.skin.scaleX = -1;
         }
         else
         {
            this.skin.scaleX = 1;
         }
         this.x += this.MoveX;
         var _loc4_:int = Math.abs(_loc2_) / 80 + 1;
         if(_loc2_ > this.distanceY2)
         {
            this.MoveY = -_loc4_;
         }
         else if(_loc2_ < -this.distanceX2)
         {
            this.MoveY = _loc4_;
         }
         this.y += this.MoveY;
      }
      
      private function WhoDead() : *
      {
         if(!this.who || this.who.hp.getValue() <= 0)
         {
            this.Close();
         }
      }
      
      private function SearchPlayer() : *
      {
         this.x = this.who.x + Math.random() * 200 - 100;
         this.y = this.who.y - 60;
      }
      
      public function ZD() : *
      {
         if(Boolean(this.jiNengZD_ID) && this.jiNengZD_num >= 100)
         {
            this["ZD" + this.jiNengZD_ID]();
            this.jiNengZD_num = 0;
            this.runType = "攻击";
            this.skin.gotoAndPlay(this.runType);
         }
      }
      
      private function BD() : *
      {
         if(this.jiNengBD_ID == 1007)
         {
            this.BD1007();
         }
      }
      
      public function BD1020(param1:int) : int
      {
         var _loc2_:int = 0;
         if(this.jiNengBD_ID == 1020)
         {
            _loc2_ = Math.random() * 100;
            if(_loc2_ < this.jiNengBD_Data[0].getValue())
            {
               param1 += this.who.use_gongji.getValue() * this.jiNengBD_Data[1].getValue() / 100;
            }
         }
         return param1;
      }
      
      private function BD1007() : *
      {
         var _loc1_:int = 0;
         if(this.who.hp.getValue() < this.who.use_hp_Max.getValue() * this.jiNengBD_Data[0].getValue())
         {
            ++this.BD1Time;
            if(this.BD1Time >= this.jiNengBD_Data[1].getValue())
            {
               this.BD1Time = 0;
               _loc1_ = this.who.hp.getValue() * this.jiNengBD_Data[2].getValue();
               this.who.HpUp(_loc1_);
            }
         }
      }
      
      public function BD1001(param1:int) : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(this.jiNengBD_ID == 1001)
         {
            _loc2_ = Math.random() * 100;
            if(_loc2_ < this.jiNengBD_Data[0].getValue())
            {
               _loc3_ = param1 * this.jiNengBD_Data[1].getValue();
               this.who.HpUp(_loc3_);
            }
         }
      }
      
      public function BD1009(param1:Enemy, param2:int) : *
      {
         var _loc3_:int = 0;
         if(this.jiNengBD_ID == 1009)
         {
            _loc3_ = param2 * this.jiNengBD_Data[0].getValue();
            if(_loc3_ > 50000)
            {
               _loc3_ = 50000;
            }
            param1.HpXX2(_loc3_);
         }
      }
      
      public function BD1010() : *
      {
      }
      
      public function BD1017(param1:int) : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         if(this.jiNengBD_ID == 1017)
         {
            _loc2_ = int(this.jiNengBD_Data[0].getValue());
            _loc3_ = int(this.jiNengBD_Data[1].getValue());
            _loc4_ = Math.random() * 100;
            if(_loc4_ < _loc2_)
            {
               _loc5_ = _loc3_ * 27;
               if(this.who.AllSkillCD[param1][1] < _loc5_)
               {
                  _loc5_ = int(this.who.AllSkillCD[param1][1]);
               }
               this.who.AllSkillCDXX[param1][1] = _loc5_;
            }
         }
      }
      
      public function ZD1002() : *
      {
         this.ZD1time = this.jiNengZD_Data[2].getValue();
         NewMC.Open("萨泽效果",this.who);
         this.ZD1num = 0;
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1002);
      }
      
      private function onENTER_FRAME_ZD1002(param1:*) : *
      {
         var _loc2_:Number = NaN;
         var _loc3_:int = 0;
         if(this.ZD1time > 0 && this.ZD1num >= this.jiNengZD_Data[0].getValue())
         {
            _loc2_ = this.jiNengZD_Data[1].getValue() + 0.0001;
            _loc3_ = this.who.use_hp_Max.getValue() * _loc2_;
            this.who.HpUp(_loc3_);
            this.ZD1num -= 10;
         }
         if(this.ZD1time <= 0)
         {
            this.ZD1num = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1002);
         }
         --this.ZD1time;
      }
      
      public function ZD1005() : *
      {
         var _loc1_:HitXX = null;
         this.ZD2Time = this.jiNengZD_Data[2].getValue();
         for(i in Enemy.All)
         {
            if(Enemy.All[i])
            {
               _loc1_ = new HitXX();
               _loc1_.type = 507;
               _loc1_.totalTime = 81;
               _loc1_.space = 81;
               _loc1_.numValue = this.jiNengZD_Data[0].getValue();
               new BuffEnemy(_loc1_,Enemy.All[i]);
               NewMC.Open("露娜效果",Enemy.All[i],0,-50);
            }
         }
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1005);
      }
      
      private function onENTER_FRAME_ZD1005(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:Enemy = null;
         if(this.ZD2Time % 27 == 0)
         {
            _loc2_ = this.who.use_gongji.getValue() * this.jiNengZD_Data[1].getValue();
            for(i in Enemy.All)
            {
               if(Enemy.All[i])
               {
                  _loc3_ = Enemy.All[i];
                  _loc3_.HpXX2(_loc2_);
               }
            }
         }
         --this.ZD2Time;
         if(this.ZD2Time <= 0)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1005);
            this.jiNengZD_num = 0;
         }
      }
      
      public function ZD1012() : *
      {
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1012);
      }
      
      private function onENTER_FRAME_ZD1012(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:Enemy = null;
         var _loc4_:Class = null;
         var _loc5_:* = undefined;
         ++this.ZD1012_Time;
         if(this.ZD1012_Time >= 23)
         {
            _loc2_ = this.who.use_gongji.getValue() * this.jiNengZD_Data[0].getValue();
            for(i in Enemy.All)
            {
               if(Enemy.All[i])
               {
                  _loc3_ = Enemy.All[i];
                  _loc3_.HpXX2(_loc2_);
                  _loc4_ = ChongWu.loadData.getClass("act_mc") as Class;
                  _loc5_ = new _loc4_();
                  Main.world.moveChild_Other.addChild(_loc5_);
                  _loc5_.x = _loc3_.x;
                  _loc5_.y = _loc3_.y;
               }
            }
            this.ZD1012_Time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1012);
         }
      }
      
      public function ZD1014() : *
      {
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1014);
      }
      
      private function onENTER_FRAME_ZD1014(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:Enemy = null;
         ++this.ZD1014_Time;
         if(this.ZD1014_Time >= 60)
         {
            _loc2_ = this.who.use_gongji.getValue() * this.jiNengZD_Data[0].getValue();
            for(i in Enemy.All)
            {
               if(Boolean(Enemy.All[i]) && Math.abs(this.x - Enemy.All[i].x) < 190)
               {
                  _loc3_ = Enemy.All[i];
                  _loc3_.HpXX2(_loc2_);
               }
            }
            this.ZD1014_Time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1014);
         }
      }
      
      public function ZD1016() : *
      {
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1016);
      }
      
      private function onENTER_FRAME_ZD1016(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:Enemy = null;
         var _loc4_:Class = null;
         var _loc5_:* = undefined;
         var _loc6_:HitXX = null;
         ++this.ZD1016_Time;
         if(this.ZD1016_Time >= 70)
         {
            _loc2_ = this.who.use_gongji.getValue() * this.jiNengZD_Data[0].getValue();
            for(i in Enemy.All)
            {
               if(Enemy.All[i] && (Enemy.All[i] as Enemy).life.getValue() > 0 && Math.abs(this.x - Enemy.All[i].x) < 300)
               {
                  _loc3_ = Enemy.All[i];
                  _loc3_.HpXX2(_loc2_);
                  _loc4_ = ChongWu.loadData.getClass("xx2016") as Class;
                  _loc5_ = new _loc4_();
                  _loc3_.addChild(_loc5_);
                  _loc5_.y = -120;
                  _loc6_ = new HitXX();
                  _loc6_.type = 606;
                  _loc6_.totalTime = 135;
                  _loc6_.numValue = this.jiNengZD_Data[1].getValue();
                  new BuffEnemy(_loc6_,Enemy.All[i]);
               }
            }
            this.ZD1016_Time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1016);
         }
      }
      
      public function ZD1018() : *
      {
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1018);
      }
      
      private function onENTER_FRAME_ZD1018(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:Enemy = null;
         var _loc4_:Class = null;
         ++this.ZD1018_Time;
         if(this.ZD1018_Time >= 20)
         {
            if((this.ZD1018_Time - 20) % 37 == 0)
            {
               _loc2_ = this.who.use_gongji.getValue() * this.jiNengZD_Data[0].getValue();
               for(i in Enemy.All)
               {
                  if(Enemy.All[i] && (Enemy.All[i] as Enemy).life.getValue() > 0 && Math.abs(this.x - Enemy.All[i].x) < 300)
                  {
                     _loc3_ = Enemy.All[i];
                     _loc3_.HpXX2(_loc2_);
                  }
               }
               this.who.HpUp(this.jiNengZD_Data[1].getValue(),2);
            }
         }
         if(this.ZD1018_Time == 20)
         {
            _loc4_ = ChongWu.loadData.getClass("shui") as Class;
            this.shuiMC = new _loc4_();
            Main.world.moveChild_Other.addChild(this.shuiMC);
            this.shuiMC.y = 530;
            this.shuiMC.x = this.who.x;
         }
         else if(this.ZD1018_Time > 20 && this.ZD1018_Time <= 40)
         {
            this.shuiMC.y -= 9;
         }
         else if(this.ZD1018_Time > 115 && this.ZD1018_Time <= 135)
         {
            this.shuiMC.y += 9;
         }
         else if(this.ZD1018_Time > 136)
         {
            if(this.shuiMC.parent)
            {
               this.shuiMC.parent.removeChild(this.shuiMC);
            }
            this.shuiMC == null;
            this.ZD1018_Time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1018);
         }
      }
      
      public function ZD1022() : *
      {
         var _loc3_:int = 0;
         var _loc4_:Enemy = null;
         var _loc1_:Class = NewLoad.XiaoGuoData.getClass("星河之灵星灵脉冲") as Class;
         var _loc2_:* = new _loc1_();
         this.who.skin_W.addChild(_loc2_);
         if(Enemy.All.length > 0)
         {
            _loc3_ = Math.random() * Enemy.All.length;
            _loc4_ = Enemy.All[_loc3_];
            _loc2_.x = _loc4_.x;
            _loc2_.y = _loc4_.y;
         }
         else
         {
            _loc2_.x = this.who.x;
            _loc2_.y = this.who.y;
         }
         _loc2_.gongJi_hp = this.jiNengZD_Data[0].getValue() / 100;
         _loc2_.attTimes = 7;
      }
   }
}

