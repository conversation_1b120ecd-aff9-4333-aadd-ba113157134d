package com.hotpoint.braveManIII.repository.gem
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import flash.events.*;
   import src.*;
   
   public class GemFactory
   {
      public static var isGemOK:Boolean = false;
      
      public static var _GemDataList:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function GemFactory()
      {
         super();
      }
      
      public static function creatGemFactory() : *
      {
         myXml = XMLAsset.createXML(InData.baoShiData);
         var _loc1_:GemFactory = new GemFactory();
         _loc1_.creatGemData();
      }
      
      private static function getGemBaseDataById(param1:Number) : GemBaseData
      {
         var _loc2_:GemBaseData = null;
         var _loc3_:GemBaseData = null;
         for each(_loc3_ in _GemDataList)
         {
            if(_loc3_.getId() == param1)
            {
               _loc2_ = _loc3_;
               break;
            }
         }
         if(_loc2_ == null)
         {
            throw new Error("找不到基础数据!id:" + param1);
         }
         return _loc2_;
      }
      
      public static function creatGemById(param1:Number) : Gem
      {
         var _loc2_:GemBaseData = getGemBaseDataById(param1);
         return _loc2_.createGem();
      }
      
      public static function createGemByStrengthen(param1:String, param2:Number) : Gem
      {
         var _loc3_:GemBaseData = null;
         var _loc5_:GemBaseData = null;
         var _loc4_:Array = getGemBaseDataByName(param1);
         for each(_loc5_ in _loc4_)
         {
            if(_loc5_.getStrengthenLevel() == param2 + 1)
            {
               _loc3_ = _loc5_;
            }
         }
         if(_loc3_ == null)
         {
            throw new Error("找不到基础数据!");
         }
         return _loc3_.upLevelGem();
      }
      
      public static function createGemByCompose(param1:Number, param2:Number, param3:Array) : Gem
      {
         var _loc4_:GemBaseData = null;
         var _loc6_:GemBaseData = null;
         var _loc5_:Array = getGemBaseDataByColor(param1);
         for each(_loc6_ in _loc5_)
         {
            if(_loc6_.getStrengthenLevel() == param2)
            {
               _loc4_ = _loc6_;
            }
         }
         if(_loc4_ == null)
         {
            throw new Error("找不到基础数据! color = " + param1 + ", level = " + param2);
         }
         return _loc4_.composeGem(param3);
      }
      
      public static function createGemByColorAndLevel(param1:Array) : Gem
      {
         var _loc3_:GemBaseData = null;
         var _loc5_:Number = NaN;
         var _loc2_:Array = [];
         for each(_loc3_ in _GemDataList)
         {
            for each(_loc5_ in param1)
            {
               if(_loc3_.getDropLevel() == _loc5_)
               {
                  _loc2_.push(_loc3_);
               }
            }
         }
         if(_loc2_.length < 1)
         {
            throw new Error("没有这个的等级物品:" + param1);
         }
         var _loc4_:int = Math.floor(Math.random() * _loc2_.length);
         _loc3_ = _loc2_[_loc4_] as GemBaseData;
         return _loc3_.createGem();
      }
      
      private static function getGemBaseDataByColor(param1:Number) : Array
      {
         var _loc3_:GemBaseData = null;
         var _loc2_:Array = [];
         for each(_loc3_ in _GemDataList)
         {
            if(_loc3_.getColor() == param1)
            {
               _loc2_.push(_loc3_);
            }
         }
         if(_loc2_.length < 1)
         {
            throw new Error("没有这个颜色的宝石:color:" + param1);
         }
         return _loc2_;
      }
      
      private static function getGemBaseDataByName(param1:String) : Array
      {
         var _loc3_:GemBaseData = null;
         var _loc2_:Array = [];
         for each(_loc3_ in _GemDataList)
         {
            if(_loc3_.getName() == param1)
            {
               _loc2_.push(_loc3_);
            }
         }
         if(_loc2_.length < 1)
         {
            throw new Error("没有这个名称的宝石:color:" + param1);
         }
         return _loc2_;
      }
      
      public static function findFrame(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getFrame();
      }
      
      public static function findClassName(param1:Number) : String
      {
         return getGemBaseDataById(param1).getClassName();
      }
      
      public static function findName(param1:Number) : String
      {
         return getGemBaseDataById(param1).getName();
      }
      
      public static function findDescript(param1:Number) : String
      {
         return getGemBaseDataById(param1).getDescript();
      }
      
      public static function findTimes(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getTimes();
      }
      
      public static function findType(param1:Number) : int
      {
         return getGemBaseDataById(param1).getType();
      }
      
      public static function findIsPile(param1:Number) : Boolean
      {
         return getGemBaseDataById(param1).getIsPile();
      }
      
      public static function findIsStrengthen(param1:Number) : Boolean
      {
         return getGemBaseDataById(param1).getIsStrengthen();
      }
      
      public static function findIsCompound(param1:Number) : Boolean
      {
         return getGemBaseDataById(param1).getIsCompound();
      }
      
      public static function findDropLevel(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getDropLevel();
      }
      
      public static function findUseLevel(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getUseLevel();
      }
      
      public static function findPileLimit(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getPileLimit();
      }
      
      public static function findPrice(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getPrice();
      }
      
      public static function findColor(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getColor();
      }
      
      public static function findProbability(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getProbability();
      }
      
      public static function findStrengthenLevel(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getStrengthenLevel();
      }
      
      public static function findSkill(param1:Number) : Number
      {
         return getGemBaseDataById(param1).getAddSkill();
      }
      
      private function creatGemData() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var _loc1_:XML = null;
         var _loc2_:String = null;
         var _loc3_:String = null;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc6_:String = null;
         var _loc7_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc10_:Number = NaN;
         var _loc11_:int = 0;
         var _loc12_:Number = NaN;
         var _loc13_:Number = NaN;
         var _loc14_:Boolean = false;
         var _loc15_:Boolean = false;
         var _loc16_:Boolean = false;
         var _loc17_:Number = NaN;
         var _loc18_:Number = NaN;
         var _loc19_:Number = NaN;
         var _loc20_:XMLList = null;
         var _loc21_:Array = null;
         var _loc22_:XML = null;
         var _loc23_:GemBaseData = null;
         for each(_loc1_ in myXml.宝石)
         {
            _loc2_ = String(_loc1_.名称);
            _loc3_ = String(_loc1_.文件名);
            _loc4_ = Number(_loc1_.帧数);
            _loc5_ = Number(_loc1_.编号);
            _loc6_ = String(_loc1_.描述);
            _loc7_ = Number(_loc1_.使用等级);
            _loc8_ = Number(_loc1_.掉落等级);
            _loc9_ = Number(_loc1_.强化等级);
            _loc10_ = Number(_loc1_.价钱);
            _loc11_ = int(_loc1_.类型);
            _loc12_ = Number(_loc1_.堆叠次数);
            _loc13_ = Number(_loc1_.颜色);
            _loc14_ = (_loc1_.是否堆叠.toString() == "true") as Boolean;
            _loc15_ = (_loc1_.是否强化.toString() == "true") as Boolean;
            _loc16_ = (_loc1_.是否合成.toString() == "true") as Boolean;
            _loc17_ = Number(_loc1_.堆叠上限);
            _loc18_ = Number(_loc1_.概率);
            _loc19_ = Number(_loc1_.宝石技能);
            _loc20_ = _loc1_.宝石属性;
            _loc21_ = [];
            for each(_loc22_ in _loc20_)
            {
               if(_loc22_.生命 != "null")
               {
                  _loc21_.push(Attribute.creatAttribute(1,Number(_loc22_.生命)));
               }
               if(_loc22_.魔法 != "null")
               {
                  _loc21_.push(Attribute.creatAttribute(2,Number(_loc22_.魔法)));
               }
               if(_loc22_.攻击 != "null")
               {
                  _loc21_.push(Attribute.creatAttribute(3,Number(_loc22_.攻击)));
               }
               if(_loc22_.防御 != "null")
               {
                  _loc21_.push(Attribute.creatAttribute(4,Number(_loc22_.防御)));
               }
               if(_loc22_.暴击 != "null")
               {
                  _loc21_.push(Attribute.creatAttribute(5,Number(_loc22_.暴击)));
               }
               if(_loc22_.闪避 != "null")
               {
                  _loc21_.push(Attribute.creatAttribute(6,Number(_loc22_.闪避)));
               }
               if(_loc22_.移动速度 != "null")
               {
                  _loc21_.push(Attribute.creatAttribute(7,Number(_loc22_.移动速度)));
               }
               if(_loc22_.硬直 != "null")
               {
                  _loc21_.push(Attribute.creatAttribute(8,Number(_loc22_.硬直)));
               }
            }
            _loc23_ = GemBaseData.cteateGemBaseData(_loc5_,_loc4_,_loc2_,_loc3_,_loc6_,_loc12_,_loc11_,_loc14_,_loc15_,_loc16_,_loc8_,_loc7_,_loc9_,_loc17_,_loc10_,_loc13_,_loc18_,_loc21_,_loc19_);
            _GemDataList.push(_loc23_);
         }
         GemFactory.isGemOK = true;
      }
   }
}

