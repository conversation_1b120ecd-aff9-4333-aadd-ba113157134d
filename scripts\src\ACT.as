package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.media.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class ACT extends MovieClip
   {
      public function ACT()
      {
         super();
      }
      
      public static function Play(param1:Object, param2:Object, param3:String = "") : *
      {
         var _loc4_:Class = null;
         var _loc5_:* = undefined;
         var _loc6_:int = 0;
         var _loc7_:String = null;
         if(param3 == "火眼金睛效果")
         {
            _loc4_ = ChongWu.chongWu_Data[33].getClass(param3) as Class;
            _loc5_ = new _loc4_();
         }
         else if(param3 != "")
         {
            _loc4_ = NewLoad.XiaoGuoData.getClass(param3) as Class;
            _loc5_ = new _loc4_();
         }
         else
         {
            _loc6_ = int(param1.data.skinArr[param1.data.skinNum]);
            _loc7_ = "ACT_" + _loc6_;
            _loc4_ = NewLoad.XiaoGuoData.getClass(_loc7_) as Class;
            _loc5_ = new _loc4_();
         }
         _loc5_.x = param2.x;
         if(param2 is Enemy && param2.id == 31)
         {
            _loc5_.y = 160;
         }
         else
         {
            _loc5_.y = param2.y - 50;
         }
         Main.world.moveChild_Other.addChild(_loc5_);
      }
      
      public static function Play2(param1:int, param2:Enemy) : *
      {
         var _loc3_:String = "ACT_" + param1;
         var _loc4_:* = NewLoad.XiaoGuoData.getClass(_loc3_) as Class;
         var _loc5_:ACT = new _loc4_();
         _loc5_.x = param2.x;
         if(param2.id == 31)
         {
            _loc5_.y = 160;
         }
         else
         {
            _loc5_.y = param2.y - 50;
         }
         Main.world.moveChild_Other.addChild(_loc5_);
      }
   }
}

