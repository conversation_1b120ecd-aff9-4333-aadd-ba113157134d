package src._data
{
   import com.hotpoint.braveManIII.models.common.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.utils.ByteArray;
   import src.*;
   import src.tool.*;
   
   public class Data_KillShop extends MovieClip
   {
      public static var data:XML;
      
      public static var data2:XML;
      
      public static var dataXXX:ByteArray;
      
      public static var buyTime:VT = VT.createVT(0);
      
      public static var buyArr:Array = new Array();
      
      public function Data_KillShop()
      {
         super();
      }
      
      public static function Init() : *
      {
         Data_Xml();
         Data_Xml2();
         Xml_KillShopArr();
         TestData_Init();
      }
      
      private static function TestData_Init() : *
      {
         var _loc1_:Array = [data,data2];
         dataXXX = Obj_Compare.getObj_ByteArray(_loc1_);
      }
      
      public static function TestData() : *
      {
         var _loc1_:Array = [data,data2];
         var _loc2_:ByteArray = Obj_Compare.getObj_ByteArray(_loc1_);
         if(Obj_Compare.CompareByteArray(dataXXX,_loc2_) == false)
         {
            Main.NoGame();
         }
         TiaoShi.txtShow("检测通过");
      }
      
      public static function BuyArrInit() : *
      {
         var _loc1_:VT = null;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         if(Main.serverTime.getValue() > buyTime.getValue())
         {
            _loc1_ = VT.createVT(3);
            _loc2_ = 0;
            while(_loc2_ < Main.player1.getTitleSlot().getListLength())
            {
               if(Main.player1.getTitleSlot().getTitleFromSlot(_loc2_).getId() == 25)
               {
                  _loc1_ = VT.createVT(4);
               }
               _loc2_++;
            }
            buyTime.setValue(Main.serverTime.getValue());
            _loc3_ = 1;
            while(_loc3_ < 5)
            {
               buyArr[_loc3_] = new Array();
               buyArr[_loc3_][0] = VT.createVT(_loc1_.getValue());
               _loc4_ = 1;
               while(_loc4_ < ShopKillPoint.ShopArr2.length + 1)
               {
                  buyArr[_loc3_][_loc4_] = false;
                  _loc4_++;
               }
               _loc3_++;
            }
            ShopKillPoint.shopX.Show2();
         }
         else
         {
            buyTime.setValue(Main.serverTime.getValue());
         }
      }
      
      private static function Xml_KillShopArr() : *
      {
         var _loc2_:int = 0;
         SelType();
         var _loc1_:Array = [-1];
         for(_loc2_ in data2.击杀商城)
         {
            TestData_2(_loc2_);
            _loc1_[_loc2_ + 1] = new Array();
            _loc1_[_loc2_ + 1][1] = int(data2.击杀商城[_loc2_].ID);
            _loc1_[_loc2_ + 1][2] = String(data2.击杀商城[_loc2_].类型);
            _loc1_[_loc2_ + 1][3] = VT.createVT(int(data2.击杀商城[_loc2_].物品ID));
            _loc1_[_loc2_ + 1][4] = String(data2.击杀商城[_loc2_].名称);
            _loc1_[_loc2_ + 1][5] = int(data2.击杀商城[_loc2_].图标);
            _loc1_[_loc2_ + 1][6] = String(data2.击杀商城[_loc2_].说明);
            _loc1_[_loc2_ + 1][7] = VT.createVT(int(data2.击杀商城[_loc2_].金币));
            _loc1_[_loc2_ + 1][8] = VT.createVT(int(data2.击杀商城[_loc2_].击杀点));
         }
         ShopKillPoint.ShopArr2 = _loc1_;
      }
      
      public static function SelType(param1:int = 0) : *
      {
         var _loc5_:int = 0;
         var _loc2_:Array = [-1];
         var _loc3_:int = 1;
         var _loc4_:int = 0;
         _loc2_[_loc3_] = new Array();
         for(_loc5_ in data.击杀商城)
         {
            if(!(param1 == 1 && String(data.击杀商城[_loc5_].类型) != "宝石类"))
            {
               if(!(param1 == 2 && String(data.击杀商城[_loc5_].类型) != "时装类"))
               {
                  if(!(param1 == 3 && String(data.击杀商城[_loc5_].类型) != "其他类"))
                  {
                     if(_loc4_ == ShopKillPoint.numX)
                     {
                        _loc3_++;
                        _loc2_[_loc3_] = new Array();
                        _loc4_ = 0;
                     }
                     _loc4_++;
                     TestData_1(_loc5_);
                     _loc2_[_loc3_][_loc4_] = new Array();
                     _loc2_[_loc3_][_loc4_][1] = int(data.击杀商城[_loc5_].ID);
                     _loc2_[_loc3_][_loc4_][2] = String(data.击杀商城[_loc5_].类型);
                     _loc2_[_loc3_][_loc4_][3] = VT.createVT(int(data.击杀商城[_loc5_].物品ID));
                     _loc2_[_loc3_][_loc4_][4] = String(data.击杀商城[_loc5_].名称);
                     _loc2_[_loc3_][_loc4_][5] = int(data.击杀商城[_loc5_].图标);
                     _loc2_[_loc3_][_loc4_][6] = String(data.击杀商城[_loc5_].说明);
                     _loc2_[_loc3_][_loc4_][7] = VT.createVT(int(data.击杀商城[_loc5_].金币));
                     _loc2_[_loc3_][_loc4_][8] = VT.createVT(int(data.击杀商城[_loc5_].击杀点));
                  }
               }
            }
         }
         ShopKillPoint.ShopArr = _loc2_;
         ShopKillPoint.numTotal = _loc3_;
      }
      
      private static function TestData_1(param1:uint) : *
      {
         var _loc2_:int = int(data.击杀商城[param1].验证);
         var _loc3_:int = int(data.击杀商城[param1].随机数);
         var _loc4_:int = int(data.击杀商城[param1].击杀点);
         var _loc5_:int = int(data.击杀商城[param1].金币);
         var _loc6_:int = int(data.击杀商城[param1].图标);
         var _loc7_:int = int(data.击杀商城[param1].物品ID);
         if(_loc2_ != _loc3_ + _loc4_ + _loc5_ + _loc6_ + _loc7_)
         {
            Main.NoGame("击杀点商城数据错误 #1");
         }
      }
      
      private static function TestData_2(param1:uint) : *
      {
         var _loc2_:int = int(data2.击杀商城[param1].验证);
         var _loc3_:int = int(data2.击杀商城[param1].随机数);
         var _loc4_:int = int(data2.击杀商城[param1].击杀点);
         var _loc5_:int = int(data2.击杀商城[param1].金币);
         var _loc6_:int = int(data2.击杀商城[param1].图标);
         var _loc7_:int = int(data2.击杀商城[param1].物品ID);
         if(_loc2_ != _loc3_ + _loc4_ + _loc5_ + _loc6_ + _loc7_)
         {
            Main.NoGame("击杀点商城数据错误 #2");
         }
      }
      
      private static function Data_Xml() : *
      {
         data = <root>
	<击杀商城>
		<ID>63</ID>
		<类型>其他类</类型>
		<物品ID>63148</物品ID>
		<名称>宠物口粮</名称>
		<图标>235</图标>
		<说明>只能给宠物食用的粮食，喂食宠物后将回复宠物20点饱食度。</说明>
		<金币>0</金币>
		<击杀点>2</击杀点>
		<验证>82470</验证>
		<随机数>19085</随机数>
	</击杀商城>
	<击杀商城>
		<ID>57</ID>
		<类型>其他类</类型>
		<物品ID>63102</物品ID>
		<名称>小礼袋</名称>
		<图标>163</图标>
		<说明>将此物件赠送给你喜欢的NPC后，能够快速的提升你们之间的友好程度。</说明>
		<金币>0</金币>
		<击杀点>25</击杀点>
		<验证>81380</验证>
		<随机数>18090</随机数>
	</击杀商城>
	<击杀商城>
		<ID>78</ID>
		<类型>消耗品类</类型>
		<物品ID>21114</物品ID>
		<名称>精灵秘药（HP）</名称>
		<图标>249</图标>
		<说明>使用后立即回复940生命值，之后每秒额外回复3%生命值，持续6秒，冷却时间6秒</说明>
		<金币>0</金币>
		<击杀点>4</击杀点>
		<验证>36416</验证>
		<随机数>15049</随机数>
	</击杀商城>
	<击杀商城>
		<ID>79</ID>
		<类型>消耗品类</类型>
		<物品ID>21214</物品ID>
		<名称>精灵秘药（MP）</名称>
		<图标>248</图标>
		<说明>使用后立即回复18%+290的魔法值，冷却时间3秒</说明>
		<金币>0</金币>
		<击杀点>4</击杀点>
		<验证>34126</验证>
		<随机数>12660</随机数>
	</击杀商城>
	<击杀商城>
		<ID>80</ID>
		<类型>消耗品类</类型>
		<物品ID>21219</物品ID>
		<名称>精灵秘药（HP）X5</名称>
		<图标>249</图标>
		<说明>使用后立即回复940生命值，之后每秒额外回复3%生命值，持续6秒，冷却时间6秒</说明>
		<金币>0</金币>
		<击杀点>20</击杀点>
		<验证>34437</验证>
		<随机数>12949</随机数>
	</击杀商城>
	<击杀商城>
		<ID>81</ID>
		<类型>消耗品类</类型>
		<物品ID>21220</物品ID>
		<名称>精灵秘药（MP）X5</名称>
		<图标>248</图标>
		<说明>使用后立即回复18%+290的魔法值，冷却时间3秒</说明>
		<金币>0</金币>
		<击杀点>20</击杀点>
		<验证>37950</验证>
		<随机数>16462</随机数>
	</击杀商城>
	<击杀商城>
		<ID>1</ID>
		<类型>其他类</类型>
		<物品ID>63132</物品ID>
		<名称>铁矿石</名称>
		<图标>212</图标>
		<说明>合成物品所必须的材料，可通过分解50级白色或蓝色装备获得。</说明>
		<金币>1100</金币>
		<击杀点>0</击杀点>
		<验证>74450</验证>
		<随机数>10006</随机数>
	</击杀商城>
	<击杀商城>
		<ID>2</ID>
		<类型>其他类</类型>
		<物品ID>63133</物品ID>
		<名称>元素精华</名称>
		<图标>213</图标>
		<说明>合成物品所必须的材料，可通过分解50级白色或蓝色装备获得。</说明>
		<金币>2200</金币>
		<击杀点>0</击杀点>
		<验证>83303</验证>
		<随机数>17757</随机数>
	</击杀商城>
	<击杀商城>
		<ID>3</ID>
		<类型>其他类</类型>
		<物品ID>63134</物品ID>
		<名称>冷凝剂</名称>
		<图标>214</图标>
		<说明>合成物品所必须的材料，可通过分解50级白色或蓝色装备获得。</说明>
		<金币>2200</金币>
		<击杀点>0</击杀点>
		<验证>81726</验证>
		<随机数>16178</随机数>
	</击杀商城>
	<击杀商城>
		<ID>4</ID>
		<类型>其他类</类型>
		<物品ID>63135</物品ID>
		<名称>特制布匹</名称>
		<图标>211</图标>
		<说明>合成物品所必须的材料，可通过分解50级白色或蓝色装备获得。</说明>
		<金币>1100</金币>
		<击杀点>0</击杀点>
		<验证>75680</验证>
		<随机数>11234</随机数>
	</击杀商城>
	<击杀商城>
		<ID>5</ID>
		<类型>其他类</类型>
		<物品ID>63136</物品ID>
		<名称>古树之灵</名称>
		<图标>215</图标>
		<说明>合成物品所必须的材料，可通过分解50级粉色装备获得。</说明>
		<金币>0</金币>
		<击杀点>2</击杀点>
		<验证>74249</验证>
		<随机数>10896</随机数>
	</击杀商城>
	<击杀商城>
		<ID>6</ID>
		<类型>其他类</类型>
		<物品ID>63137</物品ID>
		<名称>赤炎红铁</名称>
		<图标>216</图标>
		<说明>合成物品所必须的材料，可通过分解50级粉色装备获得。</说明>
		<金币>0</金币>
		<击杀点>2</击杀点>
		<验证>81075</验证>
		<随机数>17720</随机数>
	</击杀商城>
	<击杀商城>
		<ID>64</ID>
		<类型>其他类</类型>
		<物品ID>63152</物品ID>
		<名称>梦幻之翼设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁时装-梦幻之翼的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>78205</验证>
		<随机数>14595</随机数>
	</击杀商城>
	<击杀商城>
		<ID>65</ID>
		<类型>其他类</类型>
		<物品ID>63153</物品ID>
		<名称>木之本樱外套设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁时装-木之本樱外套的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>77530</验证>
		<随机数>13919</随机数>
	</击杀商城>
	<击杀商城>
		<ID>66</ID>
		<类型>其他类</类型>
		<物品ID>63154</物品ID>
		<名称>兽族之王外套设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁时装-兽族之王外套的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>81646</验证>
		<随机数>18034</随机数>
	</击杀商城>
	<击杀商城>
		<ID>67</ID>
		<类型>其他类</类型>
		<物品ID>63157</物品ID>
		<名称>星·光之印记设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁武器-星·光之印记的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>75490</验证>
		<随机数>11875</随机数>
	</击杀商城>
	<击杀商城>
		<ID>68</ID>
		<类型>其他类</类型>
		<物品ID>63158</物品ID>
		<名称>星·绝杀者之杖设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁武器-星·绝杀者之杖的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>79518</验证>
		<随机数>15902</随机数>
	</击杀商城>
	<击杀商城>
		<ID>69</ID>
		<类型>其他类</类型>
		<物品ID>63159</物品ID>
		<名称>星·雷芒拳套设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁武器-星·雷芒拳套的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>73780</验证>
		<随机数>10163</随机数>
	</击杀商城>
	<击杀商城>
		<ID>7</ID>
		<类型>其他类</类型>
		<物品ID>63112</物品ID>
		<名称>绝世神技头饰设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世神技头饰的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>79630</验证>
		<随机数>16060</随机数>
	</击杀商城>
	<击杀商城>
		<ID>8</ID>
		<类型>其他类</类型>
		<物品ID>63113</物品ID>
		<名称>绝世神技战甲设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世神技战甲的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>83507</验证>
		<随机数>19936</随机数>
	</击杀商城>
	<击杀商城>
		<ID>9</ID>
		<类型>其他类</类型>
		<物品ID>63114</物品ID>
		<名称>绝世神技项坠设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世神技项坠的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>74254</验证>
		<随机数>10682</随机数>
	</击杀商城>
	<击杀商城>
		<ID>10</ID>
		<类型>其他类</类型>
		<物品ID>63115</物品ID>
		<名称>绝世神技指环设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世神技指环的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>81424</验证>
		<随机数>17851</随机数>
	</击杀商城>
	<击杀商城>
		<ID>11</ID>
		<类型>其他类</类型>
		<物品ID>63116</物品ID>
		<名称>绝世热血头饰设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世热血头饰的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>82168</验证>
		<随机数>18594</随机数>
	</击杀商城>
	<击杀商城>
		<ID>12</ID>
		<类型>其他类</类型>
		<物品ID>63117</物品ID>
		<名称>绝世热血战甲设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世热血战甲的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>77017</验证>
		<随机数>13442</随机数>
	</击杀商城>
	<击杀商城>
		<ID>13</ID>
		<类型>其他类</类型>
		<物品ID>63118</物品ID>
		<名称>绝世热血项坠设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世热血项坠的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>76432</验证>
		<随机数>12856</随机数>
	</击杀商城>
	<击杀商城>
		<ID>14</ID>
		<类型>其他类</类型>
		<物品ID>63119</物品ID>
		<名称>绝世热血指环设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世热血指环的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>75860</验证>
		<随机数>12283</随机数>
	</击杀商城>
	<击杀商城>
		<ID>15</ID>
		<类型>其他类</类型>
		<物品ID>63120</物品ID>
		<名称>绝世怒雷头饰设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世怒雷头饰的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>79097</验证>
		<随机数>15519</随机数>
	</击杀商城>
	<击杀商城>
		<ID>16</ID>
		<类型>其他类</类型>
		<物品ID>63121</物品ID>
		<名称>绝世怒雷战甲设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世怒雷战甲的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>74879</验证>
		<随机数>11300</随机数>
	</击杀商城>
	<击杀商城>
		<ID>17</ID>
		<类型>其他类</类型>
		<物品ID>63122</物品ID>
		<名称>绝世怒雷项坠设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世怒雷项坠的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>82431</验证>
		<随机数>18851</随机数>
	</击杀商城>
	<击杀商城>
		<ID>18</ID>
		<类型>其他类</类型>
		<物品ID>63123</物品ID>
		<名称>绝世怒雷指环设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世怒雷指环的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>79888</验证>
		<随机数>16307</随机数>
	</击杀商城>
	<击杀商城>
		<ID>19</ID>
		<类型>其他类</类型>
		<物品ID>63124</物品ID>
		<名称>绝世邪灵头饰设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世邪灵头饰的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>75520</验证>
		<随机数>11938</随机数>
	</击杀商城>
	<击杀商城>
		<ID>20</ID>
		<类型>其他类</类型>
		<物品ID>63125</物品ID>
		<名称>绝世邪灵战甲设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世邪灵战甲的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>76361</验证>
		<随机数>12778</随机数>
	</击杀商城>
	<击杀商城>
		<ID>21</ID>
		<类型>其他类</类型>
		<物品ID>63126</物品ID>
		<名称>绝世邪灵项坠设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世邪灵项坠的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>77912</验证>
		<随机数>14328</随机数>
	</击杀商城>
	<击杀商城>
		<ID>22</ID>
		<类型>其他类</类型>
		<物品ID>63127</物品ID>
		<名称>绝世邪灵指环设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世邪灵指环的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>82164</验证>
		<随机数>18579</随机数>
	</击杀商城>
	<击杀商城>
		<ID>23</ID>
		<类型>其他类</类型>
		<物品ID>63128</物品ID>
		<名称>绝世圣愈头饰设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世圣愈头饰的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>73871</验证>
		<随机数>10285</随机数>
	</击杀商城>
	<击杀商城>
		<ID>24</ID>
		<类型>其他类</类型>
		<物品ID>63129</物品ID>
		<名称>绝世圣愈战甲设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世圣愈战甲的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>79024</验证>
		<随机数>15437</随机数>
	</击杀商城>
	<击杀商城>
		<ID>25</ID>
		<类型>其他类</类型>
		<物品ID>63130</物品ID>
		<名称>绝世圣愈项坠设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世圣愈项坠的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>82826</验证>
		<随机数>19238</随机数>
	</击杀商城>
	<击杀商城>
		<ID>26</ID>
		<类型>其他类</类型>
		<物品ID>63131</物品ID>
		<名称>绝世圣愈指环设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁终极套装-绝世圣愈指环的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>81138</验证>
		<随机数>17549</随机数>
	</击杀商城>
	<击杀商城>
		<ID>92</ID>
		<类型>其他类</类型>
		<物品ID>63452</物品ID>
		<名称>星·天罚之刃设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁制作机中武器“星·天罚之刃”的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>79770</验证>
		<随机数>15860</随机数>
	</击杀商城>
	<击杀商城>
		<ID>93</ID>
		<类型>其他类</类型>
		<物品ID>63456</物品ID>
		<名称>上古神之审判设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁制作机中武器“上古神之审判”的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>78085</验证>
		<随机数>14171</随机数>
	</击杀商城>
	<击杀商城>
		<ID>27</ID>
		<类型>宝石类</类型>
		<物品ID>31315</物品ID>
		<名称>低级属性石Lv.1</名称>
		<图标>168</图标>
		<说明>增加人物HP上限43</说明>
		<金币>0</金币>
		<击杀点>10</击杀点>
		<验证>48785</验证>
		<随机数>17292</随机数>
	</击杀商城>
	<击杀商城>
		<ID>28</ID>
		<类型>宝石类</类型>
		<物品ID>31316</物品ID>
		<名称>低级属性石Lv.16</名称>
		<图标>168</图标>
		<说明>增加人物HP上限60</说明>
		<金币>0</金币>
		<击杀点>15</击杀点>
		<验证>44733</验证>
		<随机数>13234</随机数>
	</击杀商城>
	<击杀商城>
		<ID>29</ID>
		<类型>宝石类</类型>
		<物品ID>31317</物品ID>
		<名称>低级属性石Lv.31</名称>
		<图标>168</图标>
		<说明>增加人物HP上限84</说明>
		<金币>0</金币>
		<击杀点>25</击杀点>
		<验证>47013</验证>
		<随机数>15503</随机数>
	</击杀商城>
	<击杀商城>
		<ID>30</ID>
		<类型>宝石类</类型>
		<物品ID>31318</物品ID>
		<名称>低级属性石Lv.50</名称>
		<图标>168</图标>
		<说明>增加人物HP上限99</说明>
		<金币>0</金币>
		<击杀点>35</击杀点>
		<验证>50301</验证>
		<随机数>18780</随机数>
	</击杀商城>
	<击杀商城>
		<ID>31</ID>
		<类型>宝石类</类型>
		<物品ID>31319</物品ID>
		<名称>低级属性石Lv.50(终）</名称>
		<图标>168</图标>
		<说明>增加人物HP上限111</说明>
		<金币>0</金币>
		<击杀点>70</击杀点>
		<验证>42351</验证>
		<随机数>10794</随机数>
	</击杀商城>
	<击杀商城>
		<ID>32</ID>
		<类型>宝石类</类型>
		<物品ID>31320</物品ID>
		<名称>低级属性石Lv.1</名称>
		<图标>169</图标>
		<说明>增加人物MP上限31</说明>
		<金币>0</金币>
		<击杀点>10</击杀点>
		<验证>41635</验证>
		<随机数>10136</随机数>
	</击杀商城>
	<击杀商城>
		<ID>33</ID>
		<类型>宝石类</类型>
		<物品ID>31321</物品ID>
		<名称>低级属性石Lv.16</名称>
		<图标>169</图标>
		<说明>增加人物MP上限42</说明>
		<金币>0</金币>
		<击杀点>15</击杀点>
		<验证>45171</验证>
		<随机数>13666</随机数>
	</击杀商城>
	<击杀商城>
		<ID>34</ID>
		<类型>宝石类</类型>
		<物品ID>31322</物品ID>
		<名称>低级属性石Lv.31</名称>
		<图标>169</图标>
		<说明>增加人物MP上限58</说明>
		<金币>0</金币>
		<击杀点>25</击杀点>
		<验证>47151</验证>
		<随机数>15635</随机数>
	</击杀商城>
	<击杀商城>
		<ID>35</ID>
		<类型>宝石类</类型>
		<物品ID>31323</物品ID>
		<名称>低级属性石Lv.50</名称>
		<图标>169</图标>
		<说明>增加人物MP上限82</说明>
		<金币>0</金币>
		<击杀点>35</击杀点>
		<验证>47734</验证>
		<随机数>16207</随机数>
	</击杀商城>
	<击杀商城>
		<ID>36</ID>
		<类型>宝石类</类型>
		<物品ID>31324</物品ID>
		<名称>低级属性石Lv.50(终）</名称>
		<图标>169</图标>
		<说明>增加人物MP上限107</说明>
		<金币>0</金币>
		<击杀点>70</击杀点>
		<验证>50153</验证>
		<随机数>18590</随机数>
	</击杀商城>
	<击杀商城>
		<ID>37</ID>
		<类型>宝石类</类型>
		<物品ID>31325</物品ID>
		<名称>低级攻击属性石Lv.1</名称>
		<图标>170</图标>
		<说明>增加人物攻击上限5</说明>
		<金币>0</金币>
		<击杀点>10</击杀点>
		<验证>45850</验证>
		<随机数>14345</随机数>
	</击杀商城>
	<击杀商城>
		<ID>38</ID>
		<类型>宝石类</类型>
		<物品ID>31326</物品ID>
		<名称>低级攻击属性石Lv.16</名称>
		<图标>170</图标>
		<说明>增加人物攻击上限6</说明>
		<金币>0</金币>
		<击杀点>15</击杀点>
		<验证>43504</验证>
		<随机数>11993</随机数>
	</击杀商城>
	<击杀商城>
		<ID>39</ID>
		<类型>宝石类</类型>
		<物品ID>31327</物品ID>
		<名称>低级攻击属性石Lv.31</名称>
		<图标>170</图标>
		<说明>增加人物攻击上限8</说明>
		<金币>0</金币>
		<击杀点>25</击杀点>
		<验证>51311</验证>
		<随机数>19789</随机数>
	</击杀商城>
	<击杀商城>
		<ID>40</ID>
		<类型>宝石类</类型>
		<物品ID>31328</物品ID>
		<名称>低级攻击属性石Lv.50</名称>
		<图标>170</图标>
		<说明>增加人物攻击上限10</说明>
		<金币>0</金币>
		<击杀点>35</击杀点>
		<验证>43066</验证>
		<随机数>11533</随机数>
	</击杀商城>
	<击杀商城>
		<ID>41</ID>
		<类型>宝石类</类型>
		<物品ID>31329</物品ID>
		<名称>低级攻击属性石Lv.50(终）</名称>
		<图标>170</图标>
		<说明>增加人物攻击上限12</说明>
		<金币>0</金币>
		<击杀点>70</击杀点>
		<验证>48194</验证>
		<随机数>16625</随机数>
	</击杀商城>
	<击杀商城>
		<ID>42</ID>
		<类型>宝石类</类型>
		<物品ID>31330</物品ID>
		<名称>低级属性石Lv.1</名称>
		<图标>171</图标>
		<说明>增加人物防御上限5</说明>
		<金币>0</金币>
		<击杀点>10</击杀点>
		<验证>43165</验证>
		<随机数>11654</随机数>
	</击杀商城>
	<击杀商城>
		<ID>43</ID>
		<类型>宝石类</类型>
		<物品ID>31331</物品ID>
		<名称>低级属性石Lv.16</名称>
		<图标>171</图标>
		<说明>增加人物防御上限7</说明>
		<金币>0</金币>
		<击杀点>15</击杀点>
		<验证>51397</验证>
		<随机数>19880</随机数>
	</击杀商城>
	<击杀商城>
		<ID>44</ID>
		<类型>宝石类</类型>
		<物品ID>31332</物品ID>
		<名称>低级属性石Lv.31</名称>
		<图标>171</图标>
		<说明>增加人物防御上限11</说明>
		<金币>0</金币>
		<击杀点>25</击杀点>
		<验证>44318</验证>
		<随机数>12790</随机数>
	</击杀商城>
	<击杀商城>
		<ID>45</ID>
		<类型>宝石类</类型>
		<物品ID>31333</物品ID>
		<名称>低级属性石Lv.50</名称>
		<图标>171</图标>
		<说明>增加人物防御上限18</说明>
		<金币>0</金币>
		<击杀点>35</击杀点>
		<验证>46576</验证>
		<随机数>15037</随机数>
	</击杀商城>
	<击杀商城>
		<ID>46</ID>
		<类型>宝石类</类型>
		<物品ID>31334</物品ID>
		<名称>低级属性石Lv.50(终）</名称>
		<图标>171</图标>
		<说明>增加人物防御上限23</说明>
		<金币>0</金币>
		<击杀点>70</击杀点>
		<验证>46270</验证>
		<随机数>14695</随机数>
	</击杀商城>
	<击杀商城>
		<ID>47</ID>
		<类型>宝石类</类型>
		<物品ID>31335</物品ID>
		<名称>低级属性石Lv.1</名称>
		<图标>172</图标>
		<说明>增加人物暴击上限60</说明>
		<金币>0</金币>
		<击杀点>10</击杀点>
		<验证>42980</验证>
		<随机数>11463</随机数>
	</击杀商城>
	<击杀商城>
		<ID>48</ID>
		<类型>宝石类</类型>
		<物品ID>31336</物品ID>
		<名称>低级属性石Lv.16</名称>
		<图标>172</图标>
		<说明>增加人物暴击上限67</说明>
		<金币>0</金币>
		<击杀点>15</击杀点>
		<验证>47238</验证>
		<随机数>15715</随机数>
	</击杀商城>
	<击杀商城>
		<ID>49</ID>
		<类型>宝石类</类型>
		<物品ID>31337</物品ID>
		<名称>低级属性石Lv.31</名称>
		<图标>172</图标>
		<说明>增加人物暴击上限74</说明>
		<金币>0</金币>
		<击杀点>25</击杀点>
		<验证>49393</验证>
		<随机数>17859</随机数>
	</击杀商城>
	<击杀商城>
		<ID>50</ID>
		<类型>宝石类</类型>
		<物品ID>31338</物品ID>
		<名称>低级属性石Lv.50</名称>
		<图标>172</图标>
		<说明>增加人物暴击上限81</说明>
		<金币>0</金币>
		<击杀点>35</击杀点>
		<验证>46673</验证>
		<随机数>15128</随机数>
	</击杀商城>
	<击杀商城>
		<ID>51</ID>
		<类型>宝石类</类型>
		<物品ID>31339</物品ID>
		<名称>低级属性石Lv.50(终）</名称>
		<图标>172</图标>
		<说明>增加人物暴击上限88</说明>
		<金币>0</金币>
		<击杀点>70</击杀点>
		<验证>48151</验证>
		<随机数>16570</随机数>
	</击杀商城>
	<击杀商城>
		<ID>52</ID>
		<类型>宝石类</类型>
		<物品ID>31340</物品ID>
		<名称>低级属性石Lv.1</名称>
		<图标>173</图标>
		<说明>增加人物闪避上限20</说明>
		<金币>0</金币>
		<击杀点>10</击杀点>
		<验证>43587</验证>
		<随机数>12064</随机数>
	</击杀商城>
	<击杀商城>
		<ID>53</ID>
		<类型>宝石类</类型>
		<物品ID>31341</物品ID>
		<名称>低级属性石Lv.16</名称>
		<图标>173</图标>
		<说明>增加人物闪避上限26</说明>
		<金币>0</金币>
		<击杀点>15</击杀点>
		<验证>48676</验证>
		<随机数>17147</随机数>
	</击杀商城>
	<击杀商城>
		<ID>54</ID>
		<类型>宝石类</类型>
		<物品ID>31342</物品ID>
		<名称>低级属性石Lv.31</名称>
		<图标>173</图标>
		<说明>增加人物闪避上限33</说明>
		<金币>0</金币>
		<击杀点>25</击杀点>
		<验证>45751</验证>
		<随机数>14211</随机数>
	</击杀商城>
	<击杀商城>
		<ID>55</ID>
		<类型>宝石类</类型>
		<物品ID>31343</物品ID>
		<名称>低级属性石Lv.50</名称>
		<图标>173</图标>
		<说明>增加人物闪避上限42</说明>
		<金币>0</金币>
		<击杀点>35</击杀点>
		<验证>50950</验证>
		<随机数>19399</随机数>
	</击杀商城>
	<击杀商城>
		<ID>56</ID>
		<类型>宝石类</类型>
		<物品ID>31344</物品ID>
		<名称>低级属性石Lv.50(终）</名称>
		<图标>173</图标>
		<说明>增加人物闪避上限56</说明>
		<金币>0</金币>
		<击杀点>70</击杀点>
		<验证>46550</验证>
		<随机数>14963</随机数>
	</击杀商城>
	<击杀商城>
		<ID>70</ID>
		<类型>其他类</类型>
		<物品ID>63161</物品ID>
		<名称>消亡魔盒</名称>
		<图标>245</图标>
		<说明>使用后可清除装备强化等级。</说明>
		<金币>500000</金币>
		<击杀点>2500</击杀点>
		<验证>585237</验证>
		<随机数>19331</随机数>
	</击杀商城>
	<击杀商城>
		<ID>58</ID>
		<类型>宝石类</类型>
		<物品ID>34610</物品ID>
		<名称>增幅宝石（攻）</名称>
		<图标>224</图标>
		<说明>增幅装备属性的稀有宝石，点击使用后可提升人物攻击力2.5%</说明>
		<金币>500000</金币>
		<击杀点>2500</击杀点>
		<验证>556657</验证>
		<随机数>19323</随机数>
	</击杀商城>
	<击杀商城>
		<ID>59</ID>
		<类型>宝石类</类型>
		<物品ID>34611</物品ID>
		<名称>增幅宝石（暴）</名称>
		<图标>224</图标>
		<说明>增幅装备属性的稀有宝石，点击使用后可提升人物暴击值17.5%</说明>
		<金币>500000</金币>
		<击杀点>2500</击杀点>
		<验证>556651</验证>
		<随机数>19316</随机数>
	</击杀商城>
	<击杀商城>
		<ID>60</ID>
		<类型>宝石类</类型>
		<物品ID>34613</物品ID>
		<名称>增幅宝石（HP）</名称>
		<图标>224</图标>
		<说明>增幅装备属性的稀有宝石，点击使用后可提升人物HP上限2.75%</说明>
		<金币>500000</金币>
		<击杀点>2500</击杀点>
		<验证>557193</验证>
		<随机数>19856</随机数>
	</击杀商城>
	<击杀商城>
		<ID>61</ID>
		<类型>宝石类</类型>
		<物品ID>34614</物品ID>
		<名称>增幅宝石（防）</名称>
		<图标>224</图标>
		<说明>增幅装备属性的稀有宝石，点击使用后可提升人物防御力2.25%</说明>
		<金币>500000</金币>
		<击杀点>2500</击杀点>
		<验证>552799</验证>
		<随机数>15461</随机数>
	</击杀商城>
	<击杀商城>
		<ID>62</ID>
		<类型>其他类</类型>
		<物品ID>63142</物品ID>
		<名称>1级圣星石设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁1级圣星石的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>75471</验证>
		<随机数>11871</随机数>
	</击杀商城>
	<击杀商城>
		<ID>71</ID>
		<类型>其他类</类型>
		<物品ID>63143</物品ID>
		<名称>2级圣星石设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁2级圣星石的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>79415</验证>
		<随机数>15814</随机数>
	</击杀商城>
	<击杀商城>
		<ID>72</ID>
		<类型>其他类</类型>
		<物品ID>63144</物品ID>
		<名称>3级圣星石设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁3级圣星石的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>81319</验证>
		<随机数>17717</随机数>
	</击杀商城>
	<击杀商城>
		<ID>77</ID>
		<类型>其他类</类型>
		<物品ID>63145</物品ID>
		<名称>4级圣星石设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁4级圣星石的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>80294</验证>
		<随机数>16691</随机数>
	</击杀商城>
	<击杀商城>
		<ID>83</ID>
		<类型>其他类</类型>
		<物品ID>63146</物品ID>
		<名称>5级圣星石设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁5级圣星石的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>80033</验证>
		<随机数>16429</随机数>
	</击杀商城>
	<击杀商城>
		<ID>73</ID>
		<类型>其他类</类型>
		<物品ID>63172</物品ID>
		<名称>魔力印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁魔力印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>77043</验证>
		<随机数>13413</随机数>
	</击杀商城>
	<击杀商城>
		<ID>74</ID>
		<类型>其他类</类型>
		<物品ID>63199</物品ID>
		<名称>体力印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁体力印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>76977</验证>
		<随机数>13320</随机数>
	</击杀商城>
	<击杀商城>
		<ID>75</ID>
		<类型>其他类</类型>
		<物品ID>63214</物品ID>
		<名称>攻击印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁攻击印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>79622</验证>
		<随机数>15950</随机数>
	</击杀商城>
	<击杀商城>
		<ID>76</ID>
		<类型>其他类</类型>
		<物品ID>63216</物品ID>
		<名称>防御印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁防御印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>81271</验证>
		<随机数>17597</随机数>
	</击杀商城>
	<击杀商城>
		<ID>82</ID>
		<类型>其他类</类型>
		<物品ID>63232</物品ID>
		<名称>生命印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁体力印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>75095</验证>
		<随机数>11405</随机数>
	</击杀商城>
	<击杀商城>
		<ID>84</ID>
		<类型>其他类</类型>
		<物品ID>63239</物品ID>
		<名称>力量印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁力量印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>76993</验证>
		<随机数>13296</随机数>
	</击杀商城>
	<击杀商城>
		<ID>85</ID>
		<类型>其他类</类型>
		<物品ID>63275</物品ID>
		<名称>速度印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁速度印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>82960</验证>
		<随机数>19227</随机数>
	</击杀商城>
	<击杀商城>
		<ID>86</ID>
		<类型>其他类</类型>
		<物品ID>63282</物品ID>
		<名称>石肤印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁石肤印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>81539</验证>
		<随机数>17799</随机数>
	</击杀商城>
	<击杀商城>
		<ID>87</ID>
		<类型>其他类</类型>
		<物品ID>63329</物品ID>
		<名称>灵介印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁灵介印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>82192</验证>
		<随机数>18405</随机数>
	</击杀商城>
	<击杀商城>
		<ID>88</ID>
		<类型>其他类</类型>
		<物品ID>63349</物品ID>
		<名称>上古神之巨兵设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁武器-上古神之巨兵的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>76686</验证>
		<随机数>12879</随机数>
	</击杀商城>
	<击杀商城>
		<ID>89</ID>
		<类型>其他类</类型>
		<物品ID>63350</物品ID>
		<名称>上古神之权杖设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁武器-上古神之权杖的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>77425</验证>
		<随机数>13617</随机数>
	</击杀商城>
	<击杀商城>
		<ID>90</ID>
		<类型>其他类</类型>
		<物品ID>63351</物品ID>
		<名称>上古神之铁握设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁武器-上古神之铁握的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>80056</验证>
		<随机数>16247</随机数>
	</击杀商城>
	<击杀商城>
		<ID>91</ID>
		<类型>其他类</类型>
		<物品ID>63392</物品ID>
		<名称>恢复印章设计图</名称>
		<图标>208</图标>
		<说明>使用后可解锁恢复印章的制作权限。</说明>
		<金币>0</金币>
		<击杀点>250</击杀点>
		<验证>83236</验证>
		<随机数>19386</随机数>
	</击杀商城>
</root>;
      }
      
      private static function Data_Xml2() : *
      {
         data2 = <root>
	<击杀商城>
		<ID>1</ID>
		<类型>其他类</类型>
		<物品ID>63138</物品ID>
		<名称>圣光水晶</名称>
		<图标>217</图标>
		<说明>合成物品所必须的珍贵材料，可通过分解50级金色装备获得。</说明>
		<金币>0</金币>
		<击杀点>125</击杀点>
		<验证>74110</验证>
		<随机数>10630</随机数>
	</击杀商城>
	<击杀商城>
		<ID>2</ID>
		<类型>其他类</类型>
		<物品ID>63100</物品ID>
		<名称>合成添加剂</名称>
		<图标>161</图标>
		<说明>合成物品时所须的添加剂。</说明>
		<金币>0</金币>
		<击杀点>60</击杀点>
		<验证>82046</验证>
		<随机数>18725</随机数>
	</击杀商城>
	<击杀商城>
		<ID>4</ID>
		<类型>宝石类</类型>
		<物品ID>31214</物品ID>
		<名称>1级强化石</名称>
		<图标>140</图标>
		<说明>可强化+3以下的任意装备，使用2个1级强化石可合成精良的1级强化石。</说明>
		<金币>0</金币>
		<击杀点>25</击杀点>
		<验证>41533</验证>
		<随机数>10154</随机数>
	</击杀商城>
	<击杀商城>
		<ID>5</ID>
		<类型>宝石类</类型>
		<物品ID>31215</物品ID>
		<名称>2级强化石</名称>
		<图标>174</图标>
		<说明>可强化+6以下的任意装备，使用2个2级强化石可合成精良的2级强化石。</说明>
		<金币>0</金币>
		<击杀点>45</击杀点>
		<验证>51161</验证>
		<随机数>19727</随机数>
	</击杀商城>
	<击杀商城>
		<ID>6</ID>
		<类型>宝石类</类型>
		<物品ID>31216</物品ID>
		<名称>3级强化石</名称>
		<图标>175</图标>
		<说明>可强化+9以下的任意装备，使用2个3级强化石可合成精良的3级强化石。</说明>
		<金币>0</金币>
		<击杀点>150</击杀点>
		<验证>42024</验证>
		<随机数>10483</随机数>
	</击杀商城>
	<击杀商城>
		<ID>7</ID>
		<类型>宝石类</类型>
		<物品ID>33510</物品ID>
		<名称>低级幸运宝石</名称>
		<图标>143</图标>
		<说明>拥有着特殊力量的幸运宝石，使用后可提高物品强化成功率10%。</说明>
		<金币>0</金币>
		<击杀点>40</击杀点>
		<验证>50183</验证>
		<随机数>16490</随机数>
	</击杀商城>
	<击杀商城>
		<ID>8</ID>
		<类型>宝石类</类型>
		<物品ID>33511</物品ID>
		<名称>中级幸运宝石</名称>
		<图标>144</图标>
		<说明>拥有着特殊力量的幸运宝石，使用后可提高物品强化成功率50%。</说明>
		<金币>0</金币>
		<击杀点>155</击杀点>
		<验证>53182</验证>
		<随机数>19372</随机数>
	</击杀商城>
	<击杀商城>
		<ID>9</ID>
		<类型>其他类</类型>
		<物品ID>63160</物品ID>
		<名称>生命之源</名称>
		<图标>244</图标>
		<说明>宠物20级时可用于帮助宠物进化的稀有物品。</说明>
		<金币>0</金币>
		<击杀点>900</击杀点>
		<验证>76706</验证>
		<随机数>12402</随机数>
	</击杀商城>
</root>;
      }
   }
}

