package src
{
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.make.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.repository.quest.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.repository.skillCondition.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import flash.display.*;
   import flash.events.*;
   import flash.external.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.system.*;
   
   public class InData extends MovieClip
   {
      public static const zbData:Class = InData_zbData;
      
      public static const tzData:Class = InData_tzData;
      
      public static const baoShiData:Class = InData_baoShiData;
      
      public static const xiaoHaoData:Class = InData_xiaoHaoData;
      
      public static const SkillData:Class = InData_SkillData;
      
      public static const SkillTJData:Class = InData_SkillTJData;
      
      public static const GemProbabiltyData:Class = InData_GemProbabiltyData;
      
      public static const GoldData:Class = InData_GoldData;
      
      public static const QuestData:Class = InData_QuestData;
      
      public static const OtherData:Class = InData_OtherData;
      
      public static const MakeData:Class = InData_MakeData;
      
      public function InData()
      {
         super();
      }
      
      public static function Loading() : *
      {
         EquipFactory.creatEquipFactory();
         GemFactory.creatGemFactory();
         SuppliesFactory.creatSuppliesFactory();
         SkillFactory.creatSkillData();
         SkillConditionFactory.creatKillCond();
         GemProbabilityFactory.creatProbabilltyData();
         GoldFactory.creatGoldFactory();
         QuestFactory.creatQuestFactory();
         OtherFactory.creatOtherFactory();
         MakeFactory.creatMakeFactory();
      }
      
      override public function get stage() : Stage
      {
         return LoaderInfo.prototype["__stage"];
      }
   }
}

