package com.hotpoint.braveManIII.views.achPanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.achievement.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.text.TextField;
   import src.*;
   import src.tool.*;
   
   public class AchPanel extends MovieClip
   {
      private static var _instance:AchPanel;
      
      private static var loadData:ClassLoader;
      
      private static var open_yn:Boolean;
      
      private static var loadName:String = "Panel_CJ_v1.swf";
      
      private static var xxxArr:Array = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,101,102,103,104,105,51,52,53,54,55,56,57,58,59,60,61,62];
      
      private var _data:AchData;
      
      private var bigState:Number = 0;
      
      private var bye:Number = 0;
      
      private var sstate:Number = 0;
      
      private var ye:Number = 0;
      
      private var dataArr:Array = [];
      
      private var dataBigArr:Array = [];
      
      private var acSlot:AchSlot;
      
      private var tSlot:AcTiaoSlot;
      
      private var xxArr:Array = [];
      
      private var aaArr:Array = [];
      
      private var bo:Boolean;
      
      public var mk1:*;
      
      public var mk2:*;
      
      public var allAcText:*;
      
      public var bt_0:*;
      
      public var bt_1:*;
      
      public var na_0:*;
      
      public var na_1:*;
      
      public var na_2:*;
      
      public var na_3:*;
      
      public var na_4:*;
      
      public var na_5:*;
      
      public var na_6:*;
      
      public var na_7:*;
      
      public var na_8:*;
      
      public var na_9:*;
      
      public var na_10:*;
      
      public var na_11:*;
      
      public var na_12:*;
      
      public var na_13:*;
      
      public var na_14:*;
      
      public var by_0:*;
      
      public var by_1:*;
      
      public var b_text:*;
      
      public var evPoint:*;
      
      public var gamePoint:*;
      
      public var closePanel:*;
      
      public var a_0:*;
      
      public var a_1:*;
      
      public var a_2:*;
      
      public var a_3:*;
      
      public var a_4:*;
      
      public var a_5:*;
      
      public var a_6:*;
      
      public var a_7:*;
      
      public var a_8:*;
      
      public var a_9:*;
      
      public var a_10:*;
      
      public var sy_0:*;
      
      public var sy_1:*;
      
      public var s_text:*;
      
      public var gy_1:*;
      
      public var a_name:*;
      
      public var a_jieShao:*;
      
      public var xxx_text:*;
      
      private var bigTyeArr:Array = new Array();
      
      public function AchPanel()
      {
         super();
         this._data = new AchData();
         this.acSlot = new AchSlot();
         this.tSlot = new AcTiaoSlot();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("com.hotpoint.braveManIII.views.achPanel.AchPanel") as Class;
         AchPanel._instance = new _loc2_();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      public static function getStateByGkId(param1:Number) : Array
      {
         var _loc7_:Number = 0;
         var _loc8_:Number = 0;
         var _loc2_:Number = 0;
         var _loc3_:Number = 0;
         while(_loc3_ < xxxArr.length)
         {
            if(xxxArr[_loc3_] == param1)
            {
               _loc2_ = _loc3_;
               break;
            }
            _loc3_++;
         }
         var _loc4_:Array = [];
         var _loc5_:Array = AchData.getEvOrGa(0);
         var _loc6_:Array = _loc5_[_loc2_];
         if(_loc6_.length != 0)
         {
            _loc7_ = 0;
            while(_loc7_ < _loc6_.length)
            {
               _loc8_ = 0;
               while(_loc8_ < _loc6_[_loc7_].length)
               {
                  _loc4_.push((_loc6_[_loc7_][_loc8_] as Achievement).getStata());
                  _loc8_++;
               }
               _loc7_++;
            }
         }
         return _loc4_;
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(AchPanel._instance == null)
         {
            Loading();
            open_yn = true;
            return;
         }
         AchPanel._instance.bo = false;
         AchData.initXq();
         AchData.gkOk();
         AchData.initStr();
         AchPanel._instance.updeAc();
         Main._stage.addChild(AchPanel._instance);
         AchPanel._instance.initPanel();
         AchPanel._instance.addEvent();
         AchPanel._instance.visible = true;
         AchPanel._instance.y = 0;
         AchPanel._instance.x = 0;
      }
      
      public static function close() : void
      {
         open_yn = false;
         if(AchPanel._instance != null)
         {
            if(AchPanel._instance.visible == true)
            {
               AchPanel._instance.visible = false;
            }
         }
      }
      
      private function initPanel() : void
      {
         this.gy_1._isClick = false;
         this.gy_1.gotoAndStop(1);
         AchData.setMadeById(1);
         AchData.setMadeById(2);
         AchData.setMadeById(3);
         AchData.isAcOk();
         this.bigState = 1;
         this.bye = 0;
         this.sstate = 0;
         this.ye = 0;
         this.initType(this.bigState,this.sstate,this.ye,this.bo);
         this.initTiao(this.bigState,this.bye);
         this.getPlint();
         this.btnStata(1,2,"bt_");
         this.btnStata(0,15,"na_");
         this.mk1.visible = true;
         this.mk2.visible = false;
      }
      
      private function updeAc() : void
      {
         var _loc2_:Achievement = null;
         var _loc1_:Array = AchData.everyAll;
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.getStata() == 3)
            {
               if(Main.serverTime.getValue() > _loc2_.getOverTimer())
               {
                  _loc2_.setStata(0);
                  _loc2_.clearAcData();
               }
            }
         }
      }
      
      public function addEvent() : void
      {
         this.addEventListener(BtnEvent.DO_CHANGE,this.doChange);
         this.addEventListener(BtnEvent.DO_CLICK,this.doClick);
         this.addEventListener(BtnEvent.DO_CLOSE,this.doClose);
         this.addEventListener(BtnEvent.DO_ONECLICK,this.doOneClick);
      }
      
      public function doChange(param1:BtnEvent) : void
      {
         var _loc2_:String = param1.target.name;
         var _loc3_:String = _loc2_.substr(0,1);
         var _loc4_:Number = Number(_loc2_.substr(3));
         if(_loc3_ == "b")
         {
            this.bigState = _loc4_;
            this.sstate = 0;
            this.ye = 0;
            this.bye = 0;
            this.initType(this.bigState,this.sstate,this.ye,this.bo);
            this.initTiao(this.bigState,this.bye);
            this.btnStata(this.bigState,2,"bt_");
            this.btnStata(0,15,"na_");
            if(this.bigState == 0)
            {
               this.mk1.visible = false;
               this.mk2.visible = true;
            }
            else if(this.bigState == 1)
            {
               this.mk1.visible = true;
               this.mk2.visible = false;
            }
         }
         else if(_loc3_ == "n")
         {
            if(this.tSlot.getName(_loc4_) != null)
            {
               this.ye = 0;
               this.sstate = this.getTypeByeNameId(_loc4_);
               this.initType(this.bigState,this.sstate,this.ye,this.bo);
               this.btnStata(_loc4_,15,"na_");
            }
         }
      }
      
      public function doClose(param1:BtnEvent) : void
      {
         close();
      }
      
      private function getTypeByeNameId(param1:Number = 0) : Number
      {
         var _loc3_:String = null;
         var _loc2_:Number = 0;
         if(this.tSlot.getName(param1) != null)
         {
            _loc3_ = this.tSlot.getName(param1);
            _loc2_ = Number(_loc3_.substring(_loc3_.lastIndexOf("_") + 1,_loc3_.length));
         }
         return _loc2_;
      }
      
      public function doClick(param1:BtnEvent) : void
      {
         var _loc2_:String = param1.target.name;
         var _loc3_:String = _loc2_.substr(0,1);
         var _loc4_:Number = Number(_loc2_.substr(3,1));
         if(_loc3_ == "b")
         {
            this.ye = 0;
            this.bye = this.addOrJian(_loc4_,this.bye,this.dataBigArr);
            this.initTiao(this.bigState,this.bye);
            this.sstate = this.getTypeByeNameId(0);
            this.initType(this.bigState,this.sstate,this.ye,this.bo);
            this.btnStata(0,15,"na_");
         }
         else if(_loc3_ == "s")
         {
            this.ye = this.addOrJian(_loc4_,this.ye,this.dataArr);
            this.initType(this.bigState,this.sstate,this.ye,this.bo);
         }
      }
      
      private function doOneClick(param1:BtnEvent) : void
      {
         this.bo = !this.bo;
         this.initType(this.bigState,this.sstate,this.ye,this.bo);
      }
      
      private function addOrJian(param1:Number, param2:Number, param3:Array) : Number
      {
         var _loc4_:Number = param2;
         if(param3 != null)
         {
            if(param1 == 0)
            {
               if(_loc4_ > 0)
               {
                  _loc4_--;
               }
            }
            else if(param1 == 1)
            {
               if(_loc4_ < param3.length - 1)
               {
                  _loc4_++;
               }
            }
         }
         return _loc4_;
      }
      
      private function initTiao(param1:Number = 0, param2:Number = 0) : void
      {
         this.bigTyeArr = [["落月之原_0","落月之森_1","冰雪废墟_2","死亡流沙_3","万年雪山_4","废弃都市_5","火山的噩梦_6","堕落城堡_7"," 幽灵船_8","机械城_9","雪狼巢穴_10","火之祭坛_11","暗影城_12","暗夜遗迹_13","机械试练场_14","熔岩城堡_15","暗黑炼狱_16","神秘地带1_17","神秘地带2_18","神秘地带3_19","神秘地带4_20","神秘地带5_21","艾尔之海_22","安塔利亚_23","阿肯色_24","雅利安_25","奥戈_26","波塞迪亚_27","密咒魔城_28","翡翠城_29","壁纹洞窟_30","神秘方舟_31","死亡山脉_32","死亡峡谷_33"],["变态_0","装备_1","强化_2","关卡_3","收集_4","任务_5","友好_6","制作_7","合成_8","镶嵌_9","升星_10","挑战_11","金币_12","在线_13","击杀点_14","战斗_15","特殊_16","技能_17","人物_18","宠物_19"]];
         this.dataBigArr = AchData.getArr10(this.bigTyeArr[param1],15);
         this.tSlot.clearAc();
         this.iniTiaoSlot(this.dataBigArr,param2);
         this.initTiaoVisble();
         this.byeNum(this.b_text,this.bye,this.dataBigArr);
      }
      
      private function byeNum(param1:TextField, param2:Number, param3:Array) : void
      {
         var _loc4_:* = undefined;
         if(param3 != null)
         {
            _loc4_ = param3.length;
            if(_loc4_ < 1)
            {
               _loc4_ = 1;
            }
            param1.text = param2 + 1 + "/" + _loc4_;
         }
         else
         {
            param1.text = param2 + 1 + "/" + 1;
         }
      }
      
      private function initTiaoVisble() : void
      {
         var _loc2_:String = null;
         var _loc3_:String = null;
         var _loc4_:int = 0;
         var _loc5_:Number = NaN;
         var _loc6_:Array = null;
         var _loc1_:Number = 0;
         while(_loc1_ < 15)
         {
            this["na_" + _loc1_].n_text.text = "";
            if(this.tSlot.getName(_loc1_) != null)
            {
               _loc3_ = this.tSlot.getName(_loc1_);
               _loc4_ = int(_loc3_.lastIndexOf("_"));
               _loc2_ = _loc3_.substr(0,_loc4_);
               _loc5_ = Number(this.getTypeByeNameId(_loc1_));
               _loc6_ = this.getNum(_loc5_);
               this["na_" + _loc1_].n_text.text = _loc2_;
               this["na_" + _loc1_].num_text.text = String(_loc6_[0]) + "/" + String(_loc6_[1]);
            }
            else
            {
               this["na_" + _loc1_].n_text.text = "";
               this["na_" + _loc1_].num_text.text = "";
            }
            _loc1_++;
         }
      }
      
      private function getNum(param1:Number) : Array
      {
         var _loc5_:Number = 0;
         var _loc6_:Number = 0;
         var _loc7_:Achievement = null;
         TiaoShi.txtShow("type = " + param1);
         var _loc2_:Array = [];
         var _loc3_:Number = 0;
         var _loc4_:Number = 0;
         if(this.bigState == 0)
         {
            TiaoShi.txtShow("type____" + param1);
            if(AchData.edArr.length > 0)
            {
               _loc2_ = AchData.edArr[param1];
               TiaoShi.txtShow("arr=" + _loc2_);
               _loc5_ = 0;
               while(_loc5_ < _loc2_.length)
               {
                  _loc4_ += _loc2_[_loc5_].length;
                  _loc6_ = 0;
                  while(_loc6_ < _loc2_[_loc5_].length)
                  {
                     _loc7_ = _loc2_[_loc5_][_loc6_];
                     if(_loc7_.getStata() == 3)
                     {
                        _loc3_++;
                     }
                     _loc6_++;
                  }
                  _loc5_++;
               }
            }
         }
         else if(this.bigState == 1)
         {
            if(AchData.gaArr.length > 0)
            {
               _loc2_ = AchData.gaArr[param1];
               _loc5_ = 0;
               while(_loc5_ < _loc2_.length)
               {
                  _loc4_ += _loc2_[_loc5_].length;
                  _loc6_ = 0;
                  while(_loc6_ < _loc2_[_loc5_].length)
                  {
                     _loc7_ = _loc2_[_loc5_][_loc6_];
                     if(_loc7_.getStata() == 2)
                     {
                        _loc3_++;
                     }
                     _loc6_++;
                  }
                  _loc5_++;
               }
            }
         }
         return new Array(_loc3_,_loc4_);
      }
      
      private function initType(param1:Number = 0, param2:Number = 0, param3:Number = 0, param4:Boolean = false) : void
      {
         this.xxArr = AchData.getEvOrGa(param1);
         this.dataArr = this.xxArr[param2];
         var _loc5_:Array = [];
         if(this.dataArr != null && this.dataArr.length > 0)
         {
            _loc5_ = this.xxArr[param2][param3];
         }
         this.acSlot.clearAc();
         this.iniSlot(_loc5_,param4);
         this.initAc();
         this.byeNum(this.s_text,this.ye,this.dataArr);
      }
      
      private function iniTiaoSlot(param1:Array, param2:Number = 0) : void
      {
         var _loc3_:Array = null;
         var _loc4_:Number = 0;
         if(param1 != null)
         {
            _loc3_ = param1[param2];
            _loc4_ = 0;
            while(_loc4_ < _loc3_.length)
            {
               this.tSlot.addName(_loc3_[_loc4_]);
               _loc4_++;
            }
         }
      }
      
      private function iniSlot(param1:Array, param2:Boolean) : void
      {
         var _loc3_:Number = 0;
         var _loc4_:Achievement = null;
         if(param1.length != 0)
         {
            _loc3_ = 0;
            while(_loc3_ < param1.length)
            {
               _loc4_ = param1[_loc3_];
               if(param2)
               {
                  if(_loc4_.getStata() == 0)
                  {
                     this.acSlot.addSlot(_loc4_);
                  }
               }
               else
               {
                  this.acSlot.addSlot(_loc4_);
               }
               _loc3_++;
            }
         }
      }
      
      private function initAc() : void
      {
         var _loc2_:Achievement = null;
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc1_:Number = 0;
         while(_loc1_ < 10)
         {
            if(this.acSlot.getAc(_loc1_) != null)
            {
               _loc2_ = this.acSlot.getAc(_loc1_);
               this["a_" + _loc1_].a_name.text = String(_loc2_.getName());
               this["a_" + _loc1_].gotoAndStop(_loc2_.getFrame());
               _loc3_ = 0;
               _loc3_ = _loc2_.getFinishNum();
               _loc4_ = Number(this.getNumByType(_loc2_));
               this["a_" + _loc1_].xxx_text.text = "进度:" + _loc4_ + "/" + _loc3_;
               if(_loc2_.getSmallType() == 26 || _loc2_.getSmallType() == 27 || _loc2_.getSmallType() == 28 || _loc2_.getSmallType() == 29 || _loc2_.getSmallType() == 30 || _loc2_.getSmallType() == 32 || _loc2_.getSmallType() == 35 || _loc2_.getSmallType() == 36 || _loc2_.getSmallType() == 37 || _loc2_.getSmallType() == 38 || _loc2_.getSmallType() == 39 || _loc2_.getSmallType() == 40 || _loc2_.getSmallType() == 41 || _loc2_.getSmallType() == 42 || _loc2_.getSmallType() == 43 || _loc2_.getSmallType() == 44 && _loc2_.isRy())
               {
                  this["a_" + _loc1_].xxx_text.text = "进度:无";
               }
               this["a_" + _loc1_].a_jieShao.text = String(_loc2_.getSm());
               if(_loc2_.getStata() != 0)
               {
                  this["a_" + _loc1_].gotoAndStop(_loc2_.getFrame() + 1);
                  this["a_" + _loc1_].xxx_text.text = "进度:完成";
               }
            }
            else
            {
               this["a_" + _loc1_].a_name.text = "";
               this["a_" + _loc1_].a_jieShao.text = "";
               this["a_" + _loc1_].xxx_text.text = "";
               this["a_" + _loc1_].gotoAndStop(1);
            }
            _loc1_++;
         }
      }
      
      private function getNumByType(param1:Achievement) : Number
      {
         switch(param1.getSmallType())
         {
            case 1:
               return Main.P1P2 ? Number(this.bjDx(Main.player1.getLevel(),Main.player2.getLevel())) : Number(Main.player1.getLevel());
            case 2:
               return Main.P1P2 ? Number(this.bjDx(Main.player1.getGold(),Main.player2.getGold())) : Number(Main.player1.getGold());
            case 5:
               return Main.P1P2 ? Number(this.bjDx(Main.player1.getKillPoint(),Main.player2.getKillPoint())) : Number(Main.player1.getKillPoint());
            case 7:
               return AchData.cjPoint_1.getValue();
            case 8:
               return AchData.cjPoint_2.getValue();
            case 10:
               return AchData.getGaAcNum();
            case 11:
               return Main.P1P2 ? Number(this.bjDx(Main.player1.getPetSlot().backPetNum(),Main.player2.getPetSlot().backPetNum())) : Number(Main.player1.getPetSlot().backPetNum());
            case 12:
               return Main.P1P2 ? Number(this.bjDx(Main.player1.getSkillNum(),Main.player2.getSkillNum())) : Number(Main.player1.getSkillNum());
            case 13:
               return Main.P1P2 ? Number(this.bjDx(AchData.xq1.getValue(),AchData.xq2.getValue())) : Number(AchData.xq1.getValue());
            case 14:
               return Main.P1P2 ? Number(this.bjDx(AchData.zf1.getValue(),AchData.zf2.getValue())) : Number(AchData.zf1.getValue());
            case 15:
               return Main.P1P2 ? Number(this.bjDx(AchData.m1.getValue(),AchData.m2.getValue())) : Number(AchData.m1.getValue());
            case 16:
               return Main.P1P2 ? Number(this.bjDx(AchData.hc1.getValue(),AchData.hc2.getValue())) : Number(AchData.hc1.getValue());
            case 17:
               return TaskData.getZxInOld();
            case 18:
               return AchData.rcTask.getValue();
            case 20:
               return Main.P1P2 ? Number(this.bjDx(AchData.strOk1.getValue(),AchData.strok2.getValue())) : Number(AchData.strOk1.getValue());
            case 21:
               return Main.P1P2 ? Number(this.bjDx(AchData.strLost1.getValue(),AchData.strLost2.getValue())) : Number(AchData.strLost1.getValue());
            case 22:
               return Main.P1P2 ? Number(this.bjDx(param1.getStrEquipNum(Main.player1),param1.getStrEquipNum(Main.player2))) : param1.getStrEquipNum(Main.player1);
            case 23:
               return Main.P1P2 ? Number(this.bjDx(Main.player1.getEquipSlot().getSuitStrength(),Main.player2.getEquipSlot().getSuitStrength())) : Number(Main.player1.getEquipSlot().getSuitStrength());
            case 24:
               return param1.getCwMaxLeve();
            case 31:
               return param1.getGkStar();
            case 33:
               return param1.getTgCs();
            case 34:
               return AchData.tgTime.getValue();
            case 44:
               return Main.P1P2 ? Number(this.bjDx(param1.getGoodsNum(1),param1.getGoodsNum(2))) : param1.getGoodsNum(1);
            case 45:
               return param1.getEnemyed();
            case 46:
               return PK_UI.whoNum;
            default:
               return;
         }
      }
      
      private function bjDx(param1:Number, param2:Number = 0) : Number
      {
         if(param1 <= param2)
         {
            return param2;
         }
         return param1;
      }
      
      private function getNumText(param1:Number) : void
      {
         var _loc2_:Array = [];
         if(param1 == 0)
         {
            _loc2_ = AchData.getEdAc();
         }
         else if(param1 == 1)
         {
            _loc2_ = AchData.getGaAc();
         }
         if(_loc2_ != null)
         {
            this.allAcText.text = String(_loc2_.length);
         }
         else
         {
            this.allAcText.text = String(0);
         }
      }
      
      private function getPlint() : void
      {
         this.evPoint.text = String(AchData.cjPoint_1.getValue());
         this.gamePoint.text = String(AchData.cjPoint_2.getValue());
      }
      
      private function btnStata(param1:Number, param2:Number, param3:String) : void
      {
         this[param3 + param1].isClick = true;
         var _loc4_:Number = 0;
         while(_loc4_ < param2)
         {
            if(param1 != _loc4_)
            {
               this[param3 + _loc4_].isClick = false;
            }
            _loc4_++;
         }
      }
   }
}

