package src
{
   import com.*;
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.pet.Pet;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   public class ChongWu extends MovieClip
   {
      public static var dataXml:XML;
      
      public static var loadData:ClassLoader;
      
      public static var loadData2:ClassLoader;
      
      public static var chongWu_Data:Array = new Array();
      
      public static var jiaZaiOK:Array = [];
      
      public static var gongJiYn1:Boolean = false;
      
      public static var gongJiYn2:Boolean = false;
      
      public var who:Player;
      
      public var data:Pet;
      
      public var food:VT = VT.createVT(100);
      
      public var RL:Boolean = true;
      
      private var distance_X:int = 0;
      
      private var distanceMax:int = 1200;
      
      private var walk_power:VT = VT.createVT(7);
      
      private var gravity:int = 20;
      
      public var skin:MovieClip;
      
      public var runType:String = "站立";
      
      public var runType2:String = "站立";
      
      public var continuous:Boolean;
      
      public var noMove:Boolean = false;
      
      public var MoveYn:Boolean = false;
      
      public var gongJiEnemy:Enemy;
      
      public var goEnemy:Boolean = false;
      
      public var goJiCD_max:Array = [];
      
      public var goJiCD_now:Array = [];
      
      public var nullTime:int = 0;
      
      public var cdOK_Arr:Array = [];
      
      public var typeClass:int;
      
      public var noLoad:Boolean = false;
      
      private var wudiTime:int = 0;
      
      public var riyanTime:int = 0;
      
      public var riyanAdd:int = 0;
      
      public var tempTT:int = 0;
      
      public var tempTTT:int = 0;
      
      public var nai_C:Class = NewLoad.XiaoGuoData.getClass("Nai") as Class;
      
      public var naizui_C:Class = NewLoad.XiaoGuoData.getClass("Naizui") as Class;
      
      public var fire_C:Class = NewLoad.XiaoGuoData.getClass("Fire") as Class;
      
      public var nai:MovieClip = new this.nai_C();
      
      public var naizui:MovieClip = new this.naizui_C();
      
      public var fire:MovieClip = new this.fire_C();
      
      public function ChongWu(param1:Pet, param2:Player)
      {
         super();
         this.who = param2;
         this.who.data.playerCW_Data = this.data = param1;
         this.who.playerCW = this;
         this.goJiCD_max = this.data.getPetCD();
         this.goJiCD_now = DeepCopyUtil.clone(this.goJiCD_max);
         this.goEnemy = this.who.data.getPetSlot().getMode();
         this.addSkin();
      }
      
      public static function get_XML_data() : *
      {
         dataXml = XMLAsset.createXML(Data2.petHit_data);
      }
      
      public function Close() : *
      {
         this.noLoad = true;
         TiaoShi.txtShow("关闭宠物");
         if(this.skin)
         {
            this.skin.gotoAndStop(1);
            this.parent.removeChild(this);
         }
         removeEventListener(Event.ENTER_FRAME,this.riyan);
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.who.playerCW = null;
         this.who.data.playerCW_Data = null;
      }
      
      private function LoadCWGo(param1:int, param2:String) : *
      {
         chongWu_Data[param1] = new ClassLoader(param2);
         if(NewPetPanel.petPanel)
         {
            NewPetPanel.petPanel["touming"].visible = true;
         }
         chongWu_Data[param1].addEventListener(Event.COMPLETE,this.LoadCWEnd);
      }
      
      private function LoadCWEnd(param1:*) : *
      {
         var _loc2_:int = int(PetFactory.getClassName(this.data._id.getValue()));
         jiaZaiOK[_loc2_] = true;
         if(NewPetPanel.petPanel)
         {
            NewPetPanel.petPanel["touming"].visible = false;
         }
         this.addSkin();
      }
      
      private function addSkin() : *
      {
         var _loc2_:String = null;
         var _loc4_:Class = null;
         var _loc1_:int = int(PetFactory.getClassName(this.data._id.getValue()));
         _loc2_ = _loc1_;
         this.typeClass = int(_loc2_);
         if(_loc1_ == 4)
         {
            _loc2_ = "4_v942";
         }
         else if(_loc1_ == 18)
         {
            _loc2_ = "18_v2";
         }
         else if(_loc1_ == 20)
         {
            _loc2_ = "20_v842";
         }
         else if(_loc1_ == 14)
         {
            _loc2_ = "14_v941";
         }
         else if(_loc1_ == 22)
         {
            _loc2_ = "22_v900";
         }
         else if(_loc1_ == 29)
         {
            _loc2_ = "29_v1041";
         }
         else if(_loc1_ == 31)
         {
            _loc2_ = "31_v1051";
         }
         var _loc3_:* = "CW" + _loc2_ + ".swf";
         _loc2_ = "CW" + _loc1_;
         if(!chongWu_Data[_loc1_])
         {
            this.LoadCWGo(_loc1_,_loc3_);
            return;
         }
         if(!jiaZaiOK[_loc1_])
         {
            return;
         }
         if(this.noLoad)
         {
            return;
         }
         _loc4_ = ChongWu.chongWu_Data[_loc1_].getClass(_loc2_) as Class;
         this.skin = new _loc4_();
         addChild(this.skin);
         this.y = 500;
         this.who.data.playerCW_Data = this.data;
         this.who.playerCW = this;
         Main.world.moveChild_ChongWu.addChild(this);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.SearchPlayer();
         mouseEnabled = false;
         mouseChildren = false;
         return true;
      }
      
      private function onENTER_FRAME(param1:*) : *
      {
         if(this.noLoad)
         {
            this.Close();
            return;
         }
         this.GongJi();
         if(!this.noMove)
         {
            this.WhereAreYou();
         }
         this.GoToPlay();
         this.WhoDead();
      }
      
      private function WhoDead() : *
      {
         if(!this.who || this.who.hp.getValue() <= 0)
         {
            this.Close();
         }
      }
      
      private function WhereAreYou() : *
      {
         var _loc1_:int = 0;
         var _loc2_:* = 0;
         if(this.parent != Main.world.moveChild_ChongWu)
         {
            Main.world.moveChild_ChongWu.addChild(this);
            this.SearchPlayer();
         }
         if(this.goEnemy && Enemy.All.length > 0)
         {
            if(!this.gongJiEnemy)
            {
               this.gongJiEnemy = Enemy.All[0];
               this.distance_X = this.x - this.gongJiEnemy.x;
               _loc1_ = 1;
               while(_loc1_ < Enemy.All.length)
               {
                  _loc2_ = this.x - Enemy.All[_loc1_].x;
                  if(Math.abs(this.distance_X) > Math.abs(_loc2_))
                  {
                     this.distance_X = _loc2_;
                  }
                  _loc1_++;
               }
            }
            else
            {
               this.distance_X = this.x - this.gongJiEnemy.x;
               if(this.gongJiEnemy.life.getValue() <= 0)
               {
                  this.gongJiEnemy = null;
               }
            }
         }
         else
         {
            this.distance_X = this.x - this.who.x;
            distance_Y = this.y - this.who.y;
            if(Math.abs(this.distance_X) > this.distanceMax)
            {
               this.SearchPlayer();
            }
            this.gongJiEnemy = null;
         }
         this.MoveRun();
      }
      
      private function SearchPlayer() : *
      {
         this.x = this.who.x + Math.random() * 200 - 100;
         this.y = this.who.y - 100;
      }
      
      private function MoveRun() : *
      {
         var _loc3_:int = 0;
         var _loc7_:Boolean = false;
         var _loc8_:Boolean = false;
         var _loc9_:Boolean = false;
         var _loc1_:int = this.x + Main.world.x;
         var _loc2_:int = this.y;
         _loc3_ = int(this.gravity);
         while(_loc3_ > 0)
         {
            _loc7_ = Boolean(Main.world.MapData.hitTestPoint(_loc1_,_loc2_ - 3,true));
            _loc8_ = Boolean(Main.world.MapData.hitTestPoint(_loc1_,_loc2_ - 1,true));
            _loc9_ = Boolean(Main.world.MapData.hitTestPoint(_loc1_,_loc2_ + 6,true));
            if(_loc7_)
            {
               _loc2_ -= 2;
            }
            else if(_loc8_)
            {
               runY = 0;
            }
            else if(_loc9_)
            {
               _loc2_ += 3;
            }
            else
            {
               _loc2_++;
            }
            _loc3_--;
         }
         this.y = _loc2_;
         var _loc4_:int = this.data._id.getValue();
         var _loc5_:int = 100;
         if(_loc4_ == 73117 || _loc4_ == 73118)
         {
            this.walk_power.setValue(12);
            _loc5_ = 300;
         }
         _loc3_ = Math.abs(this.walk_power.getValue());
         while(_loc3_ > 0)
         {
            _loc8_ = Boolean(Main.world.MapData1.hitTestPoint(_loc1_,_loc2_ - 30,true));
            if(!_loc8_)
            {
               if(this.distance_X > 5)
               {
                  this.getRL(false);
               }
               else if(this.distance_X < -5)
               {
                  this.getRL(true);
               }
               if(this.gongJiEnemy && this.distance_X > _loc5_ || this.distance_X > 200)
               {
                  _loc1_--;
                  this.GoTo("移动",true);
               }
               else if(this.gongJiEnemy && this.distance_X < -_loc5_ || this.distance_X < -200)
               {
                  _loc1_++;
                  this.GoTo("移动",true);
               }
               else
               {
                  this.GoTo("站立");
               }
            }
            _loc3_--;
         }
         var _loc6_:int = _loc1_ - Main.world.x;
         if(_loc6_ > Main.world._width + 100)
         {
            this.x = Main.world._width + 100;
         }
         else if(_loc6_ < -100)
         {
            this.x = -100;
         }
         else
         {
            this.x = _loc6_;
         }
      }
      
      public function getRL(param1:Boolean) : *
      {
         if(this.skin.currentLabel != "移动")
         {
            return;
         }
         this.RL = param1;
         if(param1)
         {
            scaleX = -1;
         }
         else if(!param1)
         {
            scaleX = 1;
         }
      }
      
      private function GoToPlay() : *
      {
         if(this.isRunOver())
         {
            if(this.runType2 != "攻击")
            {
               this.skin.gotoAndPlay(this.runType);
            }
            else
            {
               this.GoTo("站立");
            }
         }
      }
      
      public function isRunOver() : Boolean
      {
         if(this.skin.currentLabel != this.runType)
         {
            this.noMove = false;
            return true;
         }
         return false;
      }
      
      public function riyan(param1:*) : *
      {
         ++this.tempTT;
         --this.riyanTime;
         if(this.tempTT > 27)
         {
            for(i in Enemy.All)
            {
               if(Enemy.All[i])
               {
                  if(Math.abs(Enemy.All[i].x - this.x) < 200)
                  {
                     Enemy.All[i].hpCount(this.riyanAdd);
                     HPdown.Open(this.riyanAdd,Enemy.All[i].x,Enemy.All[i].y - Enemy.All[i].height);
                  }
               }
            }
            this.tempTT = 0;
         }
         if(this.riyanTime <= 0)
         {
            this.removeChild(this.fire);
            removeEventListener(Event.ENTER_FRAME,this.riyan);
         }
      }
      
      private function goWuDiMC(param1:*) : *
      {
         ++this.tempTTT;
         if(this.tempTTT >= 24)
         {
            this.nai.parent.removeChild(this.nai);
            this.tempTTT = 0;
            this.who.removeEventListener(Event.ENTER_FRAME,this.goWuDiMC);
         }
      }
      
      private function goWuDi(param1:*) : *
      {
         --this.wudiTime;
         if(this.who.skin.currentLabel == "站")
         {
            this.who.rexuewudi = true;
            if(this.wudiTime % 10 > 4)
            {
               this.who.alpha = 1;
            }
            else
            {
               this.who.alpha = 0.7;
            }
         }
         else
         {
            this.who.rexuewudi = false;
            this.who.alpha = 1;
         }
         if(this.wudiTime < 0)
         {
            this.naizui.parent.removeChild(this.naizui);
            this.who.rexuewudi = false;
            this.who.alpha = 1;
            this.who.removeEventListener(Event.ENTER_FRAME,this.goWuDi);
         }
      }
      
      private function GongJi() : *
      {
         if(this.nullTime > 0)
         {
            --this.nullTime;
         }
         this.cdOK_Arr = new Array();
         var _loc1_:int = 1;
         while(_loc1_ < 6)
         {
            if(this.goJiCD_now[_loc1_] > 0)
            {
               --this.goJiCD_now[_loc1_];
            }
            else
            {
               this.cdOK_Arr[_loc1_] = true;
            }
            _loc1_++;
         }
         if(this.gongJiEnemy || this.who == Main.player_1 && gongJiYn1 || Main.player_2 && this.who == Main.player_2 && gongJiYn2)
         {
            if(this.nullTime <= 0 && this.cdOK_Arr.length > 0)
            {
               this.GoTo("攻击",true);
            }
         }
         var _loc2_:int = int(PetFactory.getClassName(this.data._id.getValue()));
         if(_loc2_ == 20)
         {
            this.CW20_GongJi();
         }
         if(_loc2_ == 24)
         {
            this.CW24_GongJi();
         }
         if(_loc2_ == 26)
         {
            this.CW26_GongJi();
         }
      }
      
      public function CW26_GongJi() : *
      {
         if(this.skin.currentFrame >= 60 && this.skin.currentFrame <= 64 || this.skin.currentFrame >= 71 && this.skin.currentFrame <= 74 || this.skin.currentFrame >= 83 && this.skin.currentFrame <= 88)
         {
            if(this.RL)
            {
               this.x += 28;
            }
            else
            {
               this.x -= 28;
            }
            return;
         }
         if(this.skin.currentFrame >= 124 && this.skin.currentFrame <= 170)
         {
            if(this.RL)
            {
               this.x += 15;
            }
            else
            {
               this.x -= 15;
            }
            return;
         }
      }
      
      public function CW20_GongJi() : *
      {
         if(this.skin.currentFrame >= 522 && this.skin.currentFrame <= 536)
         {
            if(this.RL)
            {
               this.x += 48;
            }
            else
            {
               this.x -= 48;
            }
            return;
         }
      }
      
      public function CW24_GongJi() : *
      {
         if(this.skin.currentFrame >= 524 && this.skin.currentFrame <= 584)
         {
            if(this.RL)
            {
               this.x += 7;
            }
            else
            {
               this.x -= 7;
            }
         }
         if(this.skin.currentFrame >= 607 && this.skin.currentFrame <= 711)
         {
            if(this.RL)
            {
               this.x += 6;
            }
            else
            {
               this.x -= 6;
            }
         }
      }
      
      public function GoTo(param1:String, param2:Boolean = false) : *
      {
         var _loc4_:int = 0;
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc8_:HitXX = null;
         if(this.noMove)
         {
            return;
         }
         var _loc3_:Number = 1;
         if(this.data._id.getValue() == 73105)
         {
            _loc3_ = 5;
         }
         if(this.data._id.getValue() == 73108)
         {
            _loc3_ = 4;
         }
         if(this.data._id.getValue() == 73118)
         {
            _loc3_ = 2;
         }
         var _loc5_:String = param1;
         if(param1 == "站立")
         {
            _loc5_ = "站立" + int(Math.random() * _loc3_ + 1);
         }
         else if(param1 == "攻击")
         {
            _loc6_ = this.data.getPetSkillType();
            _loc7_ = int(Math.random() * 4);
            _loc4_ = int(_loc6_[_loc7_]);
            if(!(_loc4_ > 0 && Boolean(this.cdOK_Arr[_loc4_])))
            {
               if(_loc4_ <= 0)
               {
                  this.nullTime = 270;
                  if(this.who == Main.player_1)
                  {
                     gongJiYn1 = false;
                  }
                  else
                  {
                     gongJiYn2 = false;
                  }
               }
               return;
            }
            _loc5_ = "攻击" + _loc4_;
            if(this.data.getPetEquip())
            {
               if(this.data.getPetEquip().getType() == 7)
               {
                  _loc6_ = this.data.getPetEquip().getAffect();
                  this.riyanAdd = this.who.use_gongji.getValue() * _loc6_[0];
                  this.riyanTime = _loc6_[2];
                  this.who.playerCW.addChild(this.fire);
                  addEventListener(Event.ENTER_FRAME,this.riyan);
               }
               if(this.data.getPetEquip().getType() == 6)
               {
                  _loc6_ = this.data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc6_[1])
                  {
                     this.wudiTime = _loc6_[0];
                     this.who.addChild(this.naizui);
                     this.who.addChild(this.nai);
                     this.who.addEventListener(Event.ENTER_FRAME,this.goWuDi);
                     this.who.addEventListener(Event.ENTER_FRAME,this.goWuDiMC);
                  }
               }
            }
         }
         if(this.runType != _loc5_ && (this.isRunOver() || param2))
         {
            this.runType = _loc5_;
            this.runType2 = param1;
            if(param1 == "攻击")
            {
               if(this.data._id.getValue() == 73107 && _loc5_ == "攻击4")
               {
                  _loc8_ = new HitXX();
                  _loc8_.type = 100;
                  _loc8_.totalTime = 324;
                  new BuffEffect(_loc8_,this.who);
               }
               this.noMove = true;
               this.nullTime = this.goJiCD_now[_loc4_] = this.goJiCD_max[_loc4_];
               this.XXCD(_loc4_);
               if(this.who == Main.player_1)
               {
                  gongJiYn1 = false;
               }
               else
               {
                  gongJiYn2 = false;
               }
            }
            if(param1 == "站立")
            {
               this.skin.gotoAndStop(this.runType);
            }
            else
            {
               this.skin.gotoAndPlay(this.runType);
            }
         }
      }
      
      public function XXCD(param1:int) : *
      {
         var _loc2_:Array = null;
         if(this.data.getPetEquip())
         {
            if(this.data.getPetEquip().getType() == 14 && Enemy.wscd > 135)
            {
               Enemy.wscd = 0;
               _loc2_ = this.data.getPetEquip().getAffect();
               this.goJiCD_now[param1] = 0;
            }
         }
      }
   }
}

