package src.tool
{
   public class ShengDan2013
   {
      public function ShengDan2013()
      {
         super();
      }
      
      public static function WQ<PERSON><PERSON><PERSON>(param1:int, param2:int) : int
      {
         var _loc3_:int = 0;
         if(param1 == 14667)
         {
            if(param2 >= 0 && param2 < 5)
            {
               _loc3_ = 47;
            }
            if(param2 >= 5 && param2 < 10)
            {
               _loc3_ = 53;
            }
            else if(param2 >= 10 && param2 < 15)
            {
               _loc3_ = 62;
            }
            else if(param2 >= 15 && param2 < 20)
            {
               _loc3_ = 72;
            }
            else if(param2 >= 20 && param2 < 25)
            {
               _loc3_ = 84;
            }
            else if(param2 >= 25 && param2 < 30)
            {
               _loc3_ = 98;
            }
            else if(param2 >= 30 && param2 < 35)
            {
               _loc3_ = 114;
            }
            else if(param2 >= 35 && param2 < 40)
            {
               _loc3_ = 133;
            }
            else if(param2 >= 40 && param2 < 45)
            {
               _loc3_ = 155;
            }
            else if(param2 >= 45 && param2 < 50)
            {
               _loc3_ = 181;
            }
            else if(param2 >= 50 && param2 < 55)
            {
               _loc3_ = 212;
            }
            else if(param2 >= 55 && param2 < 60)
            {
               _loc3_ = 248;
            }
            else if(param2 >= 60 && param2 < 65)
            {
               _loc3_ = 289;
            }
            else if(param2 >= 65 && param2 < 70)
            {
               _loc3_ = 335;
            }
            else if(param2 >= 70 && param2 < 75)
            {
               _loc3_ = 386;
            }
            else if(param2 >= 75 && param2 < 80)
            {
               _loc3_ = 442;
            }
            else if(param2 >= 80 && param2 < 85)
            {
               _loc3_ = 503;
            }
            else if(param2 >= 85 && param2 < 90)
            {
               _loc3_ = 569;
            }
            else if(param2 >= 90 && param2 < 95)
            {
               _loc3_ = 640;
            }
            else if(param2 >= 95 && param2 < 100)
            {
               _loc3_ = 716;
            }
         }
         if(param1 == 14668)
         {
            if(param2 >= 0 && param2 < 5)
            {
               _loc3_ = 56;
            }
            if(param2 >= 5 && param2 < 10)
            {
               _loc3_ = 62;
            }
            else if(param2 >= 10 && param2 < 15)
            {
               _loc3_ = 73;
            }
            else if(param2 >= 15 && param2 < 20)
            {
               _loc3_ = 84;
            }
            else if(param2 >= 20 && param2 < 25)
            {
               _loc3_ = 99;
            }
            else if(param2 >= 25 && param2 < 30)
            {
               _loc3_ = 115;
            }
            else if(param2 >= 30 && param2 < 35)
            {
               _loc3_ = 134;
            }
            else if(param2 >= 35 && param2 < 40)
            {
               _loc3_ = 156;
            }
            else if(param2 >= 40 && param2 < 45)
            {
               _loc3_ = 182;
            }
            else if(param2 >= 45 && param2 < 50)
            {
               _loc3_ = 212;
            }
            else if(param2 >= 50 && param2 < 55)
            {
               _loc3_ = 249;
            }
            else if(param2 >= 55 && param2 < 60)
            {
               _loc3_ = 291;
            }
            else if(param2 >= 60 && param2 < 65)
            {
               _loc3_ = 339;
            }
            else if(param2 >= 65 && param2 < 70)
            {
               _loc3_ = 393;
            }
            else if(param2 >= 70 && param2 < 75)
            {
               _loc3_ = 453;
            }
            else if(param2 >= 75 && param2 < 80)
            {
               _loc3_ = 519;
            }
            else if(param2 >= 80 && param2 < 85)
            {
               _loc3_ = 590;
            }
            else if(param2 >= 85 && param2 < 90)
            {
               _loc3_ = 668;
            }
            else if(param2 >= 90 && param2 < 95)
            {
               _loc3_ = 751;
            }
            else if(param2 >= 95 && param2 < 100)
            {
               _loc3_ = 840;
            }
         }
         if(param1 == 14669)
         {
            if(param2 >= 0 && param2 < 5)
            {
               _loc3_ = 43;
            }
            if(param2 >= 5 && param2 < 10)
            {
               _loc3_ = 49;
            }
            else if(param2 >= 10 && param2 < 15)
            {
               _loc3_ = 57;
            }
            else if(param2 >= 15 && param2 < 20)
            {
               _loc3_ = 66;
            }
            else if(param2 >= 20 && param2 < 25)
            {
               _loc3_ = 77;
            }
            else if(param2 >= 25 && param2 < 30)
            {
               _loc3_ = 90;
            }
            else if(param2 >= 30 && param2 < 35)
            {
               _loc3_ = 105;
            }
            else if(param2 >= 35 && param2 < 40)
            {
               _loc3_ = 122;
            }
            else if(param2 >= 40 && param2 < 45)
            {
               _loc3_ = 143;
            }
            else if(param2 >= 45 && param2 < 50)
            {
               _loc3_ = 167;
            }
            else if(param2 >= 50 && param2 < 55)
            {
               _loc3_ = 195;
            }
            else if(param2 >= 55 && param2 < 60)
            {
               _loc3_ = 228;
            }
            else if(param2 >= 60 && param2 < 65)
            {
               _loc3_ = 266;
            }
            else if(param2 >= 65 && param2 < 70)
            {
               _loc3_ = 308;
            }
            else if(param2 >= 70 && param2 < 75)
            {
               _loc3_ = 355;
            }
            else if(param2 >= 75 && param2 < 80)
            {
               _loc3_ = 407;
            }
            else if(param2 >= 80 && param2 < 85)
            {
               _loc3_ = 463;
            }
            else if(param2 >= 85 && param2 < 90)
            {
               _loc3_ = 523;
            }
            else if(param2 >= 90 && param2 < 95)
            {
               _loc3_ = 589;
            }
            else if(param2 >= 95 && param2 < 100)
            {
               _loc3_ = 659;
            }
         }
         return _loc3_;
      }
      
      public static function WQwhite(param1:int, param2:int) : int
      {
         var _loc3_:int = 0;
         if(param1 == 14667)
         {
            if(param2 >= 0 && param2 < 5)
            {
               _loc3_ = 47;
            }
            if(param2 >= 5 && param2 < 10)
            {
               _loc3_ = 53;
            }
            else if(param2 >= 10 && param2 < 15)
            {
               _loc3_ = 62;
            }
            else if(param2 >= 15 && param2 < 20)
            {
               _loc3_ = 72;
            }
            else if(param2 >= 20 && param2 < 25)
            {
               _loc3_ = 84;
            }
            else if(param2 >= 25 && param2 < 30)
            {
               _loc3_ = 98;
            }
            else if(param2 >= 30 && param2 < 35)
            {
               _loc3_ = 114;
            }
            else if(param2 >= 35 && param2 < 40)
            {
               _loc3_ = 133;
            }
            else if(param2 >= 40 && param2 < 45)
            {
               _loc3_ = 155;
            }
            else if(param2 >= 45 && param2 < 50)
            {
               _loc3_ = 181;
            }
            else if(param2 >= 50 && param2 < 55)
            {
               _loc3_ = 212;
            }
            else if(param2 >= 55 && param2 < 60)
            {
               _loc3_ = 248;
            }
            else if(param2 >= 60 && param2 < 65)
            {
               _loc3_ = 289;
            }
            else if(param2 >= 65 && param2 < 70)
            {
               _loc3_ = 335;
            }
            else if(param2 >= 70 && param2 < 75)
            {
               _loc3_ = 386;
            }
            else if(param2 >= 75 && param2 < 80)
            {
               _loc3_ = 442;
            }
            else if(param2 >= 80 && param2 < 85)
            {
               _loc3_ = 503;
            }
            else if(param2 >= 85 && param2 < 90)
            {
               _loc3_ = 569;
            }
            else if(param2 >= 90 && param2 < 95)
            {
               _loc3_ = 640;
            }
            else if(param2 >= 95 && param2 < 100)
            {
               _loc3_ = 716;
            }
            return _loc3_ - 47;
         }
         if(param1 == 14668)
         {
            if(param2 >= 0 && param2 < 5)
            {
               _loc3_ = 56;
            }
            if(param2 >= 5 && param2 < 10)
            {
               _loc3_ = 62;
            }
            else if(param2 >= 10 && param2 < 15)
            {
               _loc3_ = 73;
            }
            else if(param2 >= 15 && param2 < 20)
            {
               _loc3_ = 84;
            }
            else if(param2 >= 20 && param2 < 25)
            {
               _loc3_ = 99;
            }
            else if(param2 >= 25 && param2 < 30)
            {
               _loc3_ = 115;
            }
            else if(param2 >= 30 && param2 < 35)
            {
               _loc3_ = 134;
            }
            else if(param2 >= 35 && param2 < 40)
            {
               _loc3_ = 156;
            }
            else if(param2 >= 40 && param2 < 45)
            {
               _loc3_ = 182;
            }
            else if(param2 >= 45 && param2 < 50)
            {
               _loc3_ = 212;
            }
            else if(param2 >= 50 && param2 < 55)
            {
               _loc3_ = 249;
            }
            else if(param2 >= 55 && param2 < 60)
            {
               _loc3_ = 291;
            }
            else if(param2 >= 60 && param2 < 65)
            {
               _loc3_ = 339;
            }
            else if(param2 >= 65 && param2 < 70)
            {
               _loc3_ = 393;
            }
            else if(param2 >= 70 && param2 < 75)
            {
               _loc3_ = 453;
            }
            else if(param2 >= 75 && param2 < 80)
            {
               _loc3_ = 519;
            }
            else if(param2 >= 80 && param2 < 85)
            {
               _loc3_ = 590;
            }
            else if(param2 >= 85 && param2 < 90)
            {
               _loc3_ = 668;
            }
            else if(param2 >= 90 && param2 < 95)
            {
               _loc3_ = 751;
            }
            else if(param2 >= 95 && param2 < 100)
            {
               _loc3_ = 840;
            }
            return _loc3_ - 56;
         }
         if(param1 == 14669)
         {
            if(param2 >= 0 && param2 < 5)
            {
               _loc3_ = 43;
            }
            if(param2 >= 5 && param2 < 10)
            {
               _loc3_ = 49;
            }
            else if(param2 >= 10 && param2 < 15)
            {
               _loc3_ = 57;
            }
            else if(param2 >= 15 && param2 < 20)
            {
               _loc3_ = 66;
            }
            else if(param2 >= 20 && param2 < 25)
            {
               _loc3_ = 77;
            }
            else if(param2 >= 25 && param2 < 30)
            {
               _loc3_ = 90;
            }
            else if(param2 >= 30 && param2 < 35)
            {
               _loc3_ = 105;
            }
            else if(param2 >= 35 && param2 < 40)
            {
               _loc3_ = 122;
            }
            else if(param2 >= 40 && param2 < 45)
            {
               _loc3_ = 143;
            }
            else if(param2 >= 45 && param2 < 50)
            {
               _loc3_ = 167;
            }
            else if(param2 >= 50 && param2 < 55)
            {
               _loc3_ = 195;
            }
            else if(param2 >= 55 && param2 < 60)
            {
               _loc3_ = 228;
            }
            else if(param2 >= 60 && param2 < 65)
            {
               _loc3_ = 266;
            }
            else if(param2 >= 65 && param2 < 70)
            {
               _loc3_ = 308;
            }
            else if(param2 >= 70 && param2 < 75)
            {
               _loc3_ = 355;
            }
            else if(param2 >= 75 && param2 < 80)
            {
               _loc3_ = 407;
            }
            else if(param2 >= 80 && param2 < 85)
            {
               _loc3_ = 463;
            }
            else if(param2 >= 85 && param2 < 90)
            {
               _loc3_ = 523;
            }
            else if(param2 >= 90 && param2 < 95)
            {
               _loc3_ = 589;
            }
            else if(param2 >= 95 && param2 < 100)
            {
               _loc3_ = 659;
            }
            return _loc3_ - 43;
         }
         return undefined;
      }
   }
}

