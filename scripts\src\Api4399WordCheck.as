package src
{
   import com.adobe.serialization.json.JSONs;
   import open4399Tools.*;
   import open4399Tools.events.*;
   
   public class Api4399WordCheck
   {
      private var open4399ToolsApi:Open4399ToolsApi;
      
      private var initOk:int = 0;
      
      private var checkCall:Function;
      
      public function Api4399WordCheck()
      {
         super();
         this.open4399ToolsApi = Open4399ToolsApi.getInstance();
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.SERVICE_INIT,this.onServiceInitComplete);
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS_ERROR,this.onCheckBadWordsError);
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS,this.onCheckBadWords);
         this.open4399ToolsApi.init();
      }
      
      private function onServiceInitComplete(param1:Open4399ToolsEvent) : void
      {
         this.initOk = 1;
      }
      
      private function onCheckBadWordsError(param1:Open4399ToolsEvent) : void
      {
      }
      
      public function checkWord(param1:String, param2:Function) : Boolean
      {
         if(this.initOk == 1)
         {
            this.checkCall = param2;
            this.open4399ToolsApi.checkBadWords(param1);
            return true;
         }
         return false;
      }
      
      private function onCheckBadWords(param1:Open4399ToolsEvent) : void
      {
         var _loc3_:Function = null;
         var _loc2_:Object = JSONs.decode(String(param1.data));
         if(this.checkCall != null)
         {
            _loc3_ = this.checkCall;
            this.checkCall = null;
            if(_loc2_.code == "10000")
            {
               _loc3_(0);
            }
            else
            {
               _loc3_(1);
            }
         }
      }
   }
}

