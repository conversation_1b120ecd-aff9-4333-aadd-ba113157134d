package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class GongHui_jiTan extends MovieClip
   {
      public static var jiTan:MovieClip;
      
      public static var jiTanLv_arr:Array = [false,0,0,0,0];
      
      public static var sel_num:int = 1;
      
      public static var sel_max:int = 4;
      
      public static var page:int = 1;
      
      public static var page_max:int = 2;
      
      public static var jiTanExp_arr:Array = [false,[850,3000,6000,12000,17000],[1700,8500,13000,18000,24000],[4500,10000,15000,25000,32000],[7000,11500,18000,24900,35000]];
      
      public static var jiTanNum_arr:Array = [false,[0,10,15,20,25,30],[0,10,15,20,25,30],[0,4,8,12,16,20],[0,2,4,6,8,10]];
      
      public static var sj_arr:Array = [false,[1000,2000,4000,6000,8000],[1000,3000,6000,9000,12000],[4000,6000,9000,12000,15000],[5000,8000,11000,14000,18000]];
      
      public function GongHui_jiTan()
      {
         super();
      }
      
      private static function onClose(param1:MouseEvent) : *
      {
         var _loc3_:Array = null;
         var _loc2_:MovieClip = param1.target.parent;
         _loc2_.visible = false;
         if(_loc2_ == jiTan)
         {
            _loc3_ = [157,158,159,160,161,162,163];
            Api_4399_GongHui.getNum(_loc3_);
         }
      }
      
      public static function Open(param1:MouseEvent) : *
      {
         GongHui_Interface._this.addChild(jiTan);
         jiTan.visible = true;
         jiTan.x = jiTan.y = 0;
         jiTan._info_mc.y = -5000;
         jiTan._info_mc.mouseChildren = jiTan._info_mc.mouseEnabled = false;
         jiTan.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         jiTan.back_btn.addEventListener(MouseEvent.CLICK,Back_Fun);
         jiTan.next_btn.addEventListener(MouseEvent.CLICK,Next_Fun);
         jiTan.x1_mc.sj_btn.addEventListener(MouseEvent.CLICK,SengJi);
         jiTan.x2_mc.sj_btn.addEventListener(MouseEvent.CLICK,SengJi);
         jiTan.x3_mc.sj_btn.addEventListener(MouseEvent.CLICK,SengJi);
         jiTan.x1_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_MOVE,onMOUSE_MOVE);
         jiTan.x2_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_MOVE,onMOUSE_MOVE);
         jiTan.x3_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_MOVE,onMOUSE_MOVE);
         jiTan.x1_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_OUT,onMOUSE_OUT);
         jiTan.x2_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_OUT,onMOUSE_OUT);
         jiTan.x3_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_OUT,onMOUSE_OUT);
         Show();
      }
      
      public static function Show() : *
      {
         var _loc2_:MovieClip = null;
         var _loc3_:MovieClip = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         jiTan._info_mc.y = -5000;
         var _loc1_:int = 1;
         while(_loc1_ < 4)
         {
            _loc2_ = jiTan["x" + _loc1_ + "_mc"];
            _loc3_ = jiTan["xx" + _loc1_ + "_mc"];
            _loc4_ = (page - 1) * 3 + _loc1_;
            _loc5_ = GongHui_Interface.playerInfo.unionInfo.contribution / 10;
            _loc6_ = GongHui_Interface.playerInfo.unionInfo.experience / 10;
            if(_loc4_ > sel_max)
            {
               _loc2_.visible = _loc3_.visible = false;
            }
            else
            {
               _loc7_ = int(jiTanLv_arr[_loc4_]);
               _loc2_.sj_btn.visible = true;
               if(_loc7_ >= 5)
               {
                  _loc2_.sj_btn.visible = false;
               }
               _loc8_ = jiTanExp_arr[_loc4_][_loc7_] / 10;
               _loc2_.visible = _loc3_.visible = true;
               _loc2_.gotoAndStop(_loc4_);
               _loc2_.lv_txt.text = "lv." + _loc7_;
               if(_loc7_ < 5)
               {
                  _loc9_ = sj_arr[_loc4_][_loc7_] / 10;
                  _loc2_.t1_txt.text = _loc3_.t1_txt.text = "公会经验达到" + _loc8_ + "开启";
                  _loc2_.t2_txt.text = "升级消耗贡献值:" + _loc9_;
                  if(_loc7_ == 0)
                  {
                     _loc2_.lv_txt.text = "未学习";
                     if(_loc8_ > _loc6_)
                     {
                        _loc2_.visible = false;
                     }
                  }
               }
               else
               {
                  _loc2_.t1_txt.text = "该技能已升至最高级";
                  _loc2_.t2_txt.text = "";
               }
            }
            _loc1_++;
         }
         jiTan.gx_txt.text = GongHui_Interface.playerInfo.unionInfo.contribution / 10;
      }
      
      public static function SengJi(param1:MouseEvent) : *
      {
         if(GongHui_Interface.playerInfo.unionInfo.uId != GongHui_Interface.playerInfo.member.uId && GongHui_Interface.playerInfo.member.roleId != "10")
         {
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"只有会长及副会长拥有该权限!");
            return;
         }
         var _loc2_:int = int((param1.target.parent.name as String).substr(1,1));
         var _loc3_:int = (page - 1) * 3 + _loc2_;
         var _loc4_:int = int(jiTanLv_arr[_loc3_]);
         var _loc5_:int = int(sj_arr[_loc3_][_loc4_]);
         var _loc6_:int = int(jiTanExp_arr[_loc3_][_loc4_]);
         var _loc7_:int = int(GongHui_Interface.playerInfo.unionInfo.contribution);
         var _loc8_:int = int(GongHui_Interface.playerInfo.unionInfo.experience);
         var _loc9_:int = 159 + _loc3_;
         TiaoShi.txtShow("升级所需贡献 = " + _loc5_);
         if(_loc4_ >= 5)
         {
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"该技能已升至最高级!");
            return;
         }
         if(_loc7_ >= _loc5_ && _loc8_ >= _loc6_)
         {
            Api_4399_GongHui.XiaoHaoBanghui_GX(_loc5_);
            Api_4399_GongHui.upNum(_loc9_);
            GongHui_Interface.playerInfo.unionInfo.contribution -= _loc5_;
            TiaoShi.txtShow("剩余贡献点:" + GongHui_Interface.playerInfo.unionInfo.contribution);
            TiaoShi.txtShow("jiTanLv_arr[numX] = " + jiTanLv_arr[_loc3_]);
            jiTanLv_arr[_loc3_] = int(jiTanLv_arr[_loc3_]) + 1;
            TiaoShi.txtShow("公会技能" + _loc3_ + " 提升到" + jiTanLv_arr[_loc3_]);
            TiaoShi.txtShow("祭坛技能等级jiTanLv_arr = " + jiTanLv_arr);
            Show();
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"技能升级成功!");
         }
         else if(_loc7_ >= _loc5_)
         {
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"公会经验不足!");
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"公会贡献点不足!");
         }
      }
      
      public static function onMOUSE_MOVE(param1:MouseEvent) : *
      {
         var _loc2_:MovieClip = param1.target.parent;
         var _loc3_:int = int(_loc2_.name.substr(1,1));
         var _loc4_:int = (page - 1) * 3 + _loc3_;
         if(_loc4_ > sel_max)
         {
            return;
         }
         var _loc5_:int = int(jiTanLv_arr[_loc4_]);
         var _loc6_:int = int(jiTanNum_arr[_loc4_][_loc5_]);
         var _loc7_:int = int(jiTanNum_arr[_loc4_][_loc5_ + 1]);
         if(_loc5_ <= 5)
         {
            if(_loc4_ == 1)
            {
               jiTan._info_mc._txt.text = "怪物精通\n公会玩家击杀关卡怪物获得经验提升" + _loc6_ + "%";
               if(_loc5_ != 5)
               {
                  jiTan._info_mc._txt.text += "\n\n下一等级\n公会玩家击杀关卡怪物获得经验提升" + _loc7_ + "%";
               }
            }
            else if(_loc4_ == 2)
            {
               jiTan._info_mc._txt.text = "关卡精通\n公会玩家挑战关卡每日成就点和击杀点消耗减少" + _loc6_ + "%";
               if(_loc5_ != 5)
               {
                  jiTan._info_mc._txt.text += "\n\n下一等级\n公会玩家挑战关卡每日成就点和击杀点消耗减少" + _loc7_ + "%";
               }
            }
            else if(_loc4_ == 3)
            {
               jiTan._info_mc._txt.text = "宠物精通\n出战宠物造成的伤害提升" + _loc6_ + "%";
               if(_loc5_ != 5)
               {
                  jiTan._info_mc._txt.text += "\n\n下一等级\n出战宠物造成的伤害提升" + _loc7_ + "%";
               }
            }
            else if(_loc4_ == 4)
            {
               jiTan._info_mc._txt.text = "技能精通\n公会玩家技能冷却时间削减" + _loc6_ + "%";
               if(_loc5_ != 5)
               {
                  jiTan._info_mc._txt.text += "\n\n下一等级\n公会玩家技能冷却时间削减" + _loc7_ + "%";
               }
            }
            jiTan._info_mc.x = _loc2_.x - 30;
            jiTan._info_mc.y = 230;
         }
      }
      
      public static function onMOUSE_OUT(param1:MouseEvent) : *
      {
         jiTan._info_mc._txt.text = "";
         jiTan._info_mc.y = -5000;
      }
      
      public static function Back_Fun(param1:*) : *
      {
         if(page > 1)
         {
            --page;
         }
         Show();
      }
      
      public static function Next_Fun(param1:*) : *
      {
         if(page < page_max)
         {
            ++page;
         }
         Show();
      }
      
      public static function expUP(param1:int) : int
      {
         var _loc2_:int = int(jiTanLv_arr[1]);
         if(_loc2_ > 0)
         {
            param1 = param1 * (100 + jiTanNum_arr[1][_loc2_]) / 100;
         }
         return param1;
      }
      
      public static function killPointXX(param1:int) : int
      {
         var _loc2_:int = int(jiTanLv_arr[2]);
         if(_loc2_ > 0)
         {
            param1 = param1 * (100 - jiTanNum_arr[2][_loc2_]) / 100;
         }
         return param1;
      }
      
      public static function CW_XX(param1:int) : int
      {
         var _loc2_:int = int(jiTanLv_arr[3]);
         if(_loc2_ > 0)
         {
            param1 = param1 * (100 + jiTanNum_arr[3][_loc2_]) / 100;
         }
         return param1;
      }
      
      public static function CD_XX(param1:int) : int
      {
         var _loc2_:int = int(jiTanLv_arr[4]);
         if(_loc2_ > 0)
         {
            param1 = param1 * (100 - jiTanNum_arr[4][_loc2_]) / 100;
         }
         return param1;
      }
   }
}

