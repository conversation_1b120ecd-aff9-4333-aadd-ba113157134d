package src
{
   import com.*;
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src._data.*;
   import src.tool.*;
   
   public class Shop_LiBao extends MovieClip
   {
      public static var shopX:Shop_LiBao;
      
      public static var loadData:ClassLoader;
      
      public static var open_yn:Boolean;
      
      public static const data:Class = Shop_LiBao_data;
      
      public static const data2:Class = Shop_LiBao_data2;
      
      public static var HDpoint:VT = VT.createVT(-1);
      
      public static var loadName:String = "Panel_CZ_v1.swf";
      
      public static var selYN:Boolean = true;
      
      public static var selYN2:Boolean = false;
      
      public var btn_0:*;
      
      public var btn_1:*;
      
      public var btn_2:*;
      
      public var btn_3:*;
      
      public var btn_4:*;
      
      public var btn_5:*;
      
      public var btn_6:*;
      
      public var btn_7:*;
      
      public var Close_btn:*;
      
      public var _txt_infoMC:*;
      
      public var QieHuan_btn:*;
      
      public var pic_mc1:*;
      
      public var pic_mc2:*;
      
      public var pic_mc3:*;
      
      public var pic_mc4:*;
      
      public var pic_mc5:*;
      
      public var pic_mc6:*;
      
      public var HDpoint_txt:*;
      
      public var lingQu_btn:*;
      
      public var add_Money_btn:*;
      
      public var v0:*;
      
      public var v1:*;
      
      public var v2:*;
      
      public var v3:*;
      
      public var v4:*;
      
      public var v5:*;
      
      public var v6:*;
      
      public var v7:*;
      
      public var v8:*;
      
      public var v9:*;
      
      public var myXml:XML;
      
      public var myXml2:XML;
      
      public var selGetX_Num:int = 1;
      
      private var liBaoPointArr:Array;
      
      public function Shop_LiBao()
      {
         var _loc1_:int = 0;
         var _loc2_:MovieClip = null;
         var _loc3_:int = 0;
         this.myXml = new XML();
         this.myXml2 = new XML();
         this.liBaoPointArr = [0,99,499,999,1999,2999,3999,4999,9999,9999,9999];
         super();
         Main._stage.addEventListener(Event.ACTIVATE,this.onACTIVATEx2);
         this.myXml = XMLAsset.createXML(Shop_LiBao.data);
         this.myXml2 = XMLAsset.createXML(Shop_LiBao.data2);
         this.Close_btn.addEventListener(MouseEvent.CLICK,this.CloseX);
         this.lingQu_btn.addEventListener(MouseEvent.CLICK,this.GetX);
         _loc1_ = 0;
         while(_loc1_ < 8)
         {
            this["btn_" + _loc1_].addEventListener(MouseEvent.CLICK,this.ShowObjArr);
            _loc1_++;
         }
         _loc1_ = 1;
         while(_loc1_ < 7)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = int(this["pic_mc" + _loc1_].getChildIndex(this["pic_mc" + _loc1_].pic_xx));
            _loc2_.x = this["pic_mc" + _loc1_].pic_xx.x;
            _loc2_.y = this["pic_mc" + _loc1_].pic_xx.y;
            _loc2_.name = "pic_xx";
            this["pic_mc" + _loc1_].removeChild(this["pic_mc" + _loc1_].pic_xx);
            this["pic_mc" + _loc1_].pic_xx = _loc2_;
            this["pic_mc" + _loc1_].addChild(_loc2_);
            this["pic_mc" + _loc1_].setChildIndex(_loc2_,_loc3_);
            this["pic_mc" + _loc1_].pic_xx.gotoAndStop(1);
            _loc1_++;
         }
         this.LingQu_btn_YN();
         this.QieHuan_btn.addEventListener(MouseEvent.CLICK,this.QieHuanFun);
         this.add_Money_btn.addEventListener(MouseEvent.CLICK,this.Open_AddMoney);
      }
      
      public static function Open(param1:int = 0, param2:int = 0) : *
      {
         if(!shopX)
         {
            Loading();
            open_yn = true;
            return;
         }
         SelType = "全部";
         Main.allClosePanel();
         shopX.x = param1;
         shopX.y = param2;
         shopX.visible = true;
         Main._stage.addChild(shopX);
         shopX.Show();
      }
      
      public static function Close() : *
      {
         if(shopX)
         {
            shopX.x = 5000;
            shopX.y = 5000;
            shopX.visible = false;
         }
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("src.Shop_LiBao") as Class;
         shopX = new _loc2_();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            Open();
         }
         else
         {
            Close();
         }
      }
      
      public static function JianCe() : *
      {
         var _loc3_:int = 0;
         var _loc1_:int = int(Shop4399.totalRecharged.getValue());
         if(_loc1_ < 0)
         {
            _loc3_ = 3;
            while(_loc3_ < Main.lingQueArr.length)
            {
               if(Main.lingQueArr[_loc3_] > 0)
               {
                  SelliBao();
               }
               _loc3_++;
            }
            return;
         }
         var _loc2_:int = 0;
         while(_loc2_ < Main.player1.getTitleSlot().getListLength())
         {
            if(Main.player1.getTitleSlot().getTitleFromSlot(_loc2_).getId() == 25)
            {
               if(_loc1_ < 900)
               {
                  Main.NoGame("vip称号");
               }
            }
            _loc2_++;
         }
         _loc3_ = 3;
         while(_loc3_ < shopX.liBaoPointArr.length)
         {
            if(_loc1_ >= 0 && Main.lingQueArr && Main.lingQueArr[_loc3_] != 0 && _loc1_ < shopX.liBaoPointArr[_loc3_])
            {
               SaveXX.Save(12,_loc1_,false,false,false);
            }
            _loc3_++;
         }
      }
      
      public static function SelliBao() : *
      {
         if(selYN && Shop4399.totalRecharged.getValue() < 0)
         {
            Api_4399_All.GetTotalRecharged(1);
            Api_4399_All.GetTotalRecharged();
            selYN = false;
         }
      }
      
      public static function subString() : *
      {
         var _loc1_:int = 0;
         var _loc2_:String = null;
         var _loc3_:Array = null;
         for(_loc1_ in Strat.SAVE_LIST)
         {
            _loc2_ = Strat.SAVE_LIST[_loc1_];
            if(_loc2_.substr(0,1) == "$")
            {
               _loc3_ = _loc2_.split("$");
               Strat.SAVE_LIST[_loc1_] = _loc3_[2];
            }
         }
      }
      
      public function Show() : *
      {
         var _loc1_:int = 0;
         var _loc3_:Array = null;
         _loc1_ = 0;
         while(_loc1_ < 10)
         {
            this["v" + _loc1_].visible = false;
            _loc1_++;
         }
         this["v" + (this.selGetX_Num - 1)].visible = true;
         var _loc2_:Array = this.isObjArr(this.selGetX_Num);
         for(_loc1_ in _loc2_)
         {
            if(_loc2_[_loc1_] != 0)
            {
               _loc3_ = this.isType(_loc2_[_loc1_]);
               this["pic_mc" + (_loc1_ + 1)].pic_xx.gotoAndStop(_loc3_[3]);
               this["pic_mc" + (_loc1_ + 1)].mouseChildren = false;
               this["pic_mc" + (_loc1_ + 1)].addEventListener(MouseEvent.MOUSE_OVER,this.onMOUSE_OVER);
               this["pic_mc" + (_loc1_ + 1)].addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
            }
            else
            {
               this["pic_mc" + (_loc1_ + 1)].pic_xx.gotoAndStop(1);
               this["pic_mc" + (_loc1_ + 1)].mouseChildren = false;
               this["pic_mc" + (_loc1_ + 1)].removeEventListener(MouseEvent.MOUSE_OVER,this.onMOUSE_OVER);
               this["pic_mc" + (_loc1_ + 1)].removeEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
            }
         }
         this.LingQu_btn_YN();
         this.ShowPoint();
      }
      
      public function ShowPoint() : *
      {
         var _loc1_:int = Shop4399.totalRecharged.getValue() - Shop_LiBao.HDpoint.getValue();
         if(_loc1_ < 0)
         {
            this.HDpoint_txt.text = "数据查询中请稍候";
         }
         else
         {
            this.HDpoint_txt.text = _loc1_ + "点券";
         }
      }
      
      private function CloseX(param1:*) : *
      {
         Close();
      }
      
      private function GetX(param1:*) : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:Array = null;
         var _loc6_:int = 0;
         if(Boolean(this.isNumOK(this.selGetX_Num)) && Main.lingQueArr[this.selGetX_Num] <= 0)
         {
            _loc2_ = 1;
            while(_loc2_ < 7)
            {
               _loc3_ = int(this.myXml2.充值礼包[this.selGetX_Num - 1]["id" + _loc2_]);
               _loc4_ = int(this.myXml2.充值礼包[this.selGetX_Num - 1]["数量" + _loc2_]);
               if(_loc3_ != 0)
               {
                  _loc5_ = this.isType(_loc3_);
                  _loc6_ = 0;
                  while(_loc6_ < _loc4_)
                  {
                     if(_loc5_[0] == "装备类")
                     {
                        StoragePanel.storage.addEquipStorage(EquipFactory.createEquipByID(_loc5_[1]));
                        if(Main.P1P2)
                        {
                           StoragePanel.storage.addEquipStorage(EquipFactory.createEquipByID(_loc5_[1]));
                        }
                     }
                     else if(_loc5_[0] == "宝石类")
                     {
                        StoragePanel.storage.addGemStorage(GemFactory.creatGemById(_loc5_[1]));
                     }
                     else if(_loc5_[0] == "消耗类")
                     {
                        StoragePanel.storage.addSuppliesStorage(SuppliesFactory.getSuppliesById(_loc5_[1]));
                     }
                     else if(_loc5_[0] == "其他类")
                     {
                        StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(_loc5_[1]));
                        if(Main.P1P2)
                        {
                           StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(_loc5_[1]));
                        }
                     }
                     else if(_loc5_[0] == "属性类")
                     {
                        if(_loc5_[4] == 1)
                        {
                           Main.player1.addPoint(_loc5_[5]);
                           if(Main.P1P2)
                           {
                              Main.player2.addPoint(_loc5_[5]);
                           }
                        }
                     }
                     _loc6_++;
                  }
               }
               _loc2_++;
            }
            ++Main.lingQueArr[this.selGetX_Num];
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功 物品已放入仓库");
            this.LingQu_btn_YN();
         }
      }
      
      private function isNumOK(param1:int) : Boolean
      {
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:int = 0;
         var _loc2_:int = int(StoragePanel.storage.backEquipEmptyNum());
         var _loc3_:int = int(StoragePanel.storage.backGemEmptyNum());
         var _loc4_:int = int(StoragePanel.storage.backSuppliesEmptyNum());
         var _loc5_:int = int(StoragePanel.storage.backOtherEmptyNum());
         for(_loc6_ in this.myXml.充值礼包物品)
         {
            _loc7_ = int(this.myXml2.充值礼包[_loc6_].格数_装备);
            _loc8_ = int(this.myXml2.充值礼包[_loc6_].格数_宝石);
            _loc9_ = int(this.myXml2.充值礼包[_loc6_].格数_消耗);
            _loc10_ = int(this.myXml2.充值礼包[_loc6_].格数_其他);
            if(Main.P1P2)
            {
               _loc7_ *= 2;
               _loc10_ *= 2;
            }
            _loc11_ = int(this.myXml.充值礼包物品[_loc6_].ID);
            if(param1 == _loc11_)
            {
               if(_loc7_ >= _loc2_)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
                  return false;
               }
               if(_loc8_ >= _loc3_)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
                  return false;
               }
               if(_loc9_ >= _loc4_)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
                  return false;
               }
               if(_loc10_ >= _loc5_)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
                  return false;
               }
               return true;
            }
         }
         return true;
      }
      
      private function isType(param1:int) : Array
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         for(_loc2_ in this.myXml.充值礼包物品)
         {
            _loc3_ = int(this.myXml.充值礼包物品[_loc2_].ID);
            if(param1 == _loc3_)
            {
               return [String(this.myXml.充值礼包物品[_loc2_].类型),int(this.myXml.充值礼包物品[_loc2_].物品ID),String(this.myXml.充值礼包物品[_loc2_].名称),int(this.myXml.充值礼包物品[_loc2_].图标),Number(this.myXml.充值礼包物品[_loc2_].备用1),Number(this.myXml.充值礼包物品[_loc2_].备用2),Number(this.myXml.充值礼包物品[_loc2_].备用3),String(this.myXml.充值礼包物品[_loc2_].说明)];
            }
         }
         return null;
      }
      
      private function isObjArr(param1:int) : Array
      {
         var _loc2_:int = 0;
         for(_loc2_ in this.myXml2.充值礼包)
         {
            if(param1 == this.myXml2.充值礼包[_loc2_].ID)
            {
               return [this.myXml2.充值礼包[_loc2_].id1,this.myXml2.充值礼包[_loc2_].id2,this.myXml2.充值礼包[_loc2_].id3,this.myXml2.充值礼包[_loc2_].id4,this.myXml2.充值礼包[_loc2_].id5,this.myXml2.充值礼包[_loc2_].id6];
            }
         }
         return null;
      }
      
      private function ShowObjArr(param1:MouseEvent) : *
      {
         this.selGetX_Num = int((param1.target.name as String).substr(4,1)) + 1;
         shopX.Show();
      }
      
      private function LingQu_btn_YN() : *
      {
         var _loc1_:int = Shop4399.totalRecharged.getValue() - Shop_LiBao.HDpoint.getValue();
         if(!Main.lingQueArr[this.selGetX_Num] && _loc1_ > this.liBaoPointArr[this.selGetX_Num])
         {
            this.lingQu_btn.visible = true;
         }
         else
         {
            this.lingQu_btn.visible = false;
         }
      }
      
      private function onMOUSE_OVER(param1:MouseEvent) : *
      {
         var _loc2_:int = int((param1.target.name as String).substr(6,1));
         this.objInfo(_loc2_,param1.target.x + 50,param1.target.y);
      }
      
      private function onMOUSE_OUT(param1:*) : *
      {
         this._txt_infoMC.y = 5000;
         this._txt_infoMC.x = 5000;
      }
      
      private function objInfo(param1:int, param2:int, param3:int) : *
      {
         var _loc5_:Array = null;
         this._txt_infoMC.x = param2;
         this._txt_infoMC.y = param3;
         var _loc4_:Array = this.isObjArr(this.selGetX_Num);
         if(_loc4_[param1 - 1] != 0)
         {
            _loc5_ = this.isType(_loc4_[param1 - 1]);
            this._txt_infoMC._txt.text = _loc5_[2] + "\n" + _loc5_[7];
         }
      }
      
      private function onACTIVATEx2(param1:*) : *
      {
         if(this.visible && selYN2 && Shop4399.totalRecharged.getValue() < 0)
         {
            Api_4399_All.GetTotalRecharged();
         }
      }
      
      private function QieHuanFun(param1:*) : *
      {
         Main.allClosePanel();
         Shop_LiBao2.Open();
      }
      
      private function Open_AddMoney(param1:* = null) : *
      {
         Main.ChongZhi();
         selYN2 = true;
         this.HDpoint_txt.text = "数据查询中请稍候";
      }
   }
}

