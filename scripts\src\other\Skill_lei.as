package src.other
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_lei extends Fly
   {
      public function Skill_lei()
      {
         super();
      }
      
      public static function Add_Skill_lei(param1:Player) : *
      {
         var _loc2_:int = 0;
         if(param1.data.getEquipSlot().getEquipFromSlot(6) && param1.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 487 && param1.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            _loc2_ = Math.random() * 100;
            if(_loc2_ < 35 && Player.szCD > 30)
            {
               XiaoG<PERSON>(param1);
               Player.szCD = 0;
            }
         }
      }
      
      public static function <PERSON><PERSON><PERSON>(param1:Player) : *
      {
         if(Main.gameNum.getValue() == 999 && Enemy.All.length <= 0)
         {
            return;
         }
         var _loc2_:int = Math.random() * Enemy.All.length;
         var _loc3_:Enemy = Enemy.All[_loc2_];
         Player.szCD = 0;
         var _loc4_:Class = NewLoad.OtherData.getClass("雷霆打击") as Class;
         var _loc5_:* = new _loc4_();
         param1.skin_W.addChild(_loc5_);
         _loc5_.x = _loc3_.x;
         _loc5_.y = _loc3_.y;
         param1.jianCD_lei = true;
      }
      
      public static function JianCDXXXX(param1:Player) : *
      {
         var _loc3_:int = 0;
         var _loc6_:Number = NaN;
         param1.jianCD_lei = false;
         var _loc2_:Array = [];
         for(_loc3_ in param1.AllSkillCDXX)
         {
            if(param1.AllSkillCDXX[_loc3_][1] < param1.AllSkillCD[_loc3_][1] && param1.data.getSkillLevel(param1.AllSkillCDXX[_loc3_][0]) > 0)
            {
               _loc2_.push(_loc3_);
            }
         }
         if(_loc2_.length <= 0)
         {
            return;
         }
         var _loc4_:int = Math.random() * _loc2_.length;
         var _loc5_:int = int(_loc2_[_loc4_]);
         if(param1.AllSkillCDXX[_loc5_][1] < param1.AllSkillCD[_loc5_][1] && param1.data.getSkillLevel(param1.AllSkillCDXX[_loc5_][0]) > 0)
         {
            _loc6_ = 0.2;
            if(param1.data.getEquipSlot().getEquipFromSlot(7) && param1.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 488 && param1.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
            {
               _loc6_ = 0.3;
            }
            param1.AllSkillCDXX[_loc5_][1] += int(param1.AllSkillCD[_loc5_][1] * _loc6_);
            if(param1.AllSkillCDXX[_loc5_][1] > param1.AllSkillCD[_loc5_][1])
            {
               param1.AllSkillCDXX[_loc5_][1] = param1.AllSkillCD[_loc5_][1];
            }
            param1.AllSkillCDXX[_loc5_][2] = int(param1.AllSkillCDXX[_loc5_][1] / param1.AllSkillCD[_loc5_][1] * 50);
         }
      }
      
      override public function onADDED_TO_STAGE(param1:* = null) : *
      {
         var _loc3_:Player = null;
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         var _loc2_:MovieClip = this;
         while(_loc2_ != _stage)
         {
            if(_loc2_ is Skin_WuQi && _loc2_.parent is Player)
            {
               who = _loc2_.parent;
               this.RL = (who as Player).RL;
               gongJi_hp_MAX = 6000;
               硬直 = 0;
               gongJi_hp = 3;
               _loc3_ = _loc2_.parent;
               if(_loc3_.data.getEquipSlot().getEquipFromSlot(7) && _loc3_.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 488 && _loc3_.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  gongJi_hp = 6;
               }
               attTimes = who.skin.attTimes;
               runX = who.skin.runX;
               runTime = who.skin.runTime;
               break;
            }
            _loc2_ = _loc2_.parent as MovieClip;
         }
         Main.world.moveChild_Other.addChild(this);
      }
      
      override public function onENTER_FRAME(param1:*) : *
      {
         if(over && this.currentLabel != "结束")
         {
            Dead();
            return;
         }
         if(!over && life == 0)
         {
            life = -1;
            gotoAndPlay("结束");
            continuous = false;
            over = true;
         }
         if(!over && time != -1)
         {
            --time;
            if(time == -1)
            {
               time = -1;
               gotoAndPlay("结束");
               continuous = false;
               over = true;
            }
         }
         if(continuous && this.currentLabel != "运行")
         {
            gotoAndPlay("运行");
         }
         else if(!continuous && this.currentLabel != "运行")
         {
            over = true;
         }
         if(!over)
         {
            Move();
         }
         otherXX();
      }
   }
}

