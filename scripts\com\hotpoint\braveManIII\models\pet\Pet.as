package com.hotpoint.braveManIII.models.pet
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.petEquip.PetEquip;
   import com.hotpoint.braveManIII.models.skill.Skill;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import src.*;
   import src.tool.*;
   
   public class Pet
   {
      private var _equip:PetEquip;
      
      public var time_fh:int = 2400;
      
      public var _id:VT;
      
      private var _xingge:VT;
      
      private var _lv:VT;
      
      private var _lvlimit:VT;
      
      private var _food:VT;
      
      private var _exp:VT;
      
      private var _wuxing:VT = VT.createVT(0);
      
      private var _skill:Array = [-1,-1,-1,-1];
      
      public function Pet()
      {
         super();
      }
      
      public static function creatPet(param1:*) : Pet
      {
         var _loc2_:Pet = new Pet();
         _loc2_._id = VT.createVT(param1);
         _loc2_._lvlimit = VT.createVT(20);
         _loc2_._lv = VT.createVT(1);
         _loc2_._food = VT.createVT(500);
         _loc2_._exp = VT.createVT(0);
         if(_loc2_.getWXLV() > 0)
         {
            _loc2_._wuxing = VT.createVT(Math.round(Math.random() * 3) + 1);
         }
         else
         {
            _loc2_._wuxing = VT.createVT(0);
         }
         _loc2_.setXingge();
         return _loc2_;
      }
      
      public function getLv() : Number
      {
         return this._lv.getValue();
      }
      
      public function setLv(param1:Number) : *
      {
         this._lv.setValue(param1);
         this.addSkill();
      }
      
      public function addLvLimit(param1:Number = 1) : *
      {
         this._lvlimit.setValue(this._lvlimit.getValue() + param1);
      }
      
      public function setFood(param1:Number) : *
      {
         this._food.setValue(this._food.getValue() + param1);
         if(this._food.getValue() < 0)
         {
            this._food.setValue(0);
         }
         if(this._food.getValue() > 500)
         {
            this._food.setValue(500);
         }
      }
      
      public function getLvLimit() : Number
      {
         return this._lvlimit.getValue();
      }
      
      public function getFood() : Number
      {
         return this._food.getValue();
      }
      
      public function getAtt() : Number
      {
         if(this.getLv() < 21)
         {
            return this.getAttX() + this.getAttup() * this.getLv();
         }
         return this.getAttX() + this.getAttup() * 20 + (this.getLv() - 20) * 0.002;
      }
      
      public function getDef() : Number
      {
         if(this.getLv() < 21)
         {
            return this.getDefX() + this.getDefup() * this.getLv();
         }
         return this.getDefX() + this.getDefup() * 20 + (this.getLv() - 20) * 0.002;
      }
      
      public function getCrit() : Number
      {
         if(this.getLv() < 21)
         {
            return this.getCritX() + this.getCritup() * this.getLv();
         }
         return this.getCritX() + this.getCritup() * 20 + (this.getLv() - 20) * 0.002;
      }
      
      public function getLife() : Number
      {
         if(this.getLv() < 21)
         {
            return this.getLifeX() + this.getLifeup() * this.getLv();
         }
         return this.getLifeX() + this.getLifeup() * 20 + (this.getLv() - 20) * 0.002;
      }
      
      public function getExp() : Number
      {
         return this._exp.getValue();
      }
      
      public function setExp(param1:Number) : *
      {
         this._exp.setValue(param1);
      }
      
      public function setWX() : *
      {
         if(this.getWXLV() > 0)
         {
            this._wuxing.setValue(Math.round(Math.random() * 3) + 1);
         }
         this.addSkill();
      }
      
      public function getWX() : Number
      {
         return this._wuxing.getValue();
      }
      
      public function addExp(param1:Number) : *
      {
         param1 *= 2;
         this._exp.setValue(this._exp.getValue() + param1);
         if(PetFactory.all_LVData_vt[this.getLv() - 1].getValue() < this._exp.getValue() && this.getLv() < this.getLvLimit())
         {
            this.setLv(this.getLv() + 1);
            this.setExp(0);
            return true;
         }
         return false;
      }
      
      public function testSkill() : *
      {
         this._skill = [1,1,1,1];
      }
      
      public function addSkill() : *
      {
         if(this.getWX() == 4)
         {
            if(this.getLv() >= 5 && this._skill[0] == -1)
            {
               this._skill[0] = 0;
            }
            if(this.getLv() >= 10 && this._skill[1] == -1)
            {
               this._skill[1] = 0;
            }
            if(this.getLv() >= 15 && this._skill[2] == -1)
            {
               this._skill[2] = 0;
            }
            if(this.getLv() >= 20 && this._skill[3] == -1)
            {
               this._skill[3] = 0;
            }
         }
         if(this.getWX() == 3)
         {
            if(this.getLv() >= 5 && this._skill[0] == -1)
            {
               this._skill[0] = 0;
            }
            if(this.getLv() >= 10 && this._skill[1] == -1)
            {
               this._skill[1] = 0;
            }
            if(this.getLv() >= 15 && this._skill[2] == -1)
            {
               this._skill[2] = 0;
            }
         }
         if(this.getWX() == 2)
         {
            if(this.getLv() >= 5 && this._skill[0] == -1)
            {
               this._skill[0] = 0;
            }
            if(this.getLv() >= 10 && this._skill[1] == -1)
            {
               this._skill[1] = 0;
            }
         }
         if(this.getWX() == 1)
         {
            if(this.getLv() >= 5 && this._skill[0] == -1)
            {
               this._skill[0] = 0;
            }
         }
      }
      
      public function setSkill(param1:Number) : *
      {
         this._skill[param1] = SkillFactory.getSkillByPet(this._id.getValue());
      }
      
      public function tiaoshiSkill() : *
      {
         this._skill[0] = this._skill[1] = this._skill[2] = this._skill[3] = SkillFactory.getSkillByPet(this._id.getValue());
         TiaoShi.txtShow("调试宠物技能: " + this._id.getValue() + ", " + this._skill[0]);
      }
      
      public function getPetSkill() : Array
      {
         return this._skill;
      }
      
      public function getPetSkillType() : Array
      {
         var _loc2_:* = undefined;
         var _loc1_:Array = DeepCopyUtil.clone(this._skill);
         for(_loc2_ in _loc1_)
         {
            if(_loc1_[_loc2_] > 0)
            {
               _loc1_[_loc2_] = SkillFactory.getSkillById(_loc1_[_loc2_]).getTypeId();
            }
         }
         return _loc1_;
      }
      
      public function getPetSkillNum(param1:Number) : Skill
      {
         return SkillFactory.getSkillById(this._skill[param1]);
      }
      
      public function getXingge() : Number
      {
         if(!this._xingge)
         {
            this.setXingge();
         }
         return this._xingge.getValue();
      }
      
      public function setXingge(param1:int = 0) : Number
      {
         var _loc2_:int = 0;
         if(!this._xingge)
         {
            this._xingge = VT.createVT(0);
         }
         if(param1 == 0)
         {
            if(this.getType() == 1)
            {
               this._xingge.setValue(0);
            }
            this._xingge.setValue(Math.ceil(Math.random() * 12));
            if(this._xingge.getValue() == 3)
            {
               _loc2_ = 0;
               while(_loc2_ < Main.player1.getTitleSlot().getListLength())
               {
                  if(Main.player1.getTitleSlot().getTitleFromSlot(_loc2_).getId() == 25)
                  {
                     return;
                  }
                  _loc2_++;
               }
               this._xingge.setValue(3 + Math.ceil(Math.random() * 9));
            }
         }
         else
         {
            this._xingge.setValue(param1);
         }
         Main.Save();
         return undefined;
      }
      
      public function getName() : String
      {
         return PetFactory.getName(this._id.getValue());
      }
      
      public function getFrame() : Number
      {
         return PetFactory.getFrame(this._id.getValue());
      }
      
      public function getType() : Number
      {
         return PetFactory.getType(this._id.getValue());
      }
      
      public function getClassName() : String
      {
         return PetFactory.getClassName(this._id.getValue());
      }
      
      public function getIntroduction() : String
      {
         return PetFactory.getIntroduction(this._id.getValue());
      }
      
      public function getEvolution() : Number
      {
         return PetFactory.getEvolution(this._id.getValue());
      }
      
      public function getEvolutionLV() : Number
      {
         return PetFactory.getEvolutionLV(this._id.getValue());
      }
      
      public function getAttX() : Number
      {
         return PetFactory.getAtt(this._id.getValue());
      }
      
      public function getDefX() : Number
      {
         return PetFactory.getDef(this._id.getValue());
      }
      
      public function getCritX() : Number
      {
         return PetFactory.getCrit(this._id.getValue());
      }
      
      public function getLifeX() : Number
      {
         return PetFactory.getLife(this._id.getValue());
      }
      
      public function getAttup() : Number
      {
         return PetFactory.getAttup(this._id.getValue());
      }
      
      public function getDefup() : Number
      {
         return PetFactory.getDefup(this._id.getValue());
      }
      
      public function getCritup() : Number
      {
         return PetFactory.getCritup(this._id.getValue());
      }
      
      public function getLifeup() : Number
      {
         return PetFactory.getLifeup(this._id.getValue());
      }
      
      public function getLink() : Number
      {
         return PetFactory.getLink(this._id.getValue());
      }
      
      public function getPetCD() : Array
      {
         return PetFactory.getPetCD(this._id.getValue());
      }
      
      public function getWXLV() : Number
      {
         return PetFactory.getWuxingLV(this._id.getValue());
      }
      
      public function getPetEquip() : PetEquip
      {
         return this._equip;
      }
      
      public function setPetEquip(param1:PetEquip = null) : *
      {
         this._equip = param1;
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(param1:VT) : void
      {
         this._id = param1;
      }
      
      public function get lv() : VT
      {
         return this._lv;
      }
      
      public function set lv(param1:VT) : void
      {
         this._lv = param1;
      }
      
      public function get lvlimit() : VT
      {
         return this._lvlimit;
      }
      
      public function set lvlimit(param1:VT) : void
      {
         this._lvlimit = param1;
      }
      
      public function get food() : VT
      {
         return this._food;
      }
      
      public function set food(param1:VT) : void
      {
         this._food = param1;
      }
      
      public function get exp() : VT
      {
         return this._exp;
      }
      
      public function set exp(param1:VT) : void
      {
         this._exp = param1;
      }
      
      public function get wuxing() : VT
      {
         return this._wuxing;
      }
      
      public function set wuxing(param1:VT) : void
      {
         this._wuxing = param1;
      }
      
      public function get skill() : Array
      {
         return this._skill;
      }
      
      public function set skill(param1:Array) : void
      {
         this._skill = param1;
      }
      
      public function get xingge() : VT
      {
         return this._xingge;
      }
      
      public function set xingge(param1:VT) : void
      {
         this._xingge = param1;
      }
      
      public function get equip() : PetEquip
      {
         return this._equip;
      }
      
      public function set equip(param1:PetEquip) : void
      {
         this._equip = param1;
      }
   }
}

