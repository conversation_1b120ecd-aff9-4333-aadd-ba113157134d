package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.filters.*;
   import flash.geom.*;
   import src.CuteGMFunctions;
   import src.Main;
   import src.NewMC;
   
   public class SimpleCuteGMPanel extends MovieClip
   {
      private var mainPanel:Sprite;
      private var isVisible:Boolean = false;
      
      // 颜色常量
      private static const PINK:uint = 0xFF69B4;
      private static const PURPLE:uint = 0x9370DB;
      private static const CYAN:uint = 0x00CED1;
      private static const ORANGE:uint = 0xFF7F50;
      private static const YELLOW:uint = 0xFFD700;
      private static const GREEN:uint = 0x98FB98;
      
      public function SimpleCuteGMPanel()
      {
         super();
         trace("SimpleCuteGMPanel 构造函数被调用");
         createInterface();
      }
      
      private function createInterface():void
      {
         trace("创建简化GM界面");
         
         // 创建主面板
         mainPanel = new Sprite();
         addChild(mainPanel);
         
         // 创建背景
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x000000, 0.8);
         bg.graphics.drawRoundRect(0, 0, 400, 300, 20, 20);
         bg.graphics.endFill();
         mainPanel.addChild(bg);
         
         // 创建标题
         var title:TextField = createTextField("🌸 可爱GM界面 🌸", 16, 0xFFFFFF, true);
         title.x = 20;
         title.y = 10;
         mainPanel.addChild(title);
         
         // 创建关闭按钮
         var closeBtn:Sprite = createButton("❌", 0xFF0000, 30, 30);
         closeBtn.x = 350;
         closeBtn.y = 10;
         closeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            hide();
         });
         mainPanel.addChild(closeBtn);
         
         // 创建功能按钮
         var y:int = 60;
         
         // 一键满级按钮
         var maxLevelBtn:Sprite = createButton("一键满级", PINK, 120, 35);
         maxLevelBtn.x = 20;
         maxLevelBtn.y = y;
         maxLevelBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.setPlayerLevel(1, 99);
            CuteGMFunctions.setPlayerGold(1, 999999999);
            CuteGMFunctions.setPlayerExp(1, 999999999);
            CuteGMFunctions.setPlayerSkillPoints(1, 9999);
            showMessage("✨ 一键满级完成！");
         });
         mainPanel.addChild(maxLevelBtn);
         
         // 秒杀怪物按钮
         var killAllBtn:Sprite = createButton("秒杀怪物", PURPLE, 120, 35);
         killAllBtn.x = 160;
         killAllBtn.y = y;
         killAllBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.killAllEnemies();
            showMessage("💀 所有怪物已被秒杀！");
         });
         mainPanel.addChild(killAllBtn);
         
         y += 50;
         
         // 解锁背包按钮
         var unlockBagBtn:Sprite = createButton("解锁背包", CYAN, 120, 35);
         unlockBagBtn.x = 20;
         unlockBagBtn.y = y;
         unlockBagBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockBag();
            showMessage("🎒 背包已解锁！");
         });
         mainPanel.addChild(unlockBagBtn);
         
         // 解锁仓库按钮
         var unlockStorageBtn:Sprite = createButton("解锁仓库", ORANGE, 120, 35);
         unlockStorageBtn.x = 160;
         unlockStorageBtn.y = y;
         unlockStorageBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockStorage();
            showMessage("🏪 仓库已解锁！");
         });
         mainPanel.addChild(unlockStorageBtn);
         
         y += 50;
         
         // 成就全亮按钮
         var allAchBtn:Sprite = createButton("成就全亮", YELLOW, 120, 35);
         allAchBtn.x = 20;
         allAchBtn.y = y;
         allAchBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            CuteGMFunctions.unlockAllAchievements();
            showMessage("🏆 所有成就已解锁！");
         });
         mainPanel.addChild(allAchBtn);
         
         // 保存游戏按钮
         var saveBtn:Sprite = createButton("保存游戏", GREEN, 120, 35);
         saveBtn.x = 160;
         saveBtn.y = y;
         saveBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            Main.Save();
            showMessage("💾 游戏已保存！");
         });
         mainPanel.addChild(saveBtn);
         
         // 设置位置
         mainPanel.x = (Main._stageWidth - 400) / 2;
         mainPanel.y = (Main._stageHeight - 300) / 2;
         
         // 初始隐藏
         this.visible = false;
         
         trace("简化GM界面创建完成");
      }
      
      private function createTextField(text:String, size:int, color:uint, bold:Boolean = false):TextField
      {
         var tf:TextField = new TextField();
         tf.text = text;
         tf.autoSize = TextFieldAutoSize.LEFT;
         tf.selectable = false;
         
         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = size;
         format.color = color;
         format.bold = bold;
         tf.setTextFormat(format);
         
         return tf;
      }
      
      private function createButton(text:String, color:uint, width:int, height:int):Sprite
      {
         var btn:Sprite = new Sprite();
         
         // 绘制按钮背景
         btn.graphics.beginFill(color);
         btn.graphics.drawRoundRect(0, 0, width, height, 10, 10);
         btn.graphics.endFill();
         
         // 添加文字
         var label:TextField = createTextField(text, 12, 0xFFFFFF, true);
         label.x = (width - label.width) / 2;
         label.y = (height - label.height) / 2;
         btn.addChild(label);
         
         // 添加悬停效果
         btn.buttonMode = true;
         btn.addEventListener(MouseEvent.MOUSE_OVER, function(e:MouseEvent):void {
            btn.scaleX = btn.scaleY = 1.1;
         });
         btn.addEventListener(MouseEvent.MOUSE_OUT, function(e:MouseEvent):void {
            btn.scaleX = btn.scaleY = 1.0;
         });
         
         return btn;
      }
      
      private function showMessage(message:String):void
      {
         trace("GM消息: " + message);
         
         // 尝试使用游戏的消息系统
         try {
            NewMC.Open("文字提示", Main._stage, 480, 300, 60, 0, true, 2, message);
         } catch(e:Error) {
            trace("NewMC不可用，使用trace输出: " + message);
         }
      }
      
      public function toggle():void
      {
         trace("SimpleCuteGMPanel.toggle() 被调用，当前状态: " + (isVisible ? "显示" : "隐藏"));
         
         if(isVisible) {
            hide();
         } else {
            show();
         }
      }
      
      public function show():void
      {
         trace("SimpleCuteGMPanel.show() 被调用");
         
         if(!isVisible) {
            isVisible = true;
            this.visible = true;
            this.alpha = 1;
            this.scaleX = this.scaleY = 1;
            trace("简化GM界面已显示");
         }
      }
      
      public function hide():void
      {
         trace("SimpleCuteGMPanel.hide() 被调用");
         
         if(isVisible) {
            isVisible = false;
            this.visible = false;
            trace("简化GM界面已隐藏");
         }
      }
   }
}
