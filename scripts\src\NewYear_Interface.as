package src
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class NewYear_Interface extends MovieClip
   {
      public static var loadData:ClassLoader;
      
      private static var _this:NewYear_Interface;
      
      public static var varXX:int = 2;
      
      public static var varSave:int = 0;
      
      public static var key2022:VT = VT.createVT(0);
      
      public static var juan2022:VT = VT.createVT(0);
      
      public static var allJiangLi2022:Array = [];
      
      public static var openYesNo2022:Array = [];
      
      public static var NYOK15:Boolean = false;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "NewYear_v1720.swf";
      
      public static var overTime:int = 20230129;
      
      public static var overTimeStr:String = "活动时间: 1月13日至1月29日";
      
      private var skin:MovieClip;
      
      public function NewYear_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      private static function InitOpen() : *
      {
         var _loc1_:NewYear_Interface = new NewYear_Interface();
         Main._stage.addChild(_loc1_);
         OpenYN = true;
         InitXXXXXXXXX();
      }
      
      public static function InitXXXXXXXXX() : *
      {
         if(varSave != varXX)
         {
            varSave = varXX;
            key2022 = VT.createVT(0);
            juan2022 = VT.createVT(5);
            allJiangLi2022 = [];
            openYesNo2022 = [];
         }
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.skin.visible = true;
            _this.y = 0;
            _this.x = 0;
            showAll();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.skin.visible = false;
         }
      }
      
      public static function addKey() : *
      {
         key2022.setValue(key2022.getValue() + 1);
      }
      
      public static function addJuan() : *
      {
         juan2022.setValue(juan2022.getValue() + 1);
      }
      
      private static function Close(param1:*) : *
      {
         CloseX();
      }
      
      private static function bxOVER(param1:MouseEvent) : *
      {
         _this.skin["kuang"].visible = true;
         _this.skin["kuang"].x = param1.target.x - 250;
         _this.skin["kuang"].y = param1.target.y;
      }
      
      private static function bxOUT(param1:*) : *
      {
         _this.skin["kuang"].visible = false;
      }
      
      private static function showAll() : *
      {
         for(i in openYesNo2022)
         {
            if(openYesNo2022[i] == 0)
            {
               _this.skin["b" + i].visible = true;
               _this.skin["k" + i].visible = false;
            }
            else
            {
               _this.skin["b" + i].visible = false;
               _this.skin["k" + i].visible = true;
            }
         }
         _this.skin["juan_txt"].text = "今日剩余次数:" + juan2022.getValue() + "/5";
         _this.skin["key_txt"].text = "我的宝箱钥匙:" + key2022.getValue();
         if(juan2022.getValue() <= 0)
         {
            _this.skin["juan_txt"].text = "挑战费用: 15点券";
         }
      }
      
      private static function tiaozhanIn(param1:*) : *
      {
         if(juan2022.getValue() > 0)
         {
            Main.gameNum.setValue(2015);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            Main._this.Loading();
            CloseX();
            juan2022.setValue(juan2022.getValue() - 1);
            Main.Save();
         }
         else if(Shop4399.moneyAll.getValue() >= 15)
         {
            Api_4399_All.BuyObj(InitData.newyear15.getValue());
            NYOK15 = true;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"点券不足");
         }
      }
      
      public static function newYearInRMB() : *
      {
         if(NYOK15)
         {
            Main.gameNum.setValue(2015);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            Main._this.Loading();
            CloseX();
            NYOK15 = false;
         }
      }
      
      private static function kaiBaoXiang(param1:*) : *
      {
         var _loc2_:SimpleButton = null;
         var _loc3_:* = 0;
         var _loc4_:* = undefined;
         if(key2022.getValue() > 0)
         {
            _loc2_ = param1.target as SimpleButton;
            _loc3_ = uint(_loc2_.name.substr(1,1));
            if(allJiangLi2022[_loc3_] == 0)
            {
               if(Main.player1.getBag().backOtherBagNum() > 2 && Main.player1.getBag().backGemBagNum() > 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63328));
                  _loc4_ = 0;
                  while(_loc4_ < 5)
                  {
                     Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                     _loc4_++;
                  }
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31216));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31216));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31216));
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得 宠物蛋(年兽)、3级强化石*3、洗练券*5");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
            else if(allJiangLi2022[_loc3_] == 1)
            {
               if(Main.player1.getBag().backOtherBagNum() > 1 && Main.player1.getBag().backGemBagNum() > 1)
               {
                  _loc4_ = 0;
                  while(_loc4_ < 5)
                  {
                     Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63326));
                     _loc4_++;
                  }
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31217));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31217));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31217));
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得 极限炼狱入场券*5、4级强化石*3");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
            else if(allJiangLi2022[_loc3_] == 2)
            {
               if(Main.player1.getBag().backOtherBagNum() > 2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  _loc4_ = 0;
                  while(_loc4_ < 5)
                  {
                     Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
                     _loc4_++;
                  }
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得黑暗炼狱入场券*5、封印的星灵财宝*3");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
            else if(allJiangLi2022[_loc3_] == 3)
            {
               if(Main.player1.getBag().backOtherBagNum() > 2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得高级道具兑换券、封印的星灵财宝*2");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
            else if(allJiangLi2022[_loc3_] == 4)
            {
               if(Main.player1.getBag().backOtherBagNum() > 2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  _loc4_ = 0;
                  while(_loc4_ < 5)
                  {
                     Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                     _loc4_++;
                  }
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得高级道具兑换券、星源方块*5");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
            else if(allJiangLi2022[_loc3_] == 5)
            {
               if(Main.player1.getBag().backOtherBagNum() > 2 && Main.player1.getBag().backGemBagNum() > 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  _loc4_ = 0;
                  while(_loc4_ < 5)
                  {
                     Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                     _loc4_++;
                  }
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31215));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31215));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31215));
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得击杀点礼包、2级强化石*5、洗练券*5");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
            else if(allJiangLi2022[_loc3_] == 6)
            {
               if(Main.player1.getBag().backGemBagNum() > 2)
               {
                  _loc4_ = 0;
                  while(_loc4_ < 10)
                  {
                     Main.player1.getBag().addGemBag(GemFactory.creatGemById(31214));
                     _loc4_++;
                  }
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得卓越的4级强化石*3、1级强化石*10");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
            else if(allJiangLi2022[_loc3_] == 7)
            {
               if(Main.player1.getBag().backOtherBagNum() > 1 && Main.player1.getBag().backGemBagNum() > 1)
               {
                  _loc4_ = 0;
                  while(_loc4_ < 5)
                  {
                     Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                     Main.player1.getBag().addGemBag(GemFactory.creatGemById(31217));
                     _loc4_++;
                  }
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得封印的星灵财宝*5、4级强化石*5");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
            else if(allJiangLi2022[_loc3_] == 8)
            {
               if(Main.player1.getBag().backOtherBagNum() > 1)
               {
                  Main.player1.addGold(50000);
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
                  openYesNo2022[_loc3_] = 1;
                  key2022.setValue(key2022.getValue() - 1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得50000金币、击杀点礼包(小)*1");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"物品栏空间不足，请清理");
               }
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"缺少宝箱钥匙！");
         }
         showAll();
      }
      
      internal function randomizeArray(param1:Array) : Array
      {
         var _loc3_:* = 0;
         var _loc2_:Number = 0;
         while(_loc2_ < param1.length)
         {
            _loc3_ = uint(int(Math.random() * param1.length));
            param1.push(param1.splice(_loc3_,1)[0]);
            _loc2_++;
         }
         return param1;
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("nyPanel") as Class;
         this.skin = new _loc2_();
         this.skin.addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addChild(this.skin);
         booltemp = true;
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function Close(param1:*) : *
      {
         this.visible = false;
         this.y = 5000;
         this.x = 5000;
      }
      
      private function onADDED_TO_STAGE(param1:*) : *
      {
         this.skin.huodongriqi_txt.text = overTimeStr;
         if(allJiangLi2022.length <= 0)
         {
            openYesNo2022 = [0,0,0,0,0,0,0,0,0];
            allJiangLi2022 = [0,1,2,3,4,5,6,7,8];
            this.randomizeArray(allJiangLi2022);
         }
         _this.skin["tiaozhan"].addEventListener(MouseEvent.CLICK,tiaozhanIn);
         var _loc2_:* = 0;
         while(_loc2_ < 9)
         {
            _this.skin["b" + _loc2_].addEventListener(MouseEvent.CLICK,kaiBaoXiang);
            _this.skin["b" + _loc2_].addEventListener(MouseEvent.MOUSE_OVER,bxOVER);
            _this.skin["b" + _loc2_].addEventListener(MouseEvent.MOUSE_OUT,bxOUT);
            _loc2_++;
         }
         _this.skin["close_btn"].addEventListener(MouseEvent.CLICK,this.Close);
         _this.skin["kuang"].visible = false;
      }
   }
}

