package com.hotpoint.braveManIII.repository.other
{
   import com.hotpoint.braveManIII.models.common.VT;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   
   public class OtherBasicData
   {
      private var _id:VT;
      
      private var _fallLevel:VT;
      
      private var _type:VT;
      
      private var _name:String;
      
      private var _frame:VT;
      
      private var _color:VT;
      
      private var _introduction:String;
      
      private var _many:Boolean;
      
      private var _pileLimit:VT;
      
      private var _times:VT;
      
      private var _gold:VT;
      
      private var _remaining:VT;
      
      private var _multiple:VT;
      
      private var _value_1:VT;
      
      private var _value_2:VT;
      
      private var _value_4:String;
      
      public function OtherBasicData()
      {
         super();
      }
      
      public static function creatOtherBasicData(param1:Number, param2:Number, param3:Number, param4:String, param5:Number, param6:Number, param7:String, param8:Boolean, param9:Number, param10:Number, param11:Number, param12:Number, param13:Number, param14:Number, param15:Number, param16:String) : OtherBasicData
      {
         var _loc17_:OtherBasicData = new OtherBasicData();
         _loc17_._id = VT.createVT(param1);
         _loc17_._fallLevel = VT.createVT(param2);
         _loc17_._type = VT.createVT(param3);
         _loc17_._name = param4;
         _loc17_._frame = VT.createVT(param5);
         _loc17_._color = VT.createVT(param6);
         _loc17_._introduction = param7;
         _loc17_._many = param8;
         _loc17_._pileLimit = VT.createVT(param9);
         _loc17_._times = VT.createVT(param10);
         _loc17_._gold = VT.createVT(param11);
         _loc17_._remaining = VT.createVT(param12);
         _loc17_._multiple = VT.createVT(param13);
         _loc17_._value_1 = VT.createVT(param14);
         _loc17_._value_2 = VT.createVT(param15);
         _loc17_._value_4 = param16;
         return _loc17_;
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(param1:VT) : void
      {
         this._id = param1;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function set name(param1:String) : void
      {
         this._name = param1;
      }
      
      public function get frame() : VT
      {
         return this._frame;
      }
      
      public function set frame(param1:VT) : void
      {
         this._frame = param1;
      }
      
      public function get introduction() : String
      {
         return this._introduction;
      }
      
      public function set introduction(param1:String) : void
      {
         this._introduction = param1;
      }
      
      public function get many() : Boolean
      {
         return this._many;
      }
      
      public function set many(param1:Boolean) : void
      {
         this._many = param1;
      }
      
      public function get FallLevel() : VT
      {
         return this._fallLevel;
      }
      
      public function set FallLevel(param1:VT) : void
      {
         this._fallLevel = param1;
      }
      
      public function get gold() : VT
      {
         return this._gold;
      }
      
      public function set gold(param1:VT) : void
      {
         this._gold = param1;
      }
      
      public function get type() : VT
      {
         return this._type;
      }
      
      public function set type(param1:VT) : void
      {
         this._type = param1;
      }
      
      public function get pileLimit() : VT
      {
         return this._pileLimit;
      }
      
      public function set pileLimit(param1:VT) : void
      {
         this._pileLimit = param1;
      }
      
      public function get times() : VT
      {
         return this._times;
      }
      
      public function set times(param1:VT) : void
      {
         this._times = param1;
      }
      
      public function get remaining() : VT
      {
         return this._remaining;
      }
      
      public function set remaining(param1:VT) : void
      {
         this._remaining = param1;
      }
      
      public function get multiple() : VT
      {
         return this._multiple;
      }
      
      public function set multiple(param1:VT) : void
      {
         this._multiple = param1;
      }
      
      public function get value_1() : VT
      {
         return this._value_1;
      }
      
      public function set value_1(param1:VT) : void
      {
         this._value_1 = param1;
      }
      
      public function get value_2() : VT
      {
         return this._value_2;
      }
      
      public function set value_2(param1:VT) : void
      {
         this._value_2 = param1;
      }
      
      public function get color() : VT
      {
         return this._color;
      }
      
      public function set color(param1:VT) : void
      {
         this._color = param1;
      }
      
      public function get value_4() : String
      {
         return this._value_4;
      }
      
      public function set value_4(param1:String) : void
      {
         this._value_4 = param1;
      }
      
      public function getId() : Number
      {
         return this._id.getValue();
      }
      
      public function getName() : String
      {
         return this._name;
      }
      
      public function getType() : Number
      {
         return this._type.getValue();
      }
      
      public function getFallLevel() : Number
      {
         return this._fallLevel.getValue();
      }
      
      public function getFrame() : Number
      {
         return this._frame.getValue();
      }
      
      public function getColor() : Number
      {
         return this._color.getValue();
      }
      
      public function getIntroduction() : String
      {
         return this._introduction;
      }
      
      public function isMany() : Boolean
      {
         return this._many;
      }
      
      public function getPileLimit() : Number
      {
         return this._pileLimit.getValue();
      }
      
      public function getTimes() : Number
      {
         return this._times.getValue();
      }
      
      public function getGold() : Number
      {
         return this._gold.getValue();
      }
      
      public function getRemaining() : Number
      {
         return this._remaining.getValue();
      }
      
      public function getMultiple() : Number
      {
         return this._multiple.getValue();
      }
      
      public function getValue_1() : Number
      {
         return this._value_1.getValue();
      }
      
      public function getValue_2() : Number
      {
         return this._value_2.getValue();
      }
      
      public function getValue_4() : String
      {
         return this._value_4;
      }
      
      public function creatOther() : Otherobj
      {
         return Otherobj.creatOther(this._id.getValue(),this._times.getValue());
      }
   }
}

