package src.other
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_guangQiu extends Fly
   {
      public var typeX:int = 1;
      
      public var gjPoint:Point;
      
      public var gjTime:int;
      
      public function Skill_guangQiu()
      {
         super();
      }
      
      public static function addToPlayer(param1:Player) : *
      {
         var _loc2_:Class = null;
         if(EquipYN(param1))
         {
            _loc2_ = NewLoad.OtherData.getClass("圣域光球") as Class;
            param1.guangQiu = new _loc2_();
            param1.guangQiu.who = param1;
            param1.guangQiu.init();
            Main.world.moveChild_Other.addChild(param1.guangQiu);
         }
      }
      
      public static function EquipYN(param1:Player) : Boolean
      {
         var _loc2_:Equip = null;
         if(<PERSON><PERSON><PERSON>(param1) && <PERSON><PERSON>an(param1.guangQiu))
         {
            param1.guangQiu.onREMOVED_FROM_STAGE();
         }
         if(Boolean(param1) && Boolean(param1.data.getEquipSlot().getEquipFromSlot(7)))
         {
            _loc2_ = param1.data.getEquipSlot().getEquipFromSlot(7);
            if(_loc2_ && _loc2_.getFrame() == 486 && _loc2_.getRemainingTime() > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function init() : *
      {
         RL = who.RL;
         gongJi_hp_MAX = 6000;
         硬直 = 0;
         gongJi_hp = 2;
         this.x = who.x;
         this.y = who.y - 80;
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public function onREMOVED_FROM_STAGE(param1:* = null) : *
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
         this.who.guangQiu = null;
         this.Dead();
      }
      
      override public function onADDED_TO_STAGE(param1:* = null) : *
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
      }
      
      override public function onENTER_FRAME(param1:*) : *
      {
         if(this.typeX != 2)
         {
            if(who.flyTime != 0)
            {
               if(this.typeX < 3)
               {
                  this.typeX = 3;
               }
            }
            else
            {
               this.typeX = 1;
            }
         }
         this.mcPlay();
         this.Move();
      }
      
      override public function Move() : *
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         this.scaleX = 1;
         if(this.typeX == 2)
         {
            if(this.gjTime <= 0)
            {
               this.typeX = 1;
               this.rotation = 0;
            }
            else
            {
               this.x += this.gjPoint.x;
               this.y += this.gjPoint.y;
            }
            --this.gjTime;
         }
         else if(this.typeX == 1)
         {
            this.rotation = 0;
            _loc1_ = !!who.RL ? int(who.x - 50) : int(who.x + 50);
            _loc2_ = Math.abs(who.x - this.x) / 12;
            _loc3_ = Math.abs(who.y - this.y) / 12;
            if(this.x < _loc1_ - _loc2_ - 20)
            {
               this.x += _loc2_;
            }
            else if(this.x > _loc1_ + _loc2_ + 20)
            {
               this.x -= _loc2_;
            }
            if(this.y < who.y - 45 - _loc3_ - 5)
            {
               this.y += _loc3_;
            }
            else if(this.y > who.y - 45 + _loc3_ + 5)
            {
               this.y -= _loc3_;
            }
         }
         else if(this.typeX == 4)
         {
            this.rotation = 0;
            this.x = who.x;
            this.y = who.y;
            this.scaleX = who.skin.scaleX;
         }
      }
      
      public function GongJiType() : *
      {
         var _loc1_:int = 0;
         var _loc2_:Enemy = null;
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc5_:Point = null;
         var _loc6_:Point = null;
         var _loc7_:Number = NaN;
         var _loc8_:Number = NaN;
         if(Enemy.All.length > 0)
         {
            _loc1_ = Math.random() * Enemy.All.length;
            _loc2_ = Enemy.All[_loc1_];
            _loc3_ = _loc2_.x - this.x;
            _loc4_ = _loc2_.y - this.y;
            _loc5_ = new Point(this.x,this.y);
            _loc6_ = new Point(_loc2_.x,_loc2_.y - 70);
            _loc7_ = Math.atan2(_loc5_.x - _loc6_.x,_loc5_.y - _loc6_.y);
            _loc8_ = _loc7_ * (180 / Math.PI);
            this.rotation = 180 - _loc8_;
            this.gjTime = 10;
            this.gjPoint = new Point(_loc3_ / this.gjTime,_loc4_ / this.gjTime);
            this.typeX = 2;
         }
      }
      
      public function mcPlay() : *
      {
         if(this.typeX == 2)
         {
            if(this.currentLabel != "飞")
            {
               this.gotoAndPlay("飞");
            }
         }
         else if(this.typeX == 1)
         {
            if(this.currentLabel != "跟随")
            {
               this.gotoAndPlay("跟随");
            }
         }
         else if(this.typeX == 3)
         {
            if(this.currentLabel != "消失" && this.currentLabel != "护罩生成" && this.currentLabel != "护罩")
            {
               this.gotoAndPlay("消失");
            }
            if(this.currentLabel != "消失")
            {
               this.gotoAndPlay("护罩生成");
               this.typeX = 4;
            }
         }
         else if(this.typeX == 4)
         {
            if(this.currentLabel != "护罩" && this.currentLabel != "护罩生成")
            {
               this.gotoAndPlay("护罩");
            }
         }
      }
   }
}

