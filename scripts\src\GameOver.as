package src
{
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.tool.*;
   
   public class GameOver extends MovieClip
   {
      public static var noFuHuo:Boolean = false;
      
      public function GameOver()
      {
         super();
         var _loc1_:int = int(Main.gameNum.getValue());
         back_btn.addEventListener(MouseEvent.CLICK,this.goHome);
         if(_loc1_ == 2015 || _loc1_ == 888 || _loc1_ > 5000 && _loc1_ < 5100)
         {
            re_btn.visible = false;
         }
         else
         {
            re_btn.visible = true;
         }
         re_btn.addEventListener(MouseEvent.CLICK,this.RePlay);
         AchData.gkOk();
         TaskData.isOk();
         noFuHuo = true;
         JingLing.QingChuLengQue();
      }
      
      private function goHome(param1:* = null) : *
      {
         Main._stage.frameRate = 27;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Player.一起信春哥();
         this.visible = false;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         removeEventListener(MouseEvent.CLICK,this.goHome);
         removeEventListener(MouseEvent.CLICK,this.RePlay);
         Main._this.Loading();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         noFuHuo = false;
      }
      
      private function RePlay(param1:*) : *
      {
         var _loc2_:int = 0;
         Player.skillYAO = 0;
         Main._stage.frameRate = 27;
         Main.gameNum2.setValue(1);
         Player.一起信春哥();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         this.parent.removeChild(this);
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         if(Main.gameNum.getValue() >= 18 && Main.gameNum.getValue() < 50)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.sel_GuanKaXX_Open();
         }
         else if(Main.gameNum.getValue() == 17)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 1000)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 2000)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            Main.gameNum.setValue(0);
            Main.gameNum2.setValue(0);
            Main._this.GameStart();
            CaiYaoPanel.open();
         }
         else if(Main.gameNum.getValue() > 80 && Main.gameNum.getValue() < 100)
         {
            SelMap.Open(0,0,3,3);
            SelMap.selMapX.Sel_nanDu_mc3.Open();
         }
         else if(GameData.gameLV == 5)
         {
            _loc2_ = 1;
            if(Main.gameNum.getValue() <= 9)
            {
               _loc2_ = 1;
            }
            else if(Main.gameNum.getValue() <= 16)
            {
               _loc2_ = 2;
            }
            else if(Main.gameNum.getValue() <= 70)
            {
               _loc2_ = 3;
            }
            SelMap.Open(0,0,3,_loc2_);
         }
         else
         {
            removeEventListener(MouseEvent.CLICK,this.goHome);
            removeEventListener(MouseEvent.CLICK,this.RePlay);
            Main._this.GameStart();
         }
         noFuHuo = false;
      }
   }
}

