package src
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   
   public class NpcYY extends MovieClip
   {
      public static var _this:NpcYY;
      
      public var life:int = 5;
      
      public var where:int = 0;
      
      public var yao:MovieClip;
      
      public var moveIng:Boolean;
      
      public var yaoIng:Boolean;
      
      public var siWang:Boolean;
      
      public var yaoTime:int = 405;
      
      public var yaoType:int = 0;
      
      public var wuDiTime:int = 0;
      
      public var RL:Boolean = true;
      
      private var walk_power:VT = VT.createVT(7);
      
      private var gravity:int = 20;
      
      public var skin:MovieClip;
      
      public var runType:String = "站立";
      
      public var continuous:Boolean;
      
      public function NpcYY()
      {
         super();
         _this = this;
         this.addSkin();
      }
      
      public static function DelYao() : *
      {
         (YaoYuan.yaoArr[_this.yaoType] as VT).setValue(YaoYuan.yaoArr[_this.yaoType].getValue() + InitData.BuyNum_1.getValue());
         if(_this.yao.parent)
         {
            _this.yao.parent.removeChild(_this.yao);
         }
         _this.yao = null;
         _this.yaoIng = false;
         var _loc1_:int = _this.yaoType + 1;
         NewMC.Open("药园提示",Main._this,480,290,35,_loc1_,true,2);
      }
      
      public static function NewYao() : *
      {
         var _loc1_:Class = Enemy.EnemyArr[3000].getClass("YaoCai") as Class;
         _this.yao = new _loc1_();
         _this.yao.x = Math.random() * 400 + 400;
         _this.yao.y = 500;
         _this.yaoType = Math.random() * 4;
         _this.yao.gotoAndStop(_this.yaoType + 1);
         Main.world.moveChild_ChongWu.addChild(_this.yao);
         Main.world.moveChild_ChongWu.addChild(_this);
      }
      
      private function addSkin() : *
      {
         var _loc1_:Class = Enemy.EnemyArr[3000].getClass("Npc3000") as Class;
         this.skin = new _loc1_();
         this.skin.scaleX = this.skin.scaleY = 0.85;
         addChild(this.skin);
         this.y = 500;
         this.x = 500;
         Main.world.moveChild_ChongWu.addChild(this);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         mouseChildren = mouseEnabled = false;
         return true;
      }
      
      private function onENTER_FRAME(param1:*) : *
      {
         var _loc2_:Number = NaN;
         this.skin.life_mc.gotoAndStop("life" + this.life);
         if(this.yao)
         {
            _loc2_ = this.yaoTime / 405;
            this.yao.time_mc._mc.scaleX = _loc2_;
         }
         if(this.skin.currentFrame >= 126)
         {
            this.visible = false;
            this.siWang = true;
            return;
         }
         if(this.skin.currentLabel == "死亡")
         {
            return;
         }
         if(this.skin.currentLabel != this.runType && this.runType != "被打")
         {
            this.GoTo("站立");
         }
         if(this.wuDiTime > 0)
         {
            --this.wuDiTime;
            if(this.wuDiTime % 4 > 1)
            {
               this.alpha = 0.8;
            }
            else
            {
               this.alpha = 0.6;
            }
         }
         else
         {
            this.alpha = 1;
         }
         if(this.yaoIng)
         {
            if(this.yaoTime > 0)
            {
               --this.yaoTime;
            }
            else
            {
               DelYao();
               this.yaoTime = 405;
            }
         }
         this.WhereAreYou();
      }
      
      public function over() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         _this = null;
      }
      
      public function Down() : *
      {
         if(this.life <= 0 || this.wuDiTime > 0 || this.skin.currentFrame >= 85)
         {
            return;
         }
         if(this.life > 0)
         {
            --this.life;
            this.wuDiTime = 81;
         }
         if(this.life == 0)
         {
            this.GoTo("死亡");
         }
         else
         {
            this.GoTo("被打");
         }
      }
      
      private function WhereAreYou() : *
      {
         if(this.yaoIng)
         {
            if(this.currentLabel != "采药")
            {
               this.GoTo("采药");
            }
            return;
         }
         if(this.yao)
         {
            if(this.x > this.yao.x + 50 || this.x < this.yao.x - 50)
            {
               this.where = this.yao.x;
               this.moveIng = true;
            }
            else
            {
               this.yaoIng = true;
               this.moveIng = false;
               this.GoTo("采药");
            }
         }
         else if(!this.moveIng)
         {
            this.where = Math.random() * 600 + 300;
            this.moveIng = true;
         }
         if(this.moveIng)
         {
            if(this.x > this.where + 50)
            {
               this.x -= 5;
               this.scaleX = 1;
               this.GoTo("移动");
            }
            else if(this.x < this.where - 35)
            {
               this.x += 5;
               this.scaleX = -1;
               this.GoTo("移动");
            }
            else
            {
               this.moveIng = false;
               this.GoTo("站立");
            }
         }
      }
      
      public function isRunOver() : Boolean
      {
         if(currentLabel != this.runType)
         {
            noMove = false;
            return true;
         }
         return false;
      }
      
      public function GoTo(param1:String, param2:Boolean = false) : *
      {
         if(this.skin.currentFrame >= 85)
         {
            return;
         }
         if(this.runType != param1)
         {
            this.runType = param1;
            if(param1 == "站立" || param1 == "移动" || param1 == "采药")
            {
               this.skin.gotoAndStop(this.runType);
            }
            else if(param1 == "被打" || param1 == "死亡")
            {
               this.skin.gotoAndPlay(this.runType);
            }
         }
      }
   }
}

