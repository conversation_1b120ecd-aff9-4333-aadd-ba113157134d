package com.hotpoint.braveManIII.repository.equip
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.equip.*;
   
   public class EquipBaseData
   {
      private var _id:VT;
      
      private var _frame:VT;
      
      private var _name:String;
      
      private var _className:String;
      
      private var _className2:String;
      
      private var _className3:int;
      
      private var _className4:String;
      
      private var _position:VT;
      
      private var _dressLevel:VT;
      
      private var _dropLevel:VT;
      
      private var _descript:String;
      
      private var _price:VT;
      
      private var _reincarnationLimit:VT;
      
      private var _color:VT;
      
      private var _isStrengthen:Boolean;
      
      private var _grid:VT;
      
      private var _suitId:VT;
      
      private var _star:VT;
      
      private var _remainingTime:VT;
      
      private var _defaultTime:VT;
      
      private var _jinhua:VT;
      
      private var _qianghuaMAX:VT;
      
      private var _baseAttrib:Array = [];
      
      private var _skillAttrib:VT;
      
      private var _blessAttrib:Array = [];
      
      public function EquipBaseData()
      {
         super();
      }
      
      public static function createEquipBaseData(param1:Number, param2:Number, param3:String, param4:String, param5:String, param6:int, param7:String, param8:Number, param9:Number, param10:Number, param11:String, param12:Number, param13:Number, param14:Number, param15:Boolean, param16:Number, param17:Number, param18:Number, param19:Number, param20:Number, param21:Number, param22:Number, param23:Array, param24:Array, param25:Number) : EquipBaseData
      {
         var _loc26_:EquipBaseData = new EquipBaseData();
         _loc26_._name = param3;
         _loc26_._className = param4;
         _loc26_._className2 = param5;
         _loc26_._className3 = param6;
         _loc26_._className4 = param7;
         _loc26_._descript = param11;
         _loc26_._isStrengthen = param15;
         _loc26_._baseAttrib = param23;
         _loc26_._blessAttrib = param24;
         _loc26_._id = VT.createVT(param1);
         _loc26_._frame = VT.createVT(param2);
         _loc26_._position = VT.createVT(param8);
         _loc26_._dressLevel = VT.createVT(param9);
         _loc26_._dropLevel = VT.createVT(param10);
         _loc26_._price = VT.createVT(param12);
         _loc26_._reincarnationLimit = VT.createVT(param13);
         _loc26_._color = VT.createVT(param14);
         _loc26_._grid = VT.createVT(param16);
         _loc26_._suitId = VT.createVT(param17);
         _loc26_._star = VT.createVT(param18);
         _loc26_._remainingTime = VT.createVT(param19);
         _loc26_._defaultTime = VT.createVT(param20);
         _loc26_._jinhua = VT.createVT(param21);
         _loc26_._qianghuaMAX = VT.createVT(param22);
         _loc26_._skillAttrib = VT.createVT(param25);
         return _loc26_;
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(param1:VT) : void
      {
         this._id = param1;
      }
      
      public function get frame() : VT
      {
         return this._frame;
      }
      
      public function set frame(param1:VT) : void
      {
         this._frame = param1;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function set name(param1:String) : void
      {
         this._name = param1;
      }
      
      public function get position() : VT
      {
         return this._position;
      }
      
      public function set position(param1:VT) : void
      {
         this._position = param1;
      }
      
      public function get dressLevel() : VT
      {
         return this._dressLevel;
      }
      
      public function set dressLevel(param1:VT) : void
      {
         this._dressLevel = param1;
      }
      
      public function get dropLevel() : VT
      {
         return this._dropLevel;
      }
      
      public function set dropLevel(param1:VT) : void
      {
         this._dropLevel = param1;
      }
      
      public function get descript() : String
      {
         return this._descript;
      }
      
      public function set descript(param1:String) : void
      {
         this._descript = param1;
      }
      
      public function get price() : VT
      {
         return this._price;
      }
      
      public function set price(param1:VT) : void
      {
         this._price = param1;
      }
      
      public function get reincarnationLimit() : VT
      {
         return this._reincarnationLimit;
      }
      
      public function set reincarnationLimit(param1:VT) : void
      {
         this._reincarnationLimit = param1;
      }
      
      public function get color() : VT
      {
         return this._color;
      }
      
      public function set color(param1:VT) : void
      {
         this._color = param1;
      }
      
      public function get isStrengthen() : Boolean
      {
         return this._isStrengthen;
      }
      
      public function set isStrengthen(param1:Boolean) : void
      {
         this._isStrengthen = param1;
      }
      
      public function get grid() : VT
      {
         return this._grid;
      }
      
      public function set grid(param1:VT) : void
      {
         this._grid = param1;
      }
      
      public function get suitId() : VT
      {
         return this._suitId;
      }
      
      public function set suitId(param1:VT) : void
      {
         this._suitId = param1;
      }
      
      public function get baseAttrib() : Array
      {
         return this._baseAttrib;
      }
      
      public function set baseAttrib(param1:Array) : void
      {
         this._baseAttrib = param1;
      }
      
      public function get skillAttrib() : VT
      {
         return this._skillAttrib;
      }
      
      public function set skillAttrib(param1:VT) : void
      {
         this._skillAttrib = param1;
      }
      
      public function get className() : String
      {
         return this._className;
      }
      
      public function set className(param1:String) : void
      {
         this._className = param1;
      }
      
      public function get star() : VT
      {
         return this._star;
      }
      
      public function set star(param1:VT) : void
      {
         this._star = param1;
      }
      
      public function get remainingTime() : VT
      {
         return this._remainingTime;
      }
      
      public function set remainingTime(param1:VT) : void
      {
         this._remainingTime = param1;
      }
      
      public function get className2() : String
      {
         return this._className2;
      }
      
      public function set className2(param1:String) : void
      {
         this._className2 = param1;
      }
      
      public function get defaultTime() : VT
      {
         return this._defaultTime;
      }
      
      public function set defaultTime(param1:VT) : void
      {
         this._defaultTime = param1;
      }
      
      public function get blessAttrib() : Array
      {
         return this._blessAttrib;
      }
      
      public function set blessAttrib(param1:Array) : void
      {
         this._blessAttrib = param1;
      }
      
      public function get jinhua() : VT
      {
         return this._jinhua;
      }
      
      public function set jinhua(param1:VT) : void
      {
         this._jinhua = param1;
      }
      
      public function get className3() : int
      {
         return this._className3;
      }
      
      public function set className3(param1:int) : void
      {
         this._className3 = param1;
      }
      
      public function get className4() : String
      {
         return this._className4;
      }
      
      public function set className4(param1:String) : void
      {
         this._className4 = param1;
      }
      
      public function get qianghuaMAX() : VT
      {
         return this._qianghuaMAX;
      }
      
      public function set qianghuaMAX(param1:VT) : void
      {
         this._qianghuaMAX = param1;
      }
      
      public function getId() : Number
      {
         return this._id.getValue();
      }
      
      public function getPosition() : Number
      {
         return this._position.getValue();
      }
      
      public function getDressLevel() : Number
      {
         return this._dressLevel.getValue();
      }
      
      public function getName() : String
      {
         return this._name;
      }
      
      public function getClassName() : String
      {
         return this._className;
      }
      
      public function getClassName2() : String
      {
         return this._className2;
      }
      
      public function getClassName3() : int
      {
         return this._className3;
      }
      
      public function getClassName4() : String
      {
         return this._className4;
      }
      
      public function getDescript() : String
      {
         return this._descript;
      }
      
      public function getPrice() : Number
      {
         return this._price.getValue();
      }
      
      public function getReincarnationLimit() : Number
      {
         return this._reincarnationLimit.getValue();
      }
      
      public function getColor() : Number
      {
         return this._color.getValue();
      }
      
      public function getStar() : Number
      {
         return this._star.getValue();
      }
      
      public function getRemainingTime() : Number
      {
         return this._remainingTime.getValue();
      }
      
      public function getDefaultTime() : Number
      {
         return this._defaultTime.getValue();
      }
      
      public function getJinHua() : Number
      {
         return this._jinhua.getValue();
      }
      
      public function getQianghuaMAX() : Number
      {
         return this._qianghuaMAX.getValue();
      }
      
      public function getGrid() : Number
      {
         return this._grid.getValue();
      }
      
      public function getSuitId() : Number
      {
         return this._suitId.getValue();
      }
      
      public function getFrame() : Number
      {
         return this._frame.getValue();
      }
      
      public function getIsStrengthen() : Boolean
      {
         return this._isStrengthen;
      }
      
      public function getDropLevel() : Number
      {
         return this._dropLevel.getValue();
      }
      
      public function getBlessAttrib() : Array
      {
         return this._blessAttrib;
      }
      
      private function getRandomType() : uint
      {
         var _loc1_:* = Math.random() * 100;
         if(_loc1_ >= 0 && _loc1_ < 22)
         {
            return 0;
         }
         if(_loc1_ >= 22 && _loc1_ < 44)
         {
            return 1;
         }
         if(_loc1_ >= 44 && _loc1_ < 66)
         {
            return 2;
         }
         if(_loc1_ >= 66 && _loc1_ < 83)
         {
            return 3;
         }
         return 4;
      }
      
      public function createEquip() : Equip
      {
         var _loc4_:EquipBaseAttrib = null;
         var _loc6_:int = 0;
         var _loc7_:EquipBaseAttrib = null;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc14_:EquipBaseAttrib = null;
         var _loc1_:Array = [];
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         for each(_loc4_ in this._baseAttrib)
         {
            if(this._position.getValue() <= 7 || this._position.getValue() >= 10)
            {
               if(_loc4_.getColorType() == 1)
               {
                  _loc1_.push(_loc4_.getClone());
               }
               else if(_loc4_.getColorType() == 2)
               {
                  _loc2_.push(_loc4_.getClone());
               }
               else if(_loc4_.getColorType() == 3)
               {
                  _loc3_.push(_loc4_.getClone());
               }
            }
         }
         if(this._color.getValue() == 2 && _loc2_.length > 0)
         {
            _loc6_ = int(this.getRandomType());
            _loc7_ = _loc2_[_loc6_] as EquipBaseAttrib;
            _loc1_.push(_loc7_);
         }
         if((this._color.getValue() == 3 || this._color.getValue() == 4) && _loc3_.length > 0)
         {
            _loc8_ = 2;
            _loc9_ = -1;
            while(_loc8_ > 0)
            {
               _loc10_ = int(this.getRandomType());
               if(_loc9_ != _loc10_)
               {
                  _loc14_ = _loc3_[_loc10_] as EquipBaseAttrib;
                  _loc1_.push(_loc14_);
                  _loc8_--;
                  _loc9_ = _loc10_;
                  if(_loc3_.length <= 0)
                  {
                     break;
                  }
               }
            }
         }
         if(this._color.getValue() >= 5 && _loc3_.length > 0)
         {
            _loc8_ = 3;
            while(_loc8_ > 0)
            {
               _loc10_ = int(this.getRandomType());
               _loc14_ = _loc3_[_loc10_] as EquipBaseAttrib;
               _loc1_.push(_loc14_);
               _loc8_--;
               if(_loc3_.length <= 0)
               {
                  break;
               }
            }
         }
         var _loc5_:int = int(this._skillAttrib.getValue());
         if(this._id.getValue() != 14667 && this._id.getValue() != 14668 && this._id.getValue() != 14669)
         {
            _loc1_ = this.setFloat(_loc1_);
         }
         if(this._position.getValue() == 8 || this._position.getValue() == 9)
         {
            _loc1_ = null;
         }
         return Equip.creatEquip(this._id.getValue(),_loc1_,_loc5_,this._grid.getValue());
      }
      
      private function setFloat(param1:Array) : Array
      {
         var _loc3_:EquipBaseAttrib = null;
         var _loc4_:* = undefined;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            _loc4_ = _loc3_.getValue();
            if(_loc4_ > 2 && _loc4_ <= 10)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random()));
            }
            else if(_loc4_ > 10 && _loc4_ <= 20)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 2));
            }
            else if(_loc4_ > 20 && _loc4_ <= 30)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 3));
            }
            else if(_loc4_ > 30 && _loc4_ <= 40)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 3));
            }
            else if(_loc4_ > 40 && _loc4_ <= 50)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 4));
            }
            else if(_loc4_ > 50 && _loc4_ <= 60)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 4));
            }
            else if(_loc4_ > 60 && _loc4_ <= 70)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 5));
            }
            else if(_loc4_ > 70 && _loc4_ <= 80)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 5));
            }
            else if(_loc4_ > 80 && _loc4_ <= 90)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 6));
            }
            else if(_loc4_ > 90 && _loc4_ <= 100)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 6));
            }
            else if(_loc4_ > 100 && _loc4_ <= 120)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 7));
            }
            else if(_loc4_ > 120 && _loc4_ <= 140)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 7));
            }
            else if(_loc4_ > 140 && _loc4_ <= 160)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 7));
            }
            else if(_loc4_ > 160 && _loc4_ <= 200)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 8));
            }
            else if(_loc4_ > 200 && _loc4_ <= 260)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 9));
            }
            else if(_loc4_ > 260 && _loc4_ <= 330)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 9));
            }
            else if(_loc4_ > 330 && _loc4_ <= 400)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 10));
            }
            else if(_loc4_ > 400 && _loc4_ <= 500)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 12));
            }
            else if(_loc4_ > 500 && _loc4_ <= 600)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 14));
            }
            else if(_loc4_ > 600 && _loc4_ <= 700)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 14));
            }
            else if(_loc4_ > 700 && _loc4_ <= 800)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 15));
            }
            else if(_loc4_ > 800 && _loc4_ <= 900)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 15));
            }
            else if(_loc4_ > 900 && _loc4_ <= 1000)
            {
               _loc3_.setValue(_loc4_ - Math.round(Math.random() * 15));
            }
            _loc2_.push(_loc3_);
         }
         return _loc2_;
      }
      
      public function createShopEquip() : Equip
      {
         var _loc4_:EquipBaseAttrib = null;
         var _loc6_:int = 0;
         var _loc7_:EquipBaseAttrib = null;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:EquipBaseAttrib = null;
         var _loc1_:Array = [];
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         for each(_loc4_ in this._baseAttrib)
         {
            if(_loc4_.getColorType() == 1)
            {
               _loc1_.push(_loc4_.getClone());
            }
            else if(_loc4_.getColorType() == 2)
            {
               _loc2_.push(_loc4_.getClone());
            }
            else if(_loc4_.getColorType() == 3)
            {
               _loc3_.push(_loc4_.getClone());
            }
         }
         if(this._color.getValue() == 2 && _loc2_.length > 0)
         {
            _loc6_ = int(this.getRandomType());
            _loc7_ = _loc2_[_loc6_] as EquipBaseAttrib;
            _loc1_.push(_loc7_);
         }
         if(this._color.getValue() == 3 && _loc3_.length > 0)
         {
            _loc8_ = 2;
            _loc9_ = -1;
            while(_loc8_ > 0)
            {
               _loc10_ = int(this.getRandomType());
               if(_loc9_ != _loc10_)
               {
                  _loc11_ = _loc3_[_loc10_] as EquipBaseAttrib;
                  _loc1_.push(_loc11_);
                  _loc8_--;
                  _loc9_ = _loc10_;
                  if(_loc3_.length <= 0)
                  {
                     break;
                  }
               }
            }
         }
         var _loc5_:int = int(this._skillAttrib.getValue());
         if(this._position.getValue() == 5)
         {
            if(Math.random() * 10 < 5)
            {
               _loc5_ = 0;
            }
         }
         return Equip.creatEquip(this._id.getValue(),_loc1_,_loc5_,this._grid.getValue());
      }
   }
}

