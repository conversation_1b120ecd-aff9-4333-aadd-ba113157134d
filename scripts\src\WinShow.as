package src
{
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   public class WinShow extends MovieClip
   {
      public static var txt_1:int = 0;
      
      public static var txt_2:int = 0;
      
      public static var txt_3:int = 0;
      
      public static var txt_4:int = 0;
      
      public static var txt_5:int = 0;
      
      public function WinShow()
      {
         super();
         GameData.GuanKaXX();
         this.TextShow();
         Main._stage.addChild(this);
         Main._stage.frameRate = 0;
         var _loc1_:int = int(Main.gameNum.getValue());
         if(_loc1_ == 2015 || _loc1_ == 888 || _loc1_ > 5000 && _loc1_ < 5100)
         {
            re_btn.visible = false;
         }
         else
         {
            re_btn.visible = true;
         }
         re_btn.addEventListener(MouseEvent.CLICK,this.RePlay);
         back_btn.addEventListener(MouseEvent.CLICK,this.goHome);
         AchData.setTg();
         AchData.gkOk();
         TaskData.setTg();
         TaskData.isOk();
         JingLing.QingChuLengQue();
      }
      
      public static function All_0() : *
      {
         txt_1 = txt_2 = txt_3 = txt_4 = txt_5 = 0;
      }
      
      public static function LianJi(param1:int) : *
      {
         if(param1 > txt_2)
         {
            txt_2 = param1;
         }
      }
      
      private function TextShow() : *
      {
         if(Main.gameNum.getValue() == SixOne_Interface.guankaNum && SixOne_Interface.state2021.getValue() == 2)
         {
            SixOne_Interface.state2021.setValue(3);
            SixOne_Interface.okTimes2021.setValue(SixOne_Interface.okTimes2021.getValue() + 1);
         }
         time_txt.text = this.TimeNum(txt_1);
         lianji_txt.text = "" + txt_2;
         num_txt.text = "" + txt_3;
         num2_txt.text = "" + txt_4;
         p_txt.text = "" + txt_5;
         var _loc1_:String = " / 1P等级" + Main.player_1.data.getLevel();
         if(Main.P1P2)
         {
            _loc1_ += "/ 2P等级" + Main.player_2.data.getLevel();
         }
         var _loc2_:String = " / 关卡:" + Main.gameNum.getValue();
         if(Main.gameNum.getValue() > 100 && Main.gameNum.getValue() < 200)
         {
            _loc2_ = " / 副本:" + (Main.gameNum.getValue() - 100);
         }
         else if(Main.gameNum.getValue() == 17)
         {
            _loc2_ = " / 挑关卡:" + (Main.gameNum.getValue() - 16);
         }
         else if(Main.gameNum.getValue() > 17 && Main.gameNum.getValue() < 50)
         {
            _loc2_ = " / 星灵擂台:" + (Main.gameNum.getValue() - 17);
         }
         else if(Main.gameNum.getValue() > 50 && Main.gameNum.getValue() < 81)
         {
            _loc2_ = " / 海底:" + (Main.gameNum.getValue() - 50);
         }
         else if(Main.gameNum.getValue() > 80 && Main.gameNum.getValue() < 100)
         {
            _loc2_ = " / 海底副本:" + (Main.gameNum.getValue() - 80);
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            _loc2_ = " / 药园";
         }
         else if(GameData.gameLV == 6)
         {
            _loc2_ = " / 公会boss";
         }
         else
         {
            _loc2_ = " / 关卡:" + Main.gameNum.getValue();
         }
         var _loc3_:String = " / 难度:★";
         if(GameData.gameLV == 2)
         {
            _loc3_ = " / 难度★★";
         }
         else if(GameData.gameLV == 3)
         {
            _loc3_ = " / 难度★★★";
         }
         else if(GameData.gameLV == 4)
         {
            _loc3_ = " / 难度★★★★";
         }
         else if(GameData.gameLV == 5)
         {
            _loc3_ = " / 难度★★★★★";
         }
         else if(GameData.gameLV == 6)
         {
            _loc3_ = "";
         }
         XX_txt.text = "版本" + Main.varX / 100 + _loc1_ + _loc2_ + _loc3_;
      }
      
      private function TimeNum(param1:int) : String
      {
         param1 /= 27;
         var _loc2_:int = param1 / 3600;
         var _loc3_:int = (param1 - _loc2_ * 3600) / 60;
         var _loc4_:int = param1 - _loc2_ * 3600 - _loc3_ * 60;
         return _loc2_ + ":" + _loc3_ + ":" + _loc4_;
      }
      
      private function goHome(param1:* = null) : *
      {
         GongHuiRenWu.gameOK(Main.gameNum.getValue(),GameData.gameLV);
         Main._stage.frameRate = 27;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Load.loadGameNum = 1;
         Load.loadGameNum2 = 1;
         Player.一起信春哥();
         removeEventListener(MouseEvent.CLICK,this.goHome);
         removeEventListener(MouseEvent.CLICK,this.RePlay);
         Main._this.Loading();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         this.parent.removeChild(this);
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         noFuHuo = false;
      }
      
      private function RePlay(param1:*) : *
      {
         GongHuiRenWu.gameOK(Main.gameNum.getValue(),GameData.gameLV);
         Player.skillYAO = 0;
         Main._stage.frameRate = 27;
         Main.gameNum2.setValue(1);
         Player.一起信春哥();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         this.parent.removeChild(this);
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         if(Main.gameNum.getValue() >= 18 && Main.gameNum.getValue() < 50)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.sel_GuanKaXX_Open();
         }
         else if(Main.gameNum.getValue() == 17)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 1000)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 2000)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            Main.gameNum.setValue(0);
            Main.gameNum2.setValue(0);
            Main._this.GameStart();
            CaiYaoPanel.open();
         }
         else if(Main.gameNum.getValue() > 80 && Main.gameNum.getValue() < 100)
         {
            SelMap.Open(0,0,3,3);
            SelMap.selMapX.Sel_nanDu_mc3.Open();
         }
         else
         {
            removeEventListener(MouseEvent.CLICK,this.goHome);
            removeEventListener(MouseEvent.CLICK,this.RePlay);
            Main._this.GameStart();
         }
         noFuHuo = false;
      }
   }
}

