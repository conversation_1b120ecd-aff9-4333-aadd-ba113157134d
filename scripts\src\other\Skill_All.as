package src.other
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_All extends Fly
   {
      public static var Skill_p1:Skill_All;
      
      public static var Skill_p2:Skill_All;
      
      public static var gjNum:Number = 1;
      
      public function Skill_All()
      {
         super();
      }
      
      public static function Add_Skill_All(param1:Player) : *
      {
         var _loc4_:Class = null;
         var _loc5_:MovieClip = null;
         if(param1.data.getEquipSlot().getEquipFromSlot(6) && param1.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 451 && param1.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            ++param1.gjXX_num;
         }
         if(param1.gjXX_num < 8 || Main.gameNum.getValue() == 999)
         {
            return;
         }
         param1.gjXX_num = 0;
         var _loc2_:int = 217;
         gjNum = 1;
         var _loc3_:Equip = param1.data.getEquipSlot().getEquipFromSlot(7);
         if(_loc3_ && _loc3_.getFrame() == 452 && _loc3_.getRemainingTime() > 0)
         {
            _loc2_ = 325;
            gjNum = 1.5;
         }
         if(param1 == Main.player_1 && Boolean(Skill_p1))
         {
            Skill_p1.time = _loc2_;
         }
         else if(Boolean(Main.P1P2) && param1 == Main.player_2 && Boolean(Skill_p2))
         {
            Skill_p2.time = _loc2_;
         }
         else
         {
            _loc4_ = NewLoad.OtherData.getClass("时装全范围伤害") as Class;
            _loc5_ = new _loc4_();
            param1.skin_W.addChild(_loc5_);
            if(param1 == Main.player_1)
            {
               Skill_p1 = _loc5_;
               Skill_p1.time = _loc2_;
            }
            else
            {
               Skill_p2 = _loc5_;
               Skill_p2.time = _loc2_;
            }
         }
      }
      
      public static function XiaoGuo() : *
      {
         var _loc1_:int = 0;
         var _loc2_:Class = null;
         var _loc3_:MovieClip = null;
         var _loc4_:Enemy = null;
         var _loc5_:int = 0;
         if(Main.gameNum.getValue() == 999)
         {
            Dead();
            return;
         }
         if(Boolean(Skill_p1) || Boolean(Skill_p2))
         {
            _loc1_ = 0;
            while(_loc1_ < Enemy.All.length)
            {
               _loc2_ = NewLoad.OtherData.getClass("Skill_All_mcX") as Class;
               _loc3_ = new _loc2_();
               _loc4_ = Enemy.All[_loc1_];
               Main.world.moveChild_Enemy.addChild(_loc3_);
               _loc3_.x = _loc4_.x + (Math.random() * 100 - 50);
               _loc3_.y = _loc4_.y + (Math.random() * 30 - 10);
               _loc5_ = Math.random() * 3 + 1;
               _loc3_.gotoAndPlay(_loc5_);
               _loc1_++;
            }
         }
      }
      
      override public function onADDED_TO_STAGE(param1:* = null) : *
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         var _loc2_:int = 0;
         while(_loc2_ < All.length)
         {
            if(All[_loc2_] == this)
            {
               return;
            }
            _loc2_++;
         }
         All[All.length] = this;
         var _loc3_:MovieClip = this;
         while(_loc3_ != _stage)
         {
            if(_loc3_ is Skin_WuQi && _loc3_.parent is Player)
            {
               who = _loc3_.parent;
               this.RL = (who as Player).RL;
               gongJi_hp_MAX = 6000;
               硬直 = 0;
               gongJi_hp = gjNum;
               attTimes = who.skin.attTimes;
               continuous = true;
               runX = 0;
               runTime = who.skin.runTime;
               break;
            }
            _loc3_ = _loc3_.parent as MovieClip;
         }
         this.who.addChild(this);
      }
      
      override public function onENTER_FRAME(param1:*) : *
      {
         if(over)
         {
            this.Dead();
            return;
         }
         if(this.currentFrame == 2 || this.currentFrame == 15)
         {
            XiaoGuo();
         }
         if(!over && time != -1)
         {
            --time;
            if(time == -1)
            {
               gotoAndPlay("结束");
               continuous = false;
               over = true;
            }
         }
         if(continuous && this.currentLabel != "运行")
         {
            gotoAndPlay("运行");
         }
         if(!over)
         {
            this.who.addChild(this);
         }
         otherXX();
      }
      
      override public function Dead() : *
      {
         stop();
         this.visible = false;
         for(i in All)
         {
            if(All[i] == this)
            {
               All.splice(i,1);
            }
         }
         if(Boolean(Main.player_1) && this.who == Main.player_1)
         {
            Skill_p1 = null;
         }
         else if(Main.P1P2 && Main.player_2 && this.who == Main.player_2)
         {
            Skill_p2 = null;
         }
         if(this.parent)
         {
            this.parent.removeChild(this);
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
      
      override public function Dead2() : *
      {
      }
   }
}

