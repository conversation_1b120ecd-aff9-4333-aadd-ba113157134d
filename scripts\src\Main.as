package src
{
   import com.*;
   import com.ByteArrayXX.*;
   import com.adobe.serialization.json.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.elves.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.make.*;
   import com.hotpoint.braveManIII.models.monsterCard.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.pet.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.petGem.*;
   import com.hotpoint.braveManIII.models.plan.*;
   import com.hotpoint.braveManIII.models.player.*;
   import com.hotpoint.braveManIII.models.quest.*;
   import com.hotpoint.braveManIII.models.skill.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.models.task.*;
   import com.hotpoint.braveManIII.models.title.*;
   import com.hotpoint.braveManIII.models.wantedTask.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.plan.*;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.composePanel.*;
   import com.hotpoint.braveManIII.views.elvesPanel.*;
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.fanpaiPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.jiangliPanel.*;
   import com.hotpoint.braveManIII.views.makePanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.setProfession.*;
   import com.hotpoint.braveManIII.views.setTransfer.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.stampPanel.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import com.hotpoint.braveManIII.views.strPanel.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import com.hotpoint.braveManIII.views.titelPanel.*;
   import com.hotpoint.braveManIII.views.tuijianPanel.*;
   import com.hotpoint.braveManIII.views.wantedPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.external.*;
   import flash.net.*;
   import flash.system.*;
   import flash.utils.*;
   import src._data.*;
   import src.npc.*;
   import src.other.*;
   import src.tool.*;
   import unit4399.events.*;
   
   public class Main extends MovieClip
   {
      public static var wts:WantedTaskSlot;
      
      public static var _stage:Stage;
      
      public static var _this:Main;
      
      public static var _stageWidth:int;
      
      public static var _stageHeight:int;
      
      public static var world:Map;
      
      public static var player_1:Player;
      
      public static var player_2:Player;
      
      public static var P1P2:Boolean;
      
      public static var player1:PlayerData;
      
      public static var player2:PlayerData;
      
      public static var varX:int;
      
      public static var verify:int;
      
      public static var initTime:VT;
      
      public static var saveTimeX:int;
      
      public static var JiNeng_mc:MovieClip;
      
      public static var sdysbtn:MovieClip;
      
      public static var serviceHold:* = null;
      
      public static var HuiTie8Num:int = 0;
      
      public static var HuiTie8Arr:Array = [0,0,0,0,0,0,0,0,0,0];
      
      public static var LuoPanArr:Array = [0,0,0,0];
      
      public static var shengdan:Array = [VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0)];
      
      public static var shengdanTimes:VT = VT.createVT(0);
      
      public static var lingQueArr:Array = [0,0,0,0,0,0,0,0,0,0,0];
      
      public static var lingQueArr2:Array = [0,0,0,0,0,0,0,0,0,0,0];
      
      public static var fps:int = 30;
      
      public static var gameNum:VT = VT.createVT();
      
      public static var gameNum2:VT = VT.createVT();
      
      public static var varX_old:int = 0;
      
      public static var guanKa:Array = [];
      
      public static var saveNum:int = -1;
      
      public static var stopXX:Boolean = false;
      
      public static var loadSaveOver:Boolean = false;
      
      public static var serverTime:VT = VT.createVT();
      
      public static var serverTimeStr:String = "";
      
      public static var serverDayNum:Number = 0;
      
      public static var userId:uint = 0;
      
      public static var logName:String = "";
      
      public static var logName2:String = "";
      
      public static var logNameSave:String = "";
      
      public static var logYN:Boolean = false;
      
      public static var NoLog:int = 0;
      
      public static var NoLogInfo:Array = new Array();
      
      public static var tiaoShiYN:Boolean = false;
      
      public static var noSave:int = -43994399;
      
      public static var Map0_YN:Boolean = false;
      
      public static var Map0_YN2:Boolean = false;
      
      public static var water:VT = VT.createVT(1);
      
      public static var questArr:Array = [[false,84110],[false,84111],[false,84112],[false,84113],[false,84114]];
      
      public static var newPlay:int = 0;
      
      public static var yinCangP1P2:int = 0;
      
      public static var senDanArr:* = ["彩球","圣诞帽","圣诞靴","驯鹿","糖果","雪花","礼物","铃铛","糖果棒"];
      
      public static var tempDataEnd:Boolean = false;
      
      private static var buy_DuoKai:Boolean = false;
      
      public static var saveMc_YN:Boolean = true;
      
      public static var saveStop:Array = [false,true];
      
      public static var saveID:VT = VT.createVT();
      
      public static var ServerTimeNum:int = 0;
      
      public static var ysbtn:SimpleButton = new YSbtn();
      
      private var _4399_function_store_id:String = "3885799f65acec467d97b4923caebaae";
      
      internal var _4399_function_payMoney_id:String = "10f73c09b41d9f41e761232f5f322f38";
      
      internal var _4399_function_shop_id:String = "30ea6b51a23275df624b781c3eb43ac6";
      
      internal var _4399_function_rankList_id:String = "69f52ab6eb1061853a761ee8c26324ae";
      
      internal var _4399_function_union_id:String = "7c7a741b186b91e2975006321918345f";
      
      public var blackXXXX:遮罩 = new 遮罩();
      
      private var timeLog:uint = 30;
      
      private var duoKai_num:int = 0;
      
      public var EmenyDataMD5_YN:Boolean = false;
      
      internal var chongZhiFanLi_YN:Boolean = true;
      
      public function Main()
      {
         super();
         if(stage)
         {
            this.InitXX();
         }
         else
         {
            addEventListener(Event.ADDED_TO_STAGE,this.InitXX);
         }
      }
      
      public static function addShengDan() : void
      {
         var _loc1_:int = Math.random() * 9;
         NewMC.Open("圣诞挂件",Main._this,480,290,30,0,true,2,senDanArr[_loc1_]);
         if((shengdan[_loc1_] as VT).getValue() == 0)
         {
            (shengdan[_loc1_] as VT).setValue(1);
         }
      }
      
      public static function LoadTempDataArr() : *
      {
         TempData.Init();
         tempDataEnd = true;
      }
      
      private static function NologFun() : Boolean
      {
         var _loc1_:String = ExternalInterface.call("UniLogin.getUid");
         TiaoShi.txtShow("退出帐号检测:" + _loc1_ + "," + userId);
         if(tiaoShiYN == true)
         {
            return true;
         }
         if(_loc1_ != "" && Number(_loc1_) == userId)
         {
            return true;
         }
         DuoKai_Info.Open(2,_loc1_);
         return false;
      }
      
      public static function ChongZhi() : Boolean
      {
         var _loc4_:String = null;
         var _loc5_:String = null;
         var _loc1_:int = int(Math.random() * 79461312 + 10000000);
         var _loc2_:Object = {
            "pay_union":10014,
            "username":Main.logName,
            "pay_money":100,
            "random_num":_loc1_,
            "role":Main.logName,
            "is_wpay":2
         };
         var _loc3_:String = JSONs.encode(_loc2_);
         if(ExternalInterface.available)
         {
            _loc4_ = ExternalInterface.call("eval","navigator.userAgent");
            _loc5_ = _loc4_;
            if(_loc5_.indexOf("4399.air.wd") >= 0 || _loc5_.indexOf("4399.yzzr.air") >= 0)
            {
               ExternalInterface.call("openPay",_loc3_);
               return true;
            }
         }
         if(Main.serviceHold)
         {
            PayMoneyVar.instance.money = 100;
            Main.serviceHold.payMoney_As3(PayMoneyVar.instance);
         }
         return false;
      }
      
      public static function DuoKai_Fun(param1:Boolean = false) : *
      {
         if(serviceHold)
         {
            buy_DuoKai = param1;
            serviceHold.getStoreState();
         }
      }
      
      public static function Save(param1:Boolean = true, param2:int = 0) : *
      {
         if(!Main.tiaoShiYN && NologFun() == false)
         {
            return;
         }
         if(param2 == 1)
         {
            JiHua_Interface.ppp4_9 = true;
         }
         if(!JiHua_Interface._this)
         {
            JiHua_Interface.initArr();
         }
         JiHua_Interface.planGO();
         saveStop[0] = true;
         saveStop[1] = param1;
         saveID.setValue(param2);
         DuoKai_Fun();
      }
      
      public static function SaveFun() : *
      {
         if(saveID.getValue() > 0)
         {
            if(saveID.getValue() == 1)
            {
               saveID.setValue(2);
            }
         }
         EquipShopPanel.equipShopNoRMBOK();
         MenuTooltip.getChongWu();
         MenuTooltip.getChongWu2();
         MenuTooltip.getDuanWu();
         MenuTooltip.getXingLing();
         MenuTooltip.getQiXi();
         MenuTooltip.getShiZhuang();
         MenuTooltip.getChiBang();
         Play_Interface.getBuChang();
         NewWantedPanel.getJiangLi();
         NewPetPanel.xianshiJN();
         xilianPanel.xlOK();
         ChunJiePanel.lingQuOK();
         XingLing_Interface.saveOK = true;
         StrPanel.QiangHuaOK();
         TiaoZhanPaiHang_Interface.GeiObjMC_Play();
         NewPetPanel.saveShowRL();
         ZhuanPan.saveYN = true;
         JinHuaPanel2.saveOK = true;
      }
      
      public static function Save2(param1:Boolean = true) : *
      {
         saveMc_YN = param1;
         if(noSave > InitData.BuyNum_0.getValue() || Boolean(DuoKai_Info._noSave))
         {
            return;
         }
         var _loc2_:String = "";
         _loc2_ += "P1:Lv" + player1.getLevel();
         if(Main.P1P2)
         {
            _loc2_ += " / P2:Lv" + player2.getLevel();
         }
         var _loc3_:Object = new Object();
         _loc3_["var"] = Main.varX;
         if(Main.logNameSave == "" && Main.varX_old < 182)
         {
            _loc3_["logNameSave"] = MD5contrast.SaveStr(logName.toLocaleLowerCase(),saveNum);
         }
         else
         {
            _loc3_["logNameSave"] = Main.logNameSave;
         }
         _loc3_["NoLog"] = Main.NoLog;
         _loc3_["NoLogInfo"] = Main.NoLogInfo;
         _loc3_["saveTimeX"] = saveTimeX;
         var _loc4_:int = 1;
         if(Main.P1P2)
         {
            _loc4_ = 2;
         }
         else
         {
            _loc4_ = 1;
         }
         var _loc5_:int = 1;
         while(_loc5_ <= _loc4_)
         {
            _loc3_["p" + _loc5_] = new Object();
            _loc3_["p" + _loc5_] = Main["player" + _loc5_];
            _loc5_++;
         }
         _loc3_["P1P2"] = Main.P1P2;
         _loc3_["changKu"] = StoragePanel.storage;
         _loc3_["guanKa"] = Main.guanKa;
         _loc3_["questArr"] = Main.questArr;
         _loc3_["verify"] = Math.random() * 9999;
         _loc3_["InitDataX"] = InitData.SaveInitData();
         _loc3_["RenWu"] = TaskData.saveTask();
         _loc3_["buyTime"] = Data_KillShop.buyTime;
         _loc3_["buyArr"] = Data_KillShop.buyArr;
         _loc3_["KaiQiShopArr2"] = ShopKillPoint.KaiQiShopArr2;
         _loc3_["MakeData"] = MakeData.saveMake();
         _loc3_["lingQueArr"] = lingQueArr;
         _loc3_["lingQueArr2"] = lingQueArr2;
         _loc3_["TeShuHuoDongArr"] = TeShuHuoDong.TeShuHuoDongArr;
         _loc3_["chengjiu"] = AchData.save();
         _loc3_["xuansang"] = Main.wts;
         _loc3_["kaPian"] = CardPanel.monsterSlot;
         _loc3_["petB"] = NewPetPanel.bag;
         _loc3_["xgKEY"] = NewPetPanel.XGkey;
         _loc3_["lvKEY"] = NewPetPanel.LVkey;
         _loc3_["lqTIMES"] = JiangLiPanel.LQtimes;
         _loc3_["XueMai"] = NewPetPanel.XueMai;
         _loc3_["caiYao0"] = CaiYaoPanel.saveArr;
         _loc3_["caiYao1"] = CaiYaoPanel.saveArr2;
         _loc3_["yaoArr"] = YaoYuan.yaoArr;
         _loc3_["huaZhi"] = Play_Interface.huaZhi;
         _loc3_["Map0_YN"] = Main.Map0_YN;
         _loc3_["Map0_YN2"] = Main.Map0_YN2;
         _loc3_["water"] = Main.water;
         _loc3_["LuoPanSuiPian"] = Main.LuoPanArr;
         _loc3_["JiHua"] = PlanFactory.JiHuaData;
         _loc3_["JiHua2"] = PlanFactory.JiHuaData2;
         _loc3_["XinglingUp"] = XingLingFactory.xingLingData;
         _loc3_["vipUserArr"] = Vip_Interface.vipUserArr;
         _loc3_["TaskDataBoXX5"] = TaskData.BoXX;
         _loc3_["banben_save"] = SixOne_Interface.banben_save;
         _loc3_["dayTimes2021"] = SixOne_Interface.dayTimes2021;
         _loc3_["nowdate2021"] = SixOne_Interface.nowdate2021;
         _loc3_["okTimes2021"] = SixOne_Interface.okTimes2021;
         _loc3_["state2021"] = SixOne_Interface.state2021;
         _loc3_["buChangHeCeng2"] = Play_Interface.buChangHeCeng2;
         _loc3_["huaArr"] = Panel_XianHua.huaArr;
         _loc3_["chunjie_saveNew"] = ChunJiePanel.saveNew;
         _loc3_["chunjie1_2022"] = ChunJiePanel.saveArr_2022;
         _loc3_["chunjie2_2022"] = ChunJiePanel.saveArr2_2022;
         _loc3_["varSave"] = NewYear_Interface.varSave;
         _loc3_["newyearKey2022"] = NewYear_Interface.key2022;
         _loc3_["newyearJuan2022"] = NewYear_Interface.juan2022;
         _loc3_["newyearJL2022"] = NewYear_Interface.allJiangLi2022;
         _loc3_["newyearYN2022"] = NewYear_Interface.openYesNo2022;
         _loc3_["arrAll"] = FiveOne_Interface.arrAll;
         _loc3_["ZhuanPan"] = ZhuanPan.saveTime;
         _loc3_["ZhuanPanYN"] = ZhuanPan.Num1YN2;
         _loc3_["qdArr"] = QianDao.qdArr;
         _loc3_["tuijian"] = TuiJianPanel.arrSave;
         _loc3_["initTime"] = Main.initTime;
         _loc3_["lingQu16"] = ChongZhi_Interface.lingQu16;
         _loc3_["gameTime"] = JiFenLingQue3.gameTime;
         _loc3_["TiaoZan_newTime"] = PaiHang_Data.newTime;
         _loc3_["inGameNum"] = PaiHang_Data.inGameNum;
         _loc3_["sArr2"] = PaiHang_Data.sArr2;
         _loc3_["jiFenArr"] = PaiHang_Data.jiFenArr;
         _loc3_["PK_jiFenArr"] = PK_UI.jiFenArr;
         _loc3_["saveTime"] = JiFenLingQue3.saveTime;
         _loc3_["saveTime2"] = GengXin.saveTime;
         _loc3_["saveVar"] = GengXin.saveVar;
         _loc3_["yinCangP1P2"] = Main.yinCangP1P2;
         _loc3_["HuiTie8Arr"] = HuiTie8Arr;
         _loc3_["lhs_Data"] = LingHunShi_Interface.lhs_Data;
         _loc3_["noMovPlayArr"] = LingHunShi_Interface.noMovPlayArr;
         _loc3_["rwArr"] = GongHuiRenWu.rwArr;
         _loc3_["rwArr2"] = GongHuiRenWu.rwArr2;
         _loc3_["tzData"] = GongHuiTiaoZan.tzData;
         _loc3_["yueKaTime"] = YueKa_Interface.yueKaTime;
         _loc3_["yueKaTime2"] = YueKa_Interface.yueKaTime2;
         _loc3_["lingQu_new"] = ChongZhi_Interface4.lingQu_new;
         var _loc6_:ByteArray = new ByteArray();
         _loc6_.writeObject(_loc3_);
         _loc6_.position = 0;
         var _loc7_:String = Base64.encodeByteArray(_loc6_);
         if(Main.saveNum == -1)
         {
            Main.serviceHold.saveData(_loc2_,_loc7_);
         }
         else
         {
            Main.serviceHold.saveData(_loc2_,_loc7_,false,Main.saveNum);
         }
      }
      
      public static function GetServerTime(param1:Boolean = false) : *
      {
         if(param1)
         {
            serviceHold.getServerTime();
            return;
         }
         if(serviceHold && ServerTimeNum <= 0)
         {
            serviceHold.getServerTime();
            ServerTimeNum = 90000;
         }
         else
         {
            --ServerTimeNum;
         }
      }
      
      public static function createServerDateA(param1:String) : Number
      {
         var _loc2_:Array = param1.split(" ");
         var _loc3_:Array = (_loc2_[0] as String).split("-");
         var _loc4_:Array = (_loc2_[1] as String).split(":");
         var _loc5_:String = "" + _loc3_[1] + "/" + _loc3_[2] + "/" + _loc3_[0] + " " + _loc2_[1];
         var _loc6_:Date = new Date(_loc5_ + " GMT+0800");
         var _loc7_:* = _loc6_.time;
         return Math.floor(_loc7_ / 86400000);
      }
      
      public static function allClosePanel() : *
      {
         SuppliesShopPanel.close();
         SkillPanel.close();
         SetTransferPanel.close();
         SetKeyPanel.close();
         ItemsPanel.close();
         EquipShopPanel.close();
         StoragePanel.close();
         村长任务界面.Close();
         装备商人界面.Close();
         装备商人界面2.Close();
         药水商人界面.Close();
         药水商人界面2.Close();
         技能导师界面.Close();
         技能导师界面2.Close();
         探险家界面.Close();
         探险家界面2.Close();
         伽妮雅界面.Close();
         塔丽莎界面.Close();
         星冰界面.Close();
         船长界面.Close();
         InWaterDoor.Close();
         InWaterKey.Close();
         Shop4399.Close();
         ShopKillPoint.Close();
         xilianPanel.close();
         NewPetPanel.close();
         StrPanel.close();
         ComPosePanel.close();
         MakePanel.close();
         Shop_LiBao.Close();
         Shop_LiBao2.Close();
         AchPanel.close();
         StampPanel.close();
         ItemsPanel.szcbTime();
         TaskPanel.close();
         TitelPanel.CengHaoShow();
         CardPanel.close();
         FanPaiPanel.close();
         ChunJiePanel.close();
         ElvesPanel.close();
         JiangLiPanel.close();
         PK_UI.Close();
         PK_UI_Sel.Close();
         NewWantedPanel.close();
         AchData.upPoint();
         YueKa_Interface.Close();
         if(Shop_LiBao.shopX)
         {
            Shop_LiBao.JianCe();
            Shop_LiBao2.JianCe();
         }
         XingLing_Interface.CloseX();
      }
      
      public static function isVip() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < Main.player1.getTitleSlot().getListLength())
         {
            if(Main.player1.getTitleSlot().getTitleFromSlot(_loc1_).getId() == 25)
            {
               return true;
            }
            _loc1_++;
         }
         return false;
      }
      
      public static function getQuest(param1:Number) : Boolean
      {
         var _loc2_:Number = 0;
         while(_loc2_ < questArr.length)
         {
            if(questArr[_loc2_][1] == param1)
            {
               return questArr[_loc2_][0];
            }
            _loc2_++;
         }
         return false;
      }
      
      public static function setQuest(param1:Number) : void
      {
         var _loc2_:Number = 0;
         while(_loc2_ < questArr.length)
         {
            if(questArr[_loc2_][1] == param1)
            {
               questArr[_loc2_][0] = true;
               break;
            }
            _loc2_++;
         }
      }
      
      public static function getAllQuest() : Boolean
      {
         var _loc1_:Number = 0;
         while(_loc1_ < questArr.length)
         {
            if(questArr[_loc1_][0])
            {
               return true;
            }
            _loc1_++;
         }
         return false;
      }
      
      public static function questBag() : Boolean
      {
         var _loc1_:Number = 0;
         while(_loc1_ < questArr.length)
         {
            if(Boolean(Main.player1) && Main.player1.getBag().fallQusetBag(questArr[_loc1_][1]) > 0)
            {
               return true;
            }
            if(Main.P1P2 && Main.player2 && Main.player2.getBag().fallQusetBag(questArr[_loc1_][1]) > 0)
            {
               return true;
            }
            _loc1_++;
         }
         return false;
      }
      
      public static function JiaoYan() : Boolean
      {
         if(tiaoShiYN == true)
         {
            return true;
         }
         if(Main.varX < Main.varX_old)
         {
            Strat.stratX.游戏盒信息();
            return false;
         }
         return true;
      }
      
      public static function JiaoYan2() : Boolean
      {
         if(tiaoShiYN == true)
         {
            return true;
         }
         if(NoLog < 2 && (Main.logNameSave == "" && Main.varX_old < 182 || Boolean(MD5contrast.contrast(Main.logName,Main.logNameSave,Main.saveNum))))
         {
            return true;
         }
         if(XiuZheng())
         {
            return true;
         }
         Strat.stratX.复制存档信息();
         return false;
      }
      
      private static function XiuZheng() : Boolean
      {
         if(Main.NoLogInfo[13])
         {
            Main.NoLog = 0;
            Main.NoLogInfo[13] = null;
            return true;
         }
         if(Boolean(Main.NoLogInfo[15]) && serverTime.getValue() < 20131118)
         {
            return true;
         }
         if(Main.NoLogInfo[5] == 888 || Main.NoLogInfo[5] == 111 || Main.NoLogInfo[5] == 122 || Main.NoLogInfo[5] == 333 || Main.NoLogInfo[5] == 444)
         {
            Main.NoLog = 0;
            Main.NoLogInfo[5] = null;
            return true;
         }
         if(Main.NoLogInfo[4] < 1000)
         {
            Main.NoLogInfo[4] = null;
            return true;
         }
         return false;
      }
      
      public static function NoGame(param1:String = "未知") : *
      {
         if(!Main.tiaoShiYN)
         {
            DuoKai_Info._noSave = true;
            while(true)
            {
               _this.visible = false;
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,250,540,0,false,0,"游戏异常:" + param1);
            return;
         }
      }
      
      public static function addBtn() : *
      {
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            Main.world.moveChild_Back.addChild(ysbtn);
            ysbtn.x = 2430;
            ysbtn.y = 495;
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 3)
         {
            Main.world.moveChild_Back.addChild(ysbtn);
            ysbtn.x = 2430;
            ysbtn.y = 495;
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 4)
         {
            Main.world.moveChild_Back.addChild(ysbtn);
            ysbtn.x = 1265;
            ysbtn.y = 467;
         }
         ysbtn.visible = true;
         ysbtn.addEventListener(MouseEvent.CLICK,huodong61);
      }
      
      public static function addBtn_20161225() : *
      {
         Main.sdysbtn = new SDYSbtn();
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            Main.world.moveChild_Back.addChild(sdysbtn);
            sdysbtn.x = 2200;
            sdysbtn.y = 510;
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 3)
         {
            Main.world.moveChild_Back.addChild(sdysbtn);
            sdysbtn.x = 2140;
            sdysbtn.y = 510;
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 4)
         {
            Main.world.moveChild_Back.addChild(sdysbtn);
            sdysbtn.x = 1720;
            sdysbtn.y = 470;
         }
         RefreshBtn1225();
         sdysbtn.buttonMode = true;
         sdysbtn.visible = true;
         sdysbtn.addEventListener(MouseEvent.CLICK,huodong1225);
         sdysbtn.mouseChildren = true;
         sdysbtn.mouseEnabled = false;
      }
      
      public static function parseSaveFileContent(saveFileContent:String) : Object
      {
         var byteArray:ByteArray;
         var saveObject:Object;
         var result:Object;
         var p1:PlayerData;
         var p2:PlayerData;
         try
         {
            byteArray = Base64.decodeToByteArray(saveFileContent) as ByteArray;
            byteArray.position = 0;
            saveObject = byteArray.readObject();
            result = {
               "success":true,
               "message":"存档解析成功",
               "data":saveObject,
               "hasPlayer1":saveObject["p1"] != null,
               "hasPlayer2":saveObject["p2"] != null,
               "gameVersion":saveObject["var"] || false,
               "saveTime":saveObject["saveTimeX"] || false,
               "isP1P2":saveObject["P1P2"]
            };
            if(saveObject["p1"])
            {
               p1 = saveObject["p1"] as PlayerData;
               result.player1Info = {
                  "level":p1.getLevel(),
                  "gold":p1.getGold(),
                  "exp":p1.getEXP(),
                  "points":p1.getPoints(),
                  "rebirth":p1.isRebirth()
               };
            }
            if(saveObject["p2"])
            {
               p2 = saveObject["p2"] as PlayerData;
               result.player2Info = {
                  "level":p2.getLevel(),
                  "gold":p2.getGold(),
                  "exp":p2.getEXP(),
                  "points":p2.getPoints(),
                  "rebirth":p2.isRebirth()
               };
            }
            return result;
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"存档解析失败: " + error.message
            };
         }
      }
      
      public static function serializeSaveData(saveData:Object) : Object
      {
         var byteArray:ByteArray;
         var saveFileContent:String;
         try
         {
            byteArray = new ByteArray();
            byteArray.writeObject(saveData);
            byteArray.position = 0;
            saveFileContent = Base64.encodeByteArray(byteArray);
            return {
               "success":true,
               "message":"存档序列化成功",
               "saveFileContent":saveFileContent
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"存档序列化失败: " + error.message
            };
         }
      }
      
      public static function getPlayerBagData(saveData:Object, playerNum:int) : Object
      {
         var playerData:PlayerData;
         var bag:Bag;
         var result:Object;
         var equipBag:Array;
         var i:int;
         var equip:Equip;
         var suppliesBag:Array;
         var supplies:Supplies;
         var gemBag:Array;
         var gem:Gem;
         var questBag:Array;
         var quest:Quest;
         var otherobjBag:Array;
         var otherobj:Otherobj;
         try
         {
            playerData = saveData["p" + playerNum] as PlayerData;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家" + playerNum + "数据不存在"
               };
            }
            bag = playerData.getBag();
            result = {
               "success":true,
               "equipBag":[],
               "suppliesBag":[],
               "gemBag":[],
               "questBag":[],
               "otherobjBag":[]
            };
            equipBag = bag.getEquipBag();
            i = 0;
            while(i < equipBag.length)
            {
               if(equipBag[i] != null)
               {
                  equip = equipBag[i] as Equip;
                  result.equipBag.push({
                     "index":i,
                     "id":equip.getBase(),
                     "name":equip.getName(),
                     "reinforceLevel":equip.getReinforceLevel(),
                     "gemGrid":equip.getGemGrid()
                  });
               }
               i++;
            }
            suppliesBag = bag.getSuppliesBag();
            i = 0;
            while(i < suppliesBag.length)
            {
               if(suppliesBag[i] != null)
               {
                  supplies = suppliesBag[i] as Supplies;
                  result.suppliesBag.push({
                     "index":i,
                     "id":supplies.getBase(),
                     "name":supplies.getName(),
                     "count":supplies.getTimes()
                  });
               }
               i++;
            }
            gemBag = bag.getGemBag();
            i = 0;
            while(i < gemBag.length)
            {
               if(gemBag[i] != null)
               {
                  gem = gemBag[i] as Gem;
                  result.gemBag.push({
                     "index":i,
                     "id":gem.getBase(),
                     "name":gem.getName(),
                     "count":gem.getTimes()
                  });
               }
               i++;
            }
            questBag = bag.getQuestBag();
            i = 0;
            while(i < questBag.length)
            {
               if(questBag[i] != null)
               {
                  quest = questBag[i] as Quest;
                  result.questBag.push({
                     "index":i,
                     "id":quest.getBase(),
                     "name":quest.getName(),
                     "count":quest.getTimes()
                  });
               }
               i++;
            }
            otherobjBag = bag.getOtherobjBag();
            i = 0;
            while(i < otherobjBag.length)
            {
               if(otherobjBag[i] != null)
               {
                  otherobj = otherobjBag[i] as Otherobj;
                  result.otherobjBag.push({
                     "index":i,
                     "id":otherobj.getBase(),
                     "name":otherobj.getName(),
                     "count":otherobj.getTimes()
                  });
               }
               i++;
            }
            return result;
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"获取背包数据失败: " + error.message
            };
         }
      }
      
      public static function getSaveDataTypes(saveData:Object) : Object
      {
         var result:Object;
         try
         {
            result = {
               "success":true,
               "dataTypes":[]
            };
            result.dataTypes.push({
               "key":"var",
               "name":"游戏版本",
               "type":"number",
               "value":saveData["var"] || false
            });
            result.dataTypes.push({
               "key":"P1P2",
               "name":"双人模式",
               "type":"boolean",
               "value":saveData["P1P2"]
            });
            result.dataTypes.push({
               "key":"saveTimeX",
               "name":"存档时间",
               "type":"number",
               "value":saveData["saveTimeX"] || false
            });
            if(saveData["guanKa"])
            {
               result.dataTypes.push({
                  "key":"guanKa",
                  "name":"关卡进度",
                  "type":"array",
                  "value":saveData["guanKa"]
               });
            }
            if(saveData["questArr"])
            {
               result.dataTypes.push({
                  "key":"questArr",
                  "name":"任务状态",
                  "type":"array",
                  "value":saveData["questArr"]
               });
            }
            if(saveData["chengjiu"])
            {
               result.dataTypes.push({
                  "key":"chengjiu",
                  "name":"成就数据",
                  "type":"object",
                  "value":saveData["chengjiu"]
               });
            }
            if(saveData["petB"])
            {
               result.dataTypes.push({
                  "key":"petB",
                  "name":"宠物背包",
                  "type":"object",
                  "value":saveData["petB"]
               });
            }
            if(saveData["kaPian"])
            {
               result.dataTypes.push({
                  "key":"kaPian",
                  "name":"怪物卡片",
                  "type":"object",
                  "value":saveData["kaPian"]
               });
            }
            if(saveData["changKu"])
            {
               result.dataTypes.push({
                  "key":"changKu",
                  "name":"仓库数据",
                  "type":"object",
                  "value":saveData["changKu"]
               });
            }
            return result;
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"获取数据类型失败: " + error.message
            };
         }
      }
      
      public static function updatePlayerBasicData(saveData:Object, playerNum:int, level:int, gold:int, exp:int, points:int, rebirth:Boolean) : Object
      {
         var playerData:PlayerData;
         try
         {
            playerData = saveData["p" + playerNum] as PlayerData;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家" + playerNum + "数据不存在"
               };
            }
            if(level < 1 || level > 999)
            {
               return {
                  "success":false,
                  "error":"等级必须在 1-999 之间"
               };
            }
            if(gold < 0 || gold > 999999999)
            {
               return {
                  "success":false,
                  "error":"金币必须在 0-999999999 之间"
               };
            }
            if(exp < 0 || exp > 999999999)
            {
               return {
                  "success":false,
                  "error":"经验值必须在 0-999999999 之间"
               };
            }
            if(points < 0 || points > 9999)
            {
               return {
                  "success":false,
                  "error":"属性点必须在 0-9999 之间"
               };
            }
            playerData.setLevel(level);
            playerData.setGold(gold);
            playerData.setEXP(exp);
            playerData.setPoints(points);
            playerData.setRebirth(rebirth);
            return {
               "success":true,
               "message":"玩家" + playerNum + "基本属性更新成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"更新玩家基本属性失败: " + error.message
            };
         }
      }
      
      public static function updatePlayerSkills(saveData:Object, playerNum:int, skillData:Array) : Object
      {
         var playerData:PlayerData;
         var skillArr:Array;
         var i:int;
         var skillLevel:int;
         try
         {
            playerData = saveData["p" + playerNum] as PlayerData;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家" + playerNum + "数据不存在"
               };
            }
            skillArr = playerData.getSkillArr();
            if(!skillArr)
            {
               return {
                  "success":false,
                  "error":"玩家" + playerNum + "技能数据不存在"
               };
            }
            i = 0;
            while(i < skillData.length && i < skillArr.length)
            {
               skillLevel = int(skillData[i]);
               if(skillLevel >= 0 && skillLevel <= 99)
               {
                  skillArr[i][1] = skillLevel;
               }
               i++;
            }
            return {
               "success":true,
               "message":"玩家" + playerNum + "技能数据更新成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"更新玩家技能数据失败: " + error.message
            };
         }
      }
      
      public static function maxAllPlayerSkills(saveData:Object, playerNum:int) : Object
      {
         var playerData:PlayerData;
         var skillArr:Array;
         var i:int;
         try
         {
            playerData = saveData["p" + playerNum] as PlayerData;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家" + playerNum + "数据不存在"
               };
            }
            skillArr = playerData.getSkillArr();
            if(!skillArr)
            {
               return {
                  "success":false,
                  "error":"玩家" + playerNum + "技能数据不存在"
               };
            }
            i = 0;
            while(i < skillArr.length)
            {
               if(skillArr[i] && skillArr[i].length >= 2)
               {
                  skillArr[i][1] = 99;
               }
               i++;
            }
            return {
               "success":true,
               "message":"玩家" + playerNum + "所有技能已设为满级"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"设置技能满级失败: " + error.message
            };
         }
      }
      
      public static function updateQuestStatus(saveData:Object, questIndex:int, completed:Boolean) : Object
      {
         var questArr:Array;
         try
         {
            questArr = saveData["questArr"] as Array;
            if(!questArr)
            {
               return {
                  "success":false,
                  "error":"任务数据不存在"
               };
            }
            if(questIndex < 0 || questIndex >= questArr.length)
            {
               return {
                  "success":false,
                  "error":"任务索引超出范围"
               };
            }
            questArr[questIndex][0] = completed;
            return {
               "success":true,
               "message":"任务" + questIndex + "状态更新成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"更新任务状态失败: " + error.message
            };
         }
      }
      
      public static function completeAllQuests(saveData:Object) : Object
      {
         var questArr:Array;
         var completedCount:int;
         var i:int;
         try
         {
            questArr = saveData["questArr"] as Array;
            if(!questArr)
            {
               return {
                  "success":false,
                  "error":"任务数据不存在"
               };
            }
            completedCount = 0;
            i = 0;
            while(i < questArr.length)
            {
               if(questArr[i] && questArr[i].length >= 2)
               {
                  if(!questArr[i][0])
                  {
                     questArr[i][0] = true;
                     completedCount++;
                  }
               }
               i++;
            }
            return {
               "success":true,
               "message":"已完成 " + completedCount + " 个任务"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"完成所有任务失败: " + error.message
            };
         }
      }
      
      public static function updateBagItem(saveData:Object, playerNum:int, bagType:String, itemIndex:int, itemId:int, count:int, reinforceLevel:int = 0) : Object
      {
         var playerData:PlayerData;
         var bag:Bag;
         var targetBag:Array;
         var equip:Equip;
         var item:Object;
         try
         {
            playerData = saveData["p" + playerNum] as PlayerData;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家" + playerNum + "数据不存在"
               };
            }
            bag = playerData.getBag();
            switch(bagType)
            {
               case "equip":
                  targetBag = bag.getEquipBag();
                  break;
               case "supplies":
                  targetBag = bag.getSuppliesBag();
                  break;
               case "gem":
                  targetBag = bag.getGemBag();
                  break;
               case "quest":
                  targetBag = bag.getQuestBag();
                  break;
               case "otherobj":
                  targetBag = bag.getOtherobjBag();
                  break;
               default:
                  return {
                     "success":false,
                     "error":"未知的背包类型: " + bagType
                  };
            }
            if(itemIndex < 0 || itemIndex >= targetBag.length)
            {
               return {
                  "success":false,
                  "error":"物品索引超出范围"
               };
            }
            if(bagType == "equip")
            {
               equip = new Equip();
               equip.setBase(itemId);
               equip.setReinforceLevel(reinforceLevel);
               targetBag[itemIndex] = equip;
            }
            else
            {
               switch(bagType)
               {
                  case "supplies":
                     item = new Supplies();
                     break;
                  case "gem":
                     item = new Gem();
                     break;
                  case "quest":
                     item = new Quest();
                     break;
                  case "otherobj":
                     item = new Otherobj();
               }
               if(item)
               {
                  item.setBase(itemId);
                  item.setTimes(count);
                  targetBag[itemIndex] = item;
               }
            }
            return {
               "success":true,
               "message":"背包物品更新成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"更新背包物品失败: " + error.message
            };
         }
      }
      
      public static function deleteBagItem(saveData:Object, playerNum:int, bagType:String, itemIndex:int) : Object
      {
         var playerData:PlayerData;
         var bag:Bag;
         var targetBag:Array;
         try
         {
            playerData = saveData["p" + playerNum] as PlayerData;
            if(!playerData)
            {
               return {
                  "success":false,
                  "error":"玩家" + playerNum + "数据不存在"
               };
            }
            bag = playerData.getBag();
            switch(bagType)
            {
               case "equip":
                  targetBag = bag.getEquipBag();
                  break;
               case "supplies":
                  targetBag = bag.getSuppliesBag();
                  break;
               case "gem":
                  targetBag = bag.getGemBag();
                  break;
               case "quest":
                  targetBag = bag.getQuestBag();
                  break;
               case "otherobj":
                  targetBag = bag.getOtherobjBag();
                  break;
               default:
                  return {
                     "success":false,
                     "error":"未知的背包类型: " + bagType
                  };
            }
            if(itemIndex < 0 || itemIndex >= targetBag.length)
            {
               return {
                  "success":false,
                  "error":"物品索引超出范围"
               };
            }
            targetBag[itemIndex] = null;
            return {
               "success":true,
               "message":"背包物品删除成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"删除背包物品失败: " + error.message
            };
         }
      }
      
      public static function updateStageProgress(saveData:Object, stageData:Array) : Object
      {
         try
         {
            if(!stageData)
            {
               return {
                  "success":false,
                  "error":"关卡数据不能为空"
               };
            }
            saveData["guanKa"] = stageData;
            return {
               "success":true,
               "message":"关卡进度更新成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"更新关卡进度失败: " + error.message
            };
         }
      }
      
      public static function unlockAllStages(saveData:Object) : Object
      {
         var guanKa:Array;
         var i:int;
         try
         {
            guanKa = saveData["guanKa"] as Array;
            if(!guanKa)
            {
               return {
                  "success":false,
                  "error":"关卡数据不存在"
               };
            }
            i = 0;
            while(i < guanKa.length)
            {
               guanKa[i] = 1;
               i++;
            }
            return {
               "success":true,
               "message":"所有关卡已解锁"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"解锁所有关卡失败: " + error.message
            };
         }
      }
      
      public static function updateSystemSettings(saveData:Object, gameVersion:int, isP1P2:Boolean, saveTime:int) : Object
      {
         try
         {
            if(gameVersion > 0)
            {
               saveData["var"] = gameVersion;
            }
            saveData["P1P2"] = isP1P2;
            if(saveTime > 0)
            {
               saveData["saveTimeX"] = saveTime;
            }
            return {
               "success":true,
               "message":"系统设置更新成功"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"更新系统设置失败: " + error.message
            };
         }
      }
      
      public static function clearWarehouse(saveData:Object) : Object
      {
         var changKu:Object;
         var equipBag:Array;
         var i:int;
         var suppliesBag:Array;
         var gemBag:Array;
         try
         {
            changKu = saveData["changKu"];
            if(!changKu)
            {
               return {
                  "success":false,
                  "error":"仓库数据不存在"
               };
            }
            if(changKu["_equipBag"])
            {
               equipBag = changKu["_equipBag"] as Array;
               i = 0;
               while(i < equipBag.length)
               {
                  equipBag[i] = null;
                  i++;
               }
            }
            if(changKu["_suppliesBag"])
            {
               suppliesBag = changKu["_suppliesBag"] as Array;
               i = 0;
               while(i < suppliesBag.length)
               {
                  suppliesBag[i] = null;
                  i++;
               }
            }
            if(changKu["_gemBag"])
            {
               gemBag = changKu["_gemBag"] as Array;
               i = 0;
               while(i < gemBag.length)
               {
                  gemBag[i] = null;
                  i++;
               }
            }
            return {
               "success":true,
               "message":"仓库已清空"
            };
         }
         catch(error:Error)
         {
            return {
               "success":false,
               "error":"清空仓库失败: " + error.message
            };
         }
      }
      
      public static function RefreshBtn1225() : *
      {
         if(ShengDan_Interface.moment_2021.getValue() == 1)
         {
            sdysbtn.gotoAndStop(1);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 2)
         {
            sdysbtn.gotoAndStop(2);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 3)
         {
            sdysbtn.gotoAndStop(3);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 4)
         {
            sdysbtn.gotoAndStop(4);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 5)
         {
            sdysbtn.gotoAndStop(5);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 6)
         {
            sdysbtn.gotoAndStop(6);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 7)
         {
            sdysbtn.gotoAndStop(7);
         }
         else
         {
            sdysbtn.gotoAndStop(8);
         }
      }
      
      public static function huodong61(param1:*) : *
      {
         SixOne_Interface.Open();
      }
      
      public static function huodong1225(param1:*) : *
      {
         ShengDan_Interface.Open();
      }
      
      public function setHold(param1:*) : void
      {
         serviceHold = param1;
      }
      
      public function InitXX(param1:* = null) : *
      {
         System.useCodePage = false;
         Main.GetServerTime();
         this.onADDED_TO_STAGE();
         noSave = -(Math.random() * 999999 + 999);
         removeEventListener(Event.ADDED_TO_STAGE,this.InitXX);
         InitData.Init();
         Data2.Init();
         Data_KillShop.Init();
         Data_qinMiDu.Init();
         PetFactory.AddLVDataInit();
         MusicBox.MusicPlay("封面声音");
         this.regAllClass();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_Game);
      }
      
      public function onENTER_FRAME_Game(param1:*) : *
      {
         ChunJiePanel.addZaiXianJiFen();
      }
      
      public function regAllClass() : void
      {
         registerClassAlias("PlayerData",PlayerData);
         registerClassAlias("Attribute",Attribute);
         registerClassAlias("Bag",Bag);
         registerClassAlias("Equip",Equip);
         registerClassAlias("EquipBaseAttrib",EquipBaseAttrib);
         registerClassAlias("EquipBaseAttribTypeConst",EquipBaseAttribTypeConst);
         registerClassAlias("EquipReinforce",EquipReinforce);
         registerClassAlias("EquipSkillSlot",EquipSkillSlot);
         registerClassAlias("EquipSlot",EquipSlot);
         registerClassAlias("Gem",Gem);
         registerClassAlias("GemTypeConst",GemTypeConst);
         registerClassAlias("Otherobj",Otherobj);
         registerClassAlias("PlayerData",PlayerData);
         registerClassAlias("promptSlot",promptSlot);
         registerClassAlias("Quest",Quest);
         registerClassAlias("Skill",Skill);
         registerClassAlias("SkillSlot",SkillSlot);
         registerClassAlias("Storage",Storage);
         registerClassAlias("StrengthenDeleteSlot",StrengthenDeleteSlot);
         registerClassAlias("StrengthenMosaicSlot",StrengthenMosaicSlot);
         registerClassAlias("StrengthenSlot",StrengthenSlot);
         registerClassAlias("StrengthenSynthesisSlot",StrengthenSynthesisSlot);
         registerClassAlias("SuitEquipAttrib",SuitEquipAttrib);
         registerClassAlias("Supplies",Supplies);
         registerClassAlias("SuppliesAffect",SuppliesAffect);
         registerClassAlias("SuppliesSlot",SuppliesSlot);
         registerClassAlias("Task",Task);
         registerClassAlias("Make",Make);
         registerClassAlias("VT",VT);
         registerClassAlias("Pet",Pet);
         registerClassAlias("PetSlot",PetSlot);
         registerClassAlias("BadgeSlot",BadgeSlot);
         registerClassAlias("TitleSlot",TitleSlot);
         registerClassAlias("Title",Title);
         registerClassAlias("WantedTaskSlot",WantedTaskSlot);
         registerClassAlias("WantedTask",WantedTask);
         registerClassAlias("StampSlot",StampSlot);
         registerClassAlias("Elves",Elves);
         registerClassAlias("ElvesSlot",ElvesSlot);
         registerClassAlias("MonsterSlot",MonsterSlot);
         registerClassAlias("MonsterCard",MonsterCard);
         registerClassAlias("Plan",Plan);
         registerClassAlias("PetEquip",PetEquip);
         registerClassAlias("PetBag",PetBag);
         registerClassAlias("PetGem",PetGem);
      }
      
      private function onADDED_TO_STAGE() : *
      {
         stage.addEventListener(SaveEvent.SAVE_GET,this.saveProcess);
         stage.addEventListener(SaveEvent.SAVE_SET,this.saveProcess);
         stage.addEventListener(SaveEvent.SAVE_LIST,this.saveProcess);
         stage.addEventListener("netSaveError",this.netSaveErrorHandler,false,0,true);
         stage.addEventListener("MVC_CLOSE_PANEL",this.closePanelHandler);
         stage.addEventListener("userLoginOut",this.onUserLogOutHandler,false,0,true);
         stage.addEventListener("logreturn",this.saveProcess);
         stage.addEventListener("serverTimeEvent",this.onGetServerTimeHandler);
         Main.fps = stage.frameRate;
         stage.addEventListener("multipleError",this.multipleErrorHandler,false,0,true);
         stage.addEventListener("StoreStateEvent",this.getStoreStateHandler,false,0,true);
         stage.align = StageAlign.TOP_LEFT;
         stage.scaleMode = StageScaleMode.NO_SCALE;
         Main._stage = stage;
         Main._this = this;
         Main._stageWidth = stage.width;
         Main._stageHeight = stage.height;
         Strat.Open();
         BasicKey.Start(Main._stage);
         LoadTempDataArr();
      }
      
      private function closePanelHandler(param1:Event) : void
      {
         var _loc2_:Object = serviceHold.isLog;
         if(_loc2_ == null)
         {
            addEventListener(Event.ENTER_FRAME,this.OnTimeShowLogUi);
         }
      }
      
      private function OnTimeShowLogUi(param1:*) : *
      {
         --this.timeLog;
         if(this.timeLog <= 0)
         {
            this.showLogUi();
            this.timeLog = 60;
            removeEventListener(Event.ENTER_FRAME,this.OnTimeShowLogUi);
         }
      }
      
      public function showLogUi() : void
      {
         var _loc1_:Object = null;
         if(serviceHold)
         {
            _loc1_ = serviceHold.isLog;
            if(_loc1_ == null)
            {
               serviceHold.showLogPanel();
            }
         }
      }
      
      public function onUserLogOutHandler(param1:Event = null) : void
      {
         this.blackXXXX.Black_mc.x = this.blackXXXX.Black_mc.y = 0;
         navigateToURL(new URLRequest("javascript:location.reload(); "),"_self");
         Main.player1 = Main.player2 = null;
      }
      
      private function netSaveErrorHandler(param1:Event) : void
      {
         NewMC.Open("文字提示",this,400,400,50,0,true,2,"存档失败");
      }
      
      private function saveProcess(param1:SaveEvent) : void
      {
         var _loc2_:String = null;
         var _loc3_:ByteArray = null;
         var _loc4_:Object = null;
         var _loc5_:int = 0;
         var _loc6_:String = null;
         var _loc7_:String = null;
         var _loc8_:int = 0;
         var _loc9_:Array = null;
         var _loc10_:int = 0;
         var _loc11_:Object = null;
         switch(param1.type)
         {
            case SaveEvent.SAVE_GET:
               if(param1.ret == null || param1.ret.data == null)
               {
                  return;
               }
               _loc2_ = String(param1.ret.data);
               _loc3_ = Base64.decodeToByteArray(_loc2_) as ByteArray;
               _loc3_.position = 0;
               Main.saveNum = param1.ret.index;
               _loc4_ = _loc3_.readObject();
               _loc5_ = serverTime.getValue();
               _loc6_ = param1.ret.datetime;
               _loc7_ = _loc6_.substr(0,4) + _loc6_.substr(5,2) + _loc6_.substr(8,2);
               _loc8_ = int(_loc7_);
               TiaoShi.tempVarTime = _loc8_;
               if(_loc8_ > 20151020 && _loc4_["var"] < 1020)
               {
                  NoGame("使用旧版本进行修改!");
                  if(!Main.tiaoShiYN)
                  {
                     return;
                  }
               }
               Main.varX_old = _loc4_["var"];
               if(_loc4_["logNameSave"])
               {
                  logNameSave = _loc4_["logNameSave"];
               }
               NoLog = 0;
               if(_loc4_["NoLog"])
               {
                  NoLog = _loc4_["NoLog"];
               }
               if(Boolean(_loc4_["NoLogInfo"]) && !(_loc4_["NoLogInfo"] is Array))
               {
                  _loc4_["NoLogInfo"] = null;
               }
               if(_loc4_["NoLogInfo"])
               {
                  Main.NoLogInfo = _loc4_["NoLogInfo"];
               }
               XiuZheng();
               if(JiaoYan() != true || JiaoYan2() != true)
               {
                  return;
               }
               Main.P1P2 = _loc4_["P1P2"];
               if(_loc4_["changKu"])
               {
                  StoragePanel.storage = _loc4_["changKu"];
               }
               if(_loc4_["chengjiu"])
               {
                  if(Main.varX_old > 275)
                  {
                     AchData.xxx = _loc4_["chengjiu"];
                  }
               }
               _loc10_ = 0;
               while(_loc10_ <= 150)
               {
                  if(!_loc4_["guanKa"][_loc10_])
                  {
                     Main.guanKa[_loc10_] = 0;
                  }
                  else
                  {
                     Main.guanKa[_loc10_] = _loc4_["guanKa"][_loc10_];
                  }
                  if(Main.guanKa[_loc10_] is Boolean)
                  {
                     if(Main.guanKa[_loc10_] == true)
                     {
                        Main.guanKa[_loc10_] = 1;
                     }
                     else
                     {
                        Main.guanKa[_loc10_] = 0;
                     }
                  }
                  _loc10_++;
               }
               if(Main.guanKa[1] != 0 && Main.guanKa[1] != 1 && Main.guanKa[1] != 2 && Main.guanKa[1] != 3)
               {
                  Main.guanKa[1] = 1;
               }
               Main.questArr = _loc4_["questArr"];
               Main.verify = _loc4_["verify"];
               if(_loc4_["InitDataX"])
               {
                  InitData.LoadInitData(_loc4_["InitDataX"]);
               }
               if(_loc4_["buyTime"])
               {
                  Data_KillShop.buyTime = _loc4_["buyTime"];
                  Data_KillShop.buyArr = _loc4_["buyArr"];
                  ShopKillPoint.KaiQiShopArr2 = _loc4_["KaiQiShopArr2"];
               }
               if(_loc4_["RenWu"])
               {
                  TaskData.saveArr = _loc4_["RenWu"];
               }
               if(_loc4_["MakeData"])
               {
                  MakeData.duMake(_loc4_["MakeData"]);
               }
               if(Main.P1P2)
               {
                  x = 2;
               }
               else
               {
                  x = 1;
               }
               _loc10_ = 1;
               while(_loc10_ <= x)
               {
                  Main["player" + _loc10_] = _loc4_["p" + _loc10_] as PlayerData;
                  _loc10_++;
               }
               if(Boolean(_loc4_["lingQueArr"]) && _loc4_["lingQueArr"].length == 11)
               {
                  lingQueArr = _loc4_["lingQueArr"];
               }
               if(Boolean(_loc4_["lingQueArr2"]) && _loc4_["lingQueArr2"].length == 11)
               {
                  lingQueArr2 = _loc4_["lingQueArr2"];
               }
               if(_loc4_["TeShuHuoDongArr"])
               {
                  TeShuHuoDong.TeShuHuoDongArr = _loc4_["TeShuHuoDongArr"];
               }
               TeShuHuoDong.Init();
               if(_loc4_["xuansang"])
               {
                  Main.wts = _loc4_["xuansang"];
                  if(Main.varX_old == 1100)
                  {
                     WantedTaskSlot.JLTimes10XX();
                  }
               }
               if(_loc4_["kaPian"])
               {
                  CardPanel.monsterSlot = _loc4_["kaPian"];
               }
               if(_loc4_["petB"])
               {
                  NewPetPanel.bag = _loc4_["petB"];
               }
               if(_loc4_["xgKEY"])
               {
                  NewPetPanel.XGkey = _loc4_["xgKEY"];
               }
               if(_loc4_["lvKEY"])
               {
                  NewPetPanel.LVkey = _loc4_["lvKEY"];
               }
               if(_loc4_["lqTIMES"])
               {
                  JiangLiPanel.LQtimes = _loc4_["lqTIMES"];
               }
               if(_loc4_["XueMai"])
               {
                  NewPetPanel.XueMai = _loc4_["XueMai"];
               }
               if(_loc4_["lingQu16"])
               {
                  ChongZhi_Interface.lingQu16 = _loc4_["lingQu16"];
               }
               if(_loc4_["caiYao0"])
               {
                  CaiYaoPanel.saveArr = _loc4_["caiYao0"];
               }
               if(_loc4_["caiYao1"])
               {
                  CaiYaoPanel.saveArr2 = _loc4_["caiYao1"];
               }
               if(_loc4_["yaoArr"])
               {
                  YaoYuan.yaoArr = _loc4_["yaoArr"];
               }
               if(_loc4_["huaZhi"])
               {
                  Play_Interface.huaZhi = _loc4_["huaZhi"];
               }
               if(_loc4_["Map0_YN"])
               {
                  Main.Map0_YN = _loc4_["Map0_YN"];
               }
               if(_loc4_["Map0_YN2"])
               {
                  Main.Map0_YN2 = _loc4_["Map0_YN2"];
               }
               if(_loc4_["water"])
               {
                  Main.water = _loc4_["water"];
               }
               if(_loc4_["LuoPanSuiPian"])
               {
                  Main.LuoPanArr = _loc4_["LuoPanSuiPian"];
               }
               if(_loc4_["XinglingUp"])
               {
                  XingLingFactory.xingLingData = _loc4_["XinglingUp"];
                  XingLingFactory.Init_xingLingData();
               }
               if(_loc4_["JiHua"])
               {
                  PlanFactory.JiHuaData = _loc4_["JiHua"];
               }
               if(_loc4_["JiHua2"])
               {
                  PlanFactory.JiHuaData2 = _loc4_["JiHua2"];
               }
               if(_loc4_["vipUserArr"])
               {
                  Vip_Interface.vipUserArr = _loc4_["vipUserArr"];
               }
               if(_loc4_["TaskDataBoXX5"])
               {
                  TaskData.BoXX = _loc4_["TaskDataBoXX5"];
               }
               if(_loc4_["TaskDataBoXX6"])
               {
                  TaskData.BoXX2 = _loc4_["TaskDataBoXX6"];
               }
               if(_loc4_["banben_save"])
               {
                  SixOne_Interface.banben_save = _loc4_["banben_save"];
               }
               if(_loc4_["dayTimes2021"])
               {
                  SixOne_Interface.dayTimes2021 = _loc4_["dayTimes2021"];
               }
               if(_loc4_["nowdate2021"])
               {
                  SixOne_Interface.nowdate2021 = _loc4_["nowdate2021"];
               }
               if(_loc4_["okTimes2021"])
               {
                  SixOne_Interface.okTimes2021 = _loc4_["okTimes2021"];
               }
               if(_loc4_["state2021"])
               {
                  SixOne_Interface.state2021 = _loc4_["state2021"];
               }
               if(_loc4_["buChangHeCeng2"])
               {
                  Play_Interface.buChangHeCeng2 = _loc4_["buChangHeCeng2"];
               }
               Panel_XianHua.huaArr = _loc4_["huaArr"];
               if(_loc4_["chunjie_saveNew"])
               {
                  ChunJiePanel.saveNew = _loc4_["chunjie_saveNew"];
               }
               if(_loc4_["chunjie1_2022"])
               {
                  ChunJiePanel.saveArr_2022 = _loc4_["chunjie1_2022"];
               }
               if(_loc4_["chunjie2_2022"])
               {
                  ChunJiePanel.saveArr2_2022 = _loc4_["chunjie2_2022"];
               }
               if(_loc4_["varSave"])
               {
                  NewYear_Interface.varSave = _loc4_["varSave"];
               }
               if(_loc4_["newyearKey2022"])
               {
                  NewYear_Interface.key2022 = _loc4_["newyearKey2022"];
               }
               if(_loc4_["newyearJuan2022"])
               {
                  NewYear_Interface.juan2022 = _loc4_["newyearJuan2022"];
               }
               if(_loc4_["newyearJL2022"])
               {
                  NewYear_Interface.allJiangLi2022 = _loc4_["newyearJL2022"];
               }
               if(_loc4_["newyearYN2022"])
               {
                  NewYear_Interface.openYesNo2022 = _loc4_["newyearYN2022"];
               }
               if(_loc4_["arrAll"])
               {
                  FiveOne_Interface.arrAll = _loc4_["arrAll"];
               }
               if(_loc4_["ZhuanPan"])
               {
                  ZhuanPan.saveTime = _loc4_["ZhuanPan"];
               }
               if(_loc4_["ZhuanPanYN"])
               {
                  ZhuanPan.Num1YN2 = _loc4_["ZhuanPanYN"];
               }
               if(_loc4_["qdArr"])
               {
                  QianDao.qdArr = _loc4_["qdArr"];
               }
               if(_loc4_["tuijian"])
               {
                  TuiJianPanel.arrSave = _loc4_["tuijian"];
               }
               Vip_Interface.SetData();
               SaveXX.TestGoldMax();
               if(_loc4_["TiaoZan_newTime"])
               {
                  PaiHang_Data.newTime = _loc4_["TiaoZan_newTime"];
               }
               if(_loc4_["inGameNum"])
               {
                  PaiHang_Data.inGameNum = _loc4_["inGameNum"];
               }
               if(_loc4_["sArr2"])
               {
                  PaiHang_Data.sArr2 = _loc4_["sArr2"];
               }
               if(_loc4_["jiFenArr"])
               {
                  PaiHang_Data.jiFenArr = _loc4_["jiFenArr"];
               }
               if(_loc4_["PK_jiFenArr"])
               {
                  PK_UI.jiFenArr = _loc4_["PK_jiFenArr"];
               }
               if(_loc4_["saveTime"])
               {
                  JiFenLingQue3.saveTime = _loc4_["saveTime"];
               }
               if(_loc4_["yinCangP1P2"])
               {
                  Main.yinCangP1P2 = _loc4_["yinCangP1P2"];
               }
               if(_loc4_["saveTime2"])
               {
                  GengXin.saveTime = _loc4_["saveTime2"];
               }
               if(_loc4_["saveVar"])
               {
                  GengXin.saveVar = _loc4_["saveVar"];
               }
               if(_loc4_["gameTime"])
               {
                  JiFenLingQue3.gameTime = _loc4_["gameTime"];
               }
               if(_loc4_["HuiTie8Arr"])
               {
                  HuiTie8Arr = _loc4_["HuiTie8Arr"];
               }
               PaiHang_Data.InitSave();
               if(Main.initTime)
               {
                  _loc4_["initTime"] = Main.initTime;
               }
               Strat.startGame();
               JiFenLingQue3.Post();
               if(Main.player1.level.getValue() >= 50)
               {
                  PK_UI.TiaoJiao_1();
               }
               if(_loc4_["lhs_Data"])
               {
                  LingHunShi_Interface.lhs_Data = _loc4_["lhs_Data"];
               }
               if(_loc4_["noMovPlayArr"])
               {
                  LingHunShi_Interface.noMovPlayArr = _loc4_["noMovPlayArr"];
               }
               if(_loc4_["rwArr"])
               {
                  GongHuiRenWu.rwArr = _loc4_["rwArr"];
               }
               if(_loc4_["rwArr2"])
               {
                  GongHuiRenWu.rwArr2 = _loc4_["rwArr2"];
               }
               if(_loc4_["tzData"])
               {
                  GongHuiTiaoZan.tzData = _loc4_["tzData"];
               }
               if(_loc4_["yueKaTime"])
               {
                  YueKa_Interface.yueKaTime = _loc4_["yueKaTime"];
               }
               if(_loc4_["yueKaTime2"])
               {
                  YueKa_Interface.yueKaTime2 = _loc4_["yueKaTime2"];
               }
               if(_loc4_["lingQu_new"])
               {
                  ChongZhi_Interface4.lingQu_new = _loc4_["lingQu_new"];
               }
               if(_loc4_["saveTimeX"])
               {
                  if(_loc5_ != 0 && _loc4_["saveTimeX"] != _loc5_)
                  {
                     this.DayInit();
                  }
               }
               saveTimeX = _loc5_;
               break;
            case SaveEvent.SAVE_SET:
               if(param1.ret as Boolean == true)
               {
                  if(Main.saveMc_YN)
                  {
                     NewMC.Open("保存成功",this);
                     Main.SaveFun();
                  }
               }
               break;
            case SaveEvent.SAVE_LIST:
               _loc9_ = param1.ret as Array;
               loadSaveOver = true;
               if(_loc9_ == null)
               {
                  _loc10_ = 0;
                  while(_loc10_ < 8)
                  {
                     Strat.SAVE_LIST[_loc10_] = "空存档";
                     _loc10_++;
                  }
               }
               else
               {
                  for(_loc10_ in _loc9_)
                  {
                     _loc11_ = _loc9_[_loc10_];
                     if(_loc11_ == null)
                     {
                        Strat.SAVE_LIST[_loc10_] = "空存档";
                     }
                     else
                     {
                        _loc2_ = "存档的位置:" + _loc11_.index + "存档时间:" + _loc11_.datetime + "存档标题:" + _loc11_.title;
                        Strat.SAVE_LIST[int(_loc11_.index)] = _loc11_.title + "\n" + _loc11_.datetime;
                     }
                  }
               }
               Shop_LiBao.subString();
               break;
            case "logreturn":
               userId = param1.ret.uid;
               logName = param1.ret.name;
               if(param1.ret.nickName)
               {
                  logName2 = param1.ret.nickName;
               }
               else
               {
                  logName2 = "?" + param1.ret.name;
               }
               logYN = true;
               if(!loadSaveOver)
               {
                  Main.serviceHold.getList();
                  break;
               }
         }
      }
      
      private function multipleErrorHandler(param1:Event) : void
      {
         TiaoShi.txtShow("游戏多开了!");
         DuoKai_Info.Open();
      }
      
      private function getStoreStateHandler(param1:Event) : void
      {
         switch(param1.data)
         {
            case "1":
               this.duoKai_num = 0;
               TiaoShi.txtShow("多开检测正常!");
               if(buy_DuoKai)
               {
                  buy_DuoKai = false;
                  Api_4399_All.BuyObj_GO();
               }
               if(saveStop[0])
               {
                  saveStop[0] = false;
                  Save2(saveStop[1]);
               }
               break;
            case "0":
               DuoKai_Info.Open(1);
               break;
            case "-1":
               if(this.duoKai_num > 3)
               {
                  DuoKai_Info.Open(2);
                  break;
               }
               DuoKai_Fun();
               ++this.duoKai_num;
               break;
            case "-2":
               DuoKai_Info.Open(2);
               break;
            case "-3":
               DuoKai_Info.Open(2);
         }
      }
      
      private function DayInit() : *
      {
         TeShuHuoDong.DayInit();
         this.chongZhiFanLi_YN = true;
         NewYear_Interface.juan2022.setValue(5);
      }
      
      private function onGetServerTimeHandler(param1:DataEvent) : void
      {
         var _loc3_:String = null;
         var _loc4_:Array = null;
         var _loc5_:String = null;
         if(param1.data == "")
         {
            ServerTimeNum = 0;
            GetServerTime();
            return;
         }
         Main.serverDayNum = Main.createServerDateA(param1.data);
         var _loc2_:String = param1.data.substr(0,4) + param1.data.substr(5,2) + param1.data.substr(8,2);
         if(serverTime.getValue() == 0)
         {
            serverTimeStr = param1.data.substr(0,4) + "/" + param1.data.substr(5,2) + "/" + param1.data.substr(8,2) + " " + param1.data.substr(10);
            serverTime.setValue(int(_loc2_));
            TiaoShi.txtShow("系统时间:" + Main.serverTime.getValue());
            if(!Main.initTime)
            {
               Main.initTime = serverTime;
            }
            QianDao.year = param1.data.substr(0,4);
            QianDao.month = param1.data.substr(5,2);
            QianDao.day = param1.data.substr(8,2);
         }
         else if(PK_UI.getTimeYn)
         {
            PK_UI.getTimeArr[0] = param1.data.substr(0,4) + "" + param1.data.substr(5,2) + "" + param1.data.substr(8,2);
            _loc3_ = param1.data.substr(11);
            _loc4_ = _loc3_.split(":");
            PK_UI.getTimeArr[1] = _loc4_[0];
            PK_UI.getTimeArr[2] = _loc4_[1];
            PK_UI.getTimeArr[3] = _loc4_[2];
            PK_UI.xxxTime();
            if(TiaoShi._this)
            {
               TiaoShi.txtShow("查询时间:" + _loc2_);
               _loc5_ = param1.data.substr(11);
               _loc4_ = _loc5_.split(":");
            }
         }
         if(Main.serverTime.getValue() > Panel_XianHua.overTime)
         {
            Panel_XianHua.huaArr = null;
         }
      }
      
      public function Loading() : *
      {
         Load.Open();
         addEventListener(Event.ENTER_FRAME,this.onLoading);
      }
      
      private function onLoading(param1:*) : *
      {
         if(Boolean(Load.All_Loaded) && Load.loadingX.visible == true)
         {
            Load.loadOk = false;
            Load.All_Loaded = false;
            GameData.GetData();
            this.GameStart();
            removeEventListener(Event.ENTER_FRAME,this.onLoading);
            Load.Close();
            NewPetPanel.openLoad();
            this.shanbiGAIfangyu();
            TitelPanel.open(true);
            TitelPanel.close();
         }
      }
      
      public function GameStart() : *
      {
         var _loc1_:Array = null;
         var _loc2_:NpcYY = null;
         if(gameNum.getValue() != 0 && gameNum2.getValue() == 1 && !this.EmenyDataMD5_YN)
         {
            Data2.EmenyDataMD5();
            this.EmenyDataMD5_YN = true;
         }
         GongHuiRenWu.initData();
         if(Main.gameNum.getValue() == 888)
         {
            Load.Loading(14,false);
         }
         NewLoad2.Loading();
         this.selHuiTie8();
         TiaoZhan_Interface.LoadSkin();
         EnergySlot.energyBool = true;
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            if(Main.water.getValue() != 1)
            {
               Main.gameNum2.setValue(4);
            }
            else if(Main.Map0_YN)
            {
               Main.gameNum2.setValue(3);
            }
            NewDoor.bool_1 = true;
            NewDoor.bool_2 = true;
            NewDoor.bool_3 = true;
            NewDoor.bool_4 = true;
            NewDoor.setRdm();
            Play_Interface.jihuaTiShi();
            Player.skillYAO = 0;
            if(Main.player_1)
            {
               Main.player_1.speedTemp.setValue(0);
               Main.player_1.debuff = 0;
            }
            if(Boolean(Main.P1P2) && Boolean(Main.player_2))
            {
               Main.player_2.speedTemp.setValue(0);
               Main.player_2.debuff = 0;
            }
            JingLingCatch.count = 0;
         }
         if(Main.gameNum.getValue() == 82 && Main.gameNum2.getValue() == 1)
         {
            if(NewDoor.bool_0)
            {
               NewDoor.bool_0 = false;
            }
            else
            {
               NewDoor.bool_1 = true;
               NewDoor.bool_2 = true;
               NewDoor.bool_3 = true;
               NewDoor.bool_4 = true;
               NewDoor.setRdm();
            }
         }
         Play_Interface.BossLifeYN = false;
         MakeData.initAllMake();
         if(varX_old == 995)
         {
            MakeData.AllMakeXX();
         }
         AchData.getAllAch();
         TaskData.initAllTask();
         MusicBox.MusicPlay();
         this.DelGame();
         this.AddMap();
         this.AddPlayer(1);
         if(P1P2)
         {
            this.AddPlayer(2);
         }
         this.XiuZengZhuangZhi();
         if(Main.gameNum.getValue() == 0)
         {
            _loc1_ = [157,158,159,160,161,162,163];
            Api_4399_GongHui.getNum(_loc1_);
         }
         ItemsPanel.open();
         ItemsPanel.close();
         GameData.GetData();
         Play_Interface.Open();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         CheckTest.startTest();
         SaveXX.TestGoldMax();
         if(Main.gameNum2.getValue() < 1 || Main.gameNum.getValue() == 999)
         {
            Player.一起信春哥();
            WinShow.All_0();
         }
         PaiHang_Data.All_0();
         Main.player_1.noMove = false;
         Main.player_1.noYingZhi = false;
         if(Main.P1P2)
         {
            Main.player_2.noMove = false;
            Main.player_2.noYingZhi = false;
         }
         if(Main.gameNum.getValue() != 999)
         {
            PK_UI.PK_ing = false;
            PK_UI.getTimeYn = false;
         }
         else
         {
            PK_UI.PK_ing = true;
         }
         if(Main.gameNum.getValue() > 17 && Main.gameNum.getValue() < 29)
         {
            JiHua_Interface.ppp5_4 = true;
         }
         if(Main.serverTime.getValue() <= 20221016)
         {
            addBtn();
            if(SixOne_Interface.state2021.getValue() == 1 && Main.gameNum.getValue() == SixOne_Interface.guankaNum)
            {
               SixOne_Interface.state2021.setValue(2);
               SixOne_Interface.dayTimes2021.setValue(SixOne_Interface.dayTimes2021.getValue() + 1);
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.zhongqiuState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_1.zhongqiuState = 0;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.zhongqiuState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_2.zhongqiuState = 0;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.zhongqiuState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_1.zhongqiuState = 2;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.zhongqiuState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_2.zhongqiuState = 2;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.jianrenState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_1.jianrenState = 0;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.jianrenState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_2.jianrenState = 0;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.jianrenState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_1.jianrenState = 2;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.jianrenState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_2.jianrenState = 2;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.shengmingState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_1.shengmingState = 0;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.shengmingState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_2.shengmingState = 0;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.shengmingState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_1.shengmingState = 2;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.shengmingState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_2.shengmingState = 2;
            }
         }
         if(Main.gameNum.getValue() == 0)
         {
            TuiJianPanel.openLV();
         }
         JingLingCatch.only = true;
         _stage.addChild(this.blackXXXX);
         this.blackXXXX.Black_mc.y = 5000;
         this.blackXXXX.Black_mc.x = 5000;
         if(Player.upLevelInfo == true && Main.gameNum.getValue() == 0)
         {
            NewMC.Open("文字提示",Main._this,470,500,150,0,true,1,"恭喜你升到2级! 你可以在主城找【奥古斯汀】学习技能啦!");
            Player.upLevelInfo = false;
         }
         GengXin.CaXun();
         YinCang.Init();
         if(NpcYY._this)
         {
            NpcYY._this.over();
         }
         if(gameNum.getValue() == 3000)
         {
            _loc2_ = new NpcYY();
         }
         YaoYuan.Init();

         // 强制初始化GM系统，确保在游戏完全加载后GM功能可用
         GM.forceInit();
      }
      
      public function ChongZhiFanLi() : *
      {
         if(Boolean(this.chongZhiFanLi_YN) && Main.gameNum.getValue() == 0 && serverTime.getValue() <= 20170213)
         {
            this.chongZhiFanLi_YN = false;
            Czfl.Open();
         }
      }
      
      public function selHuiTie8() : *
      {
         if(!tiaoShiYN && Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() != 1)
         {
            Api_4399_GongHui.getNum([137]);
         }
      }
      
      public function DelGame() : *
      {
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         Enemy.All = new Array();
         Player.All = new Array();
         Player2.All = new Array();
         HitXX.AllHitXX = new Array();
         Hit82.AllHit82 = new Array();
         HitF.AllHitF = new Array();
         HitP.AllHitP = new Array();
         for(i in Fly.All)
         {
            (Fly.All[i] as Fly).Dead2();
         }
         Fly.All = new Array();
         if(Main.world)
         {
            Main.world.parent.removeChild(Main.world);
            Main.world.y = 5000;
            Main.world = null;
         }
      }
      
      public function shanbiGAIfangyu() : *
      {
         var _loc1_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < 13)
         {
            if(Boolean(Main.player1.getEquipSlot().getEquipFromSlot(_loc1_)) && Main.player1.getEquipSlot().getEquipFromSlot(_loc1_).getPosition() == 3)
            {
               Main.player1.getEquipSlot().getEquipFromSlot(_loc1_).changeReinforceTest();
            }
            if(Boolean(Main.player1.getEquipSlot().getEquipFromSlot(_loc1_)) && Main.player1.getEquipSlot().getEquipFromSlot(_loc1_).getNewSkill() == 57232)
            {
               Main.player1.getEquipSlot().getEquipFromSlot(_loc1_).setNewSkill(57230);
            }
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 48)
         {
            if(Boolean(Main.player1.getBag().getEquipFromBag(_loc1_)) && Main.player1.getBag().getEquipFromBag(_loc1_).getPosition() == 3)
            {
               Main.player1.getBag().getEquipFromBag(_loc1_).changeReinforceTest();
            }
            if(Boolean(Main.player1.getBag().getEquipFromBag(_loc1_)) && Main.player1.getBag().getEquipFromBag(_loc1_).getNewSkill() == 57232)
            {
               Main.player1.getBag().getEquipFromBag(_loc1_).setNewSkill(57230);
            }
            _loc1_++;
         }
         if(Main.P1P2)
         {
            _loc1_ = 0;
            while(_loc1_ < 13)
            {
               if(Boolean(Main.player2.getEquipSlot().getEquipFromSlot(_loc1_)) && Main.player2.getEquipSlot().getEquipFromSlot(_loc1_).getPosition() == 3)
               {
                  Main.player2.getEquipSlot().getEquipFromSlot(_loc1_).changeReinforceTest();
               }
               if(Boolean(Main.player2.getEquipSlot().getEquipFromSlot(_loc1_)) && Main.player2.getEquipSlot().getEquipFromSlot(_loc1_).getNewSkill() == 57232)
               {
                  Main.player2.getEquipSlot().getEquipFromSlot(_loc1_).setNewSkill(57230);
               }
               _loc1_++;
            }
            _loc1_ = 0;
            while(_loc1_ < 48)
            {
               if(Boolean(Main.player2.getBag().getEquipFromBag(_loc1_)) && Main.player2.getBag().getEquipFromBag(_loc1_).getPosition() == 3)
               {
                  Main.player2.getBag().getEquipFromBag(_loc1_).changeReinforceTest();
               }
               if(Boolean(Main.player2.getBag().getEquipFromBag(_loc1_)) && Main.player2.getBag().getEquipFromBag(_loc1_).getNewSkill() == 57232)
               {
                  Main.player2.getBag().getEquipFromBag(_loc1_).setNewSkill(57230);
               }
               _loc1_++;
            }
         }
      }
      
      private function AddMap() : *
      {
         var _loc1_:String = GameData.SelMapName();
         var _loc2_:uint = gameNum.getValue();
         var _loc3_:Class = Map.MapArr[_loc2_].getClass(_loc1_ as String) as Class;
         world = new _loc3_();
         addChild(Main.world);
         Main._stage.stageFocusRect = false;
         Main._stage.focus = Main.world;
         if(Main.gameNum.getValue() == 81)
         {
            if(Main.world["_back"])
            {
               Main.world["_back"].addChild(new MapJG());
            }
         }
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0 && Main.player1.level.getValue() < 30 && Main.player1.getPoints() >= 4)
         {
            if(!JiNeng_mc)
            {
               JiNeng_mc = new JiNengTiShi();
               JiNeng_mc.x = 263;
               JiNeng_mc.y = 135;
               Main.world.addChild(JiNeng_mc);
            }
         }
      }
      
      private function AddPlayer(param1:int = 1) : *
      {
         if(param1 == 1)
         {
            if(!player_1)
            {
               player_1 = new Player();
               if(Main.player1)
               {
                  player_1.data = Main.player1;
               }
               else
               {
                  player_1.data = Main.player1 = PlayerData.creatPlayerData(1);
                  player_1.data.skinArr = SetProfession.skinArr1;
               }
               player_1.Load_All_Player_Data();
            }
            Main.world.moveChild_Player.addChild(player_1);
            player_1.x = 280;
            if(player_1.hp.getValue() > 0)
            {
               player_1.y = 450;
            }
            else
            {
               player_1.y = -50;
            }
            player_1.newSkin();
            player_1.KeyControl_YN = true;
            if(Main.gameNum.getValue() == 4001)
            {
               player_1.x = -200;
               player_1.KeyControl_YN = false;
            }
            if(Main.gameNum2.getValue() == 1 && player_1.playerJL && Boolean(player_1.playerJL.jiNengZD_ID))
            {
               player_1.playerJL.jiNengZD_num = 100;
            }
         }
         else if(param1 == 2)
         {
            if(!player_2)
            {
               player_2 = new Player();
               if(Main.player2)
               {
                  player_2.data = Main.player2;
               }
               else
               {
                  player_2.data = Main.player2 = PlayerData.creatPlayerData(2);
                  player_2.data.skinArr = SetProfession.skinArr2;
               }
               player_2.Load_All_Player_Data();
            }
            Main.world.moveChild_Player.addChild(player_2);
            player_2.x = 240;
            if(player_1.hp.getValue() > 0)
            {
               player_1.y = 450;
            }
            else
            {
               player_1.y = -50;
            }
            player_2.y = 450;
            player_2.newSkin();
            player_2.KeyControl_YN = true;
            if(Main.gameNum.getValue() == 4001)
            {
               player_2.x = -220;
               player_2.KeyControl_YN = false;
            }
            if(Main.gameNum2.getValue() == 1 && player_2.playerJL && Boolean(player_2.playerJL.jiNengZD_ID))
            {
               player_2.playerJL.jiNengZD_num = 100;
            }
         }
         Main.world.开启Show0();
      }
      
      private function onENTER_FRAME(param1:Event) : *
      {
         ++JiFenLingQue3.gameTime;
         --Skill_guangDun.noTime;
         if(Main.gameNum.getValue() != 0)
         {
            Hit82.HitFly();
            HitF.HitFly();
            HitP.HitPlayer();
            GameData.MonsterGo();
            ++WinShow.txt_1;
            if(Main.gameNum.getValue() == 999)
            {
               PK_UI.AddOtherPlayer();
            }
         }
         HitXX.HitEnemy();
         Load.md5_Start();
         Main.GetServerTime();
         PaiHang_Data.TimeStart();
      }
      
      public function skillArrXXX(param1:int = 1) : *
      {
         var _loc2_:PlayerData = Main.player1;
         if(param1 == 2)
         {
            _loc2_ = Main.player2;
         }
         _loc2_._skillArr[61] = ["k1",1];
         _loc2_._skillArr[62] = ["k2",1];
         _loc2_._skillArr[63] = ["k3",1];
         _loc2_._skillArr[64] = ["k4",1];
         _loc2_._skillArr[65] = ["k5",1];
         _loc2_._skillArr[66] = ["k6",1];
         _loc2_._skillArr[67] = ["k7",1];
         _loc2_._skillArr[68] = ["k8",1];
         _loc2_._skillArr[69] = ["k9",0];
         _loc2_._skillArr[70] = ["k10",0];
         _loc2_._skillArr[71] = ["k11",0];
         _loc2_._skillArr[72] = ["k12",0];
         _loc2_._skillArr[73] = ["k13",0];
         _loc2_._skillArr[74] = ["k14",0];
         _loc2_._skillArr[75] = ["k15",0];
         _loc2_._skillArr[76] = ["k16",0];
      }
      
      private function XiuZengZhuangZhi() : *
      {
         var _loc2_:int = 0;
         if(Main.player1._transferArr.length < 4)
         {
            Main.player1._transferArr[3] = false;
            this.skillArrXXX();
         }
         if(Main.P1P2)
         {
            if(Main.player2._transferArr.length < 4)
            {
               Main.player2._transferArr[3] = false;
               this.skillArrXXX(2);
            }
         }
         var _loc1_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < 3)
         {
            if(Main.player1._transferArr[_loc2_])
            {
               _loc1_++;
            }
            _loc2_++;
         }
         if(_loc1_ > 1)
         {
            SkillPanel.open();
            SkillPanel.close();
            if(SkillPanel._instance)
            {
               SkillPanel._instance.aginP1();
               _loc2_ = 0;
               while(_loc2_ < 3)
               {
                  if(Main.player1._transferArr[_loc2_])
                  {
                     Main.player1._transferArr[_loc2_] = false;
                  }
                  _loc2_++;
               }
               Main.player1.AddKillPoint(180);
               Main.player1.addGold(20000);
               TiaoShi.txtShow("清除转职---------> 1P");
            }
         }
         _loc1_ = 0;
         if(Main.P1P2)
         {
            _loc2_ = 0;
            while(_loc2_ < 3)
            {
               if(Main.player2._transferArr[_loc2_])
               {
                  _loc1_++;
               }
               _loc2_++;
            }
            if(_loc1_ > 1)
            {
               SkillPanel.open();
               SkillPanel.close();
               if(SkillPanel._instance)
               {
                  SkillPanel._instance.aginP2();
                  _loc2_ = 0;
                  while(_loc2_ < 3)
                  {
                     if(Main.player2._transferArr[_loc2_])
                     {
                        Main.player2._transferArr[_loc2_] = false;
                     }
                     _loc2_++;
                  }
                  Main.player2.AddKillPoint(180);
                  Main.player2.addGold(20000);
                  TiaoShi.txtShow("清除转职---------> 2P");
               }
            }
         }
      }
   }
}

