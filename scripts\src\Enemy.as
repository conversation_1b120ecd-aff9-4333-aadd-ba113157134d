package src
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.quest.Quest;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.quest.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.fanpaiPanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setTransfer.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.Skin.*;
   import src.other.*;
   import src.tool.*;
   
   public class Enemy extends MovieClip
   {
      public static var EnemyXml:XML;
      
      public static var hit_x:int;
      
      public static var hit_y:int;
      
      public static var xuanshang5:Boolean = true;
      
      public static var wscd:int = 0;
      
      public static var EnemyArr:Array = new Array();
      
      public static var EnemyXmlArrMd5:Array = new Array();
      
      public static var EnemyXmlArr:Array = new Array();
      
      public static var All:Array = [];
      
      public static var only:Boolean = true;
      
      public static var gongJiPOWER:VT = VT.createVT();
      
      public static var hpNumX:Number = -1;
      
      public static var addTimes:int = 0;
      
      public static var bool_5:Boolean = true;
      
      public static var bool_6:Boolean = true;
      
      public static var boss54:int = 0;
      
      public var RLXX:String = "null";
      
      public var buffTime_jl2016:int;
      
      public var buffTime_jl2016_X2:Number;
      
      public var jianShangPER:Number = 0;
      
      public var cengshu:Array;
      
      public var rebornTimes:int = 0;
      
      public var iii:int = 0;
      
      public var zhuoshaoTimes:int = 0;
      
      public var shanghaiTemp:int = 0;
      
      public var yingzhiTemp:int = 0;
      
      public var id:int;
      
      public var className:String;
      
      public var isFunction:Boolean = true;
      
      public var lifeMAX:VT;
      
      public var life:VT;
      
      public var isIce:Boolean = false;
      
      public var RL:Boolean = true;
      
      public var over:Boolean;
      
      public var skinArr:Array;
      
      public var skinNum:int = 0;
      
      public var skin:EnemySkin;
      
      public var hitXX:HitXX;
      
      public var hit:MovieClip = null;
      
      public var jumptemp:int;
      
      public var att_temp:* = 0;
      
      public var walk_temp:* = 0;
      
      public var walk_power:* = 3;
      
      public var jump_power:* = 120;
      
      public var jump_time:* = 8;
      
      public var parabola:Number = 0.2;
      
      public var jumping:Boolean;
      
      public var jumpType:int = 2;
      
      public var gravity:int = 2;
      
      public var gravityNum:* = 0;
      
      public var gravityXX:int = 2;
      
      public var lastTime:int;
      
      public var 硬直:VT;
      
      public var 攻击力:VT;
      
      public var 防御力:VT;
      
      public var 魔攻力:VT;
      
      public var 魔防力:VT;
      
      public var 震退值:VT;
      
      public var 经验:VT;
      
      public var 等级:VT;
      
      public var 掉落物品:Array;
      
      public var 掉落概率:Array;
      
      public var 金钱:VT;
      
      public var 掉落数量:VT;
      
      public var 浮动硬直:Number = 0;
      
      public var 关卡:int = 0;
      
      public var changeMode:Boolean = true;
      
      public var distance_X:int;
      
      public var distance_Y:int;
      
      public var guard_XY:Array;
      
      public var attack_XY:Array;
      
      public var attack_Time:Array;
      
      public var attack_TimeNum:Array;
      
      public var runType:String = "自由";
      
      public var noMove:Boolean = false;
      
      public var lifeMC:EnemyLife;
      
      public var noHitMap:Boolean = false;
      
      public var gjcd_Time:int = 0;
      
      public var temp2:VT;
      
      public var temp3:VT;
      
      public var temp10:VT;
      
      public var tempXXX2:VT;
      
      public var rdom:int = 0;
      
      public var cj_arr:Array;
      
      internal var iceTime:int = 0;
      
      internal var temp:String;
      
      internal var tempXX:String;
      
      internal var tempTimeXX:int = 0;
      
      public var runX:int = 0;
      
      public var runY:int = 0;
      
      public var runArr:Array;
      
      public var EnemySkinXml:XML;
      
      public var 特殊掉落物品:Array;
      
      public var 特殊掉落概率:Array;
      
      public function Enemy(param1:int = 0)
      {
         var _loc2_:String = null;
         this.cengshu = [];
         this.lifeMAX = VT.createVT();
         this.life = VT.createVT();
         this.skinArr = [0,1];
         this.硬直 = VT.createVT();
         this.攻击力 = VT.createVT();
         this.防御力 = VT.createVT();
         this.魔攻力 = VT.createVT();
         this.魔防力 = VT.createVT();
         this.震退值 = VT.createVT();
         this.经验 = VT.createVT();
         this.等级 = VT.createVT();
         this.掉落物品 = [];
         this.掉落概率 = [];
         this.金钱 = VT.createVT();
         this.掉落数量 = VT.createVT();
         this.guard_XY = [10,10];
         this.attack_XY = [];
         this.attack_Time = [];
         this.attack_TimeNum = [];
         this.lifeMC = new EnemyLife();
         this.temp2 = VT.createVT(2);
         this.temp3 = VT.createVT(3);
         this.temp10 = VT.createVT(10);
         this.tempXXX2 = VT.createVT(VT.GetTempVT("8/4"));
         this.cj_arr = [[3,3000],[6,3500],[9,4000],[13,4200],[16,4400],[19,4600],[23,4700],[28,4800],[32,4900],[35,5000],[41,5100],[46,6000],[50,5100],[55,5200],[58,5400],[62,5500],[65,5600],[203,6500],[201,6500],[202,6500],[204,6500],[205,6500],[206,6500],[207,6500],[208,6500],[209,6500],[210,6500],[211,6500],[212,6500],[213,6500],[214,6500],[215,6500],[68,7200],[71,7300],[216,6500],[217,6500],[218,6500],[74,7700],[78,7800]];
         this.runArr = new Array();
         this.EnemySkinXml = new XML();
         this.特殊掉落物品 = [];
         this.特殊掉落概率 = [];
         super();
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         if(param1 != 0)
         {
            this.id = param1;
            _loc2_ = this.SkinData(param1);
            if(_loc2_ == null)
            {
               TiaoShi.txtShow("怪物信息错误!! 找不到ID" + param1);
            }
            else
            {
               this.addSkin(_loc2_);
               All[All.length] = this;
               this.skin.EnemySkinXml = EnemySkin.EnemySkinXmlArr[GameData.gameLV][this.关卡];
            }
         }
         else
         {
            TiaoShi.txtShow("无法创建 怪物0");
         }
      }
      
      public static function hpNUMXXX(param1:int) : *
      {
         if(hpNumX >= 0)
         {
            hpNumX += param1 * 0.04;
         }
      }
      
      public static function hpNUMXXX2(param1:Object, param2:Object, param3:int) : *
      {
         var _loc4_:int = 0;
         if(param1 is Fly && param1 is Cw17_Fly3x && param2 is ChongWu && param2.who is Player)
         {
            _loc4_ = param3 * 0.008;
            (param2.who as Player).HpUp(_loc4_);
         }
      }
      
      public static function getEnemyNum(param1:String) : int
      {
         var _loc2_:int = 0;
         for(i in All)
         {
            if(All[i].className == param1)
            {
               _loc2_++;
            }
         }
         return _loc2_;
      }
      
      public function addSkin(param1:String) : *
      {
         var _loc2_:Class = EnemyArr[this.关卡].getClass(param1) as Class;
         this.skin = new _loc2_();
         addChild(this.skin);
         this.skin.who = this;
         if(GameData.gameLV == 6)
         {
            this.scaleY = 1.5;
            this.scaleX = 1.5;
         }
         this.skin.id = this.id;
         this.set_attack_Time();
         addChild(this.lifeMC);
         this.lifeMC.stop();
         this.lifeMC.y = -this.skin.height;
         this.setMode();
      }
      
      public function setMode() : *
      {
         var _loc1_:Class = null;
         if(this.id == 2014 || this.id == 2018)
         {
            this.changeMode = false;
         }
         if(this.className == "罗生门" || this.id == 7004 || this.id == 7005 || this.id == 2018 || this.id == 2013 || this.id == 2014 || this.id == 5015 || this.id == 5016 || this.id == 5017 || this.id == 5018 || this.id == 5019 || this.id == 5020 || this.id == 5021 || this.id == 5022 || this.id == 2012 || this.id == 79 || this.id == 90 || this.id == 88)
         {
            this.isFunction = false;
         }
         if(this.className == "悬赏9")
         {
            _loc1_ = EnemyArr[this.关卡].getClass("黑暗护罩") as Class;
            this.skin.addChild(new _loc1_());
         }
      }
      
      public function SkinData(param1:int) : String
      {
         var _loc2_:* = undefined;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         for(_loc2_ in EnemyXml.怪物)
         {
            _loc3_ = int(EnemyXml.怪物[_loc2_].ID);
            if(_loc3_ == param1)
            {
               this.className = String(EnemyXml.怪物[_loc2_].名称);
               this.lifeMAX.setValue(int(EnemyXml.怪物[_loc2_].HP));
               this.life.setValue(int(EnemyXml.怪物[_loc2_].HP));
               this.walk_power = this.walk_temp = int(EnemyXml.怪物[_loc2_].移动速度);
               this.jumptemp = int(EnemyXml.怪物[_loc2_].跳跃力);
               this.jump_power = int(EnemyXml.怪物[_loc2_].跳跃力);
               this.jump_time = int(EnemyXml.怪物[_loc2_].跳跃时间);
               this.gravity = int(EnemyXml.怪物[_loc2_].重力);
               this.gravityXX = int(EnemyXml.怪物[_loc2_].重力);
               this.攻击力.setValue(Number(EnemyXml.怪物[_loc2_].攻击力));
               this.att_temp = Number(EnemyXml.怪物[_loc2_].攻击力);
               this.防御力.setValue(int(EnemyXml.怪物[_loc2_].防御力));
               this.魔攻力.setValue(int(EnemyXml.怪物[_loc2_].魔攻力));
               this.魔防力.setValue(int(EnemyXml.怪物[_loc2_].魔防力));
               this.震退值.setValue(Number(EnemyXml.怪物[_loc2_].震退值));
               this.经验.setValue(int(EnemyXml.怪物[_loc2_].经验));
               this.等级.setValue(int(EnemyXml.怪物[_loc2_].等级));
               this.lastTime = int(EnemyXml.怪物[_loc2_].存在时间);
               this.金钱.setValue(int(EnemyXml.怪物[_loc2_].金钱));
               this.掉落数量.setValue(int(EnemyXml.怪物[_loc2_].掉落数量));
               this.硬直.setValue(Number(EnemyXml.怪物[_loc2_].硬直));
               this.关卡 = int(EnemyXml.怪物[_loc2_].关卡);
               _loc4_ = int(EnemyXml.怪物[_loc2_].警戒距离x);
               _loc5_ = _loc4_ - Math.random() * 51;
               if(_loc5_ < 0)
               {
                  _loc5_ = 0;
               }
               this.guard_XY = [_loc5_,int(EnemyXml.怪物[_loc2_].警戒距离y)];
               _loc6_ = 0;
               while(_loc6_ < 6)
               {
                  if(int(EnemyXml.怪物[_loc2_]["攻击距离" + (_loc6_ + 1) + "x"]) != -1)
                  {
                     this.attack_XY[_loc6_] = [int(EnemyXml.怪物[_loc2_]["攻击距离" + (_loc6_ + 1) + "x"]),int(EnemyXml.怪物[_loc2_]["攻击距离" + (_loc6_ + 1) + "y"])];
                  }
                  _loc6_++;
               }
               _loc6_ = 0;
               while(_loc6_ < 20)
               {
                  this.掉落物品[_loc6_] = int(EnemyXml.怪物[_loc2_]["掉落物品" + (_loc6_ + 1)]);
                  _loc6_++;
               }
               _loc6_ = 0;
               while(_loc6_ < 20)
               {
                  this.掉落概率[_loc6_] = int(EnemyXml.怪物[_loc2_]["掉落概率" + (_loc6_ + 1)]);
                  _loc6_++;
               }
               _loc6_ = 0;
               while(_loc6_ < 3)
               {
                  this.特殊掉落物品[_loc6_] = int(EnemyXml.怪物[_loc2_]["特殊掉落ID" + (_loc6_ + 1)]);
                  _loc6_++;
               }
               _loc6_ = 0;
               while(_loc6_ < 3)
               {
                  this.特殊掉落概率[_loc6_] = int(EnemyXml.怪物[_loc2_]["特殊概率ID" + (_loc6_ + 1)]);
                  _loc6_++;
               }
               return String(EnemyXml.怪物[_loc2_].名称);
            }
         }
         return null;
      }
      
      public function GongHuiHp_Num() : *
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(GongHuiTiaoZan.tzData)
         {
            if(GameData.gameLV == 6)
            {
               _loc1_ = int(GongHuiTiaoZan.tzData[Main.gameNum.getValue() - 5000][0]);
               _loc2_ = this.lifeMAX.getValue() * _loc1_ / 100;
               this.life.setValue(_loc2_);
               TiaoShi.txtShow("GongHuiHp_Num 剩余血量 = " + _loc2_);
            }
         }
      }
      
      public function getRandom(param1:Number) : Boolean
      {
         var _loc2_:Number = Math.random() * 100;
         if(_loc2_ < param1)
         {
            return true;
         }
         return false;
      }
      
      public function HpXX(param1:HitXX, param2:Boolean = false, param3:Boolean = false) : *
      {
         var _loc6_:Player = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc14_:Player = null;
         var _loc15_:Number = NaN;
         var _loc16_:Number = NaN;
         var _loc17_:Array = null;
         var _loc18_:int = 0;
         var _loc19_:int = 0;
         var _loc20_:HitXX = null;
         var _loc21_:Number = NaN;
         var _loc22_:Array = null;
         var _loc23_:String = null;
         var _loc24_:int = 0;
         var _loc25_:String = null;
         var _loc26_:int = 0;
         var _loc27_:Number = NaN;
         var _loc28_:Number = NaN;
         var _loc29_:Number = NaN;
         var _loc30_:Number = NaN;
         var _loc31_:BuffEnemy = null;
         var _loc32_:Player = null;
         var _loc33_:HitXX = null;
         var _loc34_:Class = null;
         var _loc35_:Class = null;
         var _loc36_:* = undefined;
         var _loc37_:int = 0;
         var _loc38_:Number = NaN;
         var _loc39_:Class = null;
         var _loc40_:Class = null;
         var _loc41_:int = 0;
         var _loc42_:Class = null;
         var _loc43_:Class = null;
         var _loc44_:Class = null;
         var _loc45_:SkillDarkBoom = null;
         var _loc46_:int = 0;
         var _loc47_:Player = null;
         var _loc48_:int = 0;
         var _loc49_:Player = null;
         var _loc50_:int = 0;
         var _loc51_:int = 0;
         var _loc52_:int = 0;
         this.hitXX = param1;
         var _loc4_:int = param1.times;
         var _loc5_:Number = 1;
         _loc8_ = this.x + Math.random() * 100 - 50;
         _loc9_ = this.x + Math.random() * 100 - 50;
         _loc10_ = this.y + Math.random() * 100 - 50;
         var _loc11_:int = this.y + Math.random() * 100;
         if(param1.who is Player)
         {
            if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == 4)
            {
               (param1.who as Player).fbChengFa = true;
            }
            _loc14_ = param1.who;
            if(_loc14_.playerJL)
            {
               ++_loc14_.playerJL.ZD1num;
               _loc14_.playerJL.JNnumUP(this.x + Main.world.x,this.y,Play_Interface.interfaceX);
            }
            if(param1.who == Main.player_1)
            {
               ChongWu.gongJiYn1 = true;
            }
            else
            {
               ChongWu.gongJiYn2 = true;
            }
            _loc6_ = param1.who as Player;
            if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 0)
            {
               _loc5_ = Number(_loc6_.职业附加[0]);
            }
            else if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 1)
            {
               _loc5_ = Number(_loc6_.职业附加[1]);
            }
            else if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 2)
            {
               _loc5_ = Number(_loc6_.职业附加[2]);
            }
            else if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 3)
            {
               _loc5_ = Number(_loc6_.职业附加[3]);
            }
            _loc15_ = _loc6_.use_gongji.getValue();
            _loc16_ = _loc15_ * param1.gongJi_hp * _loc5_;
            _loc16_ += _loc16_ * Math.random() * InitData.X002.getValue();
            if(_loc4_ == 0)
            {
               _loc4_ = 1;
            }
            _loc7_ = _loc16_ - this.防御力.getValue() / _loc4_;
            if(_loc7_ <= 0)
            {
               _loc7_ = 0;
            }
            if(param2)
            {
               _loc7_ *= this.tempXXX2.getValue();
               if(Math.random() * 100 < _loc6_.data.getElvesSlot().backElvesSkill3())
               {
                  _loc7_ *= 1 + _loc6_.data.getElvesSlot().backElvesSkill3_2();
               }
            }
            if(Math.random() * 100 < 20 && _loc6_.data.getElvesSlot().backElvesSkill6() > 0)
            {
               _loc20_ = new HitXX();
               _loc20_.type = 5011;
               _loc20_.space = 27;
               _loc20_.totalTime = 27;
               _loc20_.numValue = int(_loc6_.use_gongji.getValue() * _loc6_.data.getElvesSlot().backElvesSkill6());
               new BuffEnemy(_loc20_,this);
            }
            _loc6_.energySlot.addEnergy(param1.who,_loc7_);
            _loc17_ = _loc6_.data.getEquipSlot().getAllSuitSkill();
            _loc18_ = 0;
            for(_loc19_ in _loc17_)
            {
               if(_loc17_[_loc19_] == 57359)
               {
                  if(this.getRandom(30) && _loc6_.flagTempSY)
                  {
                     _loc6_.setInTimeCDSY();
                     _loc6_.flagTempSY = false;
                     _loc6_.HpUp(_loc6_.use_hp_Max.getValue() * 0.035);
                     _loc21_ = _loc6_.use_hp_Max.getValue();
                     if(param1.who is Player && Main.water.getValue() != 1)
                     {
                        _loc21_ = _loc21_ * (1 + (param1.who as Player).use_gongji2.getValue() / 100) / (1 + this.魔防力.getValue() / 100);
                     }
                     for(_loc19_ in All)
                     {
                        this.HpSXX(All[_loc19_],_loc21_);
                     }
                  }
               }
               else if(_loc17_[_loc19_] == 57360)
               {
                  if(this.getRandom(5) && _loc6_.flagTempSJ)
                  {
                     _loc6_.setInTimeCDSJ();
                     _loc6_.flagTempSJ = false;
                     _loc22_ = [];
                     _loc23_ = "";
                     if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 0)
                     {
                        _loc23_ = "a";
                     }
                     else if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 1)
                     {
                        _loc23_ = "b";
                     }
                     else if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 2)
                     {
                        _loc23_ = "c";
                     }
                     else if(_loc6_.data.skinArr[_loc6_.data.skinNum] == 3)
                     {
                        _loc23_ = "k";
                     }
                     for(_loc24_ in _loc6_.AllSkillCDXX)
                     {
                        _loc25_ = _loc6_.AllSkillCDXX[_loc24_][0];
                        if(_loc25_.substr(0,1) == _loc23_ && int(_loc25_.substr(1,2)) > 7 && int(_loc25_.substr(1,2)) <= 15)
                        {
                           if(_loc6_.data.getSkillLevel(_loc25_) > 0 && _loc6_.AllSkillCDXX[_loc24_][1] != _loc6_.AllSkillCD[_loc24_][1])
                           {
                              _loc22_.push(_loc24_);
                           }
                        }
                     }
                     if(_loc22_.length > 0)
                     {
                        _loc26_ = Math.random() * _loc22_.length;
                        _loc6_.AllSkillCDXX[_loc22_[_loc26_]][1] = _loc6_.AllSkillCD[_loc22_[_loc26_]][1];
                        _loc6_.AllSkillCDXX[_loc22_[_loc26_]][2] = 50;
                     }
                  }
               }
               else if(_loc17_[_loc19_] == 57361)
               {
                  _loc27_ = Math.pow(Number(_loc6_.use_baoji.getValue() / 70),0.8) / 100;
                  _loc28_ = Math.pow(1 + _loc27_,4.7);
                  if(this.getRandom(1.8) && _loc6_.flagTempBJ && param2)
                  {
                     _loc6_.setInTimeCDBJ();
                     _loc6_.flagTempBJ = false;
                     if(_loc6_.data.skinNum == 0)
                     {
                        _loc18_ = int(_loc6_.data.getEquipSlot().getEquipFromSlot(2).getAttack() * _loc28_);
                     }
                     else
                     {
                        _loc18_ = int(_loc6_.data.getEquipSlot().getEquipFromSlot(5).getAttack() * _loc28_);
                     }
                     if(param1.who is Player && Main.water.getValue() != 1)
                     {
                        _loc18_ = _loc18_ * (1 + (param1.who as Player).use_gongji2.getValue() / 100) / (1 + this.魔防力.getValue() / 100);
                     }
                     if(_loc18_ > 650000)
                     {
                        _loc18_ = 650000;
                     }
                     if(_loc18_ > 200000 && GameData.gameLV == 5)
                     {
                        _loc18_ = 200000;
                     }
                  }
                  else if(param2)
                  {
                     _loc29_ = Number(Math.pow(Number((param1.who as Player).use_baoji.getValue() / 70),InitData.Temp8.getValue()).toFixed(1));
                     _loc30_ = _loc29_ / InitData.BuyNum_2000.getValue() + (param1.who as Player).num_baoSang.getValue();
                     _loc7_ *= 1 + _loc30_;
                  }
               }
               else if(_loc17_[_loc19_] != 57362)
               {
                  if(_loc17_[_loc19_] == 57363)
                  {
                     if(this.getRandom(15) && _loc6_.flagTempXL)
                     {
                        _loc6_.setInTimeCDXL();
                        _loc6_.flagTempXL = false;
                        _loc18_ = (_loc6_.use_mp_Max.getValue() + _loc6_.mp.getValue()) * ((_loc6_.use_mp_Max.getValue() + _loc6_.mp.getValue()) / 10000);
                        if(param1.who is Player && Main.water.getValue() != 1)
                        {
                           _loc18_ = _loc18_ * (1 + (param1.who as Player).use_gongji2.getValue() / 100) / (1 + this.魔防力.getValue() / 100);
                        }
                        if(_loc18_ > 650000)
                        {
                           _loc18_ = 650000;
                        }
                        if(_loc18_ > 200000 && GameData.gameLV == 5)
                        {
                           _loc18_ = 200000;
                        }
                     }
                  }
               }
            }
         }
         else
         {
            _loc7_ = param1.gongJi_hp;
            if(_loc7_ <= 0)
            {
               _loc7_ = 0;
            }
         }
         if(this.cengshu.length > 0)
         {
            for(_loc19_ in this.cengshu)
            {
               if(this.cengshu[_loc19_].type == 605)
               {
                  this.skin.GoTo("被打",this.yingzhiTemp);
                  this.skin.continuousTime = this.yingzhiTemp;
               }
               else if(this.cengshu[_loc19_].type == 606)
               {
                  if(param1.who is Player)
                  {
                     _loc31_ = this.cengshu[_loc19_];
                     if((param1.who as Player).hp.getValue() > 0)
                     {
                        (param1.who as Player).HpUp(_loc31_.numValue * _loc7_);
                     }
                  }
               }
            }
         }
         if(param1.who is ChongWu)
         {
            this.shanghaiTemp = 0;
            _loc32_ = (param1.who as ChongWu).who;
            if((param1.who as ChongWu).data.getPetEquip())
            {
               if((param1.who as ChongWu).data.getPetEquip().getType() == 4)
               {
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  _loc20_ = new HitXX();
                  _loc20_.type = 502;
                  _loc20_.space = _loc22_[1];
                  _loc20_.totalTime = _loc22_[2];
                  _loc20_.numValue = _loc32_.use_gongji.getValue() * _loc22_[0];
                  _loc20_.speedValue = _loc22_[4];
                  new BuffEnemy(_loc20_,this);
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 5)
               {
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[3])
                  {
                     _loc20_ = new HitXX();
                     _loc20_.type = 503;
                     _loc20_.totalTime = 135;
                     _loc20_.space = _loc20_.totalTime;
                     _loc20_.numValue = _loc32_.use_gongji.getValue() * _loc22_[0];
                     _loc20_.speedValue = _loc22_[4];
                     new BuffEnemy(_loc20_,this);
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 8 && wscd > 135)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[1])
                  {
                     _loc20_ = new HitXX();
                     _loc20_.type = 504;
                     _loc20_.space = 16;
                     _loc20_.totalTime = 16;
                     _loc20_.numValue = _loc32_.use_gongji.getValue() * _loc22_[0];
                     new BuffEnemy(_loc20_,this);
                     _loc33_ = new HitXX();
                     _loc33_.type = 504;
                     _loc33_.space = 16;
                     _loc33_.totalTime = 16;
                     _loc33_.numValue = _loc22_[2];
                     new BuffEffect(_loc33_,(param1.who as ChongWu).who);
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 9 && wscd > 80)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[1])
                  {
                     _loc20_ = new HitXX();
                     _loc20_.type = 505;
                     _loc20_.totalTime = _loc22_[0];
                     new BuffEnemy(_loc20_,this);
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 17 && wscd > 54)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[0])
                  {
                     _loc32_.HpUp(int(param1.gongJi_hp * _loc22_[1]));
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 18)
               {
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  _loc32_.MpUp(Math.ceil(param1.gongJi_hp * _loc22_[0]));
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 19 && wscd > 54)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[0])
                  {
                     _loc20_ = new HitXX();
                     _loc20_.type = 66;
                     _loc20_.totalTime = 81;
                     _loc20_.numValue = _loc22_[1];
                     new BuffEffect(_loc20_,_loc32_);
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 21)
               {
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  this.shanghaiTemp = param1.gongJi_hp * _loc22_[1];
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 22 && wscd > 54)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[0])
                  {
                     hit_x = this.x;
                     hit_y = this.y;
                     _loc34_ = NewLoad.XiaoGuoData.getClass("奥加大剑") as Class;
                     _loc32_.skin_W.addChild(new _loc34_());
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 23 && wscd > 135)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[0])
                  {
                     this.yingzhiTemp = _loc22_[1];
                     _loc20_ = new HitXX();
                     _loc20_.type = 605;
                     _loc20_.totalTime = 81;
                     new BuffEnemy(_loc20_,this);
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 10 && wscd > 405)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[0])
                  {
                     ++addTimes;
                     if(addTimes >= 5)
                     {
                        addTimes = 0;
                        (param1.who as ChongWu).who.HpUp(_loc22_[1],2);
                     }
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 11 && wscd > 135)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[1])
                  {
                     _loc20_ = new HitXX();
                     _loc20_.type = 506;
                     wscd = 0;
                     _loc20_.totalTime = _loc22_[0];
                     new BuffEnemy(_loc20_,this);
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 12 && GameData.gameLV == 5)
               {
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  _loc20_ = new HitXX();
                  _loc20_.type = 501;
                  _loc20_.space = _loc22_[0];
                  _loc20_.totalTime = _loc22_[1];
                  _loc20_.numValue = int(this.lifeMAX.getValue() * 0.01);
                  new BuffEnemy(_loc20_,this);
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 13 && GameData.gameLV == 5)
               {
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  param1.gongJi_hp = param1.gongJi_Ctr * _loc22_[0];
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 2 && GameData.gameLV == 5 && wscd > 135)
               {
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[0])
                  {
                     wscd = 0;
                     if(this.life.getValue() <= this.lifeMAX.getValue() * _loc22_[1])
                     {
                        this.life.setValue(0);
                     }
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 3)
               {
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= _loc22_[1])
                  {
                     _loc20_ = new HitXX();
                     _loc20_.type = 507;
                     _loc20_.totalTime = 135;
                     _loc20_.space = _loc20_.totalTime;
                     _loc20_.numValue = _loc22_[2];
                     new BuffEnemy(_loc20_,this);
                  }
               }
               if((param1.who as ChongWu).data.getPetEquip().getType() == 1 && wscd > 80)
               {
                  wscd = 0;
                  _loc22_ = (param1.who as ChongWu).data.getPetEquip().getAffect();
                  this.rdom = Math.round(Math.random() * 100);
                  if(this.rdom <= _loc22_[1])
                  {
                     _loc19_ = 0;
                     while(_loc19_ < _loc22_[0])
                     {
                        if(Enemy.All[_loc19_])
                        {
                           _loc35_ = NewLoad.XiaoGuoData.getClass("蛇冠") as Class;
                           _loc36_ = int(param1.gongJi_hp * 2);
                           _loc37_ = Enemy.All[_loc19_].life.getValue() - _loc36_;
                           Enemy.All[_loc19_].hpCount(_loc36_);
                           Enemy.All[_loc19_].skin.addChild(new _loc35_());
                           HPdown.Open(_loc36_,Enemy.All[_loc19_].x,Enemy.All[_loc19_].y - Enemy.All[_loc19_].height);
                        }
                        _loc19_++;
                     }
                  }
               }
            }
            _loc7_ = param1.gongJi_hp;
            if(this.shanghaiTemp > 0)
            {
               _loc7_ = this.shanghaiTemp;
            }
            _loc7_ = int(GongHui_jiTan.CW_XX(_loc7_));
         }
         if(param1.who is Player && Main.water.getValue() != 1)
         {
            _loc7_ = _loc7_ * (1 + (param1.who as Player).use_gongji2.getValue() / 100) / (1 + this.魔防力.getValue() / 100);
         }
         if(GameData.gameLV == 5 && _loc7_ > param1.gongJi_hp_MAX)
         {
            _loc7_ = param1.gongJi_hp_MAX;
         }
         if(Main.tiaoShiYN)
         {
            _loc7_ += gongJiPOWER.getValue();
         }
         if(Boolean(NewPetPanel.XueMai) && (Main.player_1.playerCW || Main.P1P2 && Main.player_2.playerCW))
         {
            if(NewPetPanel.XueMai.isHaveSX(3))
            {
               _loc7_ *= 1.05;
            }
         }
         if(CaiYaoPanel.saveArr[1] > 0)
         {
            _loc38_ = 1 + (CaiYaoPanel.saveArr[1] * CaiYaoPanel.addArr[1] + CaiYaoPanel.saveArr[4] * CaiYaoPanel.addArr[4] + CaiYaoPanel.saveArr[7] * CaiYaoPanel.addArr[7] + CaiYaoPanel.saveArr[10] * CaiYaoPanel.addArr[10] + CaiYaoPanel.saveArr[13] * CaiYaoPanel.addArr[13]) * 0.01;
            _loc7_ *= _loc38_;
         }
         if(param1.who is Player)
         {
            _loc7_ *= (param1.who as Player).gongji_UP;
         }
         hit_x = this.x;
         hit_y = this.y;
         if(param1.who is Player)
         {
            if((param1.who as Player).data.skinNum == 0 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(2).getHuanHua() == 4 || (param1.who as Player).data.skinNum == 1 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(5).getHuanHua() == 4)
            {
               if(Math.random() * 100 < 40 && Player.szCD > 160)
               {
                  Player.szCD = 0;
                  _loc39_ = NewLoad.OtherData.getClass("时装技能激光") as Class;
                  (param1.who as Player).skin_W.addChild(new _loc39_());
               }
            }
            if((param1.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 372 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
            {
               if(Math.random() * 100 < 5 && Player.szCD > 54)
               {
                  Player.szCD = 0;
                  _loc40_ = NewLoad.XiaoGuoData.getClass("时装陨石") as Class;
                  (param1.who as Player).skin_W.addChild(new _loc40_());
               }
            }
            if((param1.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 377 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
            {
               _loc41_ = 0;
               _loc19_ = 0;
               while(_loc19_ < Fly.All.length)
               {
                  if(Fly.All[_loc19_]._name == "时装龙卷风")
                  {
                     _loc41_++;
                  }
                  _loc19_++;
               }
               if(Math.random() * 100 < 5 && _loc41_ < 3 && Player.szCD > 54)
               {
                  Player.szCD = 0;
                  _loc34_ = NewLoad.XiaoGuoData.getClass("时装龙卷风") as Class;
                  (param1.who as Player).skin_W.addChild(new _loc34_());
               }
            }
            if((param1.who as Player).data.skinNum == 0 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(2).getHuanHua() == 1 || (param1.who as Player).data.skinNum == 1 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(5).getHuanHua() == 1)
            {
               if(Math.random() * 100 < 10)
               {
                  _loc42_ = NewLoad.OtherData.getClass("zhuoshang") as Class;
                  this.skin.addChild(new _loc42_());
                  this.HpXX2((param1.who as Player).use_gongji.getValue() * 0.5);
                  ++this.zhuoshaoTimes;
               }
               if(this.zhuoshaoTimes >= 10)
               {
                  _loc43_ = NewLoad.OtherData.getClass("huolong") as Class;
                  (param1.who as Player).skin_W.addChild(new _loc43_());
                  this.zhuoshaoTimes = 0;
               }
            }
            if((param1.who as Player).data.skinNum == 0 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(2).getHuanHua() == 3 || (param1.who as Player).data.skinNum == 1 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(5).getHuanHua() == 3)
            {
               if(Math.random() * 100 < 20 && Player.szCD > 54)
               {
                  Player.szCD = 0;
                  _loc44_ = NewLoad.XiaoGuoData.getClass("幻化黑暗爆炸") as Class;
                  _loc45_ = new _loc44_();
                  (param1.who as Player).skin_W.addChild(_loc45_);
                  _loc45_.nameXX = "幻化黑暗爆炸";
               }
            }
            if((param1.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 404 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
            {
               if(Player.szCD > 71 && (param1.who as Player).isDarkState == false)
               {
                  Player.szCD = 0;
                  if(Math.random() * 100 < 20)
                  {
                     (param1.who as Player).darkTime = 0;
                     (param1.who as Player).isDarkState = true;
                  }
               }
            }
            if((param1.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 446 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
            {
               if(Player.szCD > 40)
               {
                  if(Math.random() * 100 < 35)
                  {
                     if((param1.who as Player).jianCDnum < 11)
                     {
                        if((param1.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 446 && (param1.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
                        {
                           new GDpic2(this.x + Main.world.x,this.y - 100,Play_Interface.interfaceX,param1.who);
                           ++(param1.who as Player).jianCDnum;
                           if(param1.who == Main.player_1)
                           {
                              if((param1.who as Player).jianCDnum < 11)
                              {
                                 Play_Interface.interfaceX.xiari1.gotoAndStop((param1.who as Player).jianCDnum);
                              }
                              else
                              {
                                 Play_Interface.interfaceX.xiari1.gotoAndPlay((param1.who as Player).jianCDnum);
                              }
                           }
                           else if((param1.who as Player).jianCDnum < 11)
                           {
                              Play_Interface.interfaceX.xiari2.gotoAndStop((param1.who as Player).jianCDnum);
                           }
                           else
                           {
                              Play_Interface.interfaceX.xiari2.gotoAndPlay((param1.who as Player).jianCDnum);
                           }
                           if((param1.who as Player).jianCDnum == 11)
                           {
                              (param1.who as Player).GetAllSkillCD();
                           }
                        }
                     }
                     (param1.who as Player).jianCD = true;
                  }
                  else
                  {
                     Player.szCD = 0;
                  }
               }
            }
         }
         if(Main.gameNum.getValue() == 28)
         {
            for(_loc19_ in Fly.All)
            {
               if((Fly.All[_loc19_] as Fly)._name == "天使护盾")
               {
                  _loc7_ *= 0.5;
               }
            }
         }
         if(this.className == "摩羯座")
         {
            for(_loc19_ in Fly.All)
            {
               if((Fly.All[_loc19_] as Fly)._name == "黑茫光环")
               {
                  _loc7_ = 1;
                  if(EnemyBoss29.BOSSENG < 5)
                  {
                     ++EnemyBoss29.BOSSENG;
                  }
               }
            }
         }
         if(Main.gameNum.getValue() == 57)
         {
            for(_loc19_ in Enemy.All)
            {
               if((Enemy.All[_loc19_] as Enemy).id == 79 && this.id != 79)
               {
                  if(Math.abs(Enemy.All[_loc19_].x - this.x) < 350)
                  {
                     _loc7_ *= 0.1;
                  }
               }
            }
         }
         if(this.className == "阿卡利" || this.className == "阿卡利召唤")
         {
            _loc46_ = Math.random() * 100;
            if(_loc46_ < 3)
            {
               this.skin.otherGoTo("攻击5");
               return;
            }
         }
         if(this.id == 7002)
         {
            this.skin.GoTo("攻击1");
            return;
         }
         if(this.id == 1056)
         {
            _loc46_ = Math.random() * 100;
            if(_loc46_ < 3)
            {
               if(this.attack_TimeNum[1] == 108)
               {
                  this.skin.GoTo("攻击2");
                  return;
               }
            }
         }
         if(this.className == "悬赏8")
         {
            _loc46_ = Math.random() * 100;
            if(_loc46_ < 3)
            {
               this.skin.otherGoTo("攻击1");
               return;
            }
         }
         if(this.id == 9001)
         {
            if(param1.who is Player)
            {
               if((param1.who as Player).skin.currentLabel != "攻击1" && (param1.who as Player).skin.currentLabel != "攻击2" && (param1.who as Player).skin.currentLabel != "攻击3" && (param1.who as Player).skin.currentLabel != "攻击4" && param1.parent._name != "法师魔法弹")
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"请按J普通攻击");
                  return;
               }
            }
         }
         if(this.id == 9002)
         {
            if(param1.who is Player)
            {
               if((param1.who as Player).skin.currentLabel != "上挑")
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"请按W+J特殊攻击");
                  return;
               }
            }
         }
         if(this.id == 9003)
         {
            if(param1.who is Player)
            {
               if((param1.who as Player).skin.currentLabel != "下斩")
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"请按S+J特殊攻击");
                  return;
               }
            }
         }
         if(this.className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value -= _loc7_;
            if(EnemyBossXS225.xishou_Value < 0)
            {
               EnemyBossXS225.xishou_Value = 0;
            }
            if(_loc7_ < EnemyBossXS225.xishou_Value)
            {
               _loc7_ = 0;
            }
            if(_loc18_ < EnemyBossXS225.xishou_Value)
            {
               _loc18_ = 0;
            }
         }
         if(this.id == 2015 || Main.gameNum.getValue() == 3000)
         {
            _loc7_ = Math.ceil(Math.random() * 2);
            _loc18_ = Math.ceil(Math.random() * 2);
         }
         if(this.id == 1056)
         {
            _loc7_ -= _loc7_ * this.jianShangPER;
         }
         if(this.id == 7007 && this.skin.redTime > 0)
         {
            _loc7_ *= 0.5;
         }
         _loc7_ = this.GongHuiBoos_HpXX(_loc7_);
         _loc18_ = this.GongHuiBoos_HpXX(_loc18_);
         if(Boolean(Main.tiaoShiYN) && this.id == 2015)
         {
            _loc7_ = 9999999;
         }
         if(param1.who is Player)
         {
            _loc47_ = param1.who;
            if(_loc47_.playerJL)
            {
               _loc7_ = _loc47_.playerJL.BD1020(_loc7_);
            }
         }
         if(_loc18_ > 0)
         {
            NewMC.Open("_特殊数字",Main.world.moveChild_Other,_loc8_,_loc10_ - 50,20,_loc18_,true);
         }
         GongHuiTiaoZan.BossHpXX(this);
         if(param1.who is Player && param1.who.hpXX2num != 0)
         {
            if(Main.gameNum.getValue() == 3000)
            {
               _loc7_ = Math.random() * 2 + 1;
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,_loc8_,_loc10_,20,_loc7_,true);
            }
            else if(this.id == 2015 || Main.gameNum.getValue() == 3000)
            {
               _loc7_ = Math.ceil(Math.random() * 2);
               _loc18_ = Math.ceil(Math.random() * 2);
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,_loc8_,_loc10_,20,_loc7_,true);
            }
            else if(this.className == "悬赏9")
            {
               EnemyBossXS225.xishou_Value -= _loc7_;
               if(EnemyBossXS225.xishou_Value < 0)
               {
                  EnemyBossXS225.xishou_Value = 0;
               }
               if(_loc7_ < EnemyBossXS225.xishou_Value)
               {
                  _loc7_ = 0;
               }
            }
            else
            {
               _loc7_ = this.GongHuiBoos_HpXX(param1.who.hpXX2num);
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,_loc8_,_loc10_,20,_loc7_,true);
            }
         }
         else if(param2)
         {
            if(Main.gameNum.getValue() == 3000)
            {
               _loc7_ = Math.random() * 2 + 1;
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,_loc8_,_loc10_,20,_loc7_,true);
            }
            else if(this.id == 31)
            {
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,_loc8_,_loc10_ - 200,20,_loc7_,true);
            }
            else if(this.id != 5003)
            {
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,_loc8_,_loc10_,20,_loc7_,true);
            }
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            _loc7_ = Math.random() * 2 + 1;
            NewMC.Open("_攻击数字",Main.world.moveChild_Other,_loc8_,_loc10_,20,_loc7_,true);
         }
         else if(this.id == 31)
         {
            NewMC.Open("_攻击数字",Main.world.moveChild_Other,_loc8_,_loc10_ - 200,15,_loc7_,true);
         }
         else if(this.id != 5003)
         {
            NewMC.Open("_攻击数字",Main.world.moveChild_Other,_loc8_,_loc10_,15,_loc7_,true);
         }
         if(this.buffTime_jl2016 > 0)
         {
            if(xArr[0] == 1)
            {
               _loc6_ = xArr[1];
               _loc48_ = _loc7_ * xArr[2];
               _loc6_.HpUp(_loc48_);
               NewMC.Open("回血效果",_loc6_,0,0,0,_loc48_);
            }
         }
         if(param1.who is Player)
         {
            _loc49_ = param1.who;
            if(_loc49_.playerJL)
            {
               _loc49_.playerJL.BD1001(_loc7_);
            }
         }
         if(_loc7_ > 0 && this.life.getValue() > 0)
         {
            this.life.setValue(this.life.getValue() - _loc7_);
            hpNUMXXX(_loc7_);
            hpNUMXXX2(param1.parent,param1.who,_loc7_);
            this.life.setValue(this.life.getValue() - _loc18_);
            hpNUMXXX(_loc18_);
            hpNUMXXX2(param1.parent,param1.who,_loc18_);
            WinShow.txt_4 += _loc7_;
            _loc50_ = 100 - this.life.getValue() / this.lifeMAX.getValue() * 100;
            this.lifeMC.gotoAndStop(_loc50_);
            if(this.life.getValue() < this.lifeMAX.getValue() / 2)
            {
               if(Boolean(JingLingCatch.only) && GameData.BOSSid.getValue() == this.id)
               {
                  JingLingCatch.addSkin();
                  JingLingCatch.only = false;
               }
            }
            GongHuiTiaoZan.BossHpXX(this);
            if(this.life.getValue() <= 0)
            {
               if(param1.name == "金箍棒")
               {
                  (param1.who as ChongWu).goJiCD_now[2] = 0;
                  (param1.who as ChongWu).goJiCD_now[1] += 2;
                  (param1.who as ChongWu).goJiCD_now[3] += 2;
                  (param1.who as ChongWu).goJiCD_now[4] += 2;
                  (param1.who as ChongWu).nullTime = 0;
                  TiaoShi.txtShow("金箍棒XXX");
               }
               if(this.rebornTimes > 0)
               {
                  this.reborn();
                  return;
               }
               this.life.setValue(0);
               if(this.金钱.getValue() > 0)
               {
                  NewMC.Open("掉钱",Main.world.moveChild_Other,_loc9_,_loc11_,15,this.金钱.getValue(),true);
               }
               if(Boolean(_loc6_) && _loc6_.data.getElvesSlot().backElvesSkill2() > 0)
               {
                  _loc6_.HpUp(_loc6_.data.getElvesSlot().backElvesSkill2(),2);
               }
               _loc51_ = this.经验.getValue() * InitData.GetEXPxN().getValue();
               _loc51_ = int(GongHui_jiTan.expUP(_loc51_));
               Main.player_1.ExpUP(_loc51_);
               Main.player_1.MoneyUP(this.金钱.getValue());
               if(Main.P1P2)
               {
                  Main.player_2.ExpUP(_loc51_);
                  Main.player_2.MoneyUP(this.金钱.getValue());
               }
               this.任务品掉落();
               this.装备掉落();
               PaiHang_Data.EnemyOver();
               AchData.addEnemyNum(this.id);
               TaskData.setEnemyNum(this.id);
               TaskData.isEnemyTaskOk();
               if(Main.serverTime.getValue() <= NewYear_Interface.overTime)
               {
                  this.newYearDiaoLuo();
               }
               this.fiveOneDiaoLuo();
               CardPanel.monsterSlot.addMonsterCard(this.id);
               GongHuiRenWu.enemyOK(this.id);
            }
            GongHuiTiaoZan.BossHpXX(this);
         }
         var _loc12_:Number = Math.random() * this.temp3.getValue() / this.temp10.getValue();
         this.浮动硬直 += _loc12_;
         var _loc13_:Number = param1.硬直 - this.skin.被攻击硬直 - this.浮动硬直;
         if(_loc13_ >= this.skin.continuousTime)
         {
            _loc13_ = param1.硬直 - this.硬直.getValue() - this.浮动硬直;
         }
         if(_loc13_ >= 0)
         {
            this.skin.GoTo("被打",int(_loc13_));
            this.jump_power = 0;
         }
         if(this.震退值.getValue() != 0)
         {
            this.runArr = new Array();
            _loc8_ = param1.runArr[0] * this.震退值.getValue();
            _loc10_ = param1.runArr[1] * this.震退值.getValue();
            _loc52_ = param1.runArr[2] * this.震退值.getValue();
            _loc9_ = Math.abs(_loc8_);
            if(this.RLXX == "R")
            {
               this.runPower(-3,_loc10_,_loc52_);
            }
            else if(this.RLXX == "L")
            {
               this.runPower(3,_loc10_,_loc52_);
            }
            else if(param1.RL)
            {
               this.runPower(-_loc8_,_loc10_,_loc52_);
            }
            else
            {
               this.runPower(_loc8_,_loc10_,_loc52_);
            }
         }
      }
      
      public function GongHuiBoos_HpXX(param1:int) : int
      {
         if(GameData.gameLV == 6)
         {
            if(param1 <= 5000)
            {
               return 1;
            }
            if(param1 <= 10000)
            {
               return 2;
            }
            if(param1 <= 20000)
            {
               return 3;
            }
            if(param1 <= 40000)
            {
               return 4;
            }
            if(param1 <= 80000)
            {
               return 5;
            }
            return 6;
         }
         return param1;
      }
      
      public function HpXX2(param1:int, param2:Array = null) : *
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         if(this.id == 7002)
         {
            return;
         }
         if(this.className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value -= param1;
            if(EnemyBossXS225.xishou_Value < 0)
            {
               EnemyBossXS225.xishou_Value = 0;
            }
            if(param1 < EnemyBossXS225.xishou_Value)
            {
               param1 = 0;
            }
         }
         if(this.id == 2015 || Main.gameNum.getValue() == 3000)
         {
            param1 = Math.ceil(Math.random() * 2);
         }
         if(Main.gameNum.getValue() == 3000)
         {
            param1 = Math.random() * 2 + 1;
         }
         param1 = this.GongHuiBoos_HpXX(param1);
         if(param1 > 0 && this.life.getValue() > 0)
         {
            this.life.setValue(this.life.getValue() - param1);
            NewMC.Open("_攻击数字",Main.world.moveChild_Other,this.x,this.y,15,param1,true);
            WinShow.txt_4 += param1;
            _loc3_ = 100 - this.life.getValue() / this.lifeMAX.getValue() * 100;
            this.lifeMC.gotoAndStop(_loc3_);
            if(this.life.getValue() <= 0)
            {
               if(this.rebornTimes > 0)
               {
                  this.reborn();
                  return;
               }
               this.life.setValue(0);
               _loc4_ = this.经验.getValue() * InitData.GetEXPxN().getValue();
               _loc4_ = int(GongHui_jiTan.expUP(_loc4_));
               Main.player_1.ExpUP(_loc4_);
               Main.player_1.MoneyUP(this.金钱.getValue());
               if(Main.P1P2)
               {
                  Main.player_2.ExpUP(_loc4_);
                  Main.player_2.MoneyUP(this.金钱.getValue());
               }
               this.任务品掉落();
               this.装备掉落();
               PaiHang_Data.EnemyOver();
               AchData.addEnemyNum(this.id);
               TaskData.setEnemyNum(this.id);
               TaskData.isEnemyTaskOk();
               CardPanel.monsterSlot.addMonsterCard(this.id);
               GongHuiRenWu.enemyOK(this.id);
            }
         }
         GongHuiTiaoZan.BossHpXX(this);
      }
      
      public function reborn() : *
      {
         --this.rebornTimes;
         this.hpUpEnemy(this.lifeMAX.getValue());
         var _loc1_:int = 100 - this.life.getValue() / this.lifeMAX.getValue() * 100;
         this.lifeMC.gotoAndStop(_loc1_);
         if(this.id == 3022)
         {
            this.skin.runOver = true;
            this.skin.GoTo("攻击3");
         }
      }
      
      public function hpUpEnemy(param1:int) : *
      {
         if(this.life.getValue <= 0)
         {
            return;
         }
         this.life.setValue(this.life.getValue() + param1);
         NewMC.Open("回血效果",this,0,0,0,param1);
      }
      
      public function hpCount(param1:int) : *
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(this.id == 7002)
         {
            return;
         }
         if(this.className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value -= param1;
            if(EnemyBossXS225.xishou_Value < 0)
            {
               EnemyBossXS225.xishou_Value = 0;
            }
            if(param1 < EnemyBossXS225.xishou_Value)
            {
               param1 = 0;
            }
         }
         if(this.id == 2015 || Main.gameNum.getValue() == 3000)
         {
            param1 = Math.ceil(Math.random() * 2);
         }
         param1 = this.GongHuiBoos_HpXX(param1);
         GongHuiTiaoZan.BossHpXX(this);
         if(param1 > 0 && this.life.getValue() > 0)
         {
            this.life.setValue(this.life.getValue() - param1);
            hpNUMXXX(param1);
            _loc2_ = 100 - this.life.getValue() / this.lifeMAX.getValue() * 100;
            this.lifeMC.gotoAndStop(_loc2_);
            if(this.life.getValue() <= 0)
            {
               if(this.rebornTimes > 0)
               {
                  this.reborn();
                  return;
               }
               this.life.setValue(0);
               _loc3_ = this.经验.getValue() * InitData.GetEXPxN().getValue();
               _loc3_ = int(GongHui_jiTan.expUP(_loc3_));
               Main.player_1.ExpUP(_loc3_);
               Main.player_1.MoneyUP(this.金钱.getValue());
               if(Main.P1P2)
               {
                  Main.player_2.ExpUP(_loc3_);
                  Main.player_2.MoneyUP(this.金钱.getValue());
               }
               this.任务品掉落();
               this.装备掉落();
               PaiHang_Data.EnemyOver();
               AchData.addEnemyNum(this.id);
               TaskData.setEnemyNum(this.id);
               TaskData.isEnemyTaskOk();
               if(Boolean(Main.tiaoShiYN) || Main.serverTime.getValue() >= 20161222 && Main.serverTime.getValue() <= 20170101)
               {
                  ShengDan_Interface.addRenWuWuPin(this);
               }
               if(Main.serverTime.getValue() <= NewYear_Interface.overTime)
               {
                  this.newYearDiaoLuo();
               }
               this.fiveOneDiaoLuo();
               CardPanel.monsterSlot.addMonsterCard(this.id);
               GongHuiRenWu.enemyOK(this.id);
            }
         }
         GongHuiTiaoZan.BossHpXX(this);
      }
      
      public function HpSXX(param1:Enemy, param2:Number) : *
      {
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         if(this.id == 7002)
         {
            return;
         }
         if(this.className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value -= hpDown;
            if(EnemyBossXS225.xishou_Value < 0)
            {
               EnemyBossXS225.xishou_Value = 0;
            }
            if(hpDown < EnemyBossXS225.xishou_Value)
            {
               hpDown = 0;
            }
         }
         if(this.id == 2015 || Main.gameNum.getValue() == 3000)
         {
            param2 = Math.ceil(Math.random() * 2);
         }
         if(param2 > 650000)
         {
            param2 = 650000;
         }
         if(param2 > 80000 && GameData.gameLV == 5)
         {
            param2 = 80000;
         }
         var _loc3_:int = param1.x + Math.random() * 100 - 50;
         var _loc4_:int = param1.y + Math.random() * 100 - 50;
         param2 = this.GongHuiBoos_HpXX(param2);
         GongHuiTiaoZan.BossHpXX(this);
         NewMC.Open("_特殊数字",Main.world.moveChild_Other,_loc3_,_loc4_ - 50,20,param2,true);
         if(param2 > 0 && param1.life.getValue() > 0)
         {
            param1.life.setValue(param1.life.getValue() - param2);
            hpNUMXXX(param2);
            _loc5_ = 100 - param1.life.getValue() / param1.lifeMAX.getValue() * 100;
            param1.lifeMC.gotoAndStop(_loc5_);
            if(param1.life.getValue() <= 0)
            {
               param1.life.setValue(0);
               NewMC.Open("掉钱",Main.world.moveChild_Other,_loc3_,_loc4_,15,this.金钱.getValue(),true);
               _loc6_ = this.经验.getValue() * InitData.GetEXPxN().getValue();
               _loc6_ = int(GongHui_jiTan.expUP(_loc6_));
               Main.player_1.ExpUP(_loc6_);
               Main.player_1.MoneyUP(param1.金钱.getValue());
               if(Main.P1P2)
               {
                  Main.player_2.ExpUP(_loc6_);
                  Main.player_2.MoneyUP(param1.金钱.getValue());
               }
               param1.任务品掉落();
               param1.装备掉落();
               AchData.addEnemyNum(param1.id);
               TaskData.setEnemyNum(param1.id);
               TaskData.isEnemyTaskOk();
               GongHuiRenWu.enemyOK(this.id);
            }
         }
         GongHuiTiaoZan.BossHpXX(this);
      }
      
      public function shengdandiaoluo(param1:int) : *
      {
         var _loc2_:int = Math.random() * 100;
         var _loc3_:int = 0;
         while(_loc3_ < InitData.shengdanArr.length)
         {
            if(InitData.shengdanArr[_loc3_][0] == param1)
            {
               if(InitData.shengdanArr[_loc3_][1] > _loc2_)
               {
                  Main.addShengDan();
               }
            }
            _loc3_++;
         }
      }
      
      public function fiveOneDiaoLuo() : *
      {
         if(Main.serverTime.getValue() > FiveOne_Interface.timeXXX)
         {
            return;
         }
         if(Boolean(GameData.BossIS) && this == GameData.BossIS)
         {
            if(this.id == 29 || this.id == 38 || this.id == 102 || this.id == 104 || this.id == 105 || this.id == 107 || this.id == 2015 || this.id == 108)
            {
               return;
            }
            if(Math.random() * 1000 < 100)
            {
               ++FiveOne_Interface.arrAll[2];
               NewMC.Open("文字提示",Main._stage,480,400,45,0,true,2,"获得生命之核！");
            }
            if(Math.random() * 1000 < 150)
            {
               ++FiveOne_Interface.arrAll[3];
               NewMC.Open("文字提示",Main._stage,480,450,45,0,true,2,"获得生命之光！");
            }
            return;
         }
         if(Math.random() * 1000 < 40)
         {
            ++FiveOne_Interface.arrAll[4];
            NewMC.Open("文字提示",Main._stage,480,400,45,0,true,2,"获得生命之水！");
            return;
         }
      }
      
      public function newYearDiaoLuo() : *
      {
         if(Boolean(GameData.BossIS) && this == GameData.BossIS)
         {
            if(this.id == 2015)
            {
               if(Math.random() * 100 < 10)
               {
                  NewYear_Interface.addKey();
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得宝箱钥匙！");
                  return;
               }
            }
         }
      }
      
      public function 装备掉落() : *
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:* = undefined;
         var _loc9_:* = undefined;
         var _loc10_:Object = null;
         var _loc11_:obj = null;
         var _loc12_:* = undefined;
         var _loc13_:* = undefined;
         var _loc14_:Object = null;
         var _loc15_:int = 0;
         var _loc16_:int = 0;
         var _loc17_:int = 0;
         var _loc18_:* = undefined;
         var _loc19_:* = undefined;
         var _loc20_:Array = null;
         var _loc21_:Equip = null;
         var _loc22_:Gem = null;
         var _loc23_:PetEquip = null;
         var _loc24_:String = null;
         var _loc25_:Otherobj = null;
         if(this.id == 4010)
         {
            _loc4_ = Math.random() * 6 + 5;
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               _loc8_ = this.x + Math.random() * 300 - 150;
               _loc9_ = this.y - Math.random() * 100 - 100;
               _loc10_ = new Object();
               _loc11_ = new obj(_loc10_,_loc8_,_loc9_,360);
               _loc5_++;
            }
            _loc6_ = Math.random() * 5 + 1;
            _loc7_ = 0;
            while(_loc7_ < _loc6_)
            {
               _loc12_ = this.x + Math.random() * 300 - 150;
               _loc13_ = this.y - Math.random() * 100 - 100;
               _loc14_ = new Object();
               _loc11_ = new obj(_loc14_,_loc12_,_loc13_,361);
               _loc7_++;
            }
         }
         if(Main.serverTime.getValue() <= ChunJiePanel.overTime && ChunJiePanel.saveArr2_2022[2] < 35)
         {
            for(_loc3_ in this.cj_arr)
            {
               if(this.cj_arr[_loc3_][0] == this.id)
               {
                  if(Math.random() * 10000 < this.cj_arr[_loc3_][1])
                  {
                     ChunJiePanel.addShaGuaiJiFen();
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜获得 魔法马刷");
                  }
               }
            }
         }
         var _loc1_:uint = this.掉落数量.getValue() * InitData.GetDOWNxN().getValue();
         var _loc2_:int = 0;
         while(_loc2_ < Main.player1.getTitleSlot().getListLength())
         {
            if(Main.player1.getTitleSlot().getTitleFromSlot(_loc2_).getId() == 25)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         if(this.className == "悬赏7" && Main.wts.getBoss() >= 222)
         {
            if(getEnemyNum("阿卡利") > 0 || getEnemyNum("阿卡利召唤") > 0)
            {
               return;
            }
         }
         if((this.className == "阿卡利" || this.className == "阿卡利召唤") && Main.wts.getBoss() >= 222)
         {
            if(getEnemyNum("悬赏7") > 0)
            {
               return;
            }
         }
         _loc3_ = 0;
         while(_loc3_ < _loc1_)
         {
            _loc15_ = this.掉落物品计算();
            _loc16_ = int(String(_loc15_).substr(1,1));
            _loc17_ = int(String(_loc15_).substr(0,1));
            _loc18_ = this.x + Math.random() * 200 - 100;
            _loc19_ = this.y - 100;
            if(this.id == "5015")
            {
               _loc18_ -= 300;
            }
            if(this.id == 8001)
            {
               _loc19_ = 450 - Math.random() * 30 + 15;
               _loc18_ = 580 + Math.random() * 80 - 40;
            }
            _loc18_ = this.ObjWhere(_loc18_);
            if(_loc15_ != 0)
            {
               if(_loc16_ == 1)
               {
                  _loc20_ = [_loc15_];
                  _loc21_ = EquipFactory.createEquipByColorAndLevel(_loc20_);
                  _loc11_ = new obj(_loc21_,_loc18_,_loc19_);
               }
               else if(_loc16_ == 2)
               {
                  _loc20_ = [_loc15_];
                  _loc22_ = GemFactory.createGemByColorAndLevel(_loc20_);
                  _loc11_ = new obj(_loc22_,_loc18_,_loc19_);
               }
               else if(_loc16_ == 7)
               {
                  _loc23_ = PetEquip.creatPetEquip(_loc15_);
                  NewPetPanel.bag.addPetBag(_loc23_);
                  _loc24_ = "获得:" + PetEquip.creatPetEquip(_loc15_).getName();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,_loc24_);
               }
               if(_loc17_ == 6)
               {
                  _loc25_ = OtherFactory.creatOther(_loc15_);
                  _loc11_ = new obj(_loc25_,_loc18_,_loc19_);
               }
            }
            _loc3_++;
         }
      }
      
      public function ObjWhere(param1:int) : int
      {
         var _loc2_:int = Math.random() * 100;
         if(param1 < 0)
         {
            return param1 + _loc2_;
         }
         if(param1 > Main.world._width)
         {
            return int(Main.world._width - _loc2_);
         }
         return param1;
      }
      
      public function 掉落物品计算() : int
      {
         var _loc1_:int = Math.random() * 10000 + 1;
         var _loc2_:int = 0;
         j = 0;
         while(j < 20)
         {
            _loc2_ += this.掉落概率[j];
            if(_loc2_ >= _loc1_)
            {
               return this.掉落物品[j];
            }
            ++j;
         }
         return 0;
      }
      
      public function onADDED_TO_STAGE(param1:*) : *
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         Main.world.moveChild_Enemy.addChild(this);
         this.cengjitiaozheng();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public function cengjitiaozheng() : *
      {
         if(this.id == 2018)
         {
            Main.world.moveChild_Other.addChild(this);
         }
      }
      
      public function onREMOVED_FROM_STAGE(param1:*) : *
      {
         if(this.skin)
         {
            this.skin.Over();
         }
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function Dead() : *
      {
         if(this.parent)
         {
            EnemyBossXS242.BeiDong_2(this.x,this.y);
            this.parent.removeChild(this);
            if(GameData.gameLV == 5 && Play_Interface.bossIS && Play_Interface.bossIS == this)
            {
               Panel_XianHua.xianHuaUp();
            }
            if(this.id == 3020 || this.id == 4003 || this.id == 2010)
            {
               if(Main.player_1)
               {
                  Main.player_1.speedTemp.setValue(0);
                  Main.player_1.debuff = 0;
               }
               if(Boolean(Main.P1P2) && Boolean(Main.player_2))
               {
                  Main.player_2.speedTemp.setValue(0);
                  Main.player_2.debuff = 0;
               }
            }
            if(this.id == 4008 || this.id == 1056)
            {
               while(Main.world.moveChild_Enemy.numChildren > 0)
               {
                  Main.world.moveChild_Enemy.removeChildAt(0);
               }
            }
            if(this.id == 7001)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(1);
               }
            }
            if(this.id == 7003)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(2);
               }
            }
            if(this.id == 7006)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(3);
               }
            }
            if(this.id == 7007)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(4);
               }
            }
            this.life.setValue(0);
         }
         if(FanPaiPanel.saveArr2[2] < 500)
         {
            if(this.id == 100 || this.id == 10 || this.id == 103 || this.id == 37 || this.id == 51 || this.id == 52 || this.id == 36 || this.id == 219 || this.id == 220 || this.id == 221 || this.id == 5005 || this.id == 5006)
            {
               return;
            }
            ++FanPaiPanel.saveArr2[2];
            ++FanPaiPanel.saveArr2[3];
         }
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function onENTER_FRAME(param1:*) : *
      {
         ++wscd;
         if(this.lastTime > -1)
         {
            --this.lastTime;
            if(this.lastTime == 0)
            {
               this.life.setValue(0);
               removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            }
         }
         if(this.buffTime_jl2016 > 0)
         {
            --this.buffTime_jl2016;
         }
         if(this.life.getValue() > 0)
         {
            if(this.isIce == true && this.life.getValue() > 0)
            {
               this.iceHPXX();
            }
            this.MoveData();
            if(!this.noMove)
            {
               this.MoveRun();
            }
         }
         else
         {
            if(this.skin)
            {
               this.skin.GoTo("死亡");
            }
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
      
      public function iceHPXX() : *
      {
         var _loc1_:* = undefined;
         var _loc2_:int = 0;
         if(Main.player1.getEquipSlot().getEquipFromSlot(7) && Main.player1.getEquipSlot().getEquipFromSlot(7).getFrame() == 385 && Main.player1.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0 || Main.P1P2 && (Main.player2.getEquipSlot().getEquipFromSlot(7) && Main.player2.getEquipSlot().getEquipFromSlot(7).getFrame() == 385 && Main.player2.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0))
         {
            ++this.iceTime;
            if(this.iceTime == 27)
            {
               _loc1_ = int(Main.player_1.use_gongji.getValue() * Math.pow(Main.player1.getLevel(),0.2));
               _loc2_ = this.life.getValue() - _loc1_;
               if(GameData.gameLV == 5)
               {
                  if(_loc1_ > 16200)
                  {
                     _loc1_ = 16200;
                  }
               }
               this.hpCount(_loc1_);
               HPdown.Open(_loc1_,this.x,this.y - this.height);
               this.iceTime = 0;
            }
         }
      }
      
      internal function test() : *
      {
         if(Main.player_1.skin.runType == "技能1" || Main.player_1.skin.runType == "技能2" || Main.player_1.skin.runType == "技能3" || Main.player_1.skin.runType == "技能4" || Main.player_1.skin.runType == "转职技能1" || Main.player_1.skin.runType == "转职技能2" || Main.player_1.skin.runType == "转职技能3" || Main.player_1.skin.runType == "转职技能4")
         {
            this.temp = Main.player_1.skin.runType;
            if(this.temp != this.tempXX && this.tempTimeXX > 5)
            {
               this.tempXX = this.temp;
               this.tempTimeXX = 0;
               this.skin.GoTo("攻击5");
            }
            return;
         }
      }
      
      public function MoveData() : *
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:String = null;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         this.WhereAreYou();
         this.Attack_TimeXX();
         var _loc1_:* = Math.abs(this.distance_X);
         var _loc2_:* = Math.abs(this.distance_Y);
         if(this.skin.runType == "站立" || this.skin.runType == "移动" || this.skin.runType == "跳")
         {
            _loc4_ = Math.random() * this.attack_XY.length;
            _loc5_ = "攻击" + (_loc4_ + 1);
            if(this.skin.continuousTime <= 0 && this.gjcd_Time <= 0)
            {
               this.jump_power = this.jumptemp;
               if(this.attack_Time[_loc4_] == this.attack_TimeNum[_loc4_] && _loc1_ < this.attack_XY[_loc4_][0] && _loc2_ < this.attack_XY[_loc4_][1])
               {
                  if(this.distance_X > 0)
                  {
                     this.getRL(false);
                  }
                  else
                  {
                     this.getRL(true);
                  }
                  this.attack_TimeNum[_loc4_] = 0;
                  if(this.className == "金牛座")
                  {
                     if(_loc4_ == 4 && bool_5)
                     {
                        this.skin.GoTo(_loc5_);
                        bool_6 = false;
                        bool_5 = false;
                     }
                     else if(_loc4_ == 5 && bool_6)
                     {
                        this.skin.GoTo(_loc5_);
                        bool_5 = false;
                        bool_6 = false;
                     }
                     else if(_loc4_ < 4)
                     {
                        this.skin.GoTo(_loc5_);
                     }
                  }
                  else if(this.className == "BOSS_54")
                  {
                     if(_loc4_ == 3)
                     {
                        return;
                     }
                     if(boss54 < 5 && _loc4_ == 5)
                     {
                        return;
                     }
                     if(boss54 == 5)
                     {
                        this.skin.GoTo("攻击6");
                        ++boss54;
                     }
                     else if(boss54 > 5)
                     {
                        if(Math.random() * 10 > 5)
                        {
                           this.skin.GoTo("攻击3");
                        }
                        else
                        {
                           this.skin.GoTo("攻击5");
                        }
                     }
                     else
                     {
                        this.skin.GoTo(_loc5_);
                     }
                  }
                  else if(this.className == "天秤座")
                  {
                     if(_loc4_ == 0 || _loc4_ == 1)
                     {
                        _loc3_ = 0;
                        while(_loc3_ < Fly.All.length)
                        {
                           if((Fly.All[_loc3_] as Fly)._name == "火焰之路" || (Fly.All[_loc3_] as Fly)._name == "寒冰之路")
                           {
                              return;
                           }
                           _loc3_++;
                        }
                     }
                     this.skin.GoTo(_loc5_);
                  }
                  else if(this.className == "摩羯座")
                  {
                     if(_loc4_ == 1)
                     {
                        if(All.length > 1)
                        {
                           return;
                        }
                     }
                     if(EnemyBoss29.BOSSENG > 0)
                     {
                        --EnemyBoss29.BOSSENG;
                     }
                     this.skin.GoTo(_loc5_);
                  }
                  else if(this.className == "双子座")
                  {
                     if(_loc4_ == 1 || _loc4_ == 2)
                     {
                        _loc7_ = 0;
                        _loc3_ = 0;
                        while(_loc3_ < Fly.All.length)
                        {
                           if((Fly.All[_loc3_] as Fly)._name == "恶魔球")
                           {
                              _loc7_++;
                           }
                           _loc3_++;
                        }
                        if(_loc7_ < 2)
                        {
                           this.skin.GoTo("攻击1");
                        }
                     }
                     this.skin.GoTo(_loc5_);
                  }
                  else if(this.id == 3022)
                  {
                     if(_loc4_ == 2)
                     {
                        return;
                     }
                     this.skin.GoTo(_loc5_);
                  }
                  else if(this.className == "悬赏7")
                  {
                     if(_loc4_ == 0)
                     {
                        for(_loc3_ in Enemy.All)
                        {
                           if((Enemy.All[_loc3_] as Enemy).className == "阿卡利" || (Enemy.All[_loc3_] as Enemy).className == "阿卡利召唤")
                           {
                              return;
                           }
                        }
                     }
                     if(_loc4_ == 2)
                     {
                        for(_loc3_ in Enemy.All)
                        {
                           if((Enemy.All[_loc3_] as Enemy).className == "罗生门")
                           {
                              return;
                           }
                        }
                     }
                     this.skin.GoTo(_loc5_);
                  }
                  else
                  {
                     if(this.className == "BOSS_53")
                     {
                        ++this.tempTimeXX;
                        this.test();
                        if(_loc4_ == 4)
                        {
                           return;
                        }
                     }
                     if(this.className == "悬赏5")
                     {
                        if(_loc4_ == 3 && xuanshang5 == false || _loc4_ == 4 && xuanshang5 == false)
                        {
                           return;
                        }
                        if(_loc4_ == 3 && xuanshang5 == true)
                        {
                           xuanshang5 = false;
                        }
                        if(_loc4_ == 4 && xuanshang5 == true)
                        {
                           xuanshang5 = false;
                        }
                     }
                     this.skin.GoTo(_loc5_);
                  }
                  this.浮动硬直 = 0;
               }
               else
               {
                  if(this.className == "火鸟王" || this.id == 5005 || this.id == 5006 || this.id == 5014 || this.id == 95)
                  {
                     this.skin.GoTo("移动");
                     return;
                  }
                  if(_loc1_ < 50 && _loc2_ < 500)
                  {
                     this.skin.GoTo("站立");
                  }
                  else if(this.distance_X > this.guard_XY[0])
                  {
                     this.runPower(this.walk_power,0,1);
                     this.getRL(false);
                     this.skin.GoTo("移动");
                  }
                  else if(this.distance_X < -this.guard_XY[0])
                  {
                     this.runPower(-this.walk_power,0,1);
                     this.getRL(true);
                     this.skin.GoTo("移动");
                  }
                  else
                  {
                     this.skin.GoTo("站立");
                  }
               }
            }
            _loc6_ = Math.random() * 60;
            if(_loc6_ == 0 && !this.jumping)
            {
               if(this.RL)
               {
                  this.runPower(-this.walk_power * 0.8,this.jump_power,this.jump_time);
               }
               else
               {
                  this.runPower(this.walk_power * 0.8,this.jump_power,this.jump_time);
               }
            }
         }
         this.runX = this.runY = 0;
         _loc3_ = int(this.runArr.length - 1);
         while(_loc3_ >= 0)
         {
            if(this.runArr[_loc3_][2] > 0)
            {
               this.runX += this.runArr[_loc3_][0] + this.runArr[_loc3_][3] * this.runArr[_loc3_][2];
               this.runY -= this.runArr[_loc3_][1] + this.runArr[_loc3_][4] * this.runArr[_loc3_][2];
               --this.runArr[_loc3_][2];
            }
            else
            {
               this.runArr.splice(_loc3_,1);
            }
            _loc3_--;
         }
         if(this.runY < 0)
         {
            this.jumpType = 1;
            this.gravityNum = 0;
            this.jumping = true;
         }
         else
         {
            this.jumpType = 2;
            if(this.gravity != 0)
            {
               this.gravityNum += 1;
            }
            this.runY += this.gravity * this.gravityNum;
         }
      }
      
      public function WhereAreYou() : *
      {
         var _loc1_:int = 0;
         var _loc2_:* = undefined;
         if(Main.gameNum.getValue() == 3000 && Boolean(NpcYY._this))
         {
            this.distance_X = this.x - NpcYY._this.x;
            this.distance_Y = this.y - NpcYY._this.y;
         }
         else if(Player.All.length > 0)
         {
            this.distance_X = this.x - Player.All[0].x;
            this.distance_Y = this.y - Player.All[0].y;
            _loc1_ = 1;
            while(_loc1_ < Player.All.length)
            {
               _loc2_ = this.x - Player.All[_loc1_].x;
               if(Math.abs(_loc2_) < Math.abs(this.distance_X))
               {
                  this.distance_X = _loc2_;
                  this.distance_Y = this.y - Player.All[_loc1_].y;
               }
               _loc1_++;
            }
         }
         else
         {
            this.distance_X = 0;
         }
      }
      
      public function MoveRun() : *
      {
         var _loc1_:Boolean = false;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc9_:Boolean = false;
         var _loc10_:Number = 0;
         var _loc11_:* = undefined;
         var _loc12_:Boolean = false;
         var _loc13_:Boolean = false;
         if(this.noHitMap)
         {
            return;
         }
         if(this.runX > 0)
         {
            _loc1_ = true;
         }
         var _loc2_:int = this.runX;
         var _loc3_:int = this.runY;
         var _loc4_:int = this.x + Main.world.x;
         var _loc5_:int = this.y;
         _loc6_ = Math.abs(_loc3_);
         while(_loc6_ > 0)
         {
            if(this.jumpType == 1)
            {
               _loc9_ = Boolean(Main.world.MapData1.hitTestPoint(_loc4_,_loc5_ - this.height,true));
               if(!_loc9_)
               {
                  _loc10_ = 0;
                  while(_loc10_ < Main.world.numChildren)
                  {
                     _loc11_ = Main.world.getChildAt(_loc10_);
                     if(_loc11_ is Map && Boolean(_loc11_.MapData1.hitTestPoint(_loc4_,_loc5_ - this.height,true)))
                     {
                        _loc9_ = true;
                        break;
                     }
                     _loc10_++;
                  }
               }
               if(!_loc9_)
               {
                  _loc5_--;
               }
            }
            else if(this.jumpType == 2)
            {
               _loc12_ = Boolean(Main.world.MapData.hitTestPoint(_loc4_,_loc5_ - 3,true));
               _loc9_ = Boolean(Main.world.MapData.hitTestPoint(_loc4_,_loc5_ - 1,true));
               _loc13_ = Boolean(Main.world.MapData.hitTestPoint(_loc4_,_loc5_ + 6,true));
               _loc10_ = 0;
               while(_loc10_ < Main.world.numChildren)
               {
                  _loc11_ = Main.world.getChildAt(_loc10_);
                  if(_loc11_ is Map && Boolean(_loc11_.MapData.hitTestPoint(_loc4_,_loc5_ - 3,true)))
                  {
                     _loc12_ = true;
                  }
                  else if(_loc11_ is Map && Boolean(_loc11_.MapData.hitTestPoint(_loc4_,_loc5_ - 1,true)))
                  {
                     _loc9_ = true;
                  }
                  else if(_loc11_ is Map && Boolean(_loc11_.MapData.hitTestPoint(_loc4_,_loc5_ + 6,true)))
                  {
                     _loc13_ = true;
                  }
                  _loc10_++;
               }
               this.jumping = false;
               if(_loc12_)
               {
                  _loc5_ -= 2;
               }
               else if(_loc9_)
               {
                  this.runY = 0;
                  this.gravityNum = 0;
               }
               else if(_loc13_)
               {
                  _loc5_ += 3;
               }
               else
               {
                  _loc5_++;
                  this.jumping = true;
               }
            }
            else if(this.jumpType == 3)
            {
               _loc9_ = Boolean(Main.world.MapData1.hitTestPoint(_loc4_,_loc5_ - 1,true));
               _loc10_ = 0;
               while(_loc10_ < Main.world.numChildren)
               {
                  _loc11_ = Main.world.getChildAt(_loc10_);
                  if(_loc11_ is Map && Boolean(_loc11_.MapData1.hitTestPoint(_loc4_,_loc5_ - this.height,true)))
                  {
                     _loc9_ = true;
                  }
                  _loc10_++;
               }
               if(!_loc9_)
               {
                  this.jumpType = 2;
                  break;
               }
               _loc5_++;
            }
            _loc6_--;
         }
         this.y = _loc5_;
         if(_loc2_ > 0)
         {
            _loc7_ = _loc4_ - 20;
         }
         else
         {
            _loc7_ = _loc4_ + 20;
         }
         _loc6_ = Math.abs(_loc2_);
         while(_loc6_ > 0)
         {
            _loc9_ = Boolean(Main.world.MapData1.hitTestPoint(_loc7_,_loc5_ - 50,true));
            if(!_loc9_)
            {
               _loc10_ = 0;
               while(_loc10_ < Main.world.numChildren)
               {
                  _loc11_ = Main.world.getChildAt(_loc10_);
                  if(_loc11_ is Map && Boolean(_loc11_.MapData.hitTestPoint(_loc7_,_loc5_ - 50,true)))
                  {
                     _loc9_ = true;
                     break;
                  }
                  _loc10_++;
               }
            }
            if(!_loc9_)
            {
               if(_loc2_ > 0)
               {
                  _loc4_--;
               }
               else
               {
                  _loc4_++;
               }
            }
            _loc6_--;
         }
         var _loc8_:int = _loc4_ - Main.world.x;
         if(_loc8_ > Main.world._width + 200)
         {
            this.x = Main.world._width + 200;
         }
         else if(_loc8_ < -200)
         {
            this.x = -200;
         }
         else
         {
            this.x = _loc8_;
         }
      }
      
      public function runPower(param1:Number = 0, param2:Number = 0, param3:int = 0) : *
      {
         if(param3 <= 3)
         {
            this.runArr[this.runArr.length] = [param1,param2,1,0,0,0];
            return;
         }
         var _loc4_:* = param1 / param3 * this.parabola;
         var _loc5_:* = param2 / param3 * this.parabola;
         var _loc6_:Number = param1 * (1 - this.parabola) / (param3 * param3 - param3 * (param3 - 1) / 2);
         var _loc7_:Number = param2 * (1 - this.parabola) / (param3 * param3 - param3 * (param3 - 1) / 2);
         this.runArr[this.runArr.length] = [_loc4_,_loc5_,param3,_loc6_,_loc7_];
      }
      
      public function getRL(param1:Boolean) : *
      {
         this.RL = param1;
         if(this.className == "左墙" || this.className == "监牢" || this.className == "金牛座石像" || this.className == "金牛座石像2" || this.id == 303 || this.id == 2014 || this.id == 88 || this.id == 5001 || this.id == 5002 || this.id == 5003 || this.id == 5015 || this.id == 88 || this.id == 5016 || this.id == 5017 || this.id == 5018 || this.id == 5019 || this.id == 5020 || this.id == 5021 || this.id == 5022)
         {
            return;
         }
         if(param1)
         {
            this.skin.scaleX = -1;
         }
         else if(!param1)
         {
            this.skin.scaleX = 1;
         }
         if(this.id == 9001 || this.id == 9002 || this.id == 9003 || this.id == 9004)
         {
            if(this.skin.xx_mc)
            {
               this.skin.xx_mc.scaleX = this.skin.scaleX;
            }
         }
      }
      
      public function set_attack_Time() : *
      {
         var _loc2_:* = undefined;
         var _loc3_:int = 0;
         var _loc4_:String = null;
         var _loc5_:int = 0;
         this.EnemySkinXml = EnemySkin.EnemySkinXmlArr[GameData.gameLV][this.关卡];
         if(!this.EnemySkinXml)
         {
            TiaoShi.txtShow("!!!找不到怪物攻击数据:" + this.关卡);
            return;
         }
         var _loc1_:int = 0;
         while(_loc1_ < this.attack_XY.length)
         {
            for(_loc2_ in this.EnemySkinXml.怪物攻击)
            {
               _loc3_ = int(this.EnemySkinXml.怪物攻击[_loc2_].ID);
               _loc4_ = String(this.EnemySkinXml.怪物攻击[_loc2_].帧标签);
               if(this.id == _loc3_ && _loc4_ == "攻击" + (_loc1_ + 1))
               {
                  _loc5_ = int(this.EnemySkinXml.怪物攻击[_loc2_].间隔);
                  this.attack_Time[_loc1_] = _loc5_;
                  this.attack_TimeNum[_loc1_] = int(_loc5_ * Math.random());
                  break;
               }
            }
            _loc1_++;
         }
      }
      
      public function Attack_TimeXX() : *
      {
         var _loc1_:int = 0;
         while(_loc1_ < this.attack_TimeNum.length)
         {
            if(this.attack_TimeNum[_loc1_] < this.attack_Time[_loc1_])
            {
               ++this.attack_TimeNum[_loc1_];
            }
            _loc1_++;
         }
      }
      
      public function 任务品掉落() : *
      {
         var _loc2_:Quest = null;
         var _loc3_:* = undefined;
         var _loc4_:* = undefined;
         var _loc5_:obj = null;
         var _loc6_:* = 0;
         var _loc1_:int = this.任务概率掉落计算();
         if(_loc1_ != 0)
         {
            if(_loc1_ >= 84117 && _loc1_ <= 84120)
            {
               _loc6_ = _loc1_ - 84117;
               if(Boolean(Main.player1) && (Main.player1.getBag().fallQusetBag(_loc1_) > 0 || Main.LuoPanArr[_loc6_] == 1))
               {
                  return;
               }
               if(Main.P1P2 && Main.player2 && (Main.player2.getBag().fallQusetBag(_loc1_) > 0 || Main.LuoPanArr[_loc6_] == 1))
               {
                  return;
               }
            }
            _loc2_ = QuestFactory.creatQust(_loc1_);
            _loc3_ = this.x;
            _loc4_ = this.y;
            if(this.id == 8001)
            {
               _loc4_ = 450 - Math.random() * 30 + 15;
               _loc3_ = 580 + Math.random() * 80 - 40;
            }
            _loc5_ = new obj(_loc2_,_loc3_,_loc4_);
         }
      }
      
      public function 任务概率掉落计算() : int
      {
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc1_:int = 0;
         if(!this.mainQuest())
         {
            if(!this.getbagQuestNum())
            {
               _loc1_ = Math.random() * 10000 + 1;
            }
            else
            {
               _loc1_ = 99999;
            }
         }
         else
         {
            _loc1_ = 99999;
         }
         j = 0;
         while(j < 3)
         {
            _loc2_ = Number(this.特殊掉落物品[j]);
            if(_loc2_ != 0)
            {
               _loc3_ = Number(QuestFactory.getType(_loc2_));
               if(_loc3_ == 0)
               {
                  _loc4_ = Number(QuestFactory.getTaskId(_loc2_));
                  if(TaskData.isTaskById(_loc4_) != null)
                  {
                     if(!TaskData.getAddBag(_loc4_))
                     {
                        if(_loc1_ <= this.特殊掉落概率[j])
                        {
                           return _loc2_;
                        }
                     }
                  }
               }
               else
               {
                  _loc4_ = Number(QuestFactory.getTaskId(_loc2_));
                  if(_loc1_ <= this.特殊掉落概率[j])
                  {
                     return _loc2_;
                  }
               }
            }
            ++j;
         }
         return 0;
      }
      
      public function getbagQuestNum() : Boolean
      {
         var _loc1_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc4_:Number = 0;
         while(_loc4_ < 3)
         {
            if(this.特殊掉落物品[_loc4_] != 0)
            {
               _loc1_ = Number(this.特殊掉落物品[_loc4_]);
               _loc3_ = Number(QuestFactory.getType(_loc1_));
               if(_loc3_ == 1)
               {
                  if(Main.player2 == null)
                  {
                     return this.p1bagFall(_loc1_);
                  }
                  if(this.p1bagFall(_loc1_) || this.p2bagFall(_loc1_))
                  {
                     return true;
                  }
               }
               else if(_loc3_ == 2)
               {
                  if(Main.player2 == null)
                  {
                     if(this.p1bagFall(_loc1_))
                     {
                        return this.p1bagFall(_loc1_);
                     }
                  }
                  else
                  {
                     if(this.p1bagFall(_loc1_) && this.p2bagFall(_loc1_))
                     {
                        return true;
                     }
                     if(Boolean(SetTransferPanel.tbo1()) && this.p2bagFall(_loc1_))
                     {
                        return true;
                     }
                     if(Boolean(SetTransferPanel.tbo2()) && this.p1bagFall(_loc1_))
                     {
                        return true;
                     }
                  }
               }
               else if(_loc3_ == 0)
               {
                  return false;
               }
            }
            _loc4_++;
         }
         return false;
      }
      
      public function p1bagFall(param1:Number) : Boolean
      {
         var _loc2_:Number = Number(Main.player1.getBag().fallQusetBag(param1));
         var _loc3_:Number = Number(QuestFactory.getFallMax(param1));
         if(_loc2_ < _loc3_)
         {
            return false;
         }
         return true;
      }
      
      public function p2bagFall(param1:Number) : Boolean
      {
         var _loc2_:Number = Number(Main.player2.getBag().fallQusetBag(param1));
         var _loc3_:Number = Number(QuestFactory.getFallMax(param1));
         if(_loc2_ < _loc3_)
         {
            return false;
         }
         return true;
      }
      
      public function mainQuest() : Boolean
      {
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc1_:Number = 0;
         while(_loc1_ < 3)
         {
            if(this.特殊掉落物品[_loc1_] != 0)
            {
               _loc4_ = Number(this.特殊掉落物品[_loc1_]);
               _loc5_ = Number(QuestFactory.getType(_loc4_));
               if(_loc5_ == 1)
               {
                  return Main.getQuest(_loc4_);
               }
               if(_loc5_ == 2)
               {
                  if(Main.player2 == null)
                  {
                     return SetTransferPanel.tbo1();
                  }
                  if(Boolean(SetTransferPanel.tbo1()) && Boolean(SetTransferPanel.tbo2()))
                  {
                     return true;
                  }
               }
               else if(_loc5_ == 0)
               {
                  return false;
               }
            }
            _loc1_++;
         }
         return false;
      }
   }
}

