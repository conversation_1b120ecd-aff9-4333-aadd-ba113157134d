package com.hotpoint.braveManIII.views.makePanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.make.Make;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   import src.tool.*;
   
   public class MakePanel extends MovieClip
   {
      public static var _instance:MakePanel;
      
      private static var loadData:ClassLoader;
      
      private static var open_yn:Boolean;
      
      private static var loadName:String = "Panel_Make_v1200.swf";
      
      private var dataArr:Array = [];
      
      private var state:Number = 0;
      
      private var ye:Number = 0;
      
      private var bookSlot:MakeBookSlot;
      
      private var finishSlot:MakeFinishSlot;
      
      private var tooltip:ItemsTooltip;
      
      private var playerState:Number = 1;
      
      private var madeOk:Boolean;
      
      private var nowId:Number = 0;
      
      private var hong:TextFormat;
      
      private var bai:TextFormat;
      
      private var lan:TextFormat;
      
      private var zi:TextFormat;
      
      private var ch:TextFormat;
      
      public var tb_0:MovieClip;
      
      public var tb_1:MovieClip;
      
      public var tb_2:MovieClip;
      
      public var tb_3:MovieClip;
      
      public var tb_4:MovieClip;
      
      public var tb_5:MovieClip;
      
      public var tb_6:MovieClip;
      
      public var mk_0:MovieClip;
      
      public var mk_1:MovieClip;
      
      public var mk_2:MovieClip;
      
      public var mk_3:MovieClip;
      
      public var bk_0:MovieClip;
      
      public var bk_1:MovieClip;
      
      public var bk_2:MovieClip;
      
      public var bk_3:MovieClip;
      
      public var n_0:MovieClip;
      
      public var n_1:MovieClip;
      
      public var n_2:MovieClip;
      
      public var n_3:MovieClip;
      
      public var n_4:MovieClip;
      
      public var n_5:MovieClip;
      
      public var z_0:MovieClip;
      
      public var z_1:MovieClip;
      
      public var z_2:MovieClip;
      
      public var z_3:MovieClip;
      
      public var z_4:MovieClip;
      
      public var name_0:*;
      
      public var name_1:*;
      
      public var name_2:*;
      
      public var name_3:*;
      
      public var name_4:*;
      
      public var name_5:*;
      
      public var nx_0:*;
      
      public var nx_1:*;
      
      public var nx_2:*;
      
      public var nx_3:*;
      
      public var nx_4:*;
      
      public var nb_0:*;
      
      public var nb_1:*;
      
      public var nb_2:*;
      
      public var nb_3:*;
      
      public var nb_4:*;
      
      public var num1:*;
      
      public var gold_text:*;
      
      public var player_gold:*;
      
      public var dianQun_Point:*;
      
      public var fb_0:*;
      
      public var fb_1:*;
      
      public var pb_0:*;
      
      public var pb_1:*;
      
      public var close_btn:*;
      
      public var NoMoney_mc:*;
      
      public var buy_mc:*;
      
      public var _BLACK_mc:*;
      
      public var all_makeBtn:*;
      
      public var buy_btn:*;
      
      private var buyType:Number;
      
      private var buyObjArr:Array = new Array();
      
      public function MakePanel()
      {
         super();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("com.hotpoint.braveManIII.views.makePanel.MakePanel") as Class;
         MakePanel._instance = new _loc2_();
         MakePanel._instance = new MakePanel();
         init2();
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
         TiaoShi.txtShow("MakePanel.onLoadingOK");
      }
      
      private static function init2() : *
      {
         _instance.bookSlot = MakeBookSlot.creatSlot();
         _instance.finishSlot = MakeFinishSlot.creatSlot();
         _instance.tooltip = new ItemsTooltip();
         _instance.hong = new TextFormat();
         _instance.hong.color = 16711680;
         _instance.bai = new TextFormat();
         _instance.bai.color = 4294967295;
         _instance.lan = new TextFormat();
         _instance.lan.color = 26367;
         _instance.zi = new TextFormat();
         _instance.zi.color = 16724991;
         _instance.ch = new TextFormat();
         _instance.ch.color = 16750848;
         InitIcon();
         _instance.initPanel();
         _instance.addChild(MakePanel._instance.tooltip);
         _instance.tooltip.visible = false;
      }
      
      private static function InitIcon() : *
      {
         var _loc2_:MovieClip = null;
         var _loc3_:int = 0;
         var _loc1_:Number = 0;
         while(_loc1_ < 6)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = int(_instance["n_" + _loc1_].getChildIndex(_instance["n_" + _loc1_].pic_xx));
            _loc2_.x = _instance["n_" + _loc1_].pic_xx.x;
            _loc2_.y = _instance["n_" + _loc1_].pic_xx.y;
            _loc2_.name = "pic_xx";
            _loc2_.gotoAndStop(1);
            _instance["n_" + _loc1_].removeChild(_instance["n_" + _loc1_].pic_xx);
            _instance["n_" + _loc1_].pic_xx = _loc2_;
            _instance["n_" + _loc1_].addChild(_loc2_);
            _instance["n_" + _loc1_].setChildIndex(_loc2_,_loc3_);
            _loc1_++;
         }
         _instance.needFrame();
      }
      
      public static function open() : void
      {
         if(MakePanel._instance == null)
         {
            Loading();
            open_yn = true;
            return;
         }
         Main.allClosePanel();
         MakePanel._instance.x = 0;
         MakePanel._instance.y = 0;
         MakePanel._instance._BLACK_mc.visible = false;
         MakePanel._instance.visible = true;
         Main._stage.addChild(MakePanel._instance);
         InitIcon();
         init2();
      }
      
      public static function close() : void
      {
         if(MakePanel._instance == null)
         {
            open_yn = false;
            return;
         }
         MakePanel._instance.x = 5000;
         MakePanel._instance.y = 5000;
         MakePanel._instance._BLACK_mc.visible = false;
         MakePanel._instance.visible = false;
      }
      
      public static function GetBuyObj() : *
      {
         if(_instance && _instance.buyObjArr && _instance.buyObjArr.length > 0)
         {
            _instance.whoFinishMade(_instance.buyType,_instance.buyObjArr);
            _instance.buyObjArr = new Array();
            _instance._BLACK_mc.visible = false;
            _instance.dianQun_Point.text = "" + Shop4399.moneyAll.getValue();
            _instance.BuyClose();
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"物品已放入背包");
         }
      }
      
      private function initPanel() : void
      {
         this.state = 0;
         this.ye = 0;
         this.p1orp2();
         this.initType(this.state,this.ye);
         this.addEvent();
         this.initMadeLan(0);
         this.btnStata(0,4,"mk_");
         this.btnStata(0,7,"tb_");
         this.needZz();
         this.playerGold();
      }
      
      private function p1orp2() : void
      {
         this.playerState = 1;
         if(Main.P1P2)
         {
            this.pb_0.visible = true;
            this.pb_1.visible = true;
            this.btnStata(0,2,"pb_");
         }
         else
         {
            this.pb_0.visible = false;
            this.pb_1.visible = false;
         }
      }
      
      private function playerGold() : void
      {
         if(this.playerState == 1)
         {
            this.player_gold.text = String(Main.player1.getGold());
         }
         else if(this.playerState == 2)
         {
            this.player_gold.text = String(Main.player2.getGold());
         }
         if(Shop4399.moneyAll.getValue() > -1)
         {
            this.dianQun_Point.text = Shop4399.moneyAll.getValue();
         }
      }
      
      private function initType(param1:Number = 0, param2:Number = 0) : void
      {
         this.dataArr = MakeData.getArr4(param1);
         this.bookSlot.clearMake();
         this.iniSlot(this.dataArr,param2);
         this.initBook();
         this.yeNum();
      }
      
      private function iniSlot(param1:Array, param2:Number = 0) : void
      {
         var _loc4_:Number = 0;
         var _loc3_:Array = param1[param2];
         if(_loc3_ != null)
         {
            _loc4_ = 0;
            while(_loc4_ < _loc3_.length)
            {
               this.bookSlot.addSlot(_loc3_[_loc4_]);
               _loc4_++;
            }
         }
      }
      
      private function initBook() : void
      {
         var _loc2_:Make = null;
         var _loc1_:Number = 0;
         while(_loc1_ < 4)
         {
            if(this.bookSlot.getMake(_loc1_) != null)
            {
               _loc2_ = this.bookSlot.getMake(_loc1_);
               this["bk_" + _loc1_].gotoAndStop(_loc2_.getFrame());
               if(_loc2_.isState())
               {
                  this["bk_" + _loc1_].m_mask.gotoAndStop(2);
               }
               else
               {
                  this["bk_" + _loc1_].m_mask.gotoAndStop(1);
               }
            }
            else
            {
               this["bk_" + _loc1_].gotoAndStop(1);
               this["bk_" + _loc1_].m_mask.gotoAndStop(2);
            }
            _loc1_++;
         }
      }
      
      private function yeNum() : void
      {
         var _loc1_:* = 0;
         if(this.dataArr.length < 1)
         {
            _loc1_ = 1;
         }
         else
         {
            _loc1_ = uint(this.dataArr.length);
         }
         this.num1.text = String(this.ye + 1) + "/" + _loc1_;
      }
      
      private function addEvent() : void
      {
         this.addEventListener(BtnEvent.DO_CHANGE,this.doChange);
         this.addEventListener(BtnEvent.DO_CLICK,this.doClick);
         this.addEventListener(BtnEvent.DO_CLOSE,this.doClose);
         this.addEventListener(BtnEvent.DO_OVER,this.doOver);
         this.addEventListener(BtnEvent.DO_OUT,this.doOut);
         this.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,this.daoJuOver);
         this.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,this.daoJuOut);
         this.buy_btn.addEventListener(MouseEvent.CLICK,this.DianQuanBuy);
      }
      
      private function doOver(param1:BtnEvent) : void
      {
         var _loc4_:Make = null;
         var _loc2_:String = param1.target.name.substr(0,1);
         var _loc3_:Number = Number(param1.target.name.substr(3,1));
         if(_loc2_ == "m")
         {
            if(this.bookSlot.getMake(_loc3_) != null)
            {
               _loc4_ = this.bookSlot.getMake(_loc3_);
               if(_loc4_.isState() == false)
               {
                  PropertyScreen.open(_loc4_,false,Main._stage.mouseX,Main._stage.mouseY,_loc4_.getSm());
               }
            }
         }
      }
      
      private function doOut(param1:BtnEvent) : void
      {
         PropertyScreen.close();
      }
      
      private function btnStata(param1:Number, param2:Number, param3:String) : void
      {
         this[param3 + param1].isClick = true;
         var _loc4_:Number = 0;
         while(_loc4_ < param2)
         {
            if(param1 != _loc4_)
            {
               this[param3 + _loc4_].isClick = false;
            }
            _loc4_++;
         }
      }
      
      private function doChange(param1:BtnEvent) : void
      {
         var _loc2_:String = param1.target.name.substr(0,1);
         var _loc3_:Number = Number(param1.target.name.substr(3,1));
         if(_loc2_ == "t")
         {
            this.state = _loc3_;
            this.ye = 0;
            this.initType(this.state,0);
            this.initMadeLan(0);
            this.btnStata(_loc3_,7,"tb_");
            this.btnStata(0,4,"mk_");
            this.needZz();
         }
         if(_loc2_ == "f")
         {
            if(_loc3_ == 0)
            {
               if(this.ye > 0)
               {
                  --this.ye;
               }
            }
            if(_loc3_ == 1)
            {
               if(this.dataArr.length > 0)
               {
                  if(this.ye < this.dataArr.length - 1)
                  {
                     ++this.ye;
                  }
               }
            }
            this.initType(this.state,this.ye);
            this.initMadeLan(0);
            this.btnStata(0,4,"mk_");
            this.needZz();
         }
         if(_loc2_ == "m")
         {
            this.initMadeLan(_loc3_);
            this.btnStata(_loc3_,4,"mk_");
            this.needZz(_loc3_);
         }
         if(_loc2_ == "p")
         {
            if(_loc3_ == 0)
            {
               this.playerState = 1;
            }
            if(_loc3_ == 1)
            {
               this.playerState = 2;
            }
            this.state = 0;
            this.ye = 0;
            this.initMadeLan(0);
            if(Main.P1P2)
            {
               this.btnStata(_loc3_,2,"pb_");
               this.playerGold();
            }
            this.btnStata(0,4,"mk_");
            this.needZz();
         }
      }
      
      private function doClick(param1:BtnEvent) : void
      {
         var _loc2_:Make = null;
         var _loc3_:Number = NaN;
         var _loc4_:Array = null;
         var _loc5_:Array = null;
         var _loc6_:Array = null;
         var _loc7_:Array = null;
         var _loc8_:Number = NaN;
         if(this.madeOk)
         {
            _loc2_ = this.bookSlot.getMake(this.nowId);
            _loc3_ = _loc2_.getType();
            _loc4_ = _loc2_.getNeedType();
            _loc5_ = _loc2_.getNeedId();
            _loc6_ = _loc2_.getNeedNum();
            _loc7_ = _loc2_.getObj();
            _loc8_ = _loc2_.getFinishNum();
            if(this.deGold(_loc2_.getGold()))
            {
               if(this.whoFinishMade(_loc3_,_loc7_))
               {
                  this.whoDelBag(_loc5_,_loc4_,_loc6_);
                  this.initMadeLan(this.nowId);
                  this.playerGold();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"物品已放入背包");
                  AchData.setMakeNum(this.playerState);
                  AchData.setMadeById(2,_loc7_[0].getId(),this.playerState,_loc7_.length);
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
            }
         }
      }
      
      private function deGold(param1:Number) : Boolean
      {
         if(this.playerState == 1)
         {
            if(Main.player1.getGold() >= param1)
            {
               Main.player1.payGold(param1);
               return true;
            }
         }
         else if(this.playerState == 2)
         {
            if(Main.player2.getGold() >= param1)
            {
               Main.player2.payGold(param1);
               return true;
            }
         }
         return false;
      }
      
      private function whoFinishMade(param1:Number, param2:Array) : Boolean
      {
         if(this.playerState == 1)
         {
            return this.finishMake(param1,param2,Main.player1.getBag());
         }
         if(this.playerState == 2)
         {
            return this.finishMake(param1,param2,Main.player2.getBag());
         }
         return undefined;
      }
      
      private function finishMake(param1:Number, param2:Array, param3:Bag) : Boolean
      {
         var _loc4_:Number = 0;
         var _loc5_:Gem = null;
         var _loc6_:Number = NaN;
         var _loc7_:Otherobj = null;
         if(param1 == 0 || param1 == 4 || param1 == 6)
         {
            if(param3.backequipBagNum() >= param2.length)
            {
               _loc4_ = 0;
               while(_loc4_ < param2.length)
               {
                  param3.addEquipBag(param2[_loc4_]);
                  _loc4_++;
               }
               return true;
            }
         }
         else if(param1 == 1)
         {
            if(param3.backSuppliesBagNum() >= param2.length)
            {
               _loc4_ = 0;
               while(_loc4_ < param2.length)
               {
                  param3.addSuppliesBag(param2[_loc4_]);
                  _loc4_++;
               }
               return true;
            }
         }
         else if(param1 == 2)
         {
            _loc5_ = param2[0];
            _loc6_ = _loc5_.getId();
            if(param3.canPutGemNum(_loc6_) >= param2.length)
            {
               _loc4_ = 0;
               while(_loc4_ < param2.length)
               {
                  param3.addGemBag(param2[_loc4_]);
                  JiHua_Interface.ppp4_18 = true;
                  _loc4_++;
               }
               return true;
            }
         }
         else if(param1 == 3)
         {
            _loc7_ = param2[0];
            _loc6_ = _loc7_.getId();
            if(param3.canPutOtherNum(_loc6_) >= param2.length)
            {
               _loc4_ = 0;
               while(_loc4_ < param2.length)
               {
                  param3.addOtherobjBag(param2[_loc4_]);
                  _loc4_++;
               }
               return true;
            }
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         return false;
      }
      
      private function whoDelBag(param1:Array, param2:Array, param3:Array) : void
      {
         if(this.playerState == 1)
         {
            this.delBag(param1,param2,param3,Main.player1.getBag());
         }
         else if(this.playerState == 2)
         {
            this.delBag(param1,param2,param3,Main.player2.getBag());
         }
      }
      
      private function delBag(param1:Array, param2:Array, param3:Array, param4:Bag) : void
      {
         var _loc5_:Number = 0;
         while(_loc5_ < param1.length)
         {
            if(param1[_loc5_] != -1)
            {
               if(param2[_loc5_] == 0 || param2[_loc5_] == 4)
               {
                  param4.delEquipById(param1[_loc5_],param3[_loc5_]);
               }
               else if(param2[_loc5_] == 1)
               {
                  param4.delSuppliesById(param1[_loc5_],param3[_loc5_]);
               }
               else if(param2[_loc5_] == 2)
               {
                  param4.delGemById(param1[_loc5_],param3[_loc5_]);
               }
               else if(param2[_loc5_] == 3)
               {
                  param4.delOtherById(param1[_loc5_],param3[_loc5_]);
               }
            }
            _loc5_++;
         }
      }
      
      private function daoJuOver(param1:DaoJuEvent) : void
      {
         var _loc3_:Make = null;
         var _loc4_:Array = null;
         var _loc5_:Array = null;
         var _loc6_:Object = null;
         var _loc7_:Object = null;
         var _loc2_:Number = Number(param1.target.name.substr(2,1));
         if(this.bookSlot.getMake(this.nowId) != null)
         {
            _loc3_ = this.bookSlot.getMake(this.nowId);
            _loc4_ = _loc3_.addNeedOb();
            _loc5_ = _loc3_.getObj();
            _loc6_ = _loc5_[0];
            if(_loc2_ <= 4)
            {
               this.tooltip.visible = true;
               this.tooltip.x = Main._stage.mouseX;
               this.tooltip.y = Main._stage.mouseY;
               if(_loc4_[_loc2_] != null)
               {
                  _loc7_ = _loc4_[_loc2_];
                  if(_loc7_ as Equip)
                  {
                     this.tooltip.equipTooltip(_loc7_);
                  }
                  else if(_loc7_ as Supplies)
                  {
                     this.tooltip.suppliesTooltip(_loc7_);
                  }
                  else if(_loc7_ as Gem)
                  {
                     this.tooltip.gemTooltip(_loc7_);
                  }
                  else if(_loc7_ as Otherobj)
                  {
                     this.tooltip.otherTooltip(_loc7_);
                  }
               }
            }
            else if(_loc6_ as Equip)
            {
               PropertyScreen.open(_loc6_,false,Main._stage.mouseX,Main._stage.mouseY,"获得后才可以查看属性(属性随机)");
            }
            else
            {
               this.tooltip.visible = true;
               this.tooltip.x = mouseX;
               this.tooltip.y = mouseY;
               if(_loc6_ as Supplies)
               {
                  this.tooltip.suppliesTooltip(_loc6_);
               }
               else if(_loc6_ as Gem)
               {
                  this.tooltip.gemTooltip(_loc6_);
               }
               else if(_loc6_ as Otherobj)
               {
                  this.tooltip.otherTooltip(_loc6_);
               }
            }
         }
      }
      
      private function pointY(param1:MovieClip) : void
      {
         var _loc2_:Point = new Point(param1.x,param1.y);
         _loc2_ = this.localToGlobal(_loc2_);
         if(_loc2_.y + param1.height > 580)
         {
            param1.y = 580 - param1.height - 80;
         }
         if(_loc2_.y < 0)
         {
            param1.y = 0;
         }
         if(_loc2_.x + param1.width > 940)
         {
            param1.x = 940 - param1.width - 40;
         }
      }
      
      private function daoJuOut(param1:DaoJuEvent) : void
      {
         this.tooltip.visible = false;
         PropertyScreen.close();
      }
      
      private function initMadeLan(param1:Number) : void
      {
         var _loc2_:Make = null;
         var _loc3_:Array = null;
         var _loc4_:Array = null;
         var _loc5_:Array = null;
         var _loc6_:Array = null;
         var _loc7_:Array = null;
         var _loc8_:Number = NaN;
         this.finishSlot.clearMake();
         this.initFrame();
         this.all_makeBtn.gotoAndStop(2);
         this.gold_text.text = "";
         this.nowId = param1;
         if(this.bookSlot.getMake(param1) != null)
         {
            _loc2_ = this.bookSlot.getMake(param1);
            _loc3_ = _loc2_.addNeedOb();
            _loc4_ = _loc2_.getNeedType();
            _loc5_ = _loc2_.getNeedId();
            _loc6_ = _loc2_.getNeedNum();
            _loc7_ = _loc2_.getObj();
            _loc8_ = _loc2_.getFinishNum();
            this.nameVisible(_loc5_,_loc4_,_loc6_,_loc3_);
            this.findIdInBag(_loc5_,_loc4_,_loc6_);
            this.needMcAp(_loc2_,_loc3_);
            this.getFinishSlot(_loc7_[0],_loc8_);
            this.gold_text.text = _loc2_.getGold().toString();
            if(_loc2_.isState())
            {
               this.madeOk = this.isOk(_loc5_);
               if(this.madeOk == true)
               {
                  this.all_makeBtn.gotoAndStop(1);
               }
            }
         }
      }
      
      private function needZz(param1:Number = 0) : void
      {
         var _loc2_:Make = null;
         var _loc3_:Array = null;
         var _loc4_:Number = 0;
         if(this.bookSlot.getMake(param1) != null)
         {
            _loc2_ = this.bookSlot.getMake(param1);
            _loc3_ = _loc2_.getNeedId();
            _loc4_ = 0;
            while(_loc4_ < _loc3_.length)
            {
               if(_loc3_[_loc4_] == -1)
               {
                  this["z_" + _loc4_].visible = true;
               }
               else
               {
                  this["z_" + _loc4_].visible = false;
               }
               _loc4_++;
            }
         }
         else
         {
            _loc4_ = 0;
            while(_loc4_ < 5)
            {
               this["z_" + _loc4_].visible = false;
               _loc4_++;
            }
         }
      }
      
      private function isOk(param1:Array) : Boolean
      {
         var _loc2_:Array = [];
         var _loc3_:Number = 0;
         while(_loc3_ < param1.length)
         {
            if(param1[_loc3_] != -1)
            {
               if(this.finishSlot.getMake(_loc3_) != null)
               {
                  _loc2_.push(1);
               }
               else
               {
                  _loc2_.push(-1);
               }
            }
            else
            {
               _loc2_.push(1);
            }
            _loc3_++;
         }
         _loc3_ = 0;
         while(_loc3_ < _loc2_.length)
         {
            if(_loc2_[_loc3_] == -1)
            {
               return false;
            }
            _loc3_++;
         }
         return true;
      }
      
      private function getFinishSlot(param1:Object, param2:Number) : void
      {
         if(param1 != null)
         {
            this.finishSlot.addFinish(param1);
            if(this.finishSlot.getMake(5) != null)
            {
               this.n_5.pic_xx.gotoAndStop(this.finishSlot.getMake(5).getFrame());
               this.name_5.text = this.finishSlot.getMake(5).getName();
               if(this.finishSlot.getMake(5).getColor() == 1)
               {
                  this.name_5.setTextFormat(this.bai);
               }
               else if(this.finishSlot.getMake(5).getColor() == 2)
               {
                  this.name_5.setTextFormat(this.lan);
               }
               else if(this.finishSlot.getMake(5).getColor() == 3)
               {
                  this.name_5.setTextFormat(this.zi);
               }
               else if(this.finishSlot.getMake(5).getColor() == 4 || this.finishSlot.getMake(5).getColor() == 5)
               {
                  this.name_5.setTextFormat(this.ch);
               }
            }
            if(param2 > 1)
            {
               this.n_5.howNum.text = String(param2);
            }
            else
            {
               this.n_5.howNum.text = "";
            }
         }
         else
         {
            this.n_5.pic_xx.gotoAndStop(1);
            this.name_5.text = "";
            this.n_5.howNum.text = "";
         }
      }
      
      private function findIdInBag(param1:Array, param2:Array, param3:Array) : void
      {
         var _loc5_:Array = null;
         var _loc4_:Number = 0;
         while(_loc4_ < param1.length)
         {
            _loc5_ = [];
            if(param1[_loc4_] != -1)
            {
               _loc5_ = this.needOk(param1[_loc4_],param2[_loc4_]);
               if(_loc5_ != null)
               {
                  if(this.dbNum(_loc5_,param2[_loc4_],param3[_loc4_]))
                  {
                     this.finishSlot.addNeedSlot(_loc4_,_loc5_[0][0]);
                  }
               }
            }
            _loc4_++;
         }
      }
      
      private function needOk(param1:Number, param2:Number) : Array
      {
         var _loc3_:Array = [];
         if(param2 == 0 || param2 == 4)
         {
            _loc3_ = MakeData.getEquip(param1,this.playerState);
         }
         else if(param2 == 1)
         {
            _loc3_ = MakeData.getSup(param1,this.playerState);
         }
         else if(param2 == 2)
         {
            _loc3_ = MakeData.getGem(param1,this.playerState);
         }
         else if(param2 == 3)
         {
            _loc3_ = MakeData.getOther(param1,this.playerState);
         }
         return _loc3_;
      }
      
      private function dbNum(param1:Array, param2:Number, param3:Number) : Boolean
      {
         var _loc7_:Gem = null;
         var _loc8_:Otherobj = null;
         var _loc4_:Number = Number(param1[0].length);
         var _loc5_:Number = 0;
         var _loc6_:Number = 0;
         while(_loc6_ < _loc4_)
         {
            if(param2 == 0 || param2 == 1)
            {
               if(_loc4_ >= param3)
               {
                  return true;
               }
            }
            else if(param2 == 2)
            {
               _loc7_ = param1[0][_loc6_];
               if(_loc7_.getIsPile())
               {
                  _loc5_ += _loc7_.getTimes();
                  if(_loc5_ >= param3)
                  {
                     return true;
                  }
               }
               else if(_loc4_ >= param3)
               {
                  return true;
               }
            }
            else if(param2 == 3)
            {
               _loc8_ = param1[0][_loc6_];
               if(_loc8_.isMany())
               {
                  _loc5_ += _loc8_.getTimes();
                  if(_loc5_ >= param3)
                  {
                     return true;
                  }
               }
               else if(_loc4_ >= param3)
               {
                  return true;
               }
            }
            _loc6_++;
         }
         return false;
      }
      
      public function nameVisible(param1:Array, param2:Array, param3:Array, param4:Array) : void
      {
         var _loc5_:Array = this.getName(param1,param2);
         var _loc6_:Array = this.getNumInBag(param1,param2);
         var _loc7_:Number = 0;
         while(_loc7_ < _loc5_.length)
         {
            if(_loc5_[_loc7_] != "")
            {
               this["name_" + _loc7_].text = _loc5_[_loc7_];
               this["nx_" + _loc7_].text = _loc6_[_loc7_];
               this["nb_" + _loc7_].text = param3[_loc7_];
               this["nx_" + _loc7_].setTextFormat(this.bai);
               if(_loc6_[_loc7_] < param3[_loc7_])
               {
                  this["nx_" + _loc7_].setTextFormat(this.hong);
               }
               if(param4[_loc7_].getColor() == 1)
               {
                  this["name_" + _loc7_].setTextFormat(this.bai);
               }
               else if(param4[_loc7_].getColor() == 2)
               {
                  this["name_" + _loc7_].setTextFormat(this.lan);
               }
               else if(param4[_loc7_].getColor() == 3)
               {
                  this["name_" + _loc7_].setTextFormat(this.zi);
               }
               else if(param4[_loc7_].getColor() == 4)
               {
                  this["name_" + _loc7_].setTextFormat(this.ch);
               }
            }
            else
            {
               this["name_" + _loc7_].text = "";
               this["nx_" + _loc7_].text = "";
               this["nb_" + _loc7_].text = "";
            }
            _loc7_++;
         }
      }
      
      public function needMcAp(param1:Make, param2:Array) : void
      {
         var _loc3_:Number = 0;
         if(param1.isState() == false)
         {
            _loc3_ = 0;
            while(_loc3_ < param2.length)
            {
               if(param2[_loc3_] != null)
               {
                  this["n_" + _loc3_].pic_xx.gotoAndStop(param2[_loc3_].getFrame());
                  this["n_" + _loc3_].ob_mast.gotoAndStop(3);
               }
               else
               {
                  this["n_" + _loc3_].pic_xx.gotoAndStop(1);
                  this["n_" + _loc3_].ob_mast.gotoAndStop(1);
               }
               _loc3_++;
            }
         }
         else
         {
            _loc3_ = 0;
            while(_loc3_ < param2.length)
            {
               if(param2[_loc3_] != null)
               {
                  this["n_" + _loc3_].pic_xx.gotoAndStop(param2[_loc3_].getFrame());
                  if(this.finishSlot.getMake(_loc3_) != null)
                  {
                     this["n_" + _loc3_].ob_mast.gotoAndStop(1);
                  }
                  else
                  {
                     this["n_" + _loc3_].ob_mast.gotoAndStop(2);
                  }
               }
               else
               {
                  this["n_" + _loc3_].pic_xx.gotoAndStop(1);
                  this["n_" + _loc3_].ob_mast.gotoAndStop(1);
               }
               _loc3_++;
            }
         }
      }
      
      private function getName(param1:Array, param2:Array) : Array
      {
         var _loc3_:String = null;
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < param1.length)
         {
            if(param1[_loc5_] == -1)
            {
               _loc3_ = "";
            }
            else if(param2[_loc5_] == 0 || param2[_loc5_] == 4)
            {
               _loc3_ = EquipFactory.findName(param1[_loc5_]);
            }
            else if(param2[_loc5_] == 1)
            {
               _loc3_ = SuppliesFactory.findName(param1[_loc5_]);
            }
            else if(param2[_loc5_] == 2)
            {
               _loc3_ = GemFactory.findName(param1[_loc5_]);
            }
            else if(param2[_loc5_] == 3)
            {
               _loc3_ = OtherFactory.getName(param1[_loc5_]);
            }
            _loc4_.push(_loc3_);
            _loc5_++;
         }
         return _loc4_;
      }
      
      private function getNumInBag(param1:Array, param2:Array) : Array
      {
         var _loc3_:Number = 0;
         var _loc4_:Array = [];
         var _loc5_:Number = 0;
         while(_loc5_ < param1.length)
         {
            if(param1[_loc5_] == -1)
            {
               _loc3_ = 0;
            }
            else if(param2[_loc5_] == 0 || param2[_loc5_] == 4)
            {
               _loc3_ = Number((Main["player" + this.playerState].getBag() as Bag).getEquipObjNum(param1[_loc5_]));
            }
            else if(param2[_loc5_] == 1)
            {
               _loc3_ = Number((Main["player" + this.playerState].getBag() as Bag).getSupObjNum(param1[_loc5_]));
            }
            else if(param2[_loc5_] == 2)
            {
               _loc3_ = Number((Main["player" + this.playerState].getBag() as Bag).getGemNum(param1[_loc5_]));
            }
            else if(param2[_loc5_] == 3)
            {
               _loc3_ = Number((Main["player" + this.playerState].getBag() as Bag).getOtherobjNum(param1[_loc5_]));
            }
            _loc4_.push(_loc3_);
            _loc5_++;
         }
         return _loc4_;
      }
      
      private function needFrame() : void
      {
         var _loc1_:Number = 0;
         while(_loc1_ < 5)
         {
            if(this.finishSlot.getMake(_loc1_) != null)
            {
               this["n_" + _loc1_].pic_xx.gotoAndStop(this.finishSlot.getMake(_loc1_).getFrame());
            }
            else
            {
               this["n_" + _loc1_].pic_xx.gotoAndStop(1);
            }
            _loc1_++;
         }
      }
      
      private function initFrame() : void
      {
         var _loc1_:Number = 0;
         while(_loc1_ < 6)
         {
            this["n_" + _loc1_].pic_xx.gotoAndStop(1);
            this["n_" + _loc1_].ob_mast.gotoAndStop(1);
            this["n_" + _loc1_].howNum.text = "";
            this["name_" + _loc1_].text = "";
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 5)
         {
            this["nx_" + _loc1_].text = "";
            this["nb_" + _loc1_].text = "";
            _loc1_++;
         }
      }
      
      private function doClose(param1:BtnEvent) : void
      {
         close();
      }
      
      private function DianQuanBuy(param1:*) : *
      {
         var _loc6_:Bag = null;
         var _loc2_:Make = this.bookSlot.getMake(this.nowId);
         var _loc3_:Number = _loc2_.getType();
         var _loc4_:Array = _loc2_.getObj();
         var _loc5_:uint = _loc2_.getDj();
         if(this.playerState == 1)
         {
            _loc6_ = Main.player1.getBag();
         }
         else if(this.playerState == 2)
         {
            _loc6_ = Main.player2.getBag();
         }
         if(this.SelBagNum(_loc3_,_loc4_,_loc6_))
         {
            if(Shop4399.moneyAll.getValue() < _loc5_)
            {
               (this.NoMoney_mc as NoMoneyInfo).Open(_loc5_);
               return;
            }
            this.BuyOpen(_loc5_);
         }
      }
      
      private function BuyOpen(param1:uint) : *
      {
         this.buy_mc.y = 0;
         this.buy_mc.x = 0;
         this.buy_mc.visible = true;
         this.buy_mc._txt.text = "是否要消费" + param1 + "点券购买";
         this.buy_mc.yes_btn.addEventListener(MouseEvent.CLICK,this.BuyGo);
         this.buy_mc.no_btn.addEventListener(MouseEvent.CLICK,this.BuyClose);
         this.buy_mc.close_btn.addEventListener(MouseEvent.CLICK,this.BuyClose);
      }
      
      private function BuyClose(param1:* = null) : *
      {
         this.buy_mc.y = 5000;
         this.buy_mc.x = 5000;
         this.buy_mc.visible = false;
         this.buy_mc.yes_btn.removeEventListener(MouseEvent.CLICK,this.BuyGo);
         this.buy_mc.no_btn.removeEventListener(MouseEvent.CLICK,this.BuyClose);
         this.buy_mc.close_btn.removeEventListener(MouseEvent.CLICK,this.BuyClose);
      }
      
      private function BuyGo(param1:*) : *
      {
         MakePanel._instance._BLACK_mc.visible = true;
         var _loc2_:Make = this.bookSlot.getMake(this.nowId);
         this.buyType = _loc2_.getType();
         this.buyObjArr = _loc2_.getObj();
         var _loc3_:uint = _loc2_.getSCID();
         Api_4399_All.BuyObj(_loc3_);
      }
      
      private function SelBagNum(param1:Number, param2:Array, param3:Bag) : Boolean
      {
         var _loc4_:Gem = null;
         var _loc5_:Number = NaN;
         var _loc6_:Otherobj = null;
         if(param1 == 0 || param1 == 4 || param1 == 6)
         {
            if(param3.backequipBagNum() >= param2.length)
            {
               return true;
            }
         }
         else if(param1 == 1)
         {
            if(param3.backSuppliesBagNum() >= param2.length)
            {
               return true;
            }
         }
         else if(param1 == 2)
         {
            _loc4_ = param2[0];
            _loc5_ = _loc4_.getId();
            if(param3.canPutGemNum(_loc5_) >= param2.length)
            {
               return true;
            }
         }
         else if(param1 == 3)
         {
            _loc6_ = param2[0];
            _loc5_ = _loc6_.getId();
            if(param3.canPutOtherNum(_loc5_) >= param2.length)
            {
               return true;
            }
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         return false;
      }
   }
}

