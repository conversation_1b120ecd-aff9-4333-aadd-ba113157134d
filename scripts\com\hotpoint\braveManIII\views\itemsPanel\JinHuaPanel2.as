package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   public class JinHuaPanel2 extends MovieClip
   {
      public static var itemsTooltip:ItemsTooltip;
      
      public static var jhPanel:MovieClip;
      
      public static var jhp:JinHuaPanel2;
      
      public static var clickObj:MovieClip;
      
      private static var nameStr:String;
      
      public static var clickNum:Number;
      
      public static var starNum:Number;
      
      public static var myplayer:PlayerData;
      
      private static var loadData:ClassLoader;
      
      public static var yeshu:Number = 0;
      
      public static var selbool:Boolean = false;
      
      public static var isPOne:Boolean = true;
      
      public static var mapNum:Number = 1;
      
      public static var partNum:Number = 1;
      
      public static var count:int = 0;
      
      public static var arr_cl:Array = [];
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_XLZF_2_v1320.swf";
      
      public static var selObj_LV:int = 1;
      
      public static var numArr:Array = [10,3,3,5];
      
      public static var maxLV:int = 12;
      
      public static var upOK:Boolean = false;
      
      public static var saveOK:Boolean = false;
      
      public static var timeXX:int = 0;
      
      public function JinHuaPanel2()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!jhPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var _loc1_:Number = 0;
         var _loc2_:MovieClip = null;
         var _loc3_:int = 0;
         _loc1_ = 0;
         while(_loc1_ < 24)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = jhPanel.getChildIndex(jhPanel["e" + _loc1_]);
            _loc2_.x = jhPanel["e" + _loc1_].x;
            _loc2_.y = jhPanel["e" + _loc1_].y;
            _loc2_.name = "e" + _loc1_;
            jhPanel.removeChild(jhPanel["e" + _loc1_]);
            jhPanel["e" + _loc1_] = _loc2_;
            jhPanel.addChild(_loc2_);
            jhPanel.setChildIndex(_loc2_,_loc3_);
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 4)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = jhPanel.getChildIndex(jhPanel["x" + _loc1_]);
            _loc2_.x = jhPanel["x" + _loc1_].x;
            _loc2_.y = jhPanel["x" + _loc1_].y;
            _loc2_.name = "x" + _loc1_;
            jhPanel.removeChild(jhPanel["x" + _loc1_]);
            jhPanel["x" + _loc1_] = _loc2_;
            jhPanel.addChild(_loc2_);
            jhPanel.setChildIndex(_loc2_,_loc3_);
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 8)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = jhPanel.getChildIndex(jhPanel["s" + _loc1_]);
            _loc2_.x = jhPanel["s" + _loc1_].x;
            _loc2_.y = jhPanel["s" + _loc1_].y;
            _loc2_.name = "s" + _loc1_;
            jhPanel.removeChild(jhPanel["s" + _loc1_]);
            jhPanel["s" + _loc1_] = _loc2_;
            jhPanel.addChild(_loc2_);
            jhPanel.setChildIndex(_loc2_,_loc3_);
            _loc1_++;
         }
         _loc2_ = new Shop_picNEW();
         _loc3_ = jhPanel.getChildIndex(jhPanel["select"]);
         _loc2_.x = jhPanel["select"].x;
         _loc2_.y = jhPanel["select"].y;
         _loc2_.name = "select";
         jhPanel.removeChild(jhPanel["select"]);
         jhPanel["select"] = _loc2_;
         jhPanel.addChild(_loc2_);
         jhPanel.setChildIndex(_loc2_,_loc3_);
      }
      
      private static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("JHShow") as Class;
         jhPanel = new _loc2_();
         jhp.addChild(jhPanel);
         InitIcon();
         jhPanel["_BLACK_mc"].visible = false;
         if(OpenYN)
         {
            open(isPOne);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         jhp = new JinHuaPanel2();
         LoadSkin();
         Main._stage.addChild(jhp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         jhp = new JinHuaPanel2();
         Main._stage.addChild(jhp);
         OpenYN = false;
      }
      
      public static function open(param1:Boolean) : void
      {
         Main.allClosePanel();
         if(jhPanel)
         {
            clickObj = null;
            Main.stopXX = true;
            jhp.x = 0;
            jhp.y = 0;
            isPOne = param1;
            if(isPOne)
            {
               myplayer = Main.player1;
            }
            addListenerP1();
            Main._stage.addChild(jhp);
            jhp.visible = true;
         }
         else
         {
            isPOne = param1;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(jhPanel)
         {
            Main.stopXX = false;
            removeListenerP1();
            jhp.visible = false;
            selbool = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         if(Main.P1P2)
         {
            jhPanel["bagOne"].visible = false;
            jhPanel["bagTwo"].visible = true;
            jhPanel["back_mc"].visible = true;
            jhPanel["bagOne"].addEventListener(MouseEvent.CLICK,to1p);
            jhPanel["bagTwo"].addEventListener(MouseEvent.CLICK,to2p);
         }
         else
         {
            jhPanel["bagOne"].visible = false;
            jhPanel["bagTwo"].visible = false;
            jhPanel["back_mc"].visible = false;
         }
         jhPanel["up_btn"].addEventListener(MouseEvent.CLICK,upGo);
         jhPanel["down_btn"].addEventListener(MouseEvent.CLICK,downGo);
         jhPanel["jh_btn"].visible = false;
         jhPanel["jh_btn"].addEventListener(MouseEvent.CLICK,doJH);
         jhPanel["close"].addEventListener(MouseEvent.CLICK,closeJH);
         var _loc1_:Number = 0;
         while(_loc1_ < 24)
         {
            jhPanel["e" + _loc1_].mouseChildren = false;
            jhPanel["e" + _loc1_].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["e" + _loc1_].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["e" + _loc1_].addEventListener(MouseEvent.CLICK,selected);
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 8)
         {
            jhPanel["s" + _loc1_].mouseChildren = false;
            jhPanel["s" + _loc1_].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["s" + _loc1_].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["s" + _loc1_].addEventListener(MouseEvent.CLICK,selected);
            _loc1_++;
         }
         jhPanel["select"].gotoAndStop(1);
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function playShow() : *
      {
         var _loc3_:* = undefined;
         var _loc4_:Equip = null;
         var _loc5_:Equip = null;
         var _loc1_:int = yeshu + 1;
         jhPanel["yeshu_txt"].text = _loc1_ + "/2";
         var _loc2_:int = 0;
         while(_loc2_ < 24)
         {
            jhPanel["e" + _loc2_].t_txt.text = "";
            _loc3_ = _loc2_ + 24 * yeshu;
            _loc4_ = myplayer.getBag().getEquipFromBag(_loc3_);
            jhPanel["e" + _loc2_].equipX = _loc4_;
            if(_loc4_ != null && _loc4_.getBlessAttrib() && _loc4_._blessAttrib.getBeishu() > 0 && _loc4_._blessAttrib.getBeishu() <= maxLV && _loc4_.getColor() == 5)
            {
               jhPanel["e" + _loc2_].gotoAndStop(_loc4_.getFrame());
               jhPanel["e" + _loc2_].visible = true;
            }
            else
            {
               jhPanel["e" + _loc2_].visible = false;
            }
            _loc2_++;
         }
         _loc2_ = 0;
         while(_loc2_ < 8)
         {
            jhPanel["s" + _loc2_].t_txt.text = "";
            _loc5_ = myplayer.getEquipSlot().getEquipFromSlot(_loc2_);
            jhPanel["s" + _loc2_].equipX = _loc5_;
            if(_loc5_ && _loc5_._blessAttrib && _loc5_._blessAttrib.getBeishu() > 0 && _loc5_._blessAttrib.getBeishu() <= maxLV && _loc5_.getColor() == 5)
            {
               jhPanel["s" + _loc2_].gotoAndStop(_loc5_.getFrame());
               jhPanel["s" + _loc2_].visible = true;
            }
            else
            {
               jhPanel["s" + _loc2_].visible = false;
            }
            _loc2_++;
         }
      }
      
      private static function tooltipOpen(param1:MouseEvent) : void
      {
         var _loc2_:MovieClip = param1.target as MovieClip;
         if(!param1.target.visible)
         {
            return;
         }
         jhPanel.addChild(itemsTooltip);
         var _loc3_:uint = uint(_loc2_.name.substr(1,2));
         var _loc4_:String = _loc2_.name.substr(0,1);
         if(_loc4_ == "e")
         {
            _loc3_ += 24 * yeshu;
            if(myplayer.getBag().getEquipFromBag(_loc3_) != null)
            {
               itemsTooltip.equipTooltip(myplayer.getBag().getEquipFromBag(_loc3_),1);
            }
         }
         else
         {
            itemsTooltip.slotTooltip(_loc3_,myplayer.getEquipSlot());
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = jhPanel.mouseX + 10;
         itemsTooltip.y = jhPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      public static function otherShow() : *
      {
         var _loc2_:Equip = null;
         var _loc3_:* = undefined;
         var _loc4_:Number = NaN;
         count = 0;
         if(Boolean(clickObj) && Boolean(clickObj.equipX))
         {
            _loc2_ = clickObj.equipX;
            JinHuaPanel2.selObj_LV = _loc2_._blessAttrib.getBeishu() < 2 ? 1 : int(_loc2_._blessAttrib.getBeishu());
            _loc3_ = Zhufu2Factory.allData[JinHuaPanel2.selObj_LV][1];
            jhPanel["cgl_txt"].text = "成功率:" + _loc3_ + "%";
         }
         arr_cl = Zhufu2Factory.allData[JinHuaPanel2.selObj_LV];
         var _loc1_:Number = 0;
         while(_loc1_ < 4)
         {
            _loc4_ = Number(arr_cl[_loc1_ + 2]);
            jhPanel["x" + _loc1_].gotoAndStop(OtherFactory.getFrame(_loc4_));
            jhPanel["n" + _loc1_].text = numArr[_loc1_];
            if(myplayer.getBag().getOtherobjNum(_loc4_) >= numArr[_loc1_])
            {
               jhPanel["c" + _loc1_].text = myplayer.getBag().getOtherobjNum(_loc4_);
               ColorX(jhPanel["c" + _loc1_],"0xFFFF00");
               ++count;
            }
            else
            {
               jhPanel["c" + _loc1_].text = myplayer.getBag().getOtherobjNum(_loc4_);
               ColorX(jhPanel["c" + _loc1_],"0xFF0000");
            }
            _loc1_++;
         }
         if(count >= 4 && clickObj && Boolean(clickObj.equipX))
         {
            jhPanel["jh_btn"].visible = true;
         }
         else
         {
            jhPanel["jh_btn"].visible = false;
            jhPanel["cgl_txt"].text = "";
         }
      }
      
      public static function doJH(param1:*) : *
      {
         var _loc5_:Equip = null;
         var _loc2_:int = 0;
         while(_loc2_ < 4)
         {
            myplayer.getBag().delOtherById(arr_cl[_loc2_ + 2],numArr[_loc2_]);
            _loc2_++;
         }
         var _loc3_:int = Math.random() * 100 + 1;
         var _loc4_:* = Zhufu2Factory.allData[JinHuaPanel2.selObj_LV][1];
         if(_loc3_ > _loc4_)
         {
            upOK = false;
         }
         else
         {
            upOK = true;
            if(nameStr == "e")
            {
               _loc5_ = myplayer.getBag().getEquipFromBag(clickNum);
               _loc5_.setBlessAttribLV3();
            }
            else
            {
               _loc5_ = myplayer.getEquipSlot().getEquipFromSlot(clickNum);
               _loc5_.setBlessAttribLV3();
            }
         }
         Main.Save();
         jhPanel["_BLACK_mc"].visible = true;
         jhPanel.addEventListener(Event.ENTER_FRAME,onTime);
      }
      
      private static function onTime(param1:*) : *
      {
         ++timeXX;
         if(timeXX > 50 && saveOK)
         {
            jhPanel["_BLACK_mc"].visible = false;
            saveOK = false;
            timeXX = 0;
            jhPanel.removeEventListener(Event.ENTER_FRAME,onTime);
            if(upOK)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备祝福成功");
               close();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备祝福失败!");
               JinHuaPanel2.otherShow();
            }
         }
      }
      
      public static function to1p(param1:*) : *
      {
         myplayer = Main.player1;
         jhPanel["bagOne"].visible = false;
         jhPanel["bagTwo"].visible = true;
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         jhPanel["jh_btn"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function to2p(param1:*) : *
      {
         myplayer = Main.player2;
         jhPanel["bagOne"].visible = true;
         jhPanel["bagTwo"].visible = false;
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         jhPanel["jh_btn"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function upGo(param1:*) : *
      {
         yeshu = 0;
         playShow();
         otherShow();
      }
      
      public static function downGo(param1:*) : *
      {
         yeshu = 1;
         playShow();
         otherShow();
      }
      
      private static function ColorX(param1:*, param2:String) : *
      {
         var _loc3_:* = new TextFormat();
         _loc3_.color = param2;
         param1.setTextFormat(_loc3_);
      }
      
      public static function removeListenerP1() : *
      {
         var _loc1_:Number = 0;
         jhPanel["close"].removeEventListener(MouseEvent.CLICK,closeJH);
         jhPanel["jh_btn"].removeEventListener(MouseEvent.CLICK,doJH);
         _loc1_ = 0;
         while(_loc1_ < 24)
         {
            jhPanel["e" + _loc1_].mouseChildren = false;
            jhPanel["e" + _loc1_].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["e" + _loc1_].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["e" + _loc1_].removeEventListener(MouseEvent.CLICK,selected);
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 8)
         {
            jhPanel["s" + _loc1_].mouseChildren = false;
            jhPanel["s" + _loc1_].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["s" + _loc1_].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["s" + _loc1_].removeEventListener(MouseEvent.CLICK,selected);
            _loc1_++;
         }
         jhPanel["up_btn"].removeEventListener(MouseEvent.CLICK,upGo);
         jhPanel["down_btn"].removeEventListener(MouseEvent.CLICK,downGo);
      }
      
      public static function closeJH(param1:*) : *
      {
         close();
      }
      
      private static function tooltipClose(param1:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      private static function selected(param1:MouseEvent) : void
      {
         selbool = true;
         clickObj = param1.target as MovieClip;
         jhPanel["chose"].visible = true;
         jhPanel["chose"].x = clickObj.x - 2;
         jhPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         jhPanel["select"].gotoAndStop(clickObj.currentFrame);
         jhPanel["select"].visible = true;
         if(nameStr == "e")
         {
            clickNum += 24 * yeshu;
         }
         if(count >= 4)
         {
            jhPanel["jh_btn"].visible = true;
         }
         var _loc2_:Equip = clickObj.equipX;
         JinHuaPanel2.selObj_LV = _loc2_._blessAttrib.getBeishu() < 2 ? 1 : int(_loc2_._blessAttrib.getBeishu());
         JinHuaPanel2.otherShow();
         var _loc3_:* = Zhufu2Factory.allData[JinHuaPanel2.selObj_LV][1];
         jhPanel["cgl_txt"].text = "成功率:" + _loc3_ + "%";
      }
   }
}

