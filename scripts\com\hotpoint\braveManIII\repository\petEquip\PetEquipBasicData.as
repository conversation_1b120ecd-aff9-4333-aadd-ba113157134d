package com.hotpoint.braveManIII.repository.petEquip
{
   import com.hotpoint.braveManIII.models.common.VT;
   import com.hotpoint.braveManIII.models.petEquip.PetEquip;
   
   public class PetEquipBasicData
   {
      private var _id:VT;
      
      private var _frame:VT;
      
      private var _name:String;
      
      private var _type:VT;
      
      private var _price:VT;
      
      private var _color:VT;
      
      private var _skilldescript:String;
      
      private var _descript:String;
      
      private var _xingge:Array = [];
      
      private var _affect:Array = [];
      
      public function PetEquipBasicData()
      {
         super();
      }
      
      public static function createPetEquipBasicData(param1:Number, param2:Number, param3:String, param4:Number, param5:Number, param6:Number, param7:String, param8:String, param9:Array, param10:Array) : PetEquipBasicData
      {
         var _loc11_:PetEquipBasicData = new PetEquipBasicData();
         _loc11_._id = VT.createVT(param1);
         _loc11_._frame = VT.createVT(param2);
         _loc11_._name = param3;
         _loc11_._type = VT.createVT(param4);
         _loc11_._price = VT.createVT(param5);
         _loc11_._color = VT.createVT(param6);
         _loc11_._skilldescript = param7;
         _loc11_._descript = param8;
         _loc11_._xingge = param9;
         _loc11_._affect = param10;
         return _loc11_;
      }
      
      public function getId() : Number
      {
         return this._id.getValue();
      }
      
      public function getName() : String
      {
         return this._name;
      }
      
      public function getFrame() : Number
      {
         return this._frame.getValue();
      }
      
      public function getType() : Number
      {
         return this._type.getValue();
      }
      
      public function getPrice() : Number
      {
         return this._price.getValue();
      }
      
      public function getColor() : Number
      {
         return this._color.getValue();
      }
      
      public function getSkillDescript() : String
      {
         return this._skilldescript;
      }
      
      public function getDescript() : String
      {
         return this._descript;
      }
      
      public function getAffect() : Array
      {
         return this._affect;
      }
      
      public function getXingge() : Array
      {
         return this._xingge;
      }
      
      public function createPetEquip() : PetEquip
      {
         return PetEquip.creatPetEquip(this._id.getValue());
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(param1:VT) : void
      {
         this._id = param1;
      }
      
      public function get frame() : VT
      {
         return this._frame;
      }
      
      public function set frame(param1:VT) : void
      {
         this._frame = param1;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function set name(param1:String) : void
      {
         this._name = param1;
      }
      
      public function get type() : VT
      {
         return this._type;
      }
      
      public function set type(param1:VT) : void
      {
         this._type = param1;
      }
      
      public function get color() : VT
      {
         return this._color;
      }
      
      public function set color(param1:VT) : void
      {
         this._color = param1;
      }
      
      public function get skilldescript() : String
      {
         return this._skilldescript;
      }
      
      public function set skilldescript(param1:String) : void
      {
         this._skilldescript = param1;
      }
      
      public function get descript() : String
      {
         return this._descript;
      }
      
      public function set descript(param1:String) : void
      {
         this._descript = param1;
      }
      
      public function get xingge() : Array
      {
         return this._xingge;
      }
      
      public function set xingge(param1:Array) : void
      {
         this._xingge = param1;
      }
      
      public function get affect() : Array
      {
         return this._affect;
      }
      
      public function set affect(param1:Array) : void
      {
         this._affect = param1;
      }
      
      public function get price() : VT
      {
         return this._price;
      }
      
      public function set price(param1:VT) : void
      {
         this._price = param1;
      }
   }
}

