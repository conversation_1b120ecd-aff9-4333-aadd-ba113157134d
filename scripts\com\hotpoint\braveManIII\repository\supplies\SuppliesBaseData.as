package com.hotpoint.braveManIII.repository.supplies
{
   import com.hotpoint.braveManIII.models.common.VT;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   
   public class SuppliesBaseData
   {
      private var _id:VT;
      
      private var _frame:VT;
      
      private var _name:String;
      
      private var _useLevel:VT;
      
      private var _price:VT;
      
      private var _times:VT;
      
      private var _isPile:Boolean;
      
      private var _percent:VT;
      
      private var _rmbID:VT;
      
      private var _rmbPrice:VT;
      
      private var _color:VT;
      
      private var _dropLevel:VT;
      
      private var _coolDowns:VT;
      
      private var _affectMode:VT;
      
      private var _duration:VT;
      
      private var _descript:String;
      
      private var _affect:Array = [];
      
      public function SuppliesBaseData()
      {
         super();
      }
      
      public static function createSuppliesBaseData(param1:Number, param2:Number, param3:String, param4:String, param5:Number, param6:Number, param7:Number, param8:Number, param9:Number, param10:Number, param11:Number, param12:Number, param13:Number, param14:Boolean, param15:Number, param16:Number, param17:Array) : SuppliesBaseData
      {
         var _loc18_:SuppliesBaseData = new SuppliesBaseData();
         _loc18_._id = VT.createVT(param1);
         _loc18_._frame = VT.createVT(param2);
         _loc18_._name = param3;
         _loc18_._descript = param4;
         _loc18_._useLevel = VT.createVT(param5);
         _loc18_._dropLevel = VT.createVT(param6);
         _loc18_._price = VT.createVT(param7);
         _loc18_._coolDowns = VT.createVT(param8);
         _loc18_._times = VT.createVT(param9);
         _loc18_._color = VT.createVT(param10);
         _loc18_._percent = VT.createVT(param11);
         _loc18_._rmbID = VT.createVT(param12);
         _loc18_._rmbPrice = VT.createVT(param13);
         _loc18_._isPile = param14;
         _loc18_._affectMode = VT.createVT(param15);
         _loc18_._duration = VT.createVT(param16);
         _loc18_._affect = param17;
         return _loc18_;
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(param1:VT) : void
      {
         this._id = param1;
      }
      
      public function get frame() : VT
      {
         return this._frame;
      }
      
      public function set frame(param1:VT) : void
      {
         this._frame = param1;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function set name(param1:String) : void
      {
         this._name = param1;
      }
      
      public function get useLevel() : VT
      {
         return this._useLevel;
      }
      
      public function set useLevel(param1:VT) : void
      {
         this._useLevel = param1;
      }
      
      public function get price() : VT
      {
         return this._price;
      }
      
      public function set price(param1:VT) : void
      {
         this._price = param1;
      }
      
      public function get times() : VT
      {
         return this._times;
      }
      
      public function set times(param1:VT) : void
      {
         this._times = param1;
      }
      
      public function get isPile() : Boolean
      {
         return this._isPile;
      }
      
      public function set isPile(param1:Boolean) : void
      {
         this._isPile = param1;
      }
      
      public function get color() : VT
      {
         return this._color;
      }
      
      public function set color(param1:VT) : void
      {
         this._color = param1;
      }
      
      public function get dropLevel() : VT
      {
         return this._dropLevel;
      }
      
      public function set dropLevel(param1:VT) : void
      {
         this._dropLevel = param1;
      }
      
      public function get coolDowns() : VT
      {
         return this._coolDowns;
      }
      
      public function set coolDowns(param1:VT) : void
      {
         this._coolDowns = param1;
      }
      
      public function get affectMode() : VT
      {
         return this._affectMode;
      }
      
      public function set affectMode(param1:VT) : void
      {
         this._affectMode = param1;
      }
      
      public function get duration() : VT
      {
         return this._duration;
      }
      
      public function set duration(param1:VT) : void
      {
         this._duration = param1;
      }
      
      public function get descript() : String
      {
         return this._descript;
      }
      
      public function set descript(param1:String) : void
      {
         this._descript = param1;
      }
      
      public function get affect() : Array
      {
         return this._affect;
      }
      
      public function set affect(param1:Array) : void
      {
         this._affect = param1;
      }
      
      public function get percent() : VT
      {
         return this._percent;
      }
      
      public function set percent(param1:VT) : void
      {
         this._percent = param1;
      }
      
      public function get rmbID() : VT
      {
         return this._rmbID;
      }
      
      public function set rmbID(param1:VT) : void
      {
         this._rmbID = param1;
      }
      
      public function get rmbPrice() : VT
      {
         return this._rmbPrice;
      }
      
      public function set rmbPrice(param1:VT) : void
      {
         this._rmbPrice = param1;
      }
      
      public function getId() : Number
      {
         return this._id.getValue();
      }
      
      public function getName() : String
      {
         return this._name;
      }
      
      public function getUseLevel() : Number
      {
         return this._useLevel.getValue();
      }
      
      public function getPrice() : Number
      {
         return this._price.getValue();
      }
      
      public function getTimes() : Number
      {
         return this._times.getValue();
      }
      
      public function getIsPile() : Boolean
      {
         return this._isPile;
      }
      
      public function getAffect() : Array
      {
         return this._affect;
      }
      
      public function getColor() : Number
      {
         return this._color.getValue();
      }
      
      public function getFrame() : Number
      {
         return this._frame.getValue();
      }
      
      public function getDropLevel() : Number
      {
         return this._dropLevel.getValue();
      }
      
      public function getDescript() : String
      {
         return this._descript;
      }
      
      public function getCoolDowns() : Number
      {
         return this._coolDowns.getValue();
      }
      
      public function getPercent() : Number
      {
         return this._percent.getValue();
      }
      
      public function getRmbId() : Number
      {
         return this._rmbID.getValue();
      }
      
      public function getRmbPrice() : Number
      {
         return this._rmbPrice.getValue();
      }
      
      public function getAffectMode() : Number
      {
         return this._affectMode.getValue();
      }
      
      public function getDuration() : Number
      {
         return this._duration.getValue();
      }
      
      public function createSupplies() : Supplies
      {
         return Supplies.creatSupplies(this._id.getValue(),this._times.getValue());
      }
   }
}

