package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class Panel_XianHua extends MovieClip
   {
      private static var loadData:ClassLoader;
      
      private static var _this:Panel_XianHua;
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var huaArr:Array;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_xianhua.swf";
      
      public static var objId_Arr:Array = [63181,63210,63235,63204,63455];
      
      public static var num_arr:Array = [1,5,5,1,1];
      
      public static var gailv_arr:Array = [25,55,94,97,100];
      
      public static var overTime:int = 20230219;
      
      public static var overTimeTxt:String = "至2023年2月19日";
      
      private var skin:MovieClip;
      
      private var buyok:Boolean = false;
      
      public function Panel_XianHua()
      {
         super();
         this.LoadSkin();
         _this = this;
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.mouseEnabled = false;
         itemsTooltip.visible = false;
      }
      
      private static function InitOpen() : *
      {
         var _loc1_:Panel_XianHua = new Panel_XianHua();
         Main._stage.addChild(_loc1_);
         OpenYN = true;
      }
      
      public static function xianHuaUp() : *
      {
         var _loc1_:* = undefined;
         if(Main.serverTime.getValue() > Panel_XianHua.overTime)
         {
            Panel_XianHua.huaArr = null;
            return;
         }
         if(!Panel_XianHua.huaArr)
         {
            Panel_XianHua.huaArr = [1,Panel_XianHua.rOBJ(),0];
         }
         else
         {
            _loc1_ = Panel_XianHua.huaArr[0];
            Panel_XianHua.huaArr[0] = _loc1_ + 1;
         }
         NewMC.Open("文字提示",Main._stage,480,400,60,0,true,1,"获得 情人节鲜花!");
      }
      
      public static function Open() : *
      {
         Main.DuoKai_Fun();
         if(_this)
         {
            _this.visible = true;
            _this.y = 0;
            _this.x = 0;
            _this.Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.y = 5000;
            _this.x = 5000;
         }
      }
      
      public static function rOBJ() : int
      {
         var _loc1_:* = Math.random() * 100;
         var _loc2_:* = 0;
         while(_loc2_ < gailv_arr.length)
         {
            if(_loc1_ < gailv_arr[_loc2_])
            {
               TiaoShi.txtShow("概率:" + _loc2_);
               if(huaArr && huaArr[2] < 15 && _loc2_ < 4)
               {
                  ++huaArr[2];
                  if(huaArr[2] >= 15)
                  {
                     return 4;
                  }
               }
               return _loc2_;
            }
            _loc2_++;
         }
         return 0;
      }
      
      public static function GetBuy() : *
      {
         if(Boolean(_this) && Boolean(_this.buyok))
         {
            _this.skin._BLACK_mc.visible = false;
            _this.buyok = false;
            NewMC.Open("文字提示",_this,460,400,60,0,true,1,"鲜花购买成功");
            ++Panel_XianHua.huaArr[0];
            Main.Save2();
            _this.Show();
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(param1:*) : *
      {
         Play_Interface.interfaceX["load_mc"].visible = false;
         var _loc2_:Class = loadData.getClass("xianHua") as Class;
         this.skin = new _loc2_();
         addChild(this.skin);
         var _loc3_:Class = loadData.getClass("情人节活动日期") as Class;
         var _loc4_:MovieClip = new _loc3_();
         this.skin.guize_mc.addChild(_loc4_);
         _loc4_.huodongriqi_txt.text = overTimeTxt;
         _loc4_.x = -277;
         _loc4_.y = -145;
         this.skin.close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.skin.song_btn.addEventListener(MouseEvent.CLICK,this.Song);
         this.skin.buy_btn.addEventListener(MouseEvent.CLICK,this.Buy);
         this.skin.guieZe_btn.addEventListener(MouseEvent.CLICK,this.Open2);
         this.skin.guize_mc.close_btn.addEventListener(MouseEvent.CLICK,this.Close2);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function Close(param1:* = null) : *
      {
         CloseX();
      }
      
      private function Open2(param1:* = null) : *
      {
         this.skin.guize_mc.visible = true;
      }
      
      private function Close2(param1:* = null) : *
      {
         this.skin.guize_mc.visible = false;
      }
      
      private function Show() : *
      {
         var _loc1_:int = 0;
         var _loc3_:MovieClip = null;
         var _loc4_:* = undefined;
         if(!Panel_XianHua.huaArr)
         {
            Panel_XianHua.huaArr = [0,Panel_XianHua.rOBJ(),0];
         }
         this.skin._BLACK_mc.visible = false;
         _loc1_ = huaArr[0] + 1;
         if(huaArr[0] > 9)
         {
            _loc1_ = 10;
         }
         this.skin.pic_mc.gotoAndStop(_loc1_);
         this.skin.song_btn.visible = true;
         if(_loc1_ < 10)
         {
            this.skin.song_btn.visible = false;
         }
         var _loc2_:Number = 0;
         while(_loc2_ < 5)
         {
            _loc1_ = int(this.skin.guize_mc.getChildIndex(this.skin.guize_mc["s" + _loc2_]));
            _loc3_ = new Shop_picNEW();
            _loc3_.x = this.skin.guize_mc["s" + _loc2_].x;
            _loc3_.y = this.skin.guize_mc["s" + _loc2_].y;
            _loc3_.name = "s" + _loc2_;
            this.skin.guize_mc.removeChild(this.skin.guize_mc["s" + _loc2_]);
            this.skin.guize_mc["s" + _loc2_] = _loc3_;
            this.skin.guize_mc.addChild(_loc3_);
            this.skin.guize_mc.setChildIndex(_loc3_,_loc1_);
            _loc4_ = OtherFactory.creatOther(objId_Arr[_loc2_]).getFrame();
            this.skin.guize_mc["s" + _loc2_].gotoAndStop(_loc4_);
            this.skin.guize_mc["s" + _loc2_].addEventListener(MouseEvent.MOUSE_OVER,this.s_over);
            this.skin.guize_mc["s" + _loc2_].addEventListener(MouseEvent.MOUSE_OUT,this.c_out);
            this.skin.guize_mc["s" + _loc2_].mouseChildren = false;
            _loc2_++;
         }
      }
      
      public function Song(param1:*) : *
      {
         var _loc2_:* = undefined;
         var _loc3_:* = undefined;
         var _loc4_:* = undefined;
         var _loc5_:* = undefined;
         var _loc6_:* = undefined;
         if(Main.player1.getBag().backOtherBagNum() < 1)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         else
         {
            _loc2_ = objId_Arr[huaArr[1]];
            _loc3_ = OtherFactory.creatOther(_loc2_).getName();
            _loc4_ = num_arr[huaArr[1]];
            Panel_XianHua.huaArr[0] -= 9;
            Panel_XianHua.huaArr[1] = rOBJ();
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(_loc2_));
               _loc5_++;
            }
            _loc6_ = "获得 " + _loc3_ + " 物品已放入背包";
            NewMC.Open("文字提示",Main._stage,480,400,60,0,true,1,_loc6_);
            this.Show();
            Main.Save2();
         }
      }
      
      public function Buy(param1:*) : *
      {
         if(Shop4399.moneyAll.getValue() >= 10)
         {
            this.buyok = true;
            this.skin._BLACK_mc.visible = true;
            Api_4399_All.BuyObj(284);
         }
         else
         {
            NewMC.Open("文字提示",_this,460,400,60,0,true,1,"点券不足");
         }
      }
      
      private function s_over(param1:*) : *
      {
         this.skin.guize_mc.addChild(itemsTooltip);
         var _loc2_:uint = uint(int(param1.target.name.substr(1,1)));
         itemsTooltip.x = this.skin.guize_mc.mouseX;
         itemsTooltip.y = this.skin.guize_mc.mouseY - 100;
         if(this.skin.guize_mc.mouseX > 770)
         {
            itemsTooltip.x -= 250;
         }
         var _loc4_:* = objId_Arr[_loc2_];
         itemsTooltip.otherTooltip(OtherFactory.creatOther(_loc4_));
         itemsTooltip.visible = true;
      }
      
      private function c_out(param1:*) : *
      {
         itemsTooltip.visible = false;
      }
   }
}

