package src.Skin
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.utils.*;
   import src.*;
   import src.other.*;
   
   public class EnemyBossXS242 extends EnemySkin
   {
      public static var thisX:EnemyBossXS242;
      
      public var gjNum:int = 1;
      
      public var gjHP:int = 0;
      
      public var lifeXX_time:int = 0;
      
      public var lifeXX_num1:int = 0;
      
      public var lifeXX_num2:int = 0;
      
      public function EnemyBossXS242()
      {
         super();
         thisX = this;
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_XXX);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGEXXX);
      }
      
      public static function BeiDong_2(param1:int, param2:int) : *
      {
         var _loc3_:int = 0;
         var _loc4_:Class = null;
         var _loc5_:MovieClip = null;
         if(Boolean(thisX) && thisX.who.life.getValue() > 0)
         {
            _loc3_ = int(Main.wts.getBoss());
            _loc4_ = Enemy.EnemyArr[_loc3_].getClass("回复火球") as Class;
            _loc5_ = new _loc4_();
            Main.world.moveChild_Other.addChild(_loc5_);
            _loc5_.x = param1;
            _loc5_.y = param2 - 20;
            _loc5_.who = thisX;
         }
      }
      
      private function BeiDong_1() : *
      {
         var _loc2_:Enemy = null;
         var _loc3_:int = 0;
         var _loc4_:Class = null;
         var _loc5_:MovieClip = null;
         var _loc1_:int = 0;
         while(_loc1_ < Enemy.All.length)
         {
            _loc2_ = Enemy.All[_loc1_];
            if(Play_Interface.bossIS == _loc2_)
            {
               _loc2_.lifeMAX.setValue(who.lifeMAX.getValue());
               _loc2_.life.setValue(who.life.getValue());
               _loc3_ = int(Main.wts.getBoss());
               _loc4_ = Enemy.EnemyArr[_loc3_].getClass("傀儡标记") as Class;
               _loc5_ = new _loc4_();
               _loc2_.addChild(_loc5_);
               _loc5_.y = -300;
            }
            _loc1_++;
         }
      }
      
      private function onENTER_FRAME_XXX(param1:*) : *
      {
         ++this.lifeXX_time;
         if(this.lifeXX_time == 20)
         {
            this.BeiDong_1();
         }
         if(this.lifeXX_time % 27 == 0)
         {
            if(Main.player_1.hp.getValue() > 0 && Math.abs(Main.player_1.x - who.x) < 200)
            {
               ++this.lifeXX_num1;
               Main.player_1.Hit3000(this.lifeXX_num1,1000);
            }
            else
            {
               this.lifeXX_num1 = 0;
            }
            if(Boolean(Main.P1P2) && Main.player_2.hp.getValue() > 0 && Math.abs(Main.player_2.x - who.x) < 200)
            {
               ++this.lifeXX_num2;
               Main.player_2.Hit3000(this.lifeXX_num2,1000);
            }
            else
            {
               this.lifeXX_num2 = 0;
            }
         }
      }
      
      public function HpUp() : *
      {
         if(this.gjHP == 0)
         {
            this.gjHP = who.攻击力.getValue();
         }
         if(this.gjNum < 10)
         {
            ++this.gjNum;
         }
         who.攻击力.setValue(this.gjHP + this.gjHP * 0.05 * this.gjNum);
         who.hpUpEnemy(who.lifeMAX.getValue() * 0.025);
         var _loc1_:int = int(Main.wts.getBoss());
         var _loc2_:Class = Enemy.EnemyArr[_loc1_].getClass("回复效果") as Class;
         var _loc3_:MovieClip = new _loc2_();
         this.addChild(_loc3_);
      }
      
      private function onREMOVED_FROM_STAGEXXX(param1:*) : *
      {
         thisX = null;
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_XXX);
      }
      
      override public function otherGoTo(param1:String) : *
      {
         if(param1 == "死亡")
         {
            Main.wts.wantedTaskComplete();
         }
      }
   }
}

