package src
{
   import com.hotpoint.braveManIII.Tool.XMLAsset;
   import com.hotpoint.braveManIII.models.common.VT;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.equip.EquipBaseAttrib;
   import com.hotpoint.braveManIII.models.gem.Attribute;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.plan.Plan;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.achievement.*;
   import com.hotpoint.braveManIII.repository.elves.ElvesFactory;
   import com.hotpoint.braveManIII.repository.equip.EquipFactory;
   import com.hotpoint.braveManIII.repository.gem.GemFactory;
   import com.hotpoint.braveManIII.repository.other.OtherFactory;
   import com.hotpoint.braveManIII.repository.pet.PetFactory;
   import com.hotpoint.braveManIII.repository.petEquip.PetEquipFactory;
   import com.hotpoint.braveManIII.repository.plan.PlanFactory;
   import com.hotpoint.braveManIII.repository.quest.QuestFactory;
   import com.hotpoint.braveManIII.repository.supplies.SuppliesFactory;
   import com.hotpoint.braveManIII.repository.title.TitleFactory;
   import com.hotpoint.braveManIII.views.achPanel.AchData;
   import com.hotpoint.braveManIII.views.caiyaoPanel.CaiYaoPanel;
   import com.hotpoint.braveManIII.views.cardPanel.CardPanel;
   import com.hotpoint.braveManIII.views.itemsPanel.JinHuaPanel;
   import com.hotpoint.braveManIII.views.itemsPanel.JinHuaPanel2;
   import com.hotpoint.braveManIII.views.petPanel.NewPetPanel;
   import com.hotpoint.braveManIII.views.skillPanel.SkillPanel;
   import com.hotpoint.braveManIII.views.storagePanel.StoragePanel;
   import com.hotpoint.braveManIII.views.taskPanel.TaskData;
   import flash.display.*;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.external.*;
   import flash.net.*;
   import flash.system.*;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.text.TextFormat;
   import flash.filters.GlowFilter;
   import flash.filters.DropShadowFilter;
   import flash.filters.BlurFilter;
   import flash.geom.Matrix;
   import flash.utils.Timer;
   import flash.events.TimerEvent;
   import src._data.XingLingFactory;
   import src.tool.PaiHang_Data;
   import src.CuteGMPanel;
   import src.CuteGMFunctions;
   import src.SimpleCuteGMPanel;
   
   public class GM extends MovieClip
   {
      public static var saveEditor:SaveEditor;

      // GM系统初始化状态
      private static var gmInitialized:Boolean = false;
      private static var gmInstance:GM;

      // 初始化尝试计数器
      private static var initAttempts:int = 0;

      public static var cuteGMPanel:CuteGMPanel;

      public static var myXml:XML = new XML();

      public static var myXml2:XML = new XML();
      
      public var temp:XX_MC;
      
      public var 界面1:XX_MC;
      
      public var 界面2:XX_MC;
      
      public var 界面3:XX_MC;
      
      public var 界面4:XX_MC;
      
      public var 界面5:XX_MC;
      
      public var b1:Sprite;
      
      public var b2:Sprite;
      
      public var b3:Sprite;
      
      public var b4:Sprite;
      
      public var b5:Sprite;
      
      public var b6:Sprite;
      
      public var b7:Sprite;
      
      public var b8:TextField;
      
      public var b9:TextField;
      
      public var yj1:Sprite;
      
      public var yj2:Sprite;
      
      public var yj3:Sprite;
      
      public var yj4:Sprite;
      
      public var yj5:Sprite;
      
      public var yj6:Sprite;
      
      public var yj7:Sprite;
      
      public var yj8:Sprite;
      
      public var yj9:Sprite;
      
      public var yj10:Sprite;
      
      public var yj11:Sprite;
      
      public var yj12:TextField;
      
      public var yj14:TextField;
      
      public var yj13:Sprite;
      
      public var yj15:Sprite;
      
      public var yj16:Sprite;
      
      public var yj17:Sprite;
      
      public var yj18:Sprite;
      
      public var yj19:Sprite;
      
      public var yj20:Sprite;
      
      public var yj21:Sprite;
      
      public var yj22:Sprite;
      
      public var zb1:TextField;
      
      public var zb2:TextField;
      
      public var zb3:Sprite;
      
      public var zb4:Sprite;
      
      public var zb5:Sprite;
      
      public var zb6:Sprite;
      
      public var zb7:Sprite;
      
      public var zb8:Sprite;
      
      public var zb9:Sprite;
      
      public var zb10:Sprite;
      
      public var zb11:Sprite;
      
      public var zb12:Sprite;
      
      public var zb13:Sprite;
      
      public var zb14:Sprite;
      
      public var zb15:Sprite;
      
      public var zb16:Sprite;
      
      public var zb17:Sprite;
      
      public var dj1:TextField;
      
      public var dj2:TextField;
      
      public var dj3:Sprite;
      
      public var dj4:Sprite;
      
      public var bs1:TextField;
      
      public var bs2:TextField;
      
      public var bs3:Sprite;
      
      public var bs4:Sprite;
      
      public var xhp1:TextField;
      
      public var xhp2:TextField;
      
      public var xhp3:Sprite;
      
      public var xhp4:Sprite;
      
      public var rw1:TextField;
      
      public var rw3:Sprite;
      
      public var rw4:Sprite;
      
      public var zdy1:TextField;
      
      public var zdy3:TextField;
      
      public var zdy2:Sprite;
      
      public var zdy4:Sprite;
      
      public var zdy5:TextField;
      
      public var zdy7:TextField;
      
      public var zdy6:Sprite;
      
      public var zdy8:Sprite;
      
      public var zdy9:TextField;
      
      public var zdy11:TextField;
      
      public var zdy10:Sprite;
      
      public var zdy12:Sprite;
      
      public var zdy13:TextField;
      
      public var zdy15:TextField;
      
      public var zdy14:Sprite;
      
      public var zdy16:Sprite;
      
      public var zdy17:TextField;
      
      public var zdy19:TextField;
      
      public var zdy18:Sprite;
      
      public var zdy20:Sprite;
      
      public var zdy21:TextField;
      
      public var zdy22:Sprite;
      
      public var zdy23:Sprite;
      
      public var zdy24:Sprite;
      
      public var zdy25:Sprite;
      
      public var zdy26:Sprite;
      
      public var zdy27:Sprite;
      
      public var zdy28:Sprite;
      
      public var zdy29:Sprite;
      
      public var zdy30:Sprite;
      
      public var js1:Sprite;
      
      public var js2:Sprite;
      
      public var js3:Sprite;
      
      public var js4:Sprite;
      
      public var js5:Sprite;
      
      public var js6:Sprite;
      
      public var js7:Sprite;
      
      public var js8:Sprite;
      
      public var js9:Sprite;
      
      public var js10:Sprite;
      
      public var js11:Sprite;
      
      public var js12:Sprite;
      
      public var js13:Sprite;
      
      public var js14:Sprite;
      
      public var js15:Sprite;
      
      public var js16:Sprite;
      
      public var js17:Sprite;
      
      public var js18:Sprite;
      
      public var js19:Sprite;
      
      public var js20:Sprite;
      
      public var js21:Sprite;
      
      public var js22:Sprite;
      
      public var js23:Sprite;
      
      public var js24:Sprite;
      
      public var js25:Sprite;
      
      public var js26:Sprite;
      
      public var js27:Sprite;
      
      public var js28:Sprite;
      
      public var js29:Sprite;
      
      public var js30:Sprite;
      
      public var js31:Sprite;
      
      public var js32:Sprite;
      
      public var js33:Sprite;
      
      public var js34:Sprite;
      
      public var js35:Sprite;
      
      public var js36:TextField;
      
      public var js37:Sprite;
      
      public var js38:Sprite;
      
      public var js39:Sprite;
      
      public var js40:Sprite;
      
      public var js41:Sprite;
      
      public var js42:Sprite;
      
      public var js43:Sprite;
      
      public var js45:Sprite;
      
      public var js46:Sprite;
      
      public var cw1:TextField;
      
      public var js44:TextField;
      
      public var cw2:Sprite;
      
      public var cw3:Sprite;
      
      public var cw4:TextField;
      
      public var cw5:Sprite;
      
      public var cw6:TextField;
      
      public var cw7:Sprite;
      
      public var cw8:TextField;
      
      public var cw9:Sprite;
      
      public var cw10:Sprite;
      
      public var cw11:TextField;
      
      public var cw12:Sprite;
      
      public var cw13:Sprite;
      
      public var cw14:TextField;
      
      public var cw15:TextField;
      
      public var cw16:Sprite;
      
      public var cw17:Sprite;
      
      public var cw18:Sprite;
      
      public var cw19:TextField;
      
      public var cw20:TextField;
      
      public var cw21:TextField;
      
      public var cw22:TextField;
      
      public var cw23:TextField;
      
      public var cw24:Sprite;
      
      public var cw25:TextField;
      
      public var zbzdy34:Sprite;
      
      public var zbzdy1:TextField;
      
      public var zbzdy2:TextField;
      
      public var zbzdy3:TextField;
      
      public var zbzdy4:TextField;
      
      public var zbzdy5:TextField;
      
      public var zbzdy6:TextField;
      
      public var zbzdy7:TextField;
      
      public var zbzdy8:TextField;
      
      public var zbzdy9:TextField;
      
      public var zbzdy10:TextField;
      
      public var zbzdy11:TextField;
      
      public var zbzdy12:TextField;
      
      public var zbzdy13:TextField;
      
      public var zbzdy14:TextField;
      
      public var zbzdy15:TextField;
      
      public var zbzdy16:TextField;
      
      public var zbzdy17:TextField;
      
      public var zbzdy18:TextField;
      
      public var zbzdy19:TextField;
      
      public var zbzdy20:TextField;
      
      public var zbzdy21:TextField;
      
      public var zbzdy22:TextField;
      
      public var zbzdy23:TextField;
      
      public var zbzdy24:TextField;
      
      public var zbzdy25:TextField;
      
      public var zbzdy26:TextField;
      
      public var zbzdy27:TextField;
      
      public var zbzdy28:TextField;
      
      public var zbzdy29:TextField;
      
      public var zbzdy30:TextField;
      
      public var zbzdy31:TextField;
      
      public var zbzdy32:TextField;
      
      public var zbzdy33:TextField;
      
      public var zbzdy35:TextField;
      
      public var zbzdy36:TextField;
      
      public var zbzdy37:TextField;
      
      public var szs1:Sprite;
      
      public var szs2:Sprite;
      
      public var szs3:Sprite;
      
      public var szs4:Sprite;
      
      public var szs5:Sprite;
      
      public var szs6:Sprite;
      
      public var szs7:Sprite;
      
      public var szs8:Sprite;
      
      public var szs9:Sprite;
      
      public var szs10:Sprite;
      
      public var szs11:Sprite;
      
      public var szs12:Sprite;
      
      public var szs13:Sprite;
      
      public var szs14:Sprite;
      
      public var szs15:Sprite;
      
      public var saveEditorBtn:Sprite;
      
      internal var _loc15_:int = 0;
      
      internal var _loc22_:int = 0;
      
      public function GM()
      {
         super();
      }
      
      public static function drawButton(param1:String, param2:uint = 0xFF69B4, param3:int = 100, param4:int = 30) : Sprite
      {
         var _loc2_:Sprite = new Sprite();

         // 创建渐变背景
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(param3, param4, Math.PI/2, 0, 0);

         // 可爱的渐变色彩
         var colors:Array = [param2, adjustBrightness(param2, -0.3)];
         var alphas:Array = [1, 1];
         var ratios:Array = [0, 255];

         _loc2_.graphics.beginGradientFill("linear", colors, alphas, ratios, matrix);
         _loc2_.graphics.lineStyle(2, 0xFFFFFF, 0.8);
         _loc2_.graphics.drawRoundRect(0, 0, param3, param4, 15, 15);
         _loc2_.graphics.endFill();

         // 添加内发光效果
         _loc2_.graphics.lineStyle(1, adjustBrightness(param2, 0.4), 0.6);
         _loc2_.graphics.drawRoundRect(1, 1, param3-2, param4-2, 14, 14);

         _loc2_.buttonMode = true;
         _loc2_.useHandCursor = true;

         var _loc3_:TextField = new TextField();
         _loc3_.width = param3 - 10;
         _loc3_.height = param4 - 6;
         _loc3_.x = 5;
         _loc3_.y = 3;
         _loc3_.autoSize = TextFieldAutoSize.CENTER;
         _loc3_.textColor = 0xFFFFFF;
         _loc3_.text = param1;
         _loc3_.mouseEnabled = false;

         // 设置可爱字体样式
         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = 12;
         format.bold = true;
         format.align = "center";
         _loc3_.setTextFormat(format);

         _loc2_.addChild(_loc3_);

         // 添加悬停效果
         _loc2_.addEventListener(MouseEvent.MOUSE_OVER, function(e:MouseEvent):void {
            _loc2_.scaleX = _loc2_.scaleY = 1.05;
            _loc2_.filters = [new GlowFilter(0xFFFFFF, 0.8, 8, 8, 2, 1)];
         });

         _loc2_.addEventListener(MouseEvent.MOUSE_OUT, function(e:MouseEvent):void {
            _loc2_.scaleX = _loc2_.scaleY = 1.0;
            _loc2_.filters = [];
         });

         return _loc2_;
      }

      // 辅助函数：调整颜色亮度
      private static function adjustBrightness(color:uint, factor:Number):uint {
         var r:uint = (color >> 16) & 0xFF;
         var g:uint = (color >> 8) & 0xFF;
         var b:uint = color & 0xFF;

         r = Math.max(0, Math.min(255, r + (255 - r) * factor));
         g = Math.max(0, Math.min(255, g + (255 - g) * factor));
         b = Math.max(0, Math.min(255, b + (255 - b) * factor));

         return (r << 16) | (g << 8) | b;
      }
      
      public static function xxj() : *
      {
         trace("GM.xxj() 开始初始化GM系统");

         // 防止重复初始化
         if(gmInitialized) {
            trace("GM系统已经初始化，跳过");
            return;
         }

         // 延迟初始化，确保Main._stage已经准备好
         if(Main._stage) {
            trace("Main._stage已准备好，立即初始化");
            gmInstance = new GM();
            gmInstance.xxj1();
            gmInitialized = true;
            trace("GM系统初始化完成");
         } else {
            trace("Main._stage未准备好，延迟初始化");
            // 如果stage还没准备好，延迟初始化
            var timer:Timer = new Timer(100, 10); // 100ms间隔，最多尝试10次
            timer.addEventListener(TimerEvent.TIMER, function(e:TimerEvent):void {
               if(Main._stage) {
                  timer.stop();
                  trace("延迟初始化：Main._stage已准备好");
                  gmInstance = new GM();
                  gmInstance.xxj1();
                  gmInitialized = true;
                  trace("GM系统延迟初始化完成");
               }
            });
            timer.start();
         }
      }

      // 强制初始化GM系统（用于游戏完全加载后调用）
      public static function forceInit() : *
      {
         initAttempts++;
         trace("GM.forceInit() 强制初始化GM系统 (尝试 #" + initAttempts + ")");

         if(!gmInitialized && Main._stage) {
            trace("执行强制初始化");
            gmInstance = new GM();
            gmInstance.xxj1();
            gmInitialized = true;
            trace("GM系统强制初始化完成");
         } else if(gmInitialized) {
            trace("GM系统已初始化");
         } else {
            trace("Main._stage仍未准备好，无法强制初始化");
         }
      }

      // 智能初始化 - 可以从任何地方调用
      public static function smartInit() : *
      {
         trace("GM.smartInit() 智能初始化");

         if(gmInitialized) {
            trace("GM系统已经初始化");
            return;
         }

         if(Main._stage) {
            trace("Main._stage可用，立即初始化");
            forceInit();
         } else {
            trace("Main._stage不可用，设置延迟初始化");
            // 使用更频繁的检查
            var timer:Timer = new Timer(50, 20); // 50ms间隔，最多尝试20次
            timer.addEventListener(TimerEvent.TIMER, function(e:TimerEvent):void {
               if(Main._stage) {
                  timer.stop();
                  trace("延迟初始化成功");
                  forceInit();
               } else {
                  trace("等待Main._stage... (剩余尝试: " + (20 - timer.currentCount) + ")");
               }
            });
            timer.start();
         }
      }

      // 全局可访问的初始化方法（可从浏览器控制台调用）
      public static function globalInit() : *
      {
         trace("GM.globalInit() 全局初始化被调用");

         try {
            // 尝试通过ExternalInterface暴露给JavaScript
            if(ExternalInterface.available) {
               ExternalInterface.addCallback("initGM", smartInit);
               ExternalInterface.addCallback("toggleGM", toggleGMPanel);
               trace("GM方法已暴露给JavaScript");
            }
         } catch(e:Error) {
            trace("ExternalInterface不可用: " + e.message);
         }

         smartInit();
      }

      // 切换GM界面的全局方法
      public static function toggleGMPanel() : *
      {
         trace("GM.toggleGMPanel() 被调用");

         if(!gmInitialized) {
            trace("GM系统未初始化，先初始化");
            smartInit();
            return;
         }

         try {
            if(!cuteGMPanel) {
               trace("创建GM界面");
               cuteGMPanel = new SimpleCuteGMPanel(); // 直接使用简化版
               Main._stage.addChild(cuteGMPanel);
               Main._stage.setChildIndex(cuteGMPanel, Main._stage.numChildren - 1);
               trace("GM界面创建完成");
            }
            cuteGMPanel.toggle();
         } catch(error:Error) {
            trace("GM界面操作失败: " + error.message);
         }
      }

      public function xxj1() : *
      {
         trace("GM.xxj1() 开始详细初始化");

         var available:Boolean;
         this.b9 = new TextField();
         this.b9.x = -300;
         this.b9.y = -300;
         this.b9.width = 100;
         this.b9.height = 30;
         this.b9.background = true;
         this.b9.backgroundColor = 3355443;
         this.b9.border = true;
         this.b9.borderColor = 65535;
         this.b9.type = "input";
         this.b9.textColor = 65535;
         this.b9.text = "1";

         trace("添加键盘事件监听器到Main._stage，使用高优先级");
         // 使用高优先级确保GM键盘事件不被其他系统拦截
         Main._stage.addEventListener(KeyboardEvent.KEY_DOWN, this.按键, false, 1000);
         trace("键盘事件监听器添加成功，优先级：1000");
         if(ExternalInterface.available)
         {
            try
            {
               ExternalInterface.addCallback("一键宠物",this.一键宠物JS);
               ExternalInterface.addCallback("成就全亮",this.成就全亮JS);
               ExternalInterface.addCallback("过检测",this.过检测JS);
               ExternalInterface.addCallback("四职业技能",this.四职业技能JS);
               ExternalInterface.addCallback("精灵槽解锁",this.精灵槽解锁JS);
               ExternalInterface.addCallback("特殊栏解锁",this.特殊栏解锁JS);
               ExternalInterface.addCallback("宠栏解锁",this.宠栏解锁JS);
               ExternalInterface.addCallback("背包解锁",this.背包解锁JS);
               ExternalInterface.addCallback("一键技能石",this.一键技能石JS);
               ExternalInterface.addCallback("一键道具",this.一键道具JS);
               ExternalInterface.addCallback("一键消耗品",this.一键消耗品JS);
               ExternalInterface.addCallback("一键宠物装备",this.一键宠物装备JS);
               ExternalInterface.addCallback("一键称号",this.一键称号JS);
               ExternalInterface.addCallback("一键精灵",this.一键精灵JS);
               ExternalInterface.addCallback("一键装备",this.一键装备JS);
               trace("ExternalInterface回调已添加: 所有功能");
               available = ExternalInterface.call("function(){return true;}");
               trace("ExternalInterface可用性: " + available);
            }
            catch(e:Error)
            {
               trace("添加ExternalInterface回调时出错: " + e.message);
            }
         }
         else
         {
            trace("ExternalInterface不可用");
         }
      }
      
      public function 按键(e:KeyboardEvent) : void
      {
         trace("GM按键检测: keyCode = " + e.keyCode);

         if(e.keyCode == 192)  // ` 键 - 开启可爱GM界面
         {
            trace("检测到 ` 键，尝试打开可爱GM界面");
            try {
               if(!cuteGMPanel) {
                  trace("尝试创建完整版CuteGMPanel");
                  cuteGMPanel = new CuteGMPanel();
                  Main._stage.addChild(cuteGMPanel);
                  // 确保GM界面在最顶层
                  Main._stage.setChildIndex(cuteGMPanel, Main._stage.numChildren - 1);
                  trace("完整版CuteGMPanel已添加到舞台顶层");
               } else {
                  // 如果已存在，确保在顶层
                  if(cuteGMPanel.parent == Main._stage) {
                     Main._stage.setChildIndex(cuteGMPanel, Main._stage.numChildren - 1);
                     trace("GM界面已移至顶层");
                  }
               }
               cuteGMPanel.toggle();
               trace("完整版GM界面操作完成");
            } catch(error:Error) {
               trace("完整版GM界面失败: " + error.message);
               trace("尝试使用简化版GM界面");

               try {
                  // 如果完整版失败，使用简化版
                  if(!cuteGMPanel) {
                     trace("创建简化版SimpleCuteGMPanel");
                     cuteGMPanel = new SimpleCuteGMPanel();
                     Main._stage.addChild(cuteGMPanel);
                     // 确保简化版也在最顶层
                     Main._stage.setChildIndex(cuteGMPanel, Main._stage.numChildren - 1);
                     trace("简化版SimpleCuteGMPanel已添加到舞台顶层");
                  } else {
                     // 如果已存在，确保在顶层
                     if(cuteGMPanel.parent == Main._stage) {
                        Main._stage.setChildIndex(cuteGMPanel, Main._stage.numChildren - 1);
                        trace("简化版GM界面已移至顶层");
                     }
                  }
                  cuteGMPanel.toggle();
                  trace("简化版GM界面操作完成");
               } catch(error2:Error) {
                  trace("简化版GM界面也失败: " + error2.message);
                  trace("所有GM界面方案都失败了");
               }
            }
         }
         var _loc12_:Enemy = null;
         var _loc10_:* = 0;
         if(e.keyCode == 86)
         {
            for(_loc10_ in Enemy.All)
            {
               _loc12_ = Enemy.All[_loc10_];
               _loc12_.HpXX2(1410065407);
            }
         }
         if(e.keyCode == 88)
         {
            CardPanel.monsterSlot.addMonsterSlot(213);
         }
      }
      
      public function 开启后台(e:MouseEvent) : void
      {
         Main._stage.removeChild(this.b1);
         this.b2 = new Sprite();
         this.b2 = drawButton("关闭界面");
         this.b2.width = 50;
         this.b2.height = 30;
         this.b2.x = 400;
         this.b2.y = 5;
         Main._stage.addChild(this.b2);
         this.b2.addEventListener(MouseEvent.CLICK,this.关闭界面);
         this.b3 = new Sprite();
         this.b3 = drawButton("角色相关");
         this.b3.width = 50;
         this.b3.height = 30;
         this.b3.x = 460;
         this.b3.y = 5;
         Main._stage.addChild(this.b3);
         this.b3.addEventListener(MouseEvent.CLICK,this.角色相关);
         this.b4 = new Sprite();
         this.b4 = drawButton("装备相关");
         this.b4.width = 50;
         this.b4.height = 30;
         this.b4.x = 515;
         this.b4.y = 5;
         Main._stage.addChild(this.b4);
         this.b4.addEventListener(MouseEvent.CLICK,this.装备相关);
         this.b5 = new Sprite();
         this.b5 = drawButton("宠物相关");
         this.b5.width = 50;
         this.b5.height = 30;
         this.b5.x = 570;
         this.b5.y = 5;
         Main._stage.addChild(this.b5);
         this.b5.addEventListener(MouseEvent.CLICK,this.宠物相关);
         this.b6 = new Sprite();
         this.b6 = drawButton("杂项相关");
         this.b6.width = 50;
         this.b6.height = 30;
         this.b6.x = 625;
         this.b6.y = 5;
         Main._stage.addChild(this.b6);
         this.b6.addEventListener(MouseEvent.CLICK,this.杂项相关);
         this.b7 = new Sprite();
         this.b7 = drawButton("测试专用");
         this.b7.width = 50;
         this.b7.height = 30;
         this.b7.x = 695;
         this.b7.y = 5;
         Main._stage.addChild(this.b7);
         this.b7.addEventListener(MouseEvent.CLICK,this.测试专用);
         this.saveEditorBtn = new Sprite();
         this.saveEditorBtn = drawButton("存档编辑器");
         this.saveEditorBtn.width = 80;
         this.saveEditorBtn.height = 30;
         this.saveEditorBtn.x = 750;
         this.saveEditorBtn.y = 5;
         Main._stage.addChild(this.saveEditorBtn);
         this.saveEditorBtn.addEventListener(MouseEvent.CLICK,this.toggleSaveEditor);
         if(!saveEditor)
         {
            saveEditor = new SaveEditor();
            Main._stage.addChild(saveEditor);
         }
         this.b8 = new TextField();
         this.b8.x = 850;
         this.b8.y = 5;
         this.b8.width = 100;
         this.b8.height = 30;
         this.b8.background = true;
         this.b8.backgroundColor = 3355443;
         this.b8.border = true;
         this.b8.borderColor = 65535;
         this.b8.type = "input";
         this.b8.textColor = 65535;
         this.b8.text = "测试";
         Main._stage.addChild(this.b8);
         this.界面1 = new XX_MC();
         Main._stage.addChild(this.界面1);
         this.界面2 = new XX_MC();
         Main._stage.addChild(this.界面2);
         this.界面3 = new XX_MC();
         Main._stage.addChild(this.界面3);
         this.界面4 = new XX_MC();
         Main._stage.addChild(this.界面4);
         this.界面5 = new XX_MC();
         Main._stage.addChild(this.界面5);
      }
      
      public function 关闭界面(e:MouseEvent) : void
      {
         Main._stage.removeChild(this.b2);
         Main._stage.removeChild(this.b3);
         Main._stage.removeChild(this.b4);
         Main._stage.removeChild(this.b5);
         Main._stage.removeChild(this.b6);
         Main._stage.removeChild(this.b7);
         Main._stage.removeChild(this.saveEditorBtn);
         Main._stage.removeChild(this.b8);
         Main._stage.removeChild(this.界面1);
         Main._stage.removeChild(this.界面2);
         Main._stage.removeChild(this.界面3);
         Main._stage.removeChild(this.界面4);
         Main._stage.removeChild(this.界面5);
         if(saveEditor)
         {
            saveEditor.hide();
         }
      }
      
      public function 角色相关(e:MouseEvent) : void
      {
         this.界面2.visible = false;
         this.界面3.visible = false;
         this.界面4.visible = false;
         this.界面5.visible = false;
         this.界面1.visible = true;
         this.dj1 = new TextField();
         this.dj1.x = 400;
         this.dj1.y = 45;
         this.dj1.width = 50;
         this.dj1.height = 25;
         this.dj1.background = true;
         this.dj1.border = true;
         this.dj1.type = "input";
         this.dj1.textColor = 16711680;
         this.dj1.text = "道具ID";
         this.界面1.addChild(this.dj1);
         this.dj2 = new TextField();
         this.dj2.x = 455;
         this.dj2.y = 45;
         this.dj2.width = 50;
         this.dj2.height = 25;
         this.dj2.background = true;
         this.dj2.border = true;
         this.dj2.type = "input";
         this.dj2.textColor = 16711680;
         this.dj2.text = "byte";
         this.界面1.addChild(this.dj2);
         this.dj3 = new Sprite();
         this.dj3 = drawButton("1P添加");
         this.dj3.width = 50;
         this.dj3.height = 25;
         this.dj3.x = 510;
         this.dj3.y = 45;
         this.界面1.addChild(this.dj3);
         this.dj3.addEventListener(MouseEvent.CLICK,this.添加道具1P);
         this.dj4 = new Sprite();
         this.dj4 = drawButton("2P添加");
         this.dj4.width = 50;
         this.dj4.height = 25;
         this.dj4.x = 565;
         this.dj4.y = 45;
         this.界面1.addChild(this.dj4);
         this.dj4.addEventListener(MouseEvent.CLICK,this.添加道具2P);
         this.bs1 = new TextField();
         this.bs1.x = 400;
         this.bs1.y = 75;
         this.bs1.width = 50;
         this.bs1.height = 25;
         this.bs1.background = true;
         this.bs1.border = true;
         this.bs1.type = "input";
         this.bs1.textColor = 16711680;
         this.bs1.text = "宝石ID";
         this.界面1.addChild(this.bs1);
         this.bs2 = new TextField();
         this.bs2.x = 455;
         this.bs2.y = 75;
         this.bs2.width = 50;
         this.bs2.height = 25;
         this.bs2.background = true;
         this.bs2.border = true;
         this.bs2.type = "input";
         this.bs2.textColor = 16711680;
         this.bs2.text = "byte";
         this.界面1.addChild(this.bs2);
         this.bs3 = new Sprite();
         this.bs3 = drawButton("1P添加");
         this.bs3.width = 50;
         this.bs3.height = 25;
         this.bs3.x = 510;
         this.bs3.y = 75;
         this.界面1.addChild(this.bs3);
         this.bs3.addEventListener(MouseEvent.CLICK,this.添加宝石1P);
         this.bs4 = new Sprite();
         this.bs4 = drawButton("2P添加");
         this.bs4.width = 50;
         this.bs4.height = 25;
         this.bs4.x = 565;
         this.bs4.y = 75;
         this.界面1.addChild(this.bs4);
         this.bs4.addEventListener(MouseEvent.CLICK,this.添加宝石2P);
         this.xhp1 = new TextField();
         this.xhp1.x = 400;
         this.xhp1.y = 105;
         this.xhp1.width = 50;
         this.xhp1.height = 25;
         this.xhp1.background = true;
         this.xhp1.border = true;
         this.xhp1.type = "input";
         this.xhp1.textColor = 16711680;
         this.xhp1.text = "消耗品ID";
         this.界面1.addChild(this.xhp1);
         this.xhp2 = new TextField();
         this.xhp2.x = 455;
         this.xhp2.y = 105;
         this.xhp2.width = 50;
         this.xhp2.height = 25;
         this.xhp2.background = true;
         this.xhp2.border = true;
         this.xhp2.type = "input";
         this.xhp2.textColor = 16711680;
         this.xhp2.text = "byte";
         this.界面1.addChild(this.xhp2);
         this.xhp3 = new Sprite();
         this.xhp3 = drawButton("1P添加");
         this.xhp3.width = 50;
         this.xhp3.height = 25;
         this.xhp3.x = 510;
         this.xhp3.y = 105;
         this.界面1.addChild(this.xhp3);
         this.xhp3.addEventListener(MouseEvent.CLICK,this.添加消耗品1P);
         this.xhp4 = new Sprite();
         this.xhp4 = drawButton("2P添加");
         this.xhp4.width = 50;
         this.xhp4.height = 25;
         this.xhp4.x = 565;
         this.xhp4.y = 105;
         this.界面1.addChild(this.xhp4);
         this.xhp4.addEventListener(MouseEvent.CLICK,this.添加消耗品2P);
         this.rw1 = new TextField();
         this.rw1.x = 400;
         this.rw1.y = 135;
         this.rw1.width = 100;
         this.rw1.height = 25;
         this.rw1.background = true;
         this.rw1.border = true;
         this.rw1.type = "input";
         this.rw1.textColor = 16711884;
         this.rw1.text = "任务物品掉落等级";
         this.界面1.addChild(this.rw1);
         this.rw3 = new Sprite();
         this.rw3 = drawButton("1P添加");
         this.rw3.width = 50;
         this.rw3.height = 25;
         this.rw3.x = 510;
         this.rw3.y = 135;
         this.界面1.addChild(this.rw3);
         this.rw3.addEventListener(MouseEvent.CLICK,this.添加任务物品1P);
         this.rw4 = new Sprite();
         this.rw4 = drawButton("2P添加");
         this.rw4.width = 50;
         this.rw4.height = 25;
         this.rw4.x = 565;
         this.rw4.y = 135;
         this.界面1.addChild(this.rw4);
         this.rw4.addEventListener(MouseEvent.CLICK,this.添加任务物品2P);
         this.zdy1 = new TextField();
         this.zdy1.x = 400;
         this.zdy1.y = 165;
         this.zdy1.width = 50;
         this.zdy1.height = 25;
         this.zdy1.background = true;
         this.zdy1.border = true;
         this.zdy1.type = "input";
         this.zdy1.textColor = 16737792;
         this.zdy1.text = "等级";
         this.界面1.addChild(this.zdy1);
         this.zdy2 = new Sprite();
         this.zdy2 = drawButton("1P");
         this.zdy2.width = 23;
         this.zdy2.height = 25;
         this.zdy2.x = 455;
         this.zdy2.y = 165;
         this.界面1.addChild(this.zdy2);
         this.zdy2.addEventListener(MouseEvent.CLICK,this.自定义等级);
         this.zdy26 = new Sprite();
         this.zdy26 = drawButton("2P");
         this.zdy26.width = 23;
         this.zdy26.height = 25;
         this.zdy26.x = 482;
         this.zdy26.y = 165;
         this.界面1.addChild(this.zdy26);
         this.zdy26.addEventListener(MouseEvent.CLICK,this.自定义等级2);
         this.zdy3 = new TextField();
         this.zdy3.x = 515;
         this.zdy3.y = 165;
         this.zdy3.width = 50;
         this.zdy3.height = 25;
         this.zdy3.background = true;
         this.zdy3.border = true;
         this.zdy3.type = "input";
         this.zdy3.textColor = 16737792;
         this.zdy3.text = "金币";
         this.界面1.addChild(this.zdy3);
         this.zdy4 = new Sprite();
         this.zdy4 = drawButton("1P");
         this.zdy4.width = 23;
         this.zdy4.height = 25;
         this.zdy4.x = 570;
         this.zdy4.y = 165;
         this.界面1.addChild(this.zdy4);
         this.zdy4.addEventListener(MouseEvent.CLICK,this.自定义金币);
         this.zdy27 = new Sprite();
         this.zdy27 = drawButton("2P");
         this.zdy27.width = 23;
         this.zdy27.height = 25;
         this.zdy27.x = 597;
         this.zdy27.y = 165;
         this.界面1.addChild(this.zdy27);
         this.zdy27.addEventListener(MouseEvent.CLICK,this.自定义金币2);
         this.zdy5 = new TextField();
         this.zdy5.x = 400;
         this.zdy5.y = 195;
         this.zdy5.width = 50;
         this.zdy5.height = 25;
         this.zdy5.background = true;
         this.zdy5.border = true;
         this.zdy5.type = "input";
         this.zdy5.textColor = 16737792;
         this.zdy5.text = "击杀点";
         this.界面1.addChild(this.zdy5);
         this.zdy6 = new Sprite();
         this.zdy6 = drawButton("1P");
         this.zdy6.width = 23;
         this.zdy6.height = 25;
         this.zdy6.x = 455;
         this.zdy6.y = 195;
         this.界面1.addChild(this.zdy6);
         this.zdy6.addEventListener(MouseEvent.CLICK,this.自定义击杀点);
         this.zdy28 = new Sprite();
         this.zdy28 = drawButton("2P");
         this.zdy28.width = 23;
         this.zdy28.height = 25;
         this.zdy28.x = 482;
         this.zdy28.y = 195;
         this.界面1.addChild(this.zdy28);
         this.zdy28.addEventListener(MouseEvent.CLICK,this.自定义击杀点2);
         this.zdy7 = new TextField();
         this.zdy7.x = 515;
         this.zdy7.y = 195;
         this.zdy7.width = 50;
         this.zdy7.height = 25;
         this.zdy7.background = true;
         this.zdy7.border = true;
         this.zdy7.type = "input";
         this.zdy7.textColor = 16737792;
         this.zdy7.text = "技能点";
         this.界面1.addChild(this.zdy7);
         this.zdy8 = new Sprite();
         this.zdy8 = drawButton("1P");
         this.zdy8.width = 23;
         this.zdy8.height = 25;
         this.zdy8.x = 570;
         this.zdy8.y = 195;
         this.界面1.addChild(this.zdy8);
         this.zdy8.addEventListener(MouseEvent.CLICK,this.自定义技能点);
         this.zdy29 = new Sprite();
         this.zdy29 = drawButton("2P");
         this.zdy29.width = 23;
         this.zdy29.height = 25;
         this.zdy29.x = 597;
         this.zdy29.y = 195;
         this.界面1.addChild(this.zdy29);
         this.zdy29.addEventListener(MouseEvent.CLICK,this.自定义技能点2);
         this.zdy9 = new TextField();
         this.zdy9.x = 400;
         this.zdy9.y = 225;
         this.zdy9.width = 50;
         this.zdy9.height = 25;
         this.zdy9.background = true;
         this.zdy9.border = true;
         this.zdy9.type = "input";
         this.zdy9.textColor = 16737792;
         this.zdy9.text = "成就点";
         this.界面1.addChild(this.zdy9);
         this.zdy10 = new Sprite();
         this.zdy10 = drawButton("自定义");
         this.zdy10.width = 50;
         this.zdy10.height = 25;
         this.zdy10.x = 455;
         this.zdy10.y = 225;
         this.界面1.addChild(this.zdy10);
         this.zdy10.addEventListener(MouseEvent.CLICK,this.自定义成就点);
         this.zdy11 = new TextField();
         this.zdy11.x = 515;
         this.zdy11.y = 225;
         this.zdy11.width = 50;
         this.zdy11.height = 25;
         this.zdy11.background = true;
         this.zdy11.border = true;
         this.zdy11.type = "input";
         this.zdy11.textColor = 16737792;
         this.zdy11.text = "竞技积分";
         this.界面1.addChild(this.zdy11);
         this.zdy12 = new Sprite();
         this.zdy12 = drawButton("自定义");
         this.zdy12.width = 50;
         this.zdy12.height = 25;
         this.zdy12.x = 570;
         this.zdy12.y = 225;
         this.界面1.addChild(this.zdy12);
         this.zdy12.addEventListener(MouseEvent.CLICK,this.自定义竞技积分);
         this.zdy13 = new TextField();
         this.zdy13.x = 400;
         this.zdy13.y = 255;
         this.zdy13.width = 50;
         this.zdy13.height = 25;
         this.zdy13.background = true;
         this.zdy13.border = true;
         this.zdy13.type = "input";
         this.zdy13.textColor = 16737792;
         this.zdy13.text = "掉落时间";
         this.界面1.addChild(this.zdy13);
         this.zdy14 = new Sprite();
         this.zdy14 = drawButton("自定义");
         this.zdy14.width = 50;
         this.zdy14.height = 25;
         this.zdy14.x = 455;
         this.zdy14.y = 255;
         this.界面1.addChild(this.zdy14);
         this.zdy14.addEventListener(MouseEvent.CLICK,this.自定义掉落时间);
         this.zdy15 = new TextField();
         this.zdy15.x = 515;
         this.zdy15.y = 255;
         this.zdy15.width = 50;
         this.zdy15.height = 25;
         this.zdy15.background = true;
         this.zdy15.border = true;
         this.zdy15.type = "input";
         this.zdy15.textColor = 16737792;
         this.zdy15.text = "经验时间";
         this.界面1.addChild(this.zdy15);
         this.zdy16 = new Sprite();
         this.zdy16 = drawButton("自定义");
         this.zdy16.width = 50;
         this.zdy16.height = 25;
         this.zdy16.x = 570;
         this.zdy16.y = 255;
         this.界面1.addChild(this.zdy16);
         this.zdy16.addEventListener(MouseEvent.CLICK,this.自定义经验时间);
         this.zdy17 = new TextField();
         this.zdy17.x = 400;
         this.zdy17.y = 285;
         this.zdy17.width = 50;
         this.zdy17.height = 25;
         this.zdy17.background = true;
         this.zdy17.border = true;
         this.zdy17.type = "input";
         this.zdy17.textColor = 16737792;
         this.zdy17.text = "炼狱BUFF";
         this.界面1.addChild(this.zdy17);
         this.zdy18 = new Sprite();
         this.zdy18 = drawButton("1P");
         this.zdy18.width = 23;
         this.zdy18.height = 25;
         this.zdy18.x = 455;
         this.zdy18.y = 285;
         this.界面1.addChild(this.zdy18);
         this.zdy18.addEventListener(MouseEvent.CLICK,this.自定义炼狱BUFF);
         this.zdy30 = new Sprite();
         this.zdy30 = drawButton("2P");
         this.zdy30.width = 23;
         this.zdy30.height = 25;
         this.zdy30.x = 482;
         this.zdy30.y = 285;
         this.界面1.addChild(this.zdy30);
         this.zdy30.addEventListener(MouseEvent.CLICK,this.自定义炼狱BUFF2);
         this.zdy19 = new TextField();
         this.zdy19.x = 515;
         this.zdy19.y = 285;
         this.zdy19.width = 50;
         this.zdy19.height = 25;
         this.zdy19.background = true;
         this.zdy19.border = true;
         this.zdy19.type = "input";
         this.zdy19.textColor = 16737792;
         this.zdy19.text = "竞技次数";
         this.界面1.addChild(this.zdy19);
         this.zdy20 = new Sprite();
         this.zdy20 = drawButton("自定义");
         this.zdy20.width = 50;
         this.zdy20.height = 25;
         this.zdy20.x = 570;
         this.zdy20.y = 285;
         this.界面1.addChild(this.zdy20);
         this.zdy20.addEventListener(MouseEvent.CLICK,this.自定义竞技次数);
         this.zdy21 = new TextField();
         this.zdy21.x = 400;
         this.zdy21.y = 315;
         this.zdy21.width = 50;
         this.zdy21.height = 25;
         this.zdy21.background = true;
         this.zdy21.border = true;
         this.zdy21.type = "input";
         this.zdy21.textColor = 16737792;
         this.zdy21.text = "符石";
         this.界面1.addChild(this.zdy21);
         this.zdy22 = new Sprite();
         this.zdy22 = drawButton("符石1");
         this.zdy22.width = 40;
         this.zdy22.height = 25;
         this.zdy22.x = 455;
         this.zdy22.y = 315;
         this.界面1.addChild(this.zdy22);
         this.zdy22.addEventListener(MouseEvent.CLICK,this.符石1);
         this.zdy23 = new Sprite();
         this.zdy23 = drawButton("符石2");
         this.zdy23.width = 40;
         this.zdy23.height = 25;
         this.zdy23.x = 500;
         this.zdy23.y = 315;
         this.界面1.addChild(this.zdy23);
         this.zdy23.addEventListener(MouseEvent.CLICK,this.符石2);
         this.zdy24 = new Sprite();
         this.zdy24 = drawButton("符石3");
         this.zdy24.width = 40;
         this.zdy24.height = 25;
         this.zdy24.x = 545;
         this.zdy24.y = 315;
         this.界面1.addChild(this.zdy24);
         this.zdy24.addEventListener(MouseEvent.CLICK,this.符石3);
         this.zdy25 = new Sprite();
         this.zdy25 = drawButton("符石4");
         this.zdy25.width = 40;
         this.zdy25.height = 25;
         this.zdy25.x = 590;
         this.zdy25.y = 315;
         this.界面1.addChild(this.zdy25);
         this.zdy25.addEventListener(MouseEvent.CLICK,this.符石4);
         this.js5 = new Sprite();
         this.js5 = drawButton("清空背包");
         this.js5.width = 50;
         this.js5.height = 25;
         this.js5.x = 635;
         this.js5.y = 45;
         this.界面1.addChild(this.js5);
         this.js5.addEventListener(MouseEvent.CLICK,this.清空背包);
         this.js6 = new Sprite();
         this.js6 = drawButton("清空仓库");
         this.js6.width = 50;
         this.js6.height = 25;
         this.js6.x = 695;
         this.js6.y = 45;
         this.界面1.addChild(this.js6);
         this.js6.addEventListener(MouseEvent.CLICK,this.清空仓库);
         this.js7 = new Sprite();
         this.js7 = drawButton("关卡解锁");
         this.js7.width = 50;
         this.js7.height = 25;
         this.js7.x = 755;
         this.js7.y = 45;
         this.界面1.addChild(this.js7);
         this.js7.addEventListener(MouseEvent.CLICK,this.关卡解锁);
         this.js8 = new Sprite();
         this.js8 = drawButton("图鉴添加");
         this.js8.width = 50;
         this.js8.height = 25;
         this.js8.x = 635;
         this.js8.y = 75;
         this.界面1.addChild(this.js8);
         this.js8.addEventListener(MouseEvent.CLICK,this.图鉴添加);
         this.js9 = new Sprite();
         this.js9 = drawButton("悬赏全满");
         this.js9.width = 50;
         this.js9.height = 25;
         this.js9.x = 695;
         this.js9.y = 75;
         this.界面1.addChild(this.js9);
         this.js9.addEventListener(MouseEvent.CLICK,this.悬赏全满);
         this.js10 = new Sprite();
         this.js10 = drawButton("背包解锁");
         this.js10.width = 50;
         this.js10.height = 25;
         this.js10.x = 755;
         this.js10.y = 75;
         this.界面1.addChild(this.js10);
         this.js10.addEventListener(MouseEvent.CLICK,this.背包解锁);
         this.js11 = new Sprite();
         this.js11 = drawButton("宠栏解锁");
         this.js11.width = 50;
         this.js11.height = 25;
         this.js11.x = 635;
         this.js11.y = 105;
         this.界面1.addChild(this.js11);
         this.js11.addEventListener(MouseEvent.CLICK,this.宠栏解锁);
         this.js12 = new Sprite();
         this.js12 = drawButton("印章解锁");
         this.js12.width = 50;
         this.js12.height = 25;
         this.js12.x = 695;
         this.js12.y = 105;
         this.界面1.addChild(this.js12);
         this.js12.addEventListener(MouseEvent.CLICK,this.印章解锁);
         this.js13 = new Sprite();
         this.js13 = drawButton("采药全满");
         this.js13.width = 50;
         this.js13.height = 25;
         this.js13.x = 755;
         this.js13.y = 105;
         this.界面1.addChild(this.js13);
         this.js13.addEventListener(MouseEvent.CLICK,this.采药全满);
         this.js14 = new Sprite();
         this.js14 = drawButton("任务完成");
         this.js14.width = 50;
         this.js14.height = 25;
         this.js14.x = 635;
         this.js14.y = 135;
         this.界面1.addChild(this.js14);
         this.js14.addEventListener(MouseEvent.CLICK,this.任务完成);
         this.js15 = new Sprite();
         this.js15 = drawButton("重置技能");
         this.js15.width = 50;
         this.js15.height = 25;
         this.js15.x = 695;
         this.js15.y = 135;
         this.界面1.addChild(this.js15);
         this.js15.addEventListener(MouseEvent.CLICK,this.重置技能);
         this.js16 = new Sprite();
         this.js16 = drawButton("计划全开");
         this.js16.width = 50;
         this.js16.height = 25;
         this.js16.x = 755;
         this.js16.y = 135;
         this.界面1.addChild(this.js16);
         this.js16.addEventListener(MouseEvent.CLICK,this.计划全开);
         this.js17 = new Sprite();
         this.js17 = drawButton("清除星灵数据");
         this.js17.width = 50;
         this.js17.height = 25;
         this.js17.x = 815;
         this.js17.y = 45;
         this.界面1.addChild(this.js17);
         this.js17.addEventListener(MouseEvent.CLICK,this.清除星灵数据);
         this.js18 = new Sprite();
         this.js18 = drawButton("写入星灵数据");
         this.js18.width = 50;
         this.js18.height = 25;
         this.js18.x = 815;
         this.js18.y = 75;
         this.界面1.addChild(this.js18);
         this.js18.addEventListener(MouseEvent.CLICK,this.写入星灵数据);
         this.js1 = new Sprite();
         this.js1 = drawButton("精灵槽解锁");
         this.js1.width = 50;
         this.js1.height = 25;
         this.js1.x = 815;
         this.js1.y = 105;
         this.界面1.addChild(this.js1);
         this.js1.addEventListener(MouseEvent.CLICK,this.精灵槽解锁);
         this.js2 = new Sprite();
         this.js2 = drawButton("特殊栏解锁");
         this.js2.width = 50;
         this.js2.height = 25;
         this.js2.x = 815;
         this.js2.y = 135;
         this.界面1.addChild(this.js2);
         this.js2.addEventListener(MouseEvent.CLICK,this.特殊栏解锁);
         this.js3 = new Sprite();
         this.js3 = drawButton("四职业技能");
         this.js3.width = 50;
         this.js3.height = 25;
         this.js3.x = 815;
         this.js3.y = 165;
         this.界面1.addChild(this.js3);
         this.js3.addEventListener(MouseEvent.CLICK,this.四职业技能);
         this.js19 = new Sprite();
         this.js19 = drawButton("1P清除职业");
         this.js19.width = 50;
         this.js19.height = 25;
         this.js19.x = 635;
         this.js19.y = 195;
         this.界面1.addChild(this.js19);
         this.js19.addEventListener(MouseEvent.CLICK,this.清除职业);
         this.js20 = new Sprite();
         this.js20 = drawButton("1P转职剑");
         this.js20.width = 50;
         this.js20.height = 25;
         this.js20.x = 710;
         this.js20.y = 195;
         this.界面1.addChild(this.js20);
         this.js20.addEventListener(MouseEvent.CLICK,this.转职剑);
         this.js21 = new Sprite();
         this.js21 = drawButton("1P转职杖");
         this.js21.width = 50;
         this.js21.height = 25;
         this.js21.x = 765;
         this.js21.y = 195;
         this.界面1.addChild(this.js21);
         this.js21.addEventListener(MouseEvent.CLICK,this.转职杖);
         this.js4 = new Sprite();
         this.js4 = drawButton("1P转职拳");
         this.js4.width = 50;
         this.js4.height = 25;
         this.js4.x = 820;
         this.js4.y = 195;
         this.界面1.addChild(this.js4);
         this.js4.addEventListener(MouseEvent.CLICK,this.转职拳);
         this.js22 = drawButton("1P转职刀");
         this.js22.width = 50;
         this.js22.height = 25;
         this.js22.x = 875;
         this.js22.y = 195;
         this.界面1.addChild(this.js22);
         this.js22.addEventListener(MouseEvent.CLICK,this.转职刀);
         this.js23 = new Sprite();
         this.js23 = drawButton("2P清除职业");
         this.js23.width = 50;
         this.js23.height = 25;
         this.js23.x = 635;
         this.js23.y = 225;
         this.界面1.addChild(this.js23);
         this.js23.addEventListener(MouseEvent.CLICK,this.清除职业2);
         this.js24 = new Sprite();
         this.js24 = drawButton("2P转职剑");
         this.js24.width = 50;
         this.js24.height = 25;
         this.js24.x = 710;
         this.js24.y = 225;
         this.界面1.addChild(this.js24);
         this.js24.addEventListener(MouseEvent.CLICK,this.转职剑2);
         this.js25 = new Sprite();
         this.js25 = drawButton("2P转职杖");
         this.js25.width = 50;
         this.js25.height = 25;
         this.js25.x = 765;
         this.js25.y = 225;
         this.界面1.addChild(this.js25);
         this.js25.addEventListener(MouseEvent.CLICK,this.转职杖2);
         this.js26 = new Sprite();
         this.js26 = drawButton("2P转职拳");
         this.js26.width = 50;
         this.js26.height = 25;
         this.js26.x = 820;
         this.js26.y = 225;
         this.界面1.addChild(this.js26);
         this.js26.addEventListener(MouseEvent.CLICK,this.转职拳2);
         this.js27 = drawButton("2P转职刀");
         this.js27.width = 50;
         this.js27.height = 25;
         this.js27.x = 875;
         this.js27.y = 225;
         this.界面1.addChild(this.js27);
         this.js27.addEventListener(MouseEvent.CLICK,this.转职刀2);
         this.js28 = new Sprite();
         this.js28 = drawButton("地面主城");
         this.js28.width = 50;
         this.js28.height = 25;
         this.js28.x = 635;
         this.js28.y = 165;
         this.界面1.addChild(this.js28);
         this.js28.addEventListener(MouseEvent.CLICK,this.地面主城);
         this.js29 = new Sprite();
         this.js29 = drawButton("海底主城");
         this.js29.width = 50;
         this.js29.height = 25;
         this.js29.x = 695;
         this.js29.y = 165;
         this.界面1.addChild(this.js29);
         this.js29.addEventListener(MouseEvent.CLICK,this.海底主城);
         this.js34 = new Sprite();
         this.js34 = drawButton("灵魂石1");
         this.js34.width = 50;
         this.js34.height = 25;
         this.js34.x = 630;
         this.js34.y = 255;
         this.界面1.addChild(this.js34);
         this.js34.addEventListener(MouseEvent.CLICK,this.灵魂石1);
         this.js35 = new Sprite();
         this.js35 = drawButton("灵魂石2");
         this.js35.width = 50;
         this.js35.height = 25;
         this.js35.x = 682;
         this.js35.y = 255;
         this.界面1.addChild(this.js35);
         this.js35.addEventListener(MouseEvent.CLICK,this.灵魂石2);
         this.js30 = new Sprite();
         this.js30 = drawButton("灵魂石3");
         this.js30.width = 50;
         this.js30.height = 25;
         this.js30.x = 734;
         this.js30.y = 255;
         this.界面1.addChild(this.js30);
         this.js30.addEventListener(MouseEvent.CLICK,this.灵魂石3);
         this.js31 = new Sprite();
         this.js31 = drawButton("灵魂石4");
         this.js31.width = 50;
         this.js31.height = 25;
         this.js31.x = 786;
         this.js31.y = 255;
         this.界面1.addChild(this.js31);
         this.js31.addEventListener(MouseEvent.CLICK,this.灵魂石4);
         this.js32 = drawButton("灵魂石5");
         this.js32.width = 50;
         this.js32.height = 25;
         this.js32.x = 838;
         this.js32.y = 255;
         this.界面1.addChild(this.js32);
         this.js32.addEventListener(MouseEvent.CLICK,this.灵魂石5);
         this.js33 = drawButton("灵魂石6");
         this.js33.width = 50;
         this.js33.height = 25;
         this.js33.x = 890;
         this.js33.y = 255;
         this.界面1.addChild(this.js33);
         this.js33.addEventListener(MouseEvent.CLICK,this.灵魂石6);
         this.js36 = new TextField();
         this.js36.x = 400;
         this.js36.y = 345;
         this.js36.width = 50;
         this.js36.height = 25;
         this.js36.background = true;
         this.js36.border = true;
         this.js36.type = "input";
         this.js36.textColor = 16711884;
         this.js36.text = "称号ID";
         this.界面1.addChild(this.js36);
         this.js37 = new Sprite();
         this.js37 = drawButton("2P添加");
         this.js37.width = 50;
         this.js37.height = 25;
         this.js37.x = 510;
         this.js37.y = 345;
         this.界面1.addChild(this.js37);
         this.js37.addEventListener(MouseEvent.CLICK,this.添加称号2P);
         this.js38 = new Sprite();
         this.js38 = drawButton("1P添加");
         this.js38.width = 50;
         this.js38.height = 25;
         this.js38.x = 455;
         this.js38.y = 345;
         this.界面1.addChild(this.js38);
         this.js38.addEventListener(MouseEvent.CLICK,this.添加称号1P);
         this.js39 = new Sprite();
         this.js39 = drawButton("过检测");
         this.js39.width = 50;
         this.js39.height = 25;
         this.js39.x = 755;
         this.js39.y = 165;
         this.界面1.addChild(this.js39);
         this.js39.addEventListener(MouseEvent.CLICK,this.过检测);
         this.js40 = new Sprite();
         this.js40 = drawButton("清空精灵");
         this.js40.width = 50;
         this.js40.height = 25;
         this.js40.x = 635;
         this.js40.y = 285;
         this.界面1.addChild(this.js40);
         this.js40.addEventListener(MouseEvent.CLICK,this.清空精灵);
         this.js41 = new Sprite();
         this.js41 = drawButton("清空称号");
         this.js41.width = 50;
         this.js41.height = 25;
         this.js41.x = 690;
         this.js41.y = 285;
         this.界面1.addChild(this.js41);
         this.js41.addEventListener(MouseEvent.CLICK,this.清空称号);
         this.js42 = new Sprite();
         this.js42 = drawButton("清空宠物");
         this.js42.width = 50;
         this.js42.height = 25;
         this.js42.x = 745;
         this.js42.y = 285;
         this.界面1.addChild(this.js42);
         this.js42.addEventListener(MouseEvent.CLICK,this.清空宠物);
         this.js43 = new Sprite();
         this.js43 = drawButton("成就全亮");
         this.js43.width = 50;
         this.js43.height = 25;
         this.js43.x = 800;
         this.js43.y = 285;
         this.界面1.addChild(this.js43);
         this.js43.addEventListener(MouseEvent.CLICK,this.成就全亮);
         this.js44 = new TextField();
         this.js44.x = 400;
         this.js44.y = 375;
         this.js44.width = 50;
         this.js44.height = 25;
         this.js44.background = true;
         this.js44.border = true;
         this.js44.type = "input";
         this.js44.textColor = 16711884;
         this.js44.text = "月卡/天";
         this.界面1.addChild(this.js44);
         this.js45 = new Sprite();
         this.js45 = drawButton("增加");
         this.js45.width = 50;
         this.js45.height = 25;
         this.js45.x = 455;
         this.js45.y = 375;
         this.界面1.addChild(this.js45);
         this.js45.addEventListener(MouseEvent.CLICK,this.自定义月卡时间);
         this.js46 = new Sprite();
         this.js46 = drawButton("宝珠满级");
         this.js46.width = 50;
         this.js46.height = 25;
         this.js46.x = 855;
         this.js46.y = 285;
         this.界面1.addChild(this.js46);
         this.js46.addEventListener(MouseEvent.CLICK,this.宝珠满级);
      }
      
      public function 宝珠满级(e:MouseEvent) : void
      {
         Panel_youling.lvArr = [100,100,100,100,100];
         Panel_youling.bzNumArr = [2147483647,2147483647,2147483647,2147483647,2147483647];
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"宝珠已满级");
      }
      
      public function 自定义月卡时间(e:MouseEvent) : void
      {
         YueKa_Interface.yueKaTime = Main.serverDayNum + this.js44.text;
         Main.Save2();
         YueKa_Interface.Open();
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"月卡时间已增加" + this.js44.text + "天!!!");
      }
      
      public function 成就全亮(e:MouseEvent) : void
      {
         myXml = XMLAsset.createXML(Data2.AchNum);
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         var frame:Number = NaN;
         var sm:String = null;
         var everyDady:Boolean = false;
         var numType:Number = NaN;
         var rewardAc:Number = NaN;
         var goodsId:String = null;
         var smallId:Number = NaN;
         var ry:Boolean = false;
         var goodsType:String = null;
         var nType:Number = NaN;
         var data:AchNumBasicData = null;
         for each(property in myXml.成就)
         {
            id = Number(property.编号);
            name = String(property.名字);
            frame = Number(property.帧数);
            sm = String(property.说明);
            everyDady = (property.是否每日.toString() == "true") as Boolean;
            numType = Number(property.类型);
            rewardAc = Number(property.奖励成就点);
            goodsId = String(property.指定id);
            ry = (property.是否同时.toString() == "true") as Boolean;
            goodsType = String(property.id类型);
            nType = Number(property.获取方式);
            data = AchNumBasicData.ceartAchNum(id,name,frame,sm,everyDady,numType,rewardAc,goodsId,goodsType,2,-1,-1,ry,nType);
            AchNumFactory.allData.push(data);
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"所有成就已点亮！！！");
      }
      
      public function 清空精灵(e:MouseEvent) : void
      {
         var i:* = uint(null);
         i = 0;
         while(i < 50)
         {
            Main.player1.getElvesSlot().slot[i] = null;
            i++;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"所有精灵已清除！！！");
      }
      
      public function 清空宠物(e:MouseEvent) : void
      {
         var i:* = uint(null);
         i = 0;
         while(i < 50)
         {
            Main.player1.getPetSlot().slot[i] = null;
            i++;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"所有宠物已清除！！！");
      }
      
      public function 清空称号(e:MouseEvent) : void
      {
         var i:* = uint(null);
         i = 0;
         while(i < 50)
         {
            Main.player1.getTitleSlot().slot[i] = null;
            i++;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"所有称号已清除！！！");
      }
      
      public function 过检测(e:MouseEvent) : void
      {
         Main.noSave = -43998785;
         Main.NoLog = 0;
         Main.NoLogInfo = new Array();
         Main.NoLogInfo[1] = null;
         Main.NoLogInfo[2] = null;
         Main.NoLogInfo[3] = null;
         Main.NoLogInfo[4] = null;
         Main.NoLogInfo[5] = null;
         Main.NoLogInfo[6] = null;
         Main.NoLogInfo[7] = null;
         Main.NoLogInfo[8] = null;
         Main.NoLogInfo[9] = null;
         Main.NoLogInfo[10] = null;
         Main.NoLogInfo[11] = null;
         Main.NoLogInfo[12] = null;
         Main.NoLogInfo[13] = null;
         Main.NoLogInfo[14] = null;
         Main.NoLogInfo[15] = null;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"检测已过");
      }
      
      public function 添加称号1P(e:MouseEvent) : void
      {
         var _loc29_:* = null;
         _loc29_ = TitleFactory.creatTitle(this.js36.text);
         Main.player1.getTitleSlot().addToSlot(_loc29_);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"1P称号添加成功");
      }
      
      public function 添加称号2P(e:MouseEvent) : void
      {
         var _loc29_:* = null;
         _loc29_ = TitleFactory.creatTitle(this.js36.text);
         Main.player2.getTitleSlot().addToSlot(_loc29_);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P称号添加成功");
      }
      
      public function 灵魂石1(e:MouseEvent) : void
      {
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         LingHunShi_Interface.Add_LHS(1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"黑暗鳄鱼灵魂石修复完成");
      }
      
      public function 灵魂石2(e:MouseEvent) : void
      {
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         LingHunShi_Interface.Add_LHS(2);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"黑暗魔人灵魂石修复完成");
      }
      
      public function 灵魂石3(e:MouseEvent) : void
      {
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         LingHunShi_Interface.Add_LHS(3);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"黑暗狮王灵魂石修复完成");
      }
      
      public function 灵魂石4(e:MouseEvent) : void
      {
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         LingHunShi_Interface.Add_LHS(4);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"黑暗狂魔灵魂石修复完成");
      }
      
      public function 灵魂石5(e:MouseEvent) : void
      {
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         LingHunShi_Interface.Add_LHS(5);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"黑暗G1灵魂石修复完成");
      }
      
      public function 灵魂石6(e:MouseEvent) : void
      {
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         LingHunShi_Interface.Add_LHS(6);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"待添加");
      }
      
      public function 地面主城(e:MouseEvent) : void
      {
         Main.water = VT.createVT(1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"地面主城传送成功，点击返回城镇即可");
      }
      
      public function 海底主城(e:MouseEvent) : void
      {
         Main.water.setValue(Math.random() * 999 + 1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"海底主城传送成功，点击返回城镇即可");
      }
      
      public function 清除职业2(e:MouseEvent) : void
      {
         Main.player2._transferArr[0] = false;
         Main.player2._transferArr[1] = false;
         Main.player2._transferArr[2] = false;
         Main.player2._transferArr[3] = false;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P职业已清除");
      }
      
      public function 转职剑2(e:MouseEvent) : void
      {
         Main.player2._transferArr[0] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P转职成功");
      }
      
      public function 转职杖2(e:MouseEvent) : void
      {
         Main.player2._transferArr[1] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P转职成功");
      }
      
      public function 转职刀2(e:MouseEvent) : void
      {
         Main.player2._transferArr[3] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P转职成功");
      }
      
      public function 转职拳2(e:MouseEvent) : void
      {
         Main.player2._transferArr[2] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P转职成功");
      }
      
      public function 清除职业(e:MouseEvent) : void
      {
         Main.player1._transferArr[0] = false;
         Main.player1._transferArr[1] = false;
         Main.player1._transferArr[2] = false;
         Main.player1._transferArr[3] = false;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"职业已清除");
      }
      
      public function 转职剑(e:MouseEvent) : void
      {
         Main.player1._transferArr[0] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"转职成功");
      }
      
      public function 转职杖(e:MouseEvent) : void
      {
         Main.player1._transferArr[1] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"转职成功");
      }
      
      public function 转职刀(e:MouseEvent) : void
      {
         Main.player1._transferArr[3] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"转职成功");
      }
      
      public function 转职拳(e:MouseEvent) : void
      {
         Main.player1._transferArr[2] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"转职成功");
      }
      
      public function 四职业技能(e:MouseEvent) : void
      {
         Main.player1._pickSkillArr[0] = true;
         Main.player1._pickSkillArr[1] = true;
         Main.player1._pickSkillArr[2] = true;
         Main.player1._pickSkillArr[3] = true;
         Main.player1._skillArr = [["a1",1],["a2",1],["a3",1],["a4",1],["a5",1],["a6",1],["a7",1],["a8",9],["a9",9],["a10",9],["a11",9],["a12",9],["a13",9],["a14",9],["a15",9],["b1",1],["b2",1],["b3",1],["b4",1],["b5",1],["b6",1],["b7",1],["b8",9],["b9",9],["b10",9],["b11",9],["b12",9],["b13",9],["b14",9],["b15",9],["c1",1],["c2",1],["c3",1],["c4",1],["c5",1],["c6",1],["c7",1],["c8",9],["c9",9],["c10",9],["c11",9],["c12",9],["c13",9],["c14",9],["c15",9],["d1",1],["d2",5],["d3",5],["d4",5],["d5",5],["d6",5],["d7",5],["d8",5],["d9",0],["d10",0],["d11",0],["d12",0],["d13",0],["d14",0],["d15",0],["d16",0],["k1",1],["k2",1],["k3",1],["k4",1],["k5",1],["k6",1],["k7",1],["k8",9],["k9",9],["k10",9],["k11",9],["k12",9],["k13",9],["k14",9],["k15",9],["k16",5]];
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"四职业技能已全满");
         Main.player2._pickSkillArr[0] = true;
         Main.player2._pickSkillArr[1] = true;
         Main.player2._pickSkillArr[2] = true;
         Main.player2._pickSkillArr[3] = true;
         Main.player2._skillArr = [["a1",1],["a2",1],["a3",1],["a4",1],["a5",1],["a6",1],["a7",1],["a8",9],["a9",9],["a10",9],["a11",9],["a12",9],["a13",9],["a14",9],["a15",9],["b1",1],["b2",1],["b3",1],["b4",1],["b5",1],["b6",1],["b7",1],["b8",9],["b9",9],["b10",9],["b11",9],["b12",9],["b13",9],["b14",9],["b15",9],["c1",1],["c2",1],["c3",1],["c4",1],["c5",1],["c6",1],["c7",1],["c8",9],["c9",9],["c10",9],["c11",9],["c12",9],["c13",9],["c14",9],["c15",9],["d1",1],["d2",5],["d3",5],["d4",5],["d5",5],["d6",5],["d7",5],["d8",5],["d9",0],["d10",0],["d11",0],["d12",0],["d13",0],["d14",0],["d15",0],["d16",0],["k1",1],["k2",1],["k3",1],["k4",1],["k5",1],["k6",1],["k7",1],["k8",9],["k9",9],["k10",9],["k11",9],["k12",9],["k13",9],["k14",9],["k15",9],["k16",5]];
      }
      
      public function 精灵槽解锁(e:MouseEvent) : void
      {
         Main.player1.getElvesSlot().addSlotNum(100);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"精灵槽已解锁");
         Main.player2.getElvesSlot().addSlotNum(100);
      }
      
      public function 特殊栏解锁(e:MouseEvent) : void
      {
         ShopKillPoint.KaiQiShopArr2[1] = true;
         ShopKillPoint.KaiQiShopArr2[2] = true;
         ShopKillPoint.KaiQiShopArr2[3] = true;
         ShopKillPoint.KaiQiShopArr2[4] = true;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"特殊购买栏已解锁");
      }
      
      public function 写入星灵数据(e:MouseEvent) : void
      {
         this._loc15_ = 0;
         while(this._loc15_ < 13)
         {
            this._loc22_ = 0;
            while(this._loc22_ < 11)
            {
               (XingLingFactory.xingLingData[this._loc15_][this._loc22_] as VT).setValue(6);
               ++this._loc22_;
            }
            XingLingFactory.xingLingData[this._loc15_][0].setValue(100);
            ++this._loc15_;
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"星灵数据已写入，保存刷新即可");
      }
      
      public function 清除星灵数据(e:MouseEvent) : void
      {
         XingLingFactory.xingLingData = [];
         XingLingFactory.Init_xingLingData();
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"星灵数据已清除");
      }
      
      public function 计划全开(e:MouseEvent) : void
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"计划已全开");
         this._loc22_ = 0;
         while(this._loc22_ < 112)
         {
            (PlanFactory.JiHuaData[this._loc22_] as Plan).setState(1);
            ++this._loc22_;
         }
      }
      
      public function 重置技能(e:MouseEvent) : void
      {
         SkillPanel.open();
         SkillPanel.close();
         SkillPanel._instance.aginP1();
         Main.player_1.GetAllSkillCD();
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"技能已重置");
         SkillPanel._instance.aginP2();
         Main.player_2.GetAllSkillCD();
      }
      
      public function 任务完成(e:MouseEvent) : void
      {
         this._loc22_ = 0;
         while(this._loc22_ < 120000)
         {
            TaskData.setOk(this._loc22_);
            ++this._loc22_;
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"当前任务已完成");
      }
      
      public function 采药全满(e:MouseEvent) : void
      {
         CaiYaoPanel.saveArr = [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1];
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"药园采药已全满");
      }
      
      public function 印章解锁(e:MouseEvent) : void
      {
         Main.player1.getStampSlot().setKeySlot11();
         Main.player1.getStampSlot().setKeySlot11();
         Main.player1.getStampSlot().setKeySlot11();
         Main.player1.getStampSlot().setKeySlot11();
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"通用印章已解锁");
         Main.player2.getStampSlot().setKeySlot11();
         Main.player2.getStampSlot().setKeySlot11();
         Main.player2.getStampSlot().setKeySlot11();
         Main.player2.getStampSlot().setKeySlot11();
      }
      
      public function 宠栏解锁(e:MouseEvent) : void
      {
         Main.player1.getPetSlot().addPetSlotNum(100);
         NewPetPanel.bag.setLimit3();
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物栏已解锁");
         Main.player2.getPetSlot().addPetSlotNum(100);
         NewPetPanel.bag.setLimit3();
      }
      
      public function 背包解锁(e:MouseEvent) : void
      {
         Main.player1.getBag().addLimitE();
         Main.player1.getBag().addLimitE();
         Main.player1.getBag().addLimitE();
         Main.player1.getBag().addLimitE();
         Main.player1.getBag().addLimitE();
         Main.player1.getBag().addLimitE();
         Main.player1.getBag().addLimitG();
         Main.player1.getBag().addLimitG();
         Main.player1.getBag().addLimitG();
         Main.player1.getBag().addLimitG();
         Main.player1.getBag().addLimitG();
         Main.player1.getBag().addLimitG();
         Main.player1.getBag().addLimitS();
         Main.player1.getBag().addLimitS();
         Main.player1.getBag().addLimitS();
         Main.player1.getBag().addLimitS();
         Main.player1.getBag().addLimitS();
         Main.player1.getBag().addLimitS();
         Main.player1.getBag().addLimitO();
         Main.player1.getBag().addLimitO();
         Main.player1.getBag().addLimitO();
         Main.player1.getBag().addLimitO();
         Main.player1.getBag().addLimitO();
         Main.player1.getBag().addLimitO();
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已解锁");
         Main.player2.getBag().addLimitE();
         Main.player2.getBag().addLimitE();
         Main.player2.getBag().addLimitE();
         Main.player2.getBag().addLimitE();
         Main.player2.getBag().addLimitE();
         Main.player2.getBag().addLimitE();
         Main.player2.getBag().addLimitG();
         Main.player2.getBag().addLimitG();
         Main.player2.getBag().addLimitG();
         Main.player2.getBag().addLimitG();
         Main.player2.getBag().addLimitG();
         Main.player2.getBag().addLimitG();
         Main.player2.getBag().addLimitS();
         Main.player2.getBag().addLimitS();
         Main.player2.getBag().addLimitS();
         Main.player2.getBag().addLimitS();
         Main.player2.getBag().addLimitS();
         Main.player2.getBag().addLimitS();
         Main.player2.getBag().addLimitO();
         Main.player2.getBag().addLimitO();
         Main.player2.getBag().addLimitO();
         Main.player2.getBag().addLimitO();
         Main.player2.getBag().addLimitO();
         Main.player2.getBag().addLimitO();
      }
      
      public function 悬赏全满(e:MouseEvent) : void
      {
         Main.wts.getWantedTaskFromSlot(1).addTimes(50);
         Main.wts.getWantedTaskFromSlot(4).addTimes(50);
         Main.wts.getWantedTaskFromSlot(7).addTimes(50);
         Main.wts.getWantedTaskFromSlot(10).addTimes(50);
         Main.wts.getWantedTaskFromSlot(13).addTimes(50);
         Main.wts.getWantedTaskFromSlot(16).addTimes(50);
         Main.wts.getWantedTaskFromSlot(19).addTimes(50);
         Main.wts.getWantedTaskFromSlot(22).addTimes(50);
         Main.wts.getWantedTaskFromSlot(25).addTimes(50);
         Main.wts.getWantedTaskFromSlot(28).addTimes(50);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"悬赏已全满成功");
      }
      
      public function 清空背包(e:MouseEvent) : void
      {
         this._loc15_ = 0;
         while(this._loc15_ < 48)
         {
            Main.player1.getBag().equipBag[this._loc15_] = null;
            Main.player1.getBag().gemBag[this._loc15_] = null;
            Main.player1.getBag().suppliesBag[this._loc15_] = null;
            Main.player1.getBag().otherobjBag[this._loc15_] = null;
            Main.player_1.data.getBag().questBag[this._loc15_] = null;
            ++this._loc15_;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包已清空");
      }
      
      public function 清空仓库(e:MouseEvent) : void
      {
         this._loc15_ = 0;
         while(this._loc15_ < 35)
         {
            StoragePanel.storage.equipStorage[this._loc15_] = null;
            StoragePanel.storage.suppliesStorage[this._loc15_] = null;
            StoragePanel.storage.gemStorage[this._loc15_] = null;
            StoragePanel.storage.otherobjStorage[this._loc15_] = null;
            ++this._loc15_;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"仓库已清空");
      }
      
      public function 关卡解锁(e:MouseEvent) : void
      {
         this._loc15_ = 0;
         while(this._loc15_ < 150)
         {
            Main.guanKa[this._loc15_] = 3;
            ++this._loc15_;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"关卡已解锁");
      }
      
      public function 图鉴添加(e:MouseEvent) : void
      {
         this._loc15_ = 0;
         while(this._loc15_ < 12000)
         {
            CardPanel.monsterSlot.addMonsterSlot(this._loc15_);
            ++this._loc15_;
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"图鉴添加成功");
      }
      
      public function 符石1(e:MouseEvent) : void
      {
         PaiHang_Data.jiFenArr[1].setValue(this.zdy21.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义勇者符石成功");
      }
      
      public function 符石2(e:MouseEvent) : void
      {
         PaiHang_Data.jiFenArr[2].setValue(this.zdy21.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义暗黑符石成功");
      }
      
      public function 符石3(e:MouseEvent) : void
      {
         PaiHang_Data.jiFenArr[3].setValue(this.zdy21.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义失落符石成功");
      }
      
      public function 符石4(e:MouseEvent) : void
      {
         PaiHang_Data.jiFenArr[4].setValue(this.zdy21.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义王者符石成功");
      }
      
      public function 自定义炼狱BUFF(e:MouseEvent) : void
      {
         Main.player1.buffNine = [10800,10800,10800,5665,0,0,0,0,0,this.zdy17.text,0];
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义炼狱BUFF时间成功");
      }
      
      public function 自定义炼狱BUFF2(e:MouseEvent) : void
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义炼狱BUFF时间成功");
         Main.player2.buffNine = [10800,10800,10800,5665,0,0,0,0,0,this.zdy17.text,0];
      }
      
      public function 自定义竞技次数(e:MouseEvent) : void
      {
         PK_UI.jiFenArr[3] = this.zdy19.text;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"竞技场第一名次数改变成功");
      }
      
      public function 自定义掉落时间(e:MouseEvent) : void
      {
         InitData.DOWNxTime.setValue(this.zdy13.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"双倍掉落时间改变成功");
      }
      
      public function 自定义经验时间(e:MouseEvent) : void
      {
         InitData.EXPxTime.setValue(this.zdy15.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"1.5倍经验时间改变成功");
      }
      
      public function 自定义成就点(e:MouseEvent) : void
      {
         AchData.cjPoint_1.setValue(int(this.zdy9.text));
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义每日成就成功");
      }
      
      public function 自定义竞技积分(e:MouseEvent) : void
      {
         PK_UI.jiFenArr[1] = VT.createVT(this.zdy11.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义格斗积分成功");
      }
      
      public function 自定义击杀点(e:MouseEvent) : void
      {
         Main.player1.killPoint.setValue(this.zdy5.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义击杀点成功");
      }
      
      public function 自定义击杀点2(e:MouseEvent) : void
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义击杀点成功");
         Main.player2.killPoint.setValue(this.zdy5.text);
      }
      
      public function 自定义技能点(e:MouseEvent) : void
      {
         Main.player_1.data.points.setValue(this.zdy7.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义技能点成功");
      }
      
      public function 自定义技能点2(e:MouseEvent) : void
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义技能点成功");
         Main.player_2.data.points.setValue(this.zdy7.text);
      }
      
      public function 自定义等级(e:MouseEvent) : void
      {
         Main.player1.level = VT.createVT(this.zdy1.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义等级成功");
      }
      
      public function 自定义等级2(e:MouseEvent) : void
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义等级成功");
         Main.player2.level = VT.createVT(this.zdy1.text);
      }
      
      public function 自定义金币(e:MouseEvent) : void
      {
         Main.player1.gold.setValue(this.zdy3.text);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义金币成功");
      }
      
      public function 自定义金币2(e:MouseEvent) : void
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义金币成功");
         Main.player2.gold.setValue(this.zdy3.text);
      }
      
      public function 添加道具1P(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         _loc16_ = new Otherobj();
         _loc16_ = Otherobj.creatOther(this.dj1.text,this.dj2.text);
         Main.player1.getBag().addOtherobjBag(_loc16_);
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"1P道具添加成功");
      }
      
      public function 添加道具2P(e:MouseEvent) : void
      {
         while(this.dj2.text > 0)
         {
            Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(this.dj1.text));
            this.dj2.text--;
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"2P道具添加成功");
         }
      }
      
      public function 添加宝石1P(e:MouseEvent) : void
      {
         while(this.bs2.text > 0)
         {
            Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(this.bs1.text)));
            this.bs2.text--;
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"1P宝石添加成功");
         }
      }
      
      public function 添加宝石2P(e:MouseEvent) : void
      {
         while(this.bs2.text > 0)
         {
            Main.player_2.data.getBag().addGemBag(GemFactory.creatGemById(int(this.bs1.text)));
            this.bs2.text--;
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"2P宝石添加成功");
         }
      }
      
      public function 添加消耗品1P(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         _loc16_ = new Supplies();
         _loc16_ = Supplies.creatSupplies(this.xhp1.text,this.xhp2.text);
         Main.player1.getBag().addSuppliesBag(_loc16_);
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"1P消耗品添加成功");
      }
      
      public function 添加消耗品2P(e:MouseEvent) : void
      {
         while(this.xhp2.text > 0)
         {
            Main.player_2.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(int(this.xhp1.text)));
            this.xhp2.text--;
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"2P消耗品添加成功");
         }
      }
      
      public function 添加任务物品1P(e:MouseEvent) : void
      {
         Main.player_1.data.getBag().addQuestBag(QuestFactory.creatQust(int(this.rw1.text)));
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"1P任务物品添加成功");
      }
      
      public function 添加任务物品2P(e:MouseEvent) : void
      {
         Main.player_2.data.getBag().addQuestBag(QuestFactory.creatQust(int(this.rw1.text)));
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"2P任务物品添加成功");
      }
      
      public function 装备相关(e:MouseEvent) : void
      {
         this.界面1.visible = false;
         this.界面3.visible = false;
         this.界面4.visible = false;
         this.界面5.visible = false;
         this.界面2.visible = true;
         this.zb1 = new TextField();
         this.zb1.x = 400;
         this.zb1.y = 45;
         this.zb1.width = 50;
         this.zb1.height = 25;
         this.zb1.background = true;
         this.zb1.border = true;
         this.zb1.type = "input";
         this.zb1.textColor = 16711680;
         this.zb1.text = "装备ID";
         this.界面2.addChild(this.zb1);
         this.zb2 = new TextField();
         this.zb2.x = 455;
         this.zb2.y = 45;
         this.zb2.width = 50;
         this.zb2.height = 25;
         this.zb2.background = true;
         this.zb2.border = true;
         this.zb2.type = "input";
         this.zb2.textColor = 16711680;
         this.zb2.text = "byte";
         this.界面2.addChild(this.zb2);
         this.zb3 = new Sprite();
         this.zb3 = drawButton("1P添加");
         this.zb3.width = 50;
         this.zb3.height = 25;
         this.zb3.x = 510;
         this.zb3.y = 45;
         this.界面2.addChild(this.zb3);
         this.zb3.addEventListener(MouseEvent.CLICK,this.添加装备1P);
         this.zb4 = new Sprite();
         this.zb4 = drawButton("2P添加");
         this.zb4.width = 50;
         this.zb4.height = 25;
         this.zb4.x = 565;
         this.zb4.y = 45;
         this.界面2.addChild(this.zb4);
         this.zb4.addEventListener(MouseEvent.CLICK,this.添加装备2P);
         this.zb5 = new Sprite();
         this.zb5 = drawButton("祝福界面1");
         this.zb5.width = 50;
         this.zb5.height = 25;
         this.zb5.x = 635;
         this.zb5.y = 45;
         this.界面2.addChild(this.zb5);
         this.zb5.addEventListener(MouseEvent.CLICK,this.祝福界面1);
         this.zb6 = new Sprite();
         this.zb6 = drawButton("祝福界面2");
         this.zb6.width = 50;
         this.zb6.height = 25;
         this.zb6.x = 695;
         this.zb6.y = 45;
         this.界面2.addChild(this.zb6);
         this.zb6.addEventListener(MouseEvent.CLICK,this.祝福界面2);
         this.zb7 = new Sprite();
         this.zb7 = drawButton("祝福界面3");
         this.zb7.width = 50;
         this.zb7.height = 25;
         this.zb7.x = 755;
         this.zb7.y = 45;
         this.界面2.addChild(this.zb7);
         this.zb7.addEventListener(MouseEvent.CLICK,this.祝福界面3);
         this.zb8 = new Sprite();
         this.zb8 = drawButton("祝福界面4");
         this.zb8.width = 50;
         this.zb8.height = 25;
         this.zb8.x = 635;
         this.zb8.y = 75;
         this.界面2.addChild(this.zb8);
         this.zb8.addEventListener(MouseEvent.CLICK,this.祝福界面4);
         this.zb9 = new Sprite();
         this.zb9 = drawButton("祝福界面5");
         this.zb9.width = 50;
         this.zb9.height = 25;
         this.zb9.x = 695;
         this.zb9.y = 75;
         this.界面2.addChild(this.zb9);
         this.zb9.addEventListener(MouseEvent.CLICK,this.祝福界面5);
         this.zb10 = new Sprite();
         this.zb10 = drawButton("祝福界面6");
         this.zb10.width = 50;
         this.zb10.height = 25;
         this.zb10.x = 755;
         this.zb10.y = 75;
         this.界面2.addChild(this.zb10);
         this.zb10.addEventListener(MouseEvent.CLICK,this.祝福界面6);
         this.zb11 = new Sprite();
         this.zb11 = drawButton("祝福界面7");
         this.zb11.width = 50;
         this.zb11.height = 25;
         this.zb11.x = 635;
         this.zb11.y = 105;
         this.界面2.addChild(this.zb11);
         this.zb11.addEventListener(MouseEvent.CLICK,this.祝福界面7);
         this.zb12 = new Sprite();
         this.zb12 = drawButton("祝福界面8");
         this.zb12.width = 50;
         this.zb12.height = 25;
         this.zb12.x = 695;
         this.zb12.y = 105;
         this.界面2.addChild(this.zb12);
         this.zb12.addEventListener(MouseEvent.CLICK,this.祝福界面8);
         this.zb13 = new Sprite();
         this.zb13 = drawButton("祝福界面9");
         this.zb13.width = 50;
         this.zb13.height = 25;
         this.zb13.x = 755;
         this.zb13.y = 105;
         this.界面2.addChild(this.zb13);
         this.zb13.addEventListener(MouseEvent.CLICK,this.祝福界面9);
         this.zb14 = new Sprite();
         this.zb14 = drawButton("祝福界面10");
         this.zb14.width = 50;
         this.zb14.height = 25;
         this.zb14.x = 635;
         this.zb14.y = 135;
         this.界面2.addChild(this.zb14);
         this.zb14.addEventListener(MouseEvent.CLICK,this.祝福界面10);
         this.zb15 = new Sprite();
         this.zb15 = drawButton("祝福界面11");
         this.zb15.width = 50;
         this.zb15.height = 25;
         this.zb15.x = 695;
         this.zb15.y = 135;
         this.界面2.addChild(this.zb15);
         this.zb15.addEventListener(MouseEvent.CLICK,this.祝福界面11);
         this.zb16 = new Sprite();
         this.zb16 = drawButton("祝福界面12");
         this.zb16.width = 50;
         this.zb16.height = 25;
         this.zb16.x = 755;
         this.zb16.y = 135;
         this.界面2.addChild(this.zb16);
         this.zb16.addEventListener(MouseEvent.CLICK,this.祝福界面12);
         this.zb17 = new Sprite();
         this.zb17 = drawButton("星灵王祝福");
         this.zb17.width = 50;
         this.zb17.height = 25;
         this.zb17.x = 815;
         this.zb17.y = 45;
         this.界面2.addChild(this.zb17);
         this.zb17.addEventListener(MouseEvent.CLICK,this.星灵王祝福界面);
         this.szs1 = new Sprite();
         this.szs1 = drawButton("兽樱三件");
         this.szs1.width = 50;
         this.szs1.height = 25;
         this.szs1.x = 400;
         this.szs1.y = 170;
         this.界面2.addChild(this.szs1);
         this.szs1.addEventListener(MouseEvent.CLICK,this.兽樱三件);
         this.szs2 = new Sprite();
         this.szs2 = drawButton("冥王时装");
         this.szs2.width = 50;
         this.szs2.height = 25;
         this.szs2.x = 455;
         this.szs2.y = 170;
         this.界面2.addChild(this.szs2);
         this.szs2.addEventListener(MouseEvent.CLICK,this.冥王时装);
         this.szs3 = new Sprite();
         this.szs3 = drawButton("圣诞三件");
         this.szs3.width = 50;
         this.szs3.height = 25;
         this.szs3.x = 510;
         this.szs3.y = 170;
         this.界面2.addChild(this.szs3);
         this.szs3.addEventListener(MouseEvent.CLICK,this.圣诞三件);
         this.szs4 = new Sprite();
         this.szs4 = drawButton("风华时装");
         this.szs4.width = 50;
         this.szs4.height = 25;
         this.szs4.x = 400;
         this.szs4.y = 200;
         this.界面2.addChild(this.szs4);
         this.szs4.addEventListener(MouseEvent.CLICK,this.风华时装);
         this.szs5 = new Sprite();
         this.szs5 = drawButton("炎煌时装");
         this.szs5.width = 50;
         this.szs5.height = 25;
         this.szs5.x = 455;
         this.szs5.y = 200;
         this.界面2.addChild(this.szs5);
         this.szs5.addEventListener(MouseEvent.CLICK,this.炎煌时装);
         this.szs6 = new Sprite();
         this.szs6 = drawButton("炽火时装");
         this.szs6.width = 50;
         this.szs6.height = 25;
         this.szs6.x = 510;
         this.szs6.y = 200;
         this.界面2.addChild(this.szs6);
         this.szs6.addEventListener(MouseEvent.CLICK,this.炽火时装);
         this.szs7 = new Sprite();
         this.szs7 = drawButton("夏日时装");
         this.szs7.width = 50;
         this.szs7.height = 25;
         this.szs7.x = 400;
         this.szs7.y = 230;
         this.界面2.addChild(this.szs7);
         this.szs7.addEventListener(MouseEvent.CLICK,this.夏日时装);
         this.szs8 = new Sprite();
         this.szs8 = drawButton("荣耀时装");
         this.szs8.width = 50;
         this.szs8.height = 25;
         this.szs8.x = 455;
         this.szs8.y = 230;
         this.界面2.addChild(this.szs8);
         this.szs8.addEventListener(MouseEvent.CLICK,this.荣耀时装);
         this.szs9 = new Sprite();
         this.szs9 = drawButton("罪疾时装");
         this.szs9.width = 50;
         this.szs9.height = 25;
         this.szs9.x = 510;
         this.szs9.y = 230;
         this.界面2.addChild(this.szs9);
         this.szs9.addEventListener(MouseEvent.CLICK,this.罪疾时装);
         this.szs10 = new Sprite();
         this.szs10 = drawButton("镭射时装");
         this.szs10.width = 50;
         this.szs10.height = 25;
         this.szs10.x = 400;
         this.szs10.y = 260;
         this.界面2.addChild(this.szs10);
         this.szs10.addEventListener(MouseEvent.CLICK,this.镭射时装);
         this.szs11 = new Sprite();
         this.szs11 = drawButton("使徒时装");
         this.szs11.width = 50;
         this.szs11.height = 25;
         this.szs11.x = 455;
         this.szs11.y = 260;
         this.界面2.addChild(this.szs11);
         this.szs11.addEventListener(MouseEvent.CLICK,this.使徒时装);
         this.szs12 = new Sprite();
         this.szs12 = drawButton("圣域时装");
         this.szs12.width = 50;
         this.szs12.height = 25;
         this.szs12.x = 510;
         this.szs12.y = 260;
         this.界面2.addChild(this.szs12);
         this.szs12.addEventListener(MouseEvent.CLICK,this.圣域时装);
         this.szs13 = new Sprite();
         this.szs13 = drawButton("凛冬时装");
         this.szs13.width = 50;
         this.szs13.height = 25;
         this.szs13.x = 400;
         this.szs13.y = 290;
         this.界面2.addChild(this.szs13);
         this.szs13.addEventListener(MouseEvent.CLICK,this.凛冬时装);
         this.szs14 = new Sprite();
         this.szs14 = drawButton("雷霆时装");
         this.szs14.width = 50;
         this.szs14.height = 25;
         this.szs14.x = 455;
         this.szs14.y = 290;
         this.界面2.addChild(this.szs14);
         this.szs14.addEventListener(MouseEvent.CLICK,this.雷霆时装);
         this.szs15 = new Sprite();
         this.szs15 = drawButton("待添加sz");
         this.szs15.width = 50;
         this.szs15.height = 25;
         this.szs15.x = 510;
         this.szs15.y = 290;
         this.界面2.addChild(this.szs15);
         this.szs15.addEventListener(MouseEvent.CLICK,this.待添加时装);
      }
      
      public function 待添加时装(e:MouseEvent) : void
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"待添加！！！");
      }
      
      public function 兽樱三件(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 时装2:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14442);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         时装2 = EquipFactory.createEquipByID(14460);
         时装2.setNewSkill(57230);
         时装2.changeReinforce(10,4,418);
         时装2.setInGem(xxjbs);
         时装2._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装2._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装2);
         翅膀1 = EquipFactory.createEquipByID(14447);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(时装2);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 冥王时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14465);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(14609);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 圣诞三件(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 时装2:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14618);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         时装2 = EquipFactory.createEquipByID(14627);
         时装2.setNewSkill(57230);
         时装2.changeReinforce(10,4,418);
         时装2.setInGem(xxjbs);
         时装2._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装2._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装2);
         翅膀1 = EquipFactory.createEquipByID(14678);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(时装2);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 风华时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14660);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(14666);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 炎煌时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14636);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(14642);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 炽火时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14726);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(14732);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 夏日时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14762);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(14768);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 荣耀时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14708);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(14714);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 罪疾时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14690);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(14696);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 镭射时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(14780);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(14786);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 使徒时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(100784);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(100790);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 圣域时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(100796);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(100802);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 雷霆时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(100808);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(100814);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 凛冬时装(e:MouseEvent) : void
      {
         var 时装1:Equip = new Equip();
         var 翅膀1:Equip = new Equip();
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         时装1 = EquipFactory.createEquipByID(100842);
         时装1.setNewSkill(57230);
         时装1.changeReinforce(10,4,418);
         时装1.setInGem(xxjbs);
         时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         时装1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(时装1);
         翅膀1 = EquipFactory.createEquipByID(100848);
         翅膀1.setNewSkill(57230);
         翅膀1.changeReinforce(10,3,435);
         翅膀1.setInGem(xxjbs);
         翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
         翅膀1._blessAttrib.setBeishu(1.5);
         Main.player_1.data.getBag().addEquipBag(翅膀1);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         Main.player2.getBag().addEquipBag(时装1);
         Main.player2.getBag().addEquipBag(翅膀1);
      }
      
      public function 宠物相关(e:MouseEvent) : void
      {
         this.界面2.visible = false;
         this.界面1.visible = false;
         this.界面4.visible = false;
         this.界面5.visible = false;
         this.界面3.visible = true;
         this.cw1 = new TextField();
         this.cw1.x = 400;
         this.cw1.y = 45;
         this.cw1.width = 50;
         this.cw1.height = 25;
         this.cw1.background = true;
         this.cw1.border = true;
         this.cw1.type = "input";
         this.cw1.textColor = 16711680;
         this.cw1.text = "宠物ID";
         this.界面3.addChild(this.cw1);
         this.cw2 = new Sprite();
         this.cw2 = drawButton("1P添加");
         this.cw2.width = 50;
         this.cw2.height = 25;
         this.cw2.x = 455;
         this.cw2.y = 45;
         this.界面3.addChild(this.cw2);
         this.cw2.addEventListener(MouseEvent.CLICK,this.添加宠物1P);
         this.cw3 = new Sprite();
         this.cw3 = drawButton("2P添加");
         this.cw3.width = 50;
         this.cw3.height = 25;
         this.cw3.x = 510;
         this.cw3.y = 45;
         this.界面3.addChild(this.cw3);
         this.cw3.addEventListener(MouseEvent.CLICK,this.添加宠物2P);
         this.cw4 = new TextField();
         this.cw4.x = 400;
         this.cw4.y = 75;
         this.cw4.width = 50;
         this.cw4.height = 25;
         this.cw4.background = true;
         this.cw4.border = true;
         this.cw4.type = "input";
         this.cw4.textColor = 16711680;
         this.cw4.text = "装备ID";
         this.界面3.addChild(this.cw4);
         this.cw5 = new Sprite();
         this.cw5 = drawButton("添加");
         this.cw5.width = 50;
         this.cw5.height = 25;
         this.cw5.x = 455;
         this.cw5.y = 75;
         this.界面3.addChild(this.cw5);
         this.cw5.addEventListener(MouseEvent.CLICK,this.添加宠物装备);
         this.cw6 = new TextField();
         this.cw6.x = 400;
         this.cw6.y = 105;
         this.cw6.width = 50;
         this.cw6.height = 25;
         this.cw6.background = true;
         this.cw6.border = true;
         this.cw6.type = "input";
         this.cw6.textColor = 16711680;
         this.cw6.text = "成长之书";
         this.界面3.addChild(this.cw6);
         this.cw7 = new Sprite();
         this.cw7 = drawButton("自定义");
         this.cw7.width = 50;
         this.cw7.height = 25;
         this.cw7.x = 455;
         this.cw7.y = 105;
         this.界面3.addChild(this.cw7);
         this.cw7.addEventListener(MouseEvent.CLICK,this.自定义宠物成长之书);
         this.cw8 = new TextField();
         this.cw8.x = 400;
         this.cw8.y = 135;
         this.cw8.width = 50;
         this.cw8.height = 25;
         this.cw8.background = true;
         this.cw8.border = true;
         this.cw8.type = "input";
         this.cw8.textColor = 16711680;
         this.cw8.text = "秘法卷轴";
         this.界面3.addChild(this.cw8);
         this.cw9 = new Sprite();
         this.cw9 = drawButton("自定义");
         this.cw9.width = 50;
         this.cw9.height = 25;
         this.cw9.x = 455;
         this.cw9.y = 135;
         this.界面3.addChild(this.cw9);
         this.cw9.addEventListener(MouseEvent.CLICK,this.自定义秘法卷轴);
         this.cw10 = new Sprite();
         this.cw10 = drawButton("神兽血脉");
         this.cw10.width = 50;
         this.cw10.height = 25;
         this.cw10.x = 455;
         this.cw10.y = 165;
         this.界面3.addChild(this.cw10);
         this.cw10.addEventListener(MouseEvent.CLICK,this.神兽血脉);
         this.cw11 = new TextField();
         this.cw11.x = 575;
         this.cw11.y = 45;
         this.cw11.width = 50;
         this.cw11.height = 25;
         this.cw11.background = true;
         this.cw11.border = true;
         this.cw11.type = "input";
         this.cw11.textColor = 16711680;
         this.cw11.text = "精灵ID";
         this.界面3.addChild(this.cw11);
         this.cw12 = new Sprite();
         this.cw12 = drawButton("1P添加");
         this.cw12.width = 50;
         this.cw12.height = 25;
         this.cw12.x = 685;
         this.cw12.y = 45;
         this.界面3.addChild(this.cw12);
         this.cw12.addEventListener(MouseEvent.CLICK,this.添加精灵1P);
         this.cw13 = new Sprite();
         this.cw13 = drawButton("2P添加");
         this.cw13.width = 50;
         this.cw13.height = 25;
         this.cw13.x = 740;
         this.cw13.y = 45;
         this.界面3.addChild(this.cw13);
         this.cw13.addEventListener(MouseEvent.CLICK,this.添加精灵2P);
         this.cw14 = new TextField();
         this.cw14.x = 630;
         this.cw14.y = 45;
         this.cw14.width = 50;
         this.cw14.height = 25;
         this.cw14.background = true;
         this.cw14.border = true;
         this.cw14.type = "input";
         this.cw14.textColor = 16711680;
         this.cw14.text = "精灵点数";
         this.界面3.addChild(this.cw14);
         this.cw15 = new TextField();
         this.cw15.x = 575;
         this.cw15.y = 75;
         this.cw15.width = 50;
         this.cw15.height = 25;
         this.cw15.background = true;
         this.cw15.border = true;
         this.cw15.type = "input";
         this.cw15.textColor = 16711680;
         this.cw15.text = "精灵格子";
         this.界面3.addChild(this.cw15);
         this.cw16 = new Sprite();
         this.cw16 = drawButton("通用UP");
         this.cw16.width = 50;
         this.cw16.height = 25;
         this.cw16.x = 630;
         this.cw16.y = 75;
         this.界面3.addChild(this.cw16);
         this.cw16.addEventListener(MouseEvent.CLICK,this.技能1UP);
         this.cw17 = new Sprite();
         this.cw17 = drawButton("被动UP");
         this.cw17.width = 50;
         this.cw17.height = 25;
         this.cw17.x = 685;
         this.cw17.y = 75;
         this.界面3.addChild(this.cw17);
         this.cw17.addEventListener(MouseEvent.CLICK,this.技能2UP);
         this.cw18 = new Sprite();
         this.cw18 = drawButton("主动UP");
         this.cw18.width = 50;
         this.cw18.height = 25;
         this.cw18.x = 740;
         this.cw18.y = 75;
         this.界面3.addChild(this.cw18);
         this.cw18.addEventListener(MouseEvent.CLICK,this.技能3UP);
         this.cw19 = new TextField();
         this.cw19.x = 515;
         this.cw19.y = 105;
         this.cw19.width = 50;
         this.cw19.height = 25;
         this.cw19.background = true;
         this.cw19.border = true;
         this.cw19.type = "input";
         this.cw19.textColor = 16711680;
         this.cw19.text = "宠物格子";
         this.界面3.addChild(this.cw19);
         this.cw20 = new TextField();
         this.cw20.x = 570;
         this.cw20.y = 105;
         this.cw20.width = 50;
         this.cw20.height = 25;
         this.cw20.background = true;
         this.cw20.border = true;
         this.cw20.type = "input";
         this.cw20.textColor = 16711680;
         this.cw20.text = "技能1ID";
         this.界面3.addChild(this.cw20);
         this.cw21 = new TextField();
         this.cw21.x = 625;
         this.cw21.y = 105;
         this.cw21.width = 50;
         this.cw21.height = 25;
         this.cw21.background = true;
         this.cw21.border = true;
         this.cw21.type = "input";
         this.cw21.textColor = 16711680;
         this.cw21.text = "技能2ID";
         this.界面3.addChild(this.cw21);
         this.cw22 = new TextField();
         this.cw22.x = 680;
         this.cw22.y = 105;
         this.cw22.width = 50;
         this.cw22.height = 25;
         this.cw22.background = true;
         this.cw22.border = true;
         this.cw22.type = "input";
         this.cw22.textColor = 16711680;
         this.cw22.text = "技能3ID";
         this.界面3.addChild(this.cw22);
         this.cw23 = new TextField();
         this.cw23.x = 735;
         this.cw23.y = 105;
         this.cw23.width = 50;
         this.cw23.height = 25;
         this.cw23.background = true;
         this.cw23.border = true;
         this.cw23.type = "input";
         this.cw23.textColor = 16711680;
         this.cw23.text = "技能4ID";
         this.界面3.addChild(this.cw23);
         this.cw24 = new Sprite();
         this.cw24 = drawButton("写入技能");
         this.cw24.width = 50;
         this.cw24.height = 25;
         this.cw24.x = 790;
         this.cw24.y = 105;
         this.界面3.addChild(this.cw24);
         this.cw24.addEventListener(MouseEvent.CLICK,this.宠物技能);
         this.cw25 = new TextField();
         this.cw25.x = 510;
         this.cw25.y = 135;
         this.cw25.width = 400;
         this.cw25.height = 400;
         this.cw25.background = false;
         this.cw25.border = true;
         this.cw25.textColor = 16777071;
         this.cw25.text = "✦✦时之吞食者3  73104\n|毒雾之海: 58100 |乱石轰击: 58101 |暗影重击: 58102 |沼毒弹: 58103 |天崩地裂: 58104\n✦✦冰原虎  73107\n|猛虎冲击: 58105 |冰脉冲拳: 58106 |霜冻天下: 58107 |猛虎咆哮: 58108\n✦✦雪月皎狼  73110\n|双重冲击: 58109 |怒狼嚎: 58110 |冰雪之歌: 58111 |皎狼锐抓: 58112\n✦✦圣诞麋鹿  73111\n|恢复图腾: 58113 |玄冰之力: 58114 |极寒雪球: 58115 |寒水战甲: 58116 |圣诞老人的礼物: 58117\n ✦✦冥王龙-湮  73113\n|湮.幽冥龙息: 58118|湮 .幽冥咆哮: 58119|湮.龙陨乱葬: 58120 |湮.冥龙怒袭: 58121\n✦✦机械小驱  73117\n|蓝光镭射炮: 58122 |连环蓝光炮: 58123 |烈火燎原: 58124 |镭射死光: 58125 |蓝光禁区: 58126\n ✦✦圣灵驹  73118\n|圣佑壁垒: 58127 |神罚之罩: 58128 |神光洗礼: 58129 |圣雷铠甲: 58130\n✦✦狂暴小子  73120\n|熔岩天火: 58131 |熔岩地火: 58132 |熔岩石柱: 58133 |狂暴之焰: 58134\n✦✦雷麟龙-傲  73123\n|傲，灭弑雷域: 58135 |傲.雷擎轰击: 58136 |傲.破雷龙击: 58137 |傲.雷磁弧圈: 58138\n ✦✦驭炎龙-烬  73126\n|烬.炎龙爆弹: 58139 |烬. 炎神冲击: 58140 |烬.炎龙咆哮: 58141 |烬. 火焰飓风: 58142\n✦✦钢铁圣诞侠  73130\n|圣诞惊喜: 58143 |速能暴动: 58144 I飞旋暴走: 58145 |高能爆弹: 58146\n✦✦年兽  73133\n|疯狂追逐: 58147|飞旋冲刺: 58148 |绝地咆哮: 58149 |狂兽击: 58150\n ✦✦灵魂勇者  73134\n|虚空能量: 58151 |虚无枷锁: 58152 |虚空之门: 58153 |虚暗爆弹: 58154\n✦✦欺霜龙  73137\n|双重冰墙: 58155 |冰墙: 58156 |凛冬咆哮: 58157 |冰晶爆轰: 58158\n ✦✦功夫小子  73139\n|落地踢: 58159 |旋风踢: 58160 |气功弹: 58161 |无影飞腿: 58162\n ✦✦冰雪萝莉  73141\n|雪球机关枪: 58163 |寒冰水震地: 58164 |暴风雪: 58165 |冰封: 58166\n✦✦孙大圣  73144\n|定身咒: 58167 |金箍棒: 58168 火眼金睛: 58169 |金刚之躯: 58170";
      }
      
      public function 宠物技能(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         _loc16_ = Main.player1.getPetSlot().slot[this.cw19.text - 1];
         if(this.cw20.text != "技能1ID")
         {
            _loc16_.skill[0] = this.cw20.text;
         }
         if(this.cw21.text != "技能2ID")
         {
            _loc16_.skill[1] = this.cw21.text;
         }
         if(this.cw22.text != "技能3ID")
         {
            _loc16_.skill[2] = this.cw22.text;
         }
         if(this.cw23.text != "技能4ID")
         {
            _loc16_.skill[3] = this.cw23.text;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"宠物技能写入成功！！！");
      }
      
      public function 技能1UP(e:MouseEvent) : void
      {
         var i:* = uint(null);
         i = 0;
         while(i < 5)
         {
            Main.player1.getElvesSlot().getElvesFromSlot(this.cw15.text - 1).upSKILL1();
            i++;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"提升成功！！！");
      }
      
      public function 技能2UP(e:MouseEvent) : void
      {
         var i:* = uint(null);
         i = 0;
         while(i < 5)
         {
            Main.player1.getElvesSlot().getElvesFromSlot(this.cw15.text - 1).upSKILL2();
            i++;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"提升成功！！！");
      }
      
      public function 技能3UP(e:MouseEvent) : void
      {
         var i:* = uint(null);
         i = 0;
         while(i < 5)
         {
            Main.player1.getElvesSlot().getElvesFromSlot(this.cw15.text - 1).upSKILL3();
            i++;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"提升成功！！！");
      }
      
      public function 杂项相关(e:MouseEvent) : void
      {
         this.界面2.visible = false;
         this.界面3.visible = false;
         this.界面1.visible = false;
         this.界面4.visible = false;
         this.界面5.visible = true;
         this.yj1 = new Sprite();
         this.yj1 = drawButton("自定义装备");
         this.yj1.width = 60;
         this.yj1.height = 30;
         this.yj1.x = 400;
         this.yj1.y = 45;
         this.界面5.addChild(this.yj1);
         this.yj1.addEventListener(MouseEvent.CLICK,this.自定义装备1);
         this.yj6 = new Sprite();
         this.yj6 = drawButton("全宠物装备");
         this.yj6.width = 60;
         this.yj6.height = 30;
         this.yj6.x = 400;
         this.yj6.y = 80;
         this.界面5.addChild(this.yj6);
         this.yj6.addEventListener(MouseEvent.CLICK,this.一键宠物装备);
         this.yj2 = new Sprite();
         this.yj2 = drawButton("一键装备");
         this.yj2.width = 60;
         this.yj2.height = 30;
         this.yj2.x = 465;
         this.yj2.y = 45;
         this.界面5.addChild(this.yj2);
         this.yj2.addEventListener(MouseEvent.CLICK,this.一键装备);
         this.yj3 = new Sprite();
         this.yj3 = drawButton("一键宠物");
         this.yj3.width = 60;
         this.yj3.height = 30;
         this.yj3.x = 465;
         this.yj3.y = 80;
         this.界面5.addChild(this.yj3);
         this.yj3.addEventListener(MouseEvent.CLICK,this.一键宠物);
         this.yj4 = new Sprite();
         this.yj4 = drawButton("一键精灵");
         this.yj4.width = 60;
         this.yj4.height = 30;
         this.yj4.x = 465;
         this.yj4.y = 115;
         this.界面5.addChild(this.yj4);
         this.yj4.addEventListener(MouseEvent.CLICK,this.一键精灵);
         this.yj5 = new Sprite();
         this.yj5 = drawButton("一键称号");
         this.yj5.width = 60;
         this.yj5.height = 30;
         this.yj5.x = 465;
         this.yj5.y = 150;
         this.界面5.addChild(this.yj5);
         this.yj5.addEventListener(MouseEvent.CLICK,this.一键称号);
         this.yj7 = new Sprite();
         this.yj7 = drawButton("一键消耗品");
         this.yj7.width = 60;
         this.yj7.height = 30;
         this.yj7.x = 530;
         this.yj7.y = 45;
         this.界面5.addChild(this.yj7);
         this.yj7.addEventListener(MouseEvent.CLICK,this.一键消耗品);
         this.yj8 = new Sprite();
         this.yj8 = drawButton("一键印章");
         this.yj8.width = 60;
         this.yj8.height = 30;
         this.yj8.x = 530;
         this.yj8.y = 80;
         this.界面5.addChild(this.yj8);
         this.yj8.addEventListener(MouseEvent.CLICK,this.一键印章);
         this.yj9 = new Sprite();
         this.yj9 = drawButton("一键徽章");
         this.yj9.width = 60;
         this.yj9.height = 30;
         this.yj9.x = 530;
         this.yj9.y = 115;
         this.界面5.addChild(this.yj9);
         this.yj9.addEventListener(MouseEvent.CLICK,this.一键徽章);
         this.yj10 = new Sprite();
         this.yj10 = drawButton("一键女神罗盘");
         this.yj10.width = 60;
         this.yj10.height = 30;
         this.yj10.x = 530;
         this.yj10.y = 150;
         this.界面5.addChild(this.yj10);
         this.yj10.addEventListener(MouseEvent.CLICK,this.一键女神罗盘);
         this.yj11 = new Sprite();
         this.yj11 = drawButton("竞技上榜");
         this.yj11.width = 50;
         this.yj11.height = 30;
         this.yj11.x = 695;
         this.yj11.y = 45;
         this.界面5.addChild(this.yj11);
         this.yj11.addEventListener(MouseEvent.CLICK,this.竞技上榜);
         this.yj12 = new TextField();
         this.yj12.x = 750;
         this.yj12.y = 45;
         this.yj12.width = 100;
         this.yj12.height = 30;
         this.yj12.background = true;
         this.yj12.border = true;
         this.yj12.type = "input";
         this.yj12.textColor = 16711680;
         this.yj12.text = "分数";
         this.界面5.addChild(this.yj12);
         this.yj13 = new Sprite();
         this.yj13 = drawButton("一键技能石");
         this.yj13.width = 60;
         this.yj13.height = 30;
         this.yj13.x = 600;
         this.yj13.y = 45;
         this.界面5.addChild(this.yj13);
         this.yj13.addEventListener(MouseEvent.CLICK,this.一键技能石);
         this.yj14 = new TextField();
         this.yj14.x = 665;
         this.yj14.y = 80;
         this.yj14.width = 180;
         this.yj14.height = 500;
         this.yj14.background = true;
         this.yj14.border = false;
         this.yj14.textColor = 16711680;
         this.yj14.type = "input";
         this.yj14.text = "";
         this.界面5.addChild(this.yj14);
         this.yj15 = new Sprite();
         this.yj15 = drawButton("装备代码");
         this.yj15.width = 60;
         this.yj15.height = 30;
         this.yj15.x = 850;
         this.yj15.y = 80;
         this.界面5.addChild(this.yj15);
         this.yj15.addEventListener(MouseEvent.CLICK,this.装备代码);
         this.yj16 = new Sprite();
         this.yj16 = drawButton("道具代码");
         this.yj16.width = 60;
         this.yj16.height = 30;
         this.yj16.x = 850;
         this.yj16.y = 115;
         this.界面5.addChild(this.yj16);
         this.yj16.addEventListener(MouseEvent.CLICK,this.道具代码);
         this.yj17 = new Sprite();
         this.yj17 = drawButton("药品代码");
         this.yj17.width = 60;
         this.yj17.height = 30;
         this.yj17.x = 850;
         this.yj17.y = 150;
         this.界面5.addChild(this.yj17);
         this.yj17.addEventListener(MouseEvent.CLICK,this.药品代码);
         this.yj18 = new Sprite();
         this.yj18 = drawButton("宝石代码");
         this.yj18.width = 60;
         this.yj18.height = 30;
         this.yj18.x = 850;
         this.yj18.y = 185;
         this.界面5.addChild(this.yj18);
         this.yj18.addEventListener(MouseEvent.CLICK,this.宝石代码);
         this.yj19 = new Sprite();
         this.yj19 = drawButton("宠物代码");
         this.yj19.width = 60;
         this.yj19.height = 30;
         this.yj19.x = 850;
         this.yj19.y = 220;
         this.界面5.addChild(this.yj19);
         this.yj19.addEventListener(MouseEvent.CLICK,this.宠物代码);
         this.yj20 = new Sprite();
         this.yj20 = drawButton("精灵代码");
         this.yj20.width = 60;
         this.yj20.height = 30;
         this.yj20.x = 850;
         this.yj20.y = 255;
         this.界面5.addChild(this.yj20);
         this.yj20.addEventListener(MouseEvent.CLICK,this.精灵代码);
         this.yj21 = new Sprite();
         this.yj21 = drawButton("宠装代码");
         this.yj21.width = 60;
         this.yj21.height = 30;
         this.yj21.x = 850;
         this.yj21.y = 290;
         this.界面5.addChild(this.yj21);
         this.yj21.addEventListener(MouseEvent.CLICK,this.宠装代码);
         this.yj22 = new Sprite();
         this.yj22 = drawButton("称号代码");
         this.yj22.width = 60;
         this.yj22.height = 30;
         this.yj22.x = 850;
         this.yj22.y = 325;
         this.界面5.addChild(this.yj22);
         this.yj22.addEventListener(MouseEvent.CLICK,this.称号代码);
      }
      
      public function 称号代码(e:MouseEvent) : void
      {
         this.yj14.text = "";
         myXml = XMLAsset.createXML(Data2.title);
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         for each(property in myXml.称号)
         {
            id = Number(property.编号);
            name = String(property.名字);
            this.yj14.text += name + ":  " + id + "\n";
         }
      }
      
      public function 宠物代码(e:MouseEvent) : void
      {
         this.yj14.text = "";
         myXml = XMLAsset.createXML(Data2.pet_set);
         myXml2 = XMLAsset.createXML(InData.SkillData);
         var property:XML = null;
         var property1:XML = null;
         var id:Number = NaN;
         var name:String = null;
         for each(property in myXml.宠物)
         {
            id = Number(property.编号);
            name = String(property.名字);
            this.yj14.text += name + ":  " + id + "\n";
            for each(property1 in myXml2.数据)
            {
               if(id == property1.职业要求)
               {
                  this.yj14.text += "★★" + property1.技能名称 + ":  " + property1.编号 + "★★" + "\n";
               }
            }
         }
      }
      
      public function 精灵代码(e:MouseEvent) : void
      {
         this.yj14.text = "";
         myXml = XMLAsset.createXML(Data2.elves);
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         for each(property in myXml.精灵)
         {
            id = Number(property.编号);
            name = String(property.名字);
            this.yj14.text += name + ":  " + id + "\n";
         }
      }
      
      public function 宠装代码(e:MouseEvent) : void
      {
         this.yj14.text = "";
         myXml = XMLAsset.createXML(Data2.petEquip);
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         for each(property in myXml.宠物装备)
         {
            id = Number(property.编号);
            name = String(property.名字);
            this.yj14.text += name + ":  " + id + "\n";
         }
      }
      
      public function 装备代码(e:MouseEvent) : void
      {
         this.yj14.text = "";
         myXml = XMLAsset.createXML(InData.zbData);
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         for each(property in myXml.装备)
         {
            id = Number(property.编号);
            name = String(property.名称);
            this.yj14.text += name + ":  " + id + "\n";
         }
      }
      
      public function 道具代码(e:MouseEvent) : void
      {
         this.yj14.text = "";
         myXml = XMLAsset.createXML(InData.OtherData);
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         for each(property in myXml.其他道具)
         {
            id = Number(property.编号);
            name = String(property.名字);
            this.yj14.text += name + ":  " + id + "\n";
         }
      }
      
      public function 药品代码(e:MouseEvent) : void
      {
         this.yj14.text = "";
         myXml = XMLAsset.createXML(InData.xiaoHaoData);
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         for each(property in myXml.消耗品)
         {
            id = Number(property.编号);
            name = String(property.名称);
            this.yj14.text += name + ":  " + id + "\n";
         }
      }
      
      public function 宝石代码(e:MouseEvent) : void
      {
         this.yj14.text = "";
         myXml = XMLAsset.createXML(InData.baoShiData);
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         for each(property in myXml.宝石)
         {
            id = Number(property.编号);
            name = String(property.名称);
            this.yj14.text += name + ":  " + id + "\n";
         }
      }
      
      public function 一键技能石(e:MouseEvent) : void
      {
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55104)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55114)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55119)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55124)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55129)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55134)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55139)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55144)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55149)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55154)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55159)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55164)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55169)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55174)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55189)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55224)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55194)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55199)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55204)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55209)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55214)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55219)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55224)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55229)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55234)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55239)));
         Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(55244)));
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"怪物+4技能石已放入背包！！！");
      }
      
      public function 竞技上榜(e:MouseEvent) : void
      {
         GameData.TiJiaoFenShu(this.yj12.text);
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"已提交上榜分数！！！");
      }
      
      public function 一键女神罗盘(e:MouseEvent) : void
      {
         Main.questArr = [[true,84110],[true,84111],[true,84112],[true,84113],[true,84114]];
         Main.LuoPanArr = [1,1,1,1];
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"女神罗盘修复完成");
      }
      
      public function 一键徽章(e:MouseEvent) : void
      {
         Main.player1.getBadgeSlot().addToSlot(OtherFactory.creatOther(63164),0);
         Main.player1.getBadgeSlot().addToSlot(OtherFactory.creatOther(63163),1);
         Main.player1.getBadgeSlot().addToSlot(OtherFactory.creatOther(63168),2);
         Main.player1.getBadgeSlot().addToSlot(OtherFactory.creatOther(63166),3);
         Main.player1.getBadgeSlot().addToSlot(OtherFactory.creatOther(63167),4);
         Main.player1.getBadgeSlot().addToSlot(OtherFactory.creatOther(63165),5);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功");
         Main.player2.getBadgeSlot().addToSlot(OtherFactory.creatOther(63164),0);
         Main.player2.getBadgeSlot().addToSlot(OtherFactory.creatOther(63163),1);
         Main.player2.getBadgeSlot().addToSlot(OtherFactory.creatOther(63168),2);
         Main.player2.getBadgeSlot().addToSlot(OtherFactory.creatOther(63166),3);
         Main.player2.getBadgeSlot().addToSlot(OtherFactory.creatOther(63167),4);
         Main.player2.getBadgeSlot().addToSlot(OtherFactory.creatOther(63165),5);
      }
      
      public function 一键印章(e:MouseEvent) : void
      {
         Main.player1.getStampSlot().setSlot1(GemFactory.creatGemById(34715),0);
         Main.player1.getStampSlot().setSlot1(GemFactory.creatGemById(34715),1);
         Main.player1.getStampSlot().setSlot1(GemFactory.creatGemById(34715),2);
         Main.player1.getStampSlot().setSlot1(GemFactory.creatGemById(34715),3);
         Main.player1.getStampSlot().setSlot2(GemFactory.creatGemById(34716),0);
         Main.player1.getStampSlot().setSlot2(GemFactory.creatGemById(34716),1);
         Main.player1.getStampSlot().setSlot2(GemFactory.creatGemById(34716),2);
         Main.player1.getStampSlot().setSlot2(GemFactory.creatGemById(34716),3);
         Main.player1.getStampSlot().setSlot3(GemFactory.creatGemById(34717),0);
         Main.player1.getStampSlot().setSlot3(GemFactory.creatGemById(34717),1);
         Main.player1.getStampSlot().setSlot3(GemFactory.creatGemById(34717),2);
         Main.player1.getStampSlot().setSlot3(GemFactory.creatGemById(34717),3);
         Main.player1.getStampSlot().setSlot4(GemFactory.creatGemById(34718),0);
         Main.player1.getStampSlot().setSlot4(GemFactory.creatGemById(34718),1);
         Main.player1.getStampSlot().setSlot4(GemFactory.creatGemById(34718),2);
         Main.player1.getStampSlot().setSlot4(GemFactory.creatGemById(34718),3);
         Main.player1.getStampSlot().setSlot5(GemFactory.creatGemById(34719),0);
         Main.player1.getStampSlot().setSlot5(GemFactory.creatGemById(34719),1);
         Main.player1.getStampSlot().setSlot5(GemFactory.creatGemById(34719),2);
         Main.player1.getStampSlot().setSlot5(GemFactory.creatGemById(34719),3);
         Main.player1.getStampSlot().setSlot6(GemFactory.creatGemById(34720),0);
         Main.player1.getStampSlot().setSlot6(GemFactory.creatGemById(34720),1);
         Main.player1.getStampSlot().setSlot6(GemFactory.creatGemById(34720),2);
         Main.player1.getStampSlot().setSlot6(GemFactory.creatGemById(34720),3);
         Main.player1.getStampSlot().setSlot7(GemFactory.creatGemById(34721),0);
         Main.player1.getStampSlot().setSlot7(GemFactory.creatGemById(34721),1);
         Main.player1.getStampSlot().setSlot7(GemFactory.creatGemById(34721),2);
         Main.player1.getStampSlot().setSlot7(GemFactory.creatGemById(34721),3);
         Main.player1.getStampSlot().setSlot8(GemFactory.creatGemById(34722),0);
         Main.player1.getStampSlot().setSlot8(GemFactory.creatGemById(34722),1);
         Main.player1.getStampSlot().setSlot8(GemFactory.creatGemById(34722),2);
         Main.player1.getStampSlot().setSlot8(GemFactory.creatGemById(34722),3);
         Main.player1.getStampSlot().setSlot9(GemFactory.creatGemById(34723),0);
         Main.player1.getStampSlot().setSlot9(GemFactory.creatGemById(34723),1);
         Main.player1.getStampSlot().setSlot9(GemFactory.creatGemById(34723),2);
         Main.player1.getStampSlot().setSlot9(GemFactory.creatGemById(34723),3);
         Main.player1.getStampSlot().setSlot10(GemFactory.creatGemById(34724),0);
         Main.player1.getStampSlot().setSlot10(GemFactory.creatGemById(34724),1);
         Main.player1.getStampSlot().setSlot10(GemFactory.creatGemById(34724),2);
         Main.player1.getStampSlot().setSlot10(GemFactory.creatGemById(34724),3);
         Main.player1.getStampSlot().setKeySlot11();
         Main.player1.getStampSlot().setKeySlot11();
         Main.player1.getStampSlot().setKeySlot11();
         Main.player1.getStampSlot().setKeySlot11();
         Main.player1.getStampSlot().setSlot11(GemFactory.creatGemById(34717),0);
         Main.player1.getStampSlot().setSlot11(GemFactory.creatGemById(34717),1);
         Main.player1.getStampSlot().setSlot11(GemFactory.creatGemById(34717),2);
         Main.player1.getStampSlot().setSlot11(GemFactory.creatGemById(34717),3);
         Main.player1.getStampSlot().setSlot12(GemFactory.creatGemById(34755),0);
         Main.player1.getStampSlot().setSlot12(GemFactory.creatGemById(34755),1);
         Main.player1.getStampSlot().setSlot12(GemFactory.creatGemById(34755),2);
         Main.player1.getStampSlot().setSlot12(GemFactory.creatGemById(34755),3);
         Main.player1.getStampSlot().setSlot13(GemFactory.creatGemById(34756),0);
         Main.player1.getStampSlot().setSlot13(GemFactory.creatGemById(34756),1);
         Main.player1.getStampSlot().setSlot13(GemFactory.creatGemById(34756),2);
         Main.player1.getStampSlot().setSlot13(GemFactory.creatGemById(34756),3);
         Main.player1.getStampSlot().setSlot14(GemFactory.creatGemById(34757),0);
         Main.player1.getStampSlot().setSlot14(GemFactory.creatGemById(34757),1);
         Main.player1.getStampSlot().setSlot14(GemFactory.creatGemById(34757),2);
         Main.player1.getStampSlot().setSlot14(GemFactory.creatGemById(34757),3);
         Main.player1.getStampSlot().setSlot15(GemFactory.creatGemById(34758),0);
         Main.player1.getStampSlot().setSlot15(GemFactory.creatGemById(34758),1);
         Main.player1.getStampSlot().setSlot15(GemFactory.creatGemById(34758),2);
         Main.player1.getStampSlot().setSlot15(GemFactory.creatGemById(34758),3);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功");
         Main.player2.getStampSlot().setSlot1(GemFactory.creatGemById(34715),0);
         Main.player2.getStampSlot().setSlot1(GemFactory.creatGemById(34715),1);
         Main.player2.getStampSlot().setSlot1(GemFactory.creatGemById(34715),2);
         Main.player2.getStampSlot().setSlot1(GemFactory.creatGemById(34715),3);
         Main.player2.getStampSlot().setSlot2(GemFactory.creatGemById(34716),0);
         Main.player2.getStampSlot().setSlot2(GemFactory.creatGemById(34716),1);
         Main.player2.getStampSlot().setSlot2(GemFactory.creatGemById(34716),2);
         Main.player2.getStampSlot().setSlot2(GemFactory.creatGemById(34716),3);
         Main.player2.getStampSlot().setSlot3(GemFactory.creatGemById(34717),0);
         Main.player2.getStampSlot().setSlot3(GemFactory.creatGemById(34717),1);
         Main.player2.getStampSlot().setSlot3(GemFactory.creatGemById(34717),2);
         Main.player2.getStampSlot().setSlot3(GemFactory.creatGemById(34717),3);
         Main.player2.getStampSlot().setSlot4(GemFactory.creatGemById(34718),0);
         Main.player2.getStampSlot().setSlot4(GemFactory.creatGemById(34718),1);
         Main.player2.getStampSlot().setSlot4(GemFactory.creatGemById(34718),2);
         Main.player2.getStampSlot().setSlot4(GemFactory.creatGemById(34718),3);
         Main.player2.getStampSlot().setSlot5(GemFactory.creatGemById(34719),0);
         Main.player2.getStampSlot().setSlot5(GemFactory.creatGemById(34719),1);
         Main.player2.getStampSlot().setSlot5(GemFactory.creatGemById(34719),2);
         Main.player2.getStampSlot().setSlot5(GemFactory.creatGemById(34719),3);
         Main.player2.getStampSlot().setSlot6(GemFactory.creatGemById(34720),0);
         Main.player2.getStampSlot().setSlot6(GemFactory.creatGemById(34720),1);
         Main.player2.getStampSlot().setSlot6(GemFactory.creatGemById(34720),2);
         Main.player2.getStampSlot().setSlot6(GemFactory.creatGemById(34720),3);
         Main.player2.getStampSlot().setSlot7(GemFactory.creatGemById(34721),0);
         Main.player2.getStampSlot().setSlot7(GemFactory.creatGemById(34721),1);
         Main.player2.getStampSlot().setSlot7(GemFactory.creatGemById(34721),2);
         Main.player2.getStampSlot().setSlot7(GemFactory.creatGemById(34721),3);
         Main.player2.getStampSlot().setSlot8(GemFactory.creatGemById(34722),0);
         Main.player2.getStampSlot().setSlot8(GemFactory.creatGemById(34722),1);
         Main.player2.getStampSlot().setSlot8(GemFactory.creatGemById(34722),2);
         Main.player2.getStampSlot().setSlot8(GemFactory.creatGemById(34722),3);
         Main.player2.getStampSlot().setSlot9(GemFactory.creatGemById(34723),0);
         Main.player2.getStampSlot().setSlot9(GemFactory.creatGemById(34723),1);
         Main.player2.getStampSlot().setSlot9(GemFactory.creatGemById(34723),2);
         Main.player2.getStampSlot().setSlot9(GemFactory.creatGemById(34723),3);
         Main.player2.getStampSlot().setSlot10(GemFactory.creatGemById(34724),0);
         Main.player2.getStampSlot().setSlot10(GemFactory.creatGemById(34724),1);
         Main.player2.getStampSlot().setSlot10(GemFactory.creatGemById(34724),2);
         Main.player2.getStampSlot().setSlot10(GemFactory.creatGemById(34724),3);
         Main.player2.getStampSlot().setKeySlot11();
         Main.player2.getStampSlot().setKeySlot11();
         Main.player2.getStampSlot().setKeySlot11();
         Main.player2.getStampSlot().setKeySlot11();
         Main.player2.getStampSlot().setSlot11(GemFactory.creatGemById(34717),0);
         Main.player2.getStampSlot().setSlot11(GemFactory.creatGemById(34717),1);
         Main.player2.getStampSlot().setSlot11(GemFactory.creatGemById(34717),2);
         Main.player2.getStampSlot().setSlot11(GemFactory.creatGemById(34717),3);
         Main.player2.getStampSlot().setSlot12(GemFactory.creatGemById(34755),0);
         Main.player2.getStampSlot().setSlot12(GemFactory.creatGemById(34755),1);
         Main.player2.getStampSlot().setSlot12(GemFactory.creatGemById(34755),2);
         Main.player2.getStampSlot().setSlot12(GemFactory.creatGemById(34755),3);
         Main.player2.getStampSlot().setSlot13(GemFactory.creatGemById(34756),0);
         Main.player2.getStampSlot().setSlot13(GemFactory.creatGemById(34756),1);
         Main.player2.getStampSlot().setSlot13(GemFactory.creatGemById(34756),2);
         Main.player2.getStampSlot().setSlot13(GemFactory.creatGemById(34756),3);
         Main.player2.getStampSlot().setSlot14(GemFactory.creatGemById(34757),0);
         Main.player2.getStampSlot().setSlot14(GemFactory.creatGemById(34757),1);
         Main.player2.getStampSlot().setSlot14(GemFactory.creatGemById(34757),2);
         Main.player2.getStampSlot().setSlot14(GemFactory.creatGemById(34757),3);
         Main.player2.getStampSlot().setSlot15(GemFactory.creatGemById(34758),0);
         Main.player2.getStampSlot().setSlot15(GemFactory.creatGemById(34758),1);
         Main.player2.getStampSlot().setSlot15(GemFactory.creatGemById(34758),2);
         Main.player2.getStampSlot().setSlot15(GemFactory.creatGemById(34758),3);
      }
      
      public function 一键道具(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         if(this.b9.text == "1")
         {
            _loc16_ = new Otherobj();
            _loc16_ = Otherobj.creatOther(63100,100);
            Main.player1.getBag().addOtherobjBag(_loc16_);
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功");
      }
      
      public function 一键消耗品(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21234,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21233,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21230,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21226,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(23111,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21223,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21221,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21214,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21114,999);
            Main.player1.getBag().addSuppliesBag(_loc16_);
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功");
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21234,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21233,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21230,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21226,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(23111,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21223,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21221,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21214,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = new Supplies();
            _loc16_ = Supplies.creatSupplies(21114,999);
            Main.player2.getBag().addSuppliesBag(_loc16_);
         }
      }
      
      public function 一键宠物装备(e:MouseEvent) : void
      {
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17080));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17075));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17070));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17065));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17060));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17055));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17050));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17045));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17040));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17035));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17030));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17025));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17020));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17015));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17010));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17005));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17110));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17100));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17090));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17085));
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17095));
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"全部高品质宠物装备添加成功");
      }
      
      public function 一键称号(e:MouseEvent) : void
      {
         var _loc29_:* = null;
         var _loc30_:* = undefined;
         _loc30_ = 0;
         while(_loc30_ < 43)
         {
            _loc30_++;
            _loc29_ = TitleFactory.creatTitle(_loc30_);
            Main.player1.getTitleSlot().addToSlot(_loc29_);
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"添加成功！！！");
         var _loc27_:* = null;
         var _loc28_:* = undefined;
         _loc28_ = 0;
         while(_loc28_ < 43)
         {
            _loc28_++;
            _loc27_ = TitleFactory.creatTitle(_loc28_);
            Main.player2.getTitleSlot().addToSlot(_loc27_);
         }
      }
      
      public function 一键宠物JS() : void
      {
         trace("ExternalInterface回调函数一键宠物JS被调用");
         this.一键宠物(null);
      }
      
      public function 成就全亮JS() : void
      {
         trace("ExternalInterface回调函数成就全亮JS被调用");
         this.成就全亮(null);
      }
      
      public function 过检测JS() : void
      {
         trace("ExternalInterface回调函数过检测JS被调用");
         this.过检测(null);
      }
      
      public function 四职业技能JS() : void
      {
         trace("ExternalInterface回调函数四职业技能JS被调用");
         this.四职业技能(null);
      }
      
      public function 精灵槽解锁JS() : void
      {
         trace("ExternalInterface回调函数精灵槽解锁JS被调用");
         this.精灵槽解锁(null);
      }
      
      public function 特殊栏解锁JS() : void
      {
         trace("ExternalInterface回调函数特殊栏解锁JS被调用");
         this.特殊栏解锁(null);
      }
      
      public function 宠栏解锁JS() : void
      {
         trace("ExternalInterface回调函数宠栏解锁JS被调用");
         this.宠栏解锁(null);
      }
      
      public function 背包解锁JS() : void
      {
         trace("ExternalInterface回调函数背包解锁JS被调用");
         this.背包解锁(null);
      }
      
      public function 一键技能石JS() : void
      {
         trace("ExternalInterface回调函数一键技能石JS被调用");
         this.一键技能石(null);
      }
      
      public function 一键道具JS() : void
      {
         trace("ExternalInterface回调函数一键道具JS被调用");
         this.一键道具(null);
      }
      
      public function 一键消耗品JS() : void
      {
         trace("ExternalInterface回调函数一键消耗品JS被调用");
         this.一键消耗品(null);
      }
      
      public function 一键宠物装备JS() : void
      {
         trace("ExternalInterface回调函数一键宠物装备JS被调用");
         this.一键宠物装备(null);
      }
      
      public function 一键称号JS() : void
      {
         trace("ExternalInterface回调函数一键称号JS被调用");
         this.一键称号(null);
      }
      
      public function 一键精灵JS() : void
      {
         trace("ExternalInterface回调函数一键精灵JS被调用");
         this.一键精灵(null);
      }
      
      public function 一键装备JS() : void
      {
         trace("ExternalInterface回调函数一键装备JS被调用");
         this.一键装备(null);
      }
      
      public function 一键宠物(e:MouseEvent = null) : void
      {
         var _loc16_:* = undefined;
         if(this.b9 && this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73104);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            _loc16_.skill[0] = _loc16_.skill[1] = _loc16_.skill[2] = _loc16_.skill[3] = 58100;
            Main.player1.getPetSlot().addPetSlot(_loc16_);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物添加成功");
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73100);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73105);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73141);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73144);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73107);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73110);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73111);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73113);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73117);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73118);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73120);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73123);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73126);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73130);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73133);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73134);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73137);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73139);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73146);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player1.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73104);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            _loc16_.skill[0] = _loc16_.skill[1] = _loc16_.skill[2] = _loc16_.skill[3] = 58100;
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73146);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            _loc16_.skill[0] = _loc16_.skill[1] = _loc16_.skill[2] = _loc16_.skill[3] = 58100;
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73100);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73105);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73141);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73144);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73107);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73110);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73111);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73113);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73117);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73118);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73120);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73123);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73126);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73130);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73133);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73134);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73137);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = PetFactory.creatPet(73139);
            _loc16_.wuxing = VT.createVT(4);
            _loc16_.setLv(30);
            _loc16_.addLvLimit(10);
            _loc16_.xingge = VT.createVT(3);
            Main.player2.getPetSlot().addPetSlot(_loc16_);
         }
      }
      
      public function 一键精灵(e:MouseEvent) : void
      {
         var _loc16_:* = undefined;
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(1);
            _loc16_.setAllPoint(678);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(2);
            _loc16_.setAllPoint(675);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(3);
            _loc16_.setAllPoint(677);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(4);
            _loc16_.setAllPoint(678);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(5);
            _loc16_.setAllPoint(675);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(6);
            _loc16_.setAllPoint(677);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(7);
            _loc16_.setAllPoint(677);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(8);
            _loc16_.setAllPoint(675);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(9);
            _loc16_.setAllPoint(675);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"全部精灵添加成功");
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(1);
            _loc16_.setAllPoint(678);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(2);
            _loc16_.setAllPoint(675);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(3);
            _loc16_.setAllPoint(677);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(4);
            _loc16_.setAllPoint(678);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(5);
            _loc16_.setAllPoint(675);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(6);
            _loc16_.setAllPoint(677);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(7);
            _loc16_.setAllPoint(677);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(8);
            _loc16_.setAllPoint(675);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
         if(this.b9.text == "1")
         {
            _loc16_ = null;
            _loc16_ = ElvesFactory.creatElves(9);
            _loc16_.setAllPoint(675);
            _loc16_.setLevel(10);
            _loc16_.blueEquipNum = VT.createVT(0);
            _loc16_.pinkEquipNum = VT.createVT(0);
            _loc16_.goldEquipNum = VT.createVT(0);
            Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         }
      }
      
      public function 一键装备(e:MouseEvent) : void
      {
         var 血耀战甲:Equip = null;
         var 血耀头饰:Equip = null;
         var 血耀项链:Equip = null;
         var 血耀指环:Equip = null;
         var 剑:Equip = null;
         var 杖:Equip = null;
         var 拳:Equip = null;
         var 刀:Equip = null;
         var 神舞战甲:Equip = null;
         var 神舞头饰:Equip = null;
         var 神舞项链:Equip = null;
         var 神舞指环:Equip = null;
         var 海底1:Equip = null;
         var 海底2:Equip = null;
         var 海底3:Equip = null;
         var 海底4:Equip = null;
         var 时装1:Equip = null;
         var 时装2:Equip = null;
         var 翅膀3:Equip = null;
         var 时装3:Equip = null;
         var 翅膀4:Equip = null;
         var 时装4:Equip = null;
         var 翅膀1:Equip = null;
         var 翅膀2:Equip = null;
         var baseAttrib:Array = null;
         var xxjbs:Gem = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         if(this.b9.text == "1")
         {
            血耀战甲 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(3957)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,4,Number(518)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            血耀战甲 = EquipFactory.createEquipByID(14561);
            血耀战甲._baseAttrib = baseAttrib;
            血耀战甲.setNewSkill(57230);
            血耀战甲.changeReinforce(12,1,10117);
            血耀战甲.setInGem(xxjbs);
            血耀战甲._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            血耀战甲._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(血耀战甲);
         }
         if(this.b9.text == "1")
         {
            血耀头饰 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(1007)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,2,Number(1001)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(457)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,4,Number(264)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            血耀头饰 = EquipFactory.createEquipByID(14567);
            血耀头饰._baseAttrib = baseAttrib;
            血耀头饰.setNewSkill(57230);
            血耀头饰.changeReinforce(12,2,5953);
            血耀头饰.setInGem(xxjbs);
            血耀头饰._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            血耀头饰._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(血耀头饰);
         }
         if(this.b9.text == "1")
         {
            血耀项链 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,2,Number(2956)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,4,Number(264)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            血耀项链 = EquipFactory.createEquipByID(14579);
            血耀项链._baseAttrib = baseAttrib;
            血耀项链.setNewSkill(57230);
            血耀项链.changeReinforce(12,4,992);
            血耀项链.setInGem(xxjbs);
            血耀项链._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            血耀项链._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(血耀项链);
         }
         if(this.b9.text == "1")
         {
            血耀指环 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(6901)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(753)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            血耀指环 = EquipFactory.createEquipByID(14573);
            血耀指环._baseAttrib = baseAttrib;
            血耀指环.setNewSkill(57230);
            血耀指环.changeReinforce(12,5,3041);
            血耀指环.setInGem(xxjbs);
            血耀指环._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            血耀指环._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(血耀指环);
         }
         if(this.b9.text == "1")
         {
            神舞战甲 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(4598)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,4,Number(720)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            神舞战甲 = EquipFactory.createEquipByID(14513);
            神舞战甲._baseAttrib = baseAttrib;
            神舞战甲.setNewSkill(57230);
            神舞战甲.changeReinforce(12,1,10117);
            神舞战甲.setInGem(xxjbs);
            神舞战甲._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            神舞战甲._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(神舞战甲);
         }
         if(this.b9.text == "1")
         {
            神舞头饰 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(1171)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,2,Number(1054)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(368)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,4,Number(367)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            神舞头饰 = EquipFactory.createEquipByID(14519);
            神舞头饰._baseAttrib = baseAttrib;
            神舞头饰.setNewSkill(57230);
            神舞头饰.changeReinforce(12,2,5953);
            神舞头饰.setInGem(xxjbs);
            神舞头饰._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            神舞头饰._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(神舞头饰);
         }
         if(this.b9.text == "1")
         {
            神舞项链 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,2,Number(3112)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,4,Number(367)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            神舞项链 = EquipFactory.createEquipByID(14531);
            神舞项链._baseAttrib = baseAttrib;
            神舞项链.setNewSkill(57230);
            神舞项链.changeReinforce(12,4,992);
            神舞项链.setInGem(xxjbs);
            神舞项链._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            神舞项链._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(神舞项链);
         }
         if(this.b9.text == "1")
         {
            神舞指环 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(8021)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(606)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            神舞指环 = EquipFactory.createEquipByID(14525);
            神舞指环._baseAttrib = baseAttrib;
            神舞指环.setNewSkill(57230);
            神舞指环.changeReinforce(12,5,3041);
            神舞指环.setInGem(xxjbs);
            神舞指环._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            神舞指环._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(神舞指环);
         }
         if(this.b9.text == "1")
         {
            剑 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(1779)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            剑 = EquipFactory.createEquipByID(14738);
            剑._baseAttrib = baseAttrib;
            剑.setNewSkill(57230);
            剑.changeReinforce(10,3,435);
            剑.setInGem(xxjbs);
            剑._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,0);
            剑._blessAttrib.setBeishu(13);
            Main.player_1.data.getBag().addEquipBag(剑);
            Main.player_1.data.getBag().addEquipBag(剑);
         }
         if(this.b9.text == "1")
         {
            杖 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(2039)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            杖 = EquipFactory.createEquipByID(14744);
            杖._baseAttrib = baseAttrib;
            杖.setNewSkill(57230);
            杖.changeReinforce(10,3,471);
            杖.setInGem(xxjbs);
            杖._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,0);
            杖._blessAttrib.setBeishu(13);
            Main.player_1.data.getBag().addEquipBag(杖);
            Main.player_1.data.getBag().addEquipBag(杖);
         }
         if(this.b9.text == "1")
         {
            拳 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(1504)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            拳 = EquipFactory.createEquipByID(14750);
            拳._baseAttrib = baseAttrib;
            拳.setNewSkill(57230);
            拳.changeReinforce(10,3,387);
            拳.setInGem(xxjbs);
            拳._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,0);
            拳._blessAttrib.setBeishu(13);
            Main.player_1.data.getBag().addEquipBag(拳);
            Main.player_1.data.getBag().addEquipBag(拳);
         }
         if(this.b9.text == "1")
         {
            刀 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(1609)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(24)));
            刀 = EquipFactory.createEquipByID(100777);
            刀._baseAttrib = baseAttrib;
            刀.setNewSkill(57230);
            刀.changeReinforce(10,3,387);
            刀.setInGem(xxjbs);
            刀._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,0);
            刀._blessAttrib.setBeishu(13);
            Main.player_1.data.getBag().addEquipBag(刀);
            Main.player_1.data.getBag().addEquipBag(刀);
         }
         if(this.b9.text == "1")
         {
            海底1 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(1846)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            海底1 = EquipFactory.createEquipByID(20078);
            海底1._baseAttrib = baseAttrib;
            海底1.setNewSkill(57230);
            海底1.changeReinforce(10,3,1840);
            Main.player_1.data.getBag().addEquipBag(海底1);
         }
         if(this.b9.text == "1")
         {
            海底2 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(10986)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            海底2 = EquipFactory.createEquipByID(20084);
            海底2._baseAttrib = baseAttrib;
            海底2.setNewSkill(57230);
            海底2.changeReinforce(10,1,10980);
            Main.player_1.data.getBag().addEquipBag(海底2);
         }
         if(this.b9.text == "1")
         {
            海底3 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,9,Number(200)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            海底3 = EquipFactory.createEquipByID(20090);
            海底3._baseAttrib = baseAttrib;
            海底3.setNewSkill(57230);
            海底3.changeReinforce(10,9,200);
            Main.player_1.data.getBag().addEquipBag(海底3);
         }
         if(this.b9.text == "1")
         {
            海底4 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,10,Number(200)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(40)));
            海底4 = EquipFactory.createEquipByID(20096);
            海底4._baseAttrib = baseAttrib;
            海底4.setNewSkill(57230);
            海底4.changeReinforce(10,10,200);
            Main.player_1.data.getBag().addEquipBag(海底4);
         }
         if(this.b9.text == "1")
         {
            时装1 = new Equip();
            时装1 = EquipFactory.createEquipByID(14780);
            时装1.setNewSkill(57230);
            时装1.changeReinforce(10,4,418);
            时装1.setInGem(xxjbs);
            时装1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            时装1._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(时装1);
         }
         if(this.b9.text == "1")
         {
            时装2 = new Equip();
            时装2 = EquipFactory.createEquipByID(100784);
            时装2.setNewSkill(57230);
            时装2.changeReinforce(10,4,418);
            时装2.setInGem(xxjbs);
            时装2._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            时装2._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(时装2);
         }
         if(this.b9.text == "1")
         {
            翅膀1 = new Equip();
            翅膀1 = EquipFactory.createEquipByID(14786);
            翅膀1.setNewSkill(57230);
            翅膀1.changeReinforce(10,3,435);
            翅膀1.setInGem(xxjbs);
            翅膀1._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            翅膀1._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(翅膀1);
         }
         if(this.b9.text == "1")
         {
            翅膀2 = new Equip();
            翅膀2 = EquipFactory.createEquipByID(100790);
            翅膀2.setNewSkill(57230);
            翅膀2.changeReinforce(10,3,435);
            翅膀2.setInGem(xxjbs);
            翅膀2._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            翅膀2._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(翅膀2);
         }
         if(this.b9.text == "1")
         {
            时装3 = new Equip();
            时装3 = EquipFactory.createEquipByID(100808);
            时装3.setNewSkill(57230);
            时装3.changeReinforce(10,4,418);
            时装3.setInGem(xxjbs);
            时装3._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            时装3._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(时装3);
         }
         if(this.b9.text == "1")
         {
            翅膀3 = new Equip();
            翅膀3 = EquipFactory.createEquipByID(100814);
            翅膀3.setNewSkill(57230);
            翅膀3.changeReinforce(10,3,435);
            翅膀3.setInGem(xxjbs);
            翅膀3._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            翅膀3._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(翅膀3);
         }
         if(this.b9.text == "1")
         {
            时装4 = new Equip();
            时装4 = EquipFactory.createEquipByID(100842);
            时装4.setNewSkill(57230);
            时装4.changeReinforce(10,4,418);
            时装4.setInGem(xxjbs);
            时装4._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            时装4._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(时装4);
         }
         if(this.b9.text == "1")
         {
            翅膀4 = new Equip();
            翅膀4 = EquipFactory.createEquipByID(100848);
            翅膀4.setNewSkill(57230);
            翅膀4.changeReinforce(10,3,435);
            翅膀4.setInGem(xxjbs);
            翅膀4._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,180);
            翅膀4._blessAttrib.setBeishu(1.5);
            Main.player_1.data.getBag().addEquipBag(翅膀4);
         }
         var 海底11:Equip = null;
         var 海底22:Equip = null;
         var 海底33:Equip = null;
         var 海底44:Equip = null;
         baseAttrib = null;
         xxjbs = new Gem();
         xxjbs = GemFactory.creatGemById(33615);
         var 剑11:Equip = null;
         var 杖11:Equip = null;
         var 拳11:Equip = null;
         var 刀11:Equip = null;
         if(this.b9.text == "1")
         {
            剑11 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(3814)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            剑11 = EquipFactory.createEquipByID(100910);
            剑11._baseAttrib = baseAttrib;
            剑11.setNewSkill(57230);
            剑11.changeReinforce(12,3,780);
            剑11.setInGem(xxjbs);
            剑11._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,0);
            剑11._blessAttrib.setBeishu(13);
            Main.player_1.data.getBag().addEquipBag(剑11);
            Main.player_1.data.getBag().addEquipBag(剑11);
         }
         if(this.b9.text == "1")
         {
            杖11 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(4132)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            杖11 = EquipFactory.createEquipByID(100930);
            杖11._baseAttrib = baseAttrib;
            杖11.setNewSkill(57230);
            杖11.changeReinforce(12,3,840);
            杖11.setInGem(xxjbs);
            杖11._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,0);
            杖11._blessAttrib.setBeishu(13);
            Main.player_1.data.getBag().addEquipBag(杖11);
            Main.player_1.data.getBag().addEquipBag(杖11);
         }
         if(this.b9.text == "1")
         {
            拳11 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(3496)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            拳11 = EquipFactory.createEquipByID(100950);
            拳11._baseAttrib = baseAttrib;
            拳11.setNewSkill(57230);
            拳11.changeReinforce(12,3,720);
            拳11.setInGem(xxjbs);
            拳11._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,0);
            拳11._blessAttrib.setBeishu(13);
            Main.player_1.data.getBag().addEquipBag(拳11);
            Main.player_1.data.getBag().addEquipBag(拳11);
         }
         if(this.b9.text == "1")
         {
            刀11 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(3179)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            刀11 = EquipFactory.createEquipByID(100970);
            刀11._baseAttrib = baseAttrib;
            刀11.setNewSkill(57230);
            刀11.changeReinforce(12,3,720);
            刀11.setInGem(xxjbs);
            刀11._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,3,0);
            刀11._blessAttrib.setBeishu(13);
            Main.player_1.data.getBag().addEquipBag(刀11);
            Main.player_1.data.getBag().addEquipBag(刀11);
         }
         if(this.b9.text == "1")
         {
            海底11 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(3198)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            海底11 = EquipFactory.createEquipByID(20110);
            海底11._baseAttrib = baseAttrib;
            海底11.setNewSkill(57230);
            海底11.changeReinforce(12,3,2556);
            Main.player_1.data.getBag().addEquipBag(海底11);
         }
         if(this.b9.text == "1")
         {
            海底22 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(18228)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            海底22 = EquipFactory.createEquipByID(20130);
            海底22._baseAttrib = baseAttrib;
            海底22.setNewSkill(57230);
            海底22.changeReinforce(12,1,14580);
            Main.player_1.data.getBag().addEquipBag(海底22);
         }
         if(this.b9.text == "1")
         {
            海底33 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,9,Number(540)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            海底33 = EquipFactory.createEquipByID(20150);
            海底33._baseAttrib = baseAttrib;
            海底33.setNewSkill(57230);
            海底33.changeReinforce(12,9,360);
            Main.player_1.data.getBag().addEquipBag(海底33);
         }
         if(this.b9.text == "1")
         {
            海底44 = new Equip();
            baseAttrib = [];
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,10,Number(540)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,3,Number(60)));
            海底44 = EquipFactory.createEquipByID(20170);
            海底44._baseAttrib = baseAttrib;
            海底44.setNewSkill(57230);
            海底44.changeReinforce(12,10,360);
            Main.player_1.data.getBag().addEquipBag(海底44);
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"一键成品装备添加成功！！！");
      }
      
      public function 添加精灵1P(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         _loc16_ = ElvesFactory.creatElves(this.cw11.text);
         _loc16_.setAllPoint(this.cw14.text);
         _loc16_.setLevel(10);
         _loc16_.blueEquipNum = VT.createVT(0);
         _loc16_.pinkEquipNum = VT.createVT(0);
         _loc16_.goldEquipNum = VT.createVT(0);
         Main.player1.getElvesSlot().addElvesSlot(_loc16_);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"1P精灵添加成功");
      }
      
      public function 添加精灵2P(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         _loc16_ = ElvesFactory.creatElves(this.cw11.text);
         _loc16_.setAllPoint(this.cw14.text);
         _loc16_.setLevel(10);
         _loc16_.blueEquipNum = VT.createVT(0);
         _loc16_.pinkEquipNum = VT.createVT(0);
         _loc16_.goldEquipNum = VT.createVT(0);
         Main.player2.getElvesSlot().addElvesSlot(_loc16_);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P精灵添加成功");
      }
      
      public function 神兽血脉(e:MouseEvent) : void
      {
         NewPetPanel.XueMai.xingge = new Array(1,2,3,4,5,6,7,8,9,10,11,12);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"神兽血脉写入成功");
      }
      
      public function 自定义宠物成长之书(e:MouseEvent) : void
      {
         NewPetPanel.LVkey.setValue(int(this.cw6.text));
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义宠物成长之书成功");
      }
      
      public function 自定义秘法卷轴(e:MouseEvent) : void
      {
         NewPetPanel.XGkey.setValue(int(this.cw8.text));
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义秘法卷轴成功");
      }
      
      public function 添加宠物1P(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         _loc16_ = PetFactory.creatPet(this.cw1.text);
         _loc16_.wuxing = VT.createVT(4);
         _loc16_.setLv(30);
         _loc16_.addLvLimit(10);
         _loc16_.xingge = VT.createVT(3);
         Main.player1.getPetSlot().addPetSlot(_loc16_);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物添加成功");
      }
      
      public function 添加宠物2P(e:MouseEvent) : void
      {
         var _loc16_:* = null;
         _loc16_ = PetFactory.creatPet(this.cw1.text);
         _loc16_.wuxing = VT.createVT(4);
         _loc16_.setLv(30);
         _loc16_.addLvLimit(10);
         _loc16_.xingge = VT.createVT(3);
         Main.player2.getPetSlot().addPetSlot(_loc16_);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P宠物添加成功");
      }
      
      public function 添加宠物装备(e:MouseEvent) : void
      {
         NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(this.cw4.text));
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物装备添加成功");
      }
      
      public function 自定义装备(e:MouseEvent) : void
      {
         var xxjbs:Gem = new Gem();
         var xxj:Equip = new Equip();
         var baseAttrib:Array = null;
         var arr:Array = null;
         arr = [];
         baseAttrib = [];
         if(this.zbzdy1.text != "固定生命")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,1,Number(this.zbzdy1.text)));
         }
         if(this.zbzdy2.text != "固定魔法")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,2,Number(this.zbzdy2.text)));
         }
         if(this.zbzdy3.text != "固定攻击")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,3,Number(this.zbzdy3.text)));
         }
         if(this.zbzdy4.text != "固定防御")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,4,Number(this.zbzdy4.text)));
         }
         if(this.zbzdy5.text != "固定暴击")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,5,Number(this.zbzdy5.text)));
         }
         if(this.zbzdy6.text != "固定闪避")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,6,Number(this.zbzdy6.text)));
         }
         if(this.zbzdy7.text != "固定移速")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,7,Number(this.zbzdy7.text)));
         }
         if(this.zbzdy8.text != "固定硬值")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,8,Number(this.zbzdy8.text)));
         }
         if(this.zbzdy9.text != "固定魔抗")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,9,Number(this.zbzdy9.text)));
         }
         if(this.zbzdy10.text != "固定破魔")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,10,Number(this.zbzdy10.text)));
         }
         if(this.zbzdy11.text != "固定回血")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,11,Number(this.zbzdy11.text)));
         }
         if(this.zbzdy12.text != "固定回魔")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(1,12,Number(this.zbzdy12.text)));
         }
         if(this.zbzdy13.text != "随机TYPE1")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,this.zbzdy13.text,Number(this.zbzdy16.text)));
         }
         if(this.zbzdy14.text != "随机TYPE2")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,this.zbzdy14.text,Number(this.zbzdy17.text)));
         }
         if(this.zbzdy15.text != "随机TYPE3")
         {
            baseAttrib.push(EquipBaseAttrib.creatEquipBaseAttrib(3,this.zbzdy15.text,Number(this.zbzdy18.text)));
         }
         if(this.zbzdy26.text != "宝石TYPE1")
         {
            arr.push(Attribute.creatAttribute(this.zbzdy26.text,this.zbzdy29.text));
         }
         if(this.zbzdy27.text != "宝石TYPE2")
         {
            arr.push(Attribute.creatAttribute(this.zbzdy27.text,this.zbzdy30.text));
         }
         if(this.zbzdy28.text != "宝石TYPE3")
         {
            arr.push(Attribute.creatAttribute(this.zbzdy28.text,this.zbzdy31.text));
         }
         if(this.zbzdy35.text != "宝石TYPE4")
         {
            arr.push(Attribute.creatAttribute(this.zbzdy35.text,this.zbzdy36.text));
         }
         xxjbs = Gem.creatGem(this.zbzdy25.text,1,arr);
         xxj = EquipFactory.createEquipByID(this.zbzdy32.text);
         xxj._baseAttrib = baseAttrib;
         if(this.zbzdy19.text != "强化等级")
         {
            xxj.changeReinforce(this.zbzdy19.text,this.zbzdy20.text,this.zbzdy21.text);
         }
         if(this.zbzdy25.text != "宝石ID")
         {
            xxj.setInGem(xxjbs);
         }
         if(this.zbzdy33.text != "增幅ID")
         {
            xxj.setNewSkill(this.zbzdy33.text);
         }
         if(this.zbzdy22.text != "二阶祝福数值")
         {
            xxj._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,this.zbzdy23.text,this.zbzdy22.text);
            xxj._blessAttrib.setBeishu(1.5);
         }
         if(this.zbzdy24.text != "星灵王祝福LV")
         {
            xxj._blessAttrib = EquipBaseAttrib.creatEquipBaseAttrib(2,this.zbzdy23.text,180);
            xxj._blessAttrib.setBeishu(this.zbzdy24.text + 1);
         }
         Main.player_1.data.getBag().addEquipBag(xxj);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"自定义装备添加成功！！！");
      }
      
      public function 自定义装备1(e:MouseEvent) : void
      {
         this.界面2.visible = false;
         this.界面3.visible = false;
         this.界面1.visible = false;
         this.界面5.visible = false;
         this.界面4.visible = true;
         this.zbzdy32 = new TextField();
         this.zbzdy32.x = 400;
         this.zbzdy32.y = 45;
         this.zbzdy32.width = 70;
         this.zbzdy32.height = 25;
         this.zbzdy32.background = true;
         this.zbzdy32.border = true;
         this.zbzdy32.type = "input";
         this.zbzdy32.textColor = 16711680;
         this.zbzdy32.text = "装备ID";
         this.界面4.addChild(this.zbzdy32);
         this.zbzdy33 = new TextField();
         this.zbzdy33.x = 475;
         this.zbzdy33.y = 45;
         this.zbzdy33.width = 70;
         this.zbzdy33.height = 25;
         this.zbzdy33.background = true;
         this.zbzdy33.border = true;
         this.zbzdy33.type = "input";
         this.zbzdy33.textColor = 16711680;
         this.zbzdy33.text = "增幅ID";
         this.界面4.addChild(this.zbzdy33);
         this.zbzdy34 = new Sprite();
         this.zbzdy34 = drawButton("添加");
         this.zbzdy34.width = 70;
         this.zbzdy34.height = 25;
         this.zbzdy34.x = 560;
         this.zbzdy34.y = 45;
         this.界面4.addChild(this.zbzdy34);
         this.zbzdy34.addEventListener(MouseEvent.CLICK,this.自定义装备);
         this.zbzdy1 = new TextField();
         this.zbzdy1.x = 400;
         this.zbzdy1.y = 75;
         this.zbzdy1.width = 70;
         this.zbzdy1.height = 25;
         this.zbzdy1.background = true;
         this.zbzdy1.border = true;
         this.zbzdy1.type = "input";
         this.zbzdy1.textColor = 16711680;
         this.zbzdy1.text = "固定生命";
         this.界面4.addChild(this.zbzdy1);
         this.zbzdy2 = new TextField();
         this.zbzdy2.x = 475;
         this.zbzdy2.y = 75;
         this.zbzdy2.width = 70;
         this.zbzdy2.height = 25;
         this.zbzdy2.background = true;
         this.zbzdy2.border = true;
         this.zbzdy2.type = "input";
         this.zbzdy2.textColor = 16711680;
         this.zbzdy2.text = "固定魔法";
         this.界面4.addChild(this.zbzdy2);
         this.zbzdy3 = new TextField();
         this.zbzdy3.x = 550;
         this.zbzdy3.y = 75;
         this.zbzdy3.width = 70;
         this.zbzdy3.height = 25;
         this.zbzdy3.background = true;
         this.zbzdy3.border = true;
         this.zbzdy3.type = "input";
         this.zbzdy3.textColor = 16711680;
         this.zbzdy3.text = "固定攻击";
         this.界面4.addChild(this.zbzdy3);
         this.zbzdy4 = new TextField();
         this.zbzdy4.x = 625;
         this.zbzdy4.y = 75;
         this.zbzdy4.width = 70;
         this.zbzdy4.height = 25;
         this.zbzdy4.background = true;
         this.zbzdy4.border = true;
         this.zbzdy4.type = "input";
         this.zbzdy4.textColor = 16711680;
         this.zbzdy4.text = "固定防御";
         this.界面4.addChild(this.zbzdy4);
         this.zbzdy5 = new TextField();
         this.zbzdy5.x = 400;
         this.zbzdy5.y = 105;
         this.zbzdy5.width = 70;
         this.zbzdy5.height = 25;
         this.zbzdy5.background = true;
         this.zbzdy5.border = true;
         this.zbzdy5.type = "input";
         this.zbzdy5.textColor = 16711680;
         this.zbzdy5.text = "固定暴击";
         this.界面4.addChild(this.zbzdy5);
         this.zbzdy6 = new TextField();
         this.zbzdy6.x = 475;
         this.zbzdy6.y = 105;
         this.zbzdy6.width = 70;
         this.zbzdy6.height = 25;
         this.zbzdy6.background = true;
         this.zbzdy6.border = true;
         this.zbzdy6.type = "input";
         this.zbzdy6.textColor = 16711680;
         this.zbzdy6.text = "固定闪避";
         this.界面4.addChild(this.zbzdy6);
         this.zbzdy7 = new TextField();
         this.zbzdy7.x = 550;
         this.zbzdy7.y = 105;
         this.zbzdy7.width = 70;
         this.zbzdy7.height = 25;
         this.zbzdy7.background = true;
         this.zbzdy7.border = true;
         this.zbzdy7.type = "input";
         this.zbzdy7.textColor = 16711680;
         this.zbzdy7.text = "固定移速";
         this.界面4.addChild(this.zbzdy7);
         this.zbzdy8 = new TextField();
         this.zbzdy8.x = 625;
         this.zbzdy8.y = 105;
         this.zbzdy8.width = 70;
         this.zbzdy8.height = 25;
         this.zbzdy8.background = true;
         this.zbzdy8.border = true;
         this.zbzdy8.type = "input";
         this.zbzdy8.textColor = 16711680;
         this.zbzdy8.text = "固定硬值";
         this.界面4.addChild(this.zbzdy8);
         this.zbzdy9 = new TextField();
         this.zbzdy9.x = 400;
         this.zbzdy9.y = 135;
         this.zbzdy9.width = 70;
         this.zbzdy9.height = 25;
         this.zbzdy9.background = true;
         this.zbzdy9.border = true;
         this.zbzdy9.type = "input";
         this.zbzdy9.textColor = 16711680;
         this.zbzdy9.text = "固定魔抗";
         this.界面4.addChild(this.zbzdy9);
         this.zbzdy10 = new TextField();
         this.zbzdy10.x = 475;
         this.zbzdy10.y = 135;
         this.zbzdy10.width = 70;
         this.zbzdy10.height = 25;
         this.zbzdy10.background = true;
         this.zbzdy10.border = true;
         this.zbzdy10.type = "input";
         this.zbzdy10.textColor = 16711680;
         this.zbzdy10.text = "固定破魔";
         this.界面4.addChild(this.zbzdy10);
         this.zbzdy11 = new TextField();
         this.zbzdy11.x = 550;
         this.zbzdy11.y = 135;
         this.zbzdy11.width = 70;
         this.zbzdy11.height = 25;
         this.zbzdy11.background = true;
         this.zbzdy11.border = true;
         this.zbzdy11.type = "input";
         this.zbzdy11.textColor = 16711680;
         this.zbzdy11.text = "固定回血";
         this.界面4.addChild(this.zbzdy11);
         this.zbzdy12 = new TextField();
         this.zbzdy12.x = 625;
         this.zbzdy12.y = 135;
         this.zbzdy12.width = 70;
         this.zbzdy12.height = 25;
         this.zbzdy12.background = true;
         this.zbzdy12.border = true;
         this.zbzdy12.type = "input";
         this.zbzdy12.textColor = 16711680;
         this.zbzdy12.text = "固定回魔";
         this.界面4.addChild(this.zbzdy12);
         this.zbzdy13 = new TextField();
         this.zbzdy13.x = 400;
         this.zbzdy13.y = 165;
         this.zbzdy13.width = 70;
         this.zbzdy13.height = 25;
         this.zbzdy13.background = true;
         this.zbzdy13.border = true;
         this.zbzdy13.type = "input";
         this.zbzdy13.textColor = 16711680;
         this.zbzdy13.text = "随机TYPE1";
         this.界面4.addChild(this.zbzdy13);
         this.zbzdy14 = new TextField();
         this.zbzdy14.x = 475;
         this.zbzdy14.y = 165;
         this.zbzdy14.width = 70;
         this.zbzdy14.height = 25;
         this.zbzdy14.background = true;
         this.zbzdy14.border = true;
         this.zbzdy14.type = "input";
         this.zbzdy14.textColor = 16711680;
         this.zbzdy14.text = "随机TYPE2";
         this.界面4.addChild(this.zbzdy14);
         this.zbzdy15 = new TextField();
         this.zbzdy15.x = 550;
         this.zbzdy15.y = 165;
         this.zbzdy15.width = 70;
         this.zbzdy15.height = 25;
         this.zbzdy15.background = true;
         this.zbzdy15.border = true;
         this.zbzdy15.type = "input";
         this.zbzdy15.textColor = 16711680;
         this.zbzdy15.text = "随机TYPE3";
         this.界面4.addChild(this.zbzdy15);
         this.zbzdy16 = new TextField();
         this.zbzdy16.x = 400;
         this.zbzdy16.y = 195;
         this.zbzdy16.width = 70;
         this.zbzdy16.height = 25;
         this.zbzdy16.background = true;
         this.zbzdy16.border = true;
         this.zbzdy16.type = "input";
         this.zbzdy16.textColor = 16711680;
         this.zbzdy16.text = "随机数值1";
         this.界面4.addChild(this.zbzdy16);
         this.zbzdy17 = new TextField();
         this.zbzdy17.x = 475;
         this.zbzdy17.y = 195;
         this.zbzdy17.width = 70;
         this.zbzdy17.height = 25;
         this.zbzdy17.background = true;
         this.zbzdy17.border = true;
         this.zbzdy17.type = "input";
         this.zbzdy17.textColor = 16711680;
         this.zbzdy17.text = "随机数值2";
         this.界面4.addChild(this.zbzdy17);
         this.zbzdy18 = new TextField();
         this.zbzdy18.x = 550;
         this.zbzdy18.y = 195;
         this.zbzdy18.width = 70;
         this.zbzdy18.height = 25;
         this.zbzdy18.background = true;
         this.zbzdy18.border = true;
         this.zbzdy18.type = "input";
         this.zbzdy18.textColor = 16711680;
         this.zbzdy18.text = "随机数值3";
         this.界面4.addChild(this.zbzdy18);
         this.zbzdy19 = new TextField();
         this.zbzdy19.x = 400;
         this.zbzdy19.y = 225;
         this.zbzdy19.width = 70;
         this.zbzdy19.height = 25;
         this.zbzdy19.background = true;
         this.zbzdy19.border = true;
         this.zbzdy19.type = "input";
         this.zbzdy19.textColor = 16711680;
         this.zbzdy19.text = "强化等级";
         this.界面4.addChild(this.zbzdy19);
         this.zbzdy20 = new TextField();
         this.zbzdy20.x = 475;
         this.zbzdy20.y = 225;
         this.zbzdy20.width = 70;
         this.zbzdy20.height = 25;
         this.zbzdy20.background = true;
         this.zbzdy20.border = true;
         this.zbzdy20.type = "input";
         this.zbzdy20.textColor = 16711680;
         this.zbzdy20.text = "强化TYPE";
         this.界面4.addChild(this.zbzdy20);
         this.zbzdy21 = new TextField();
         this.zbzdy21.x = 550;
         this.zbzdy21.y = 225;
         this.zbzdy21.width = 70;
         this.zbzdy21.height = 25;
         this.zbzdy21.background = true;
         this.zbzdy21.border = true;
         this.zbzdy21.type = "input";
         this.zbzdy21.textColor = 16711680;
         this.zbzdy21.text = "强化数值";
         this.界面4.addChild(this.zbzdy21);
         this.zbzdy22 = new TextField();
         this.zbzdy22.x = 400;
         this.zbzdy22.y = 255;
         this.zbzdy22.width = 70;
         this.zbzdy22.height = 25;
         this.zbzdy22.background = true;
         this.zbzdy22.border = true;
         this.zbzdy22.type = "input";
         this.zbzdy22.textColor = 16711680;
         this.zbzdy22.text = "二阶祝福数值";
         this.界面4.addChild(this.zbzdy22);
         this.zbzdy23 = new TextField();
         this.zbzdy23.x = 475;
         this.zbzdy23.y = 255;
         this.zbzdy23.width = 70;
         this.zbzdy23.height = 25;
         this.zbzdy23.background = true;
         this.zbzdy23.border = true;
         this.zbzdy23.type = "input";
         this.zbzdy23.textColor = 16711680;
         this.zbzdy23.text = "祝福TYPE";
         this.界面4.addChild(this.zbzdy23);
         this.zbzdy24 = new TextField();
         this.zbzdy24.x = 550;
         this.zbzdy24.y = 255;
         this.zbzdy24.width = 70;
         this.zbzdy24.height = 25;
         this.zbzdy24.background = true;
         this.zbzdy24.border = true;
         this.zbzdy24.type = "input";
         this.zbzdy24.textColor = 16711680;
         this.zbzdy24.text = "星灵王祝福LV";
         this.界面4.addChild(this.zbzdy24);
         this.zbzdy25 = new TextField();
         this.zbzdy25.x = 400;
         this.zbzdy25.y = 285;
         this.zbzdy25.width = 70;
         this.zbzdy25.height = 25;
         this.zbzdy25.background = true;
         this.zbzdy25.border = true;
         this.zbzdy25.type = "input";
         this.zbzdy25.textColor = 16711680;
         this.zbzdy25.text = "宝石ID";
         this.界面4.addChild(this.zbzdy25);
         this.zbzdy26 = new TextField();
         this.zbzdy26.x = 475;
         this.zbzdy26.y = 285;
         this.zbzdy26.width = 70;
         this.zbzdy26.height = 25;
         this.zbzdy26.background = true;
         this.zbzdy26.border = true;
         this.zbzdy26.type = "input";
         this.zbzdy26.textColor = 16711680;
         this.zbzdy26.text = "宝石TYPE1";
         this.界面4.addChild(this.zbzdy26);
         this.zbzdy27 = new TextField();
         this.zbzdy27.x = 550;
         this.zbzdy27.y = 285;
         this.zbzdy27.width = 70;
         this.zbzdy27.height = 25;
         this.zbzdy27.background = true;
         this.zbzdy27.border = true;
         this.zbzdy27.type = "input";
         this.zbzdy27.textColor = 16711680;
         this.zbzdy27.text = "宝石TYPE2";
         this.界面4.addChild(this.zbzdy27);
         this.zbzdy28 = new TextField();
         this.zbzdy28.x = 625;
         this.zbzdy28.y = 285;
         this.zbzdy28.width = 70;
         this.zbzdy28.height = 25;
         this.zbzdy28.background = true;
         this.zbzdy28.border = true;
         this.zbzdy28.type = "input";
         this.zbzdy28.textColor = 16711680;
         this.zbzdy28.text = "宝石TYPE3";
         this.界面4.addChild(this.zbzdy28);
         this.zbzdy29 = new TextField();
         this.zbzdy29.x = 475;
         this.zbzdy29.y = 315;
         this.zbzdy29.width = 70;
         this.zbzdy29.height = 25;
         this.zbzdy29.background = true;
         this.zbzdy29.border = true;
         this.zbzdy29.type = "input";
         this.zbzdy29.textColor = 16711680;
         this.zbzdy29.text = "宝石数值1";
         this.界面4.addChild(this.zbzdy29);
         this.zbzdy30 = new TextField();
         this.zbzdy30.x = 550;
         this.zbzdy30.y = 315;
         this.zbzdy30.width = 70;
         this.zbzdy30.height = 25;
         this.zbzdy30.background = true;
         this.zbzdy30.border = true;
         this.zbzdy30.type = "input";
         this.zbzdy30.textColor = 16711680;
         this.zbzdy30.text = "宝石数值2";
         this.界面4.addChild(this.zbzdy30);
         this.zbzdy31 = new TextField();
         this.zbzdy31.x = 625;
         this.zbzdy31.y = 315;
         this.zbzdy31.width = 70;
         this.zbzdy31.height = 25;
         this.zbzdy31.background = true;
         this.zbzdy31.border = true;
         this.zbzdy31.type = "input";
         this.zbzdy31.textColor = 16711680;
         this.zbzdy31.text = "宝石数值3";
         this.界面4.addChild(this.zbzdy31);
         this.zbzdy36 = new TextField();
         this.zbzdy36.x = 700;
         this.zbzdy36.y = 315;
         this.zbzdy36.width = 70;
         this.zbzdy36.height = 25;
         this.zbzdy36.background = true;
         this.zbzdy36.border = true;
         this.zbzdy36.type = "input";
         this.zbzdy36.textColor = 16711680;
         this.zbzdy36.text = "宝石数值4";
         this.界面4.addChild(this.zbzdy36);
         this.zbzdy35 = new TextField();
         this.zbzdy35.x = 700;
         this.zbzdy35.y = 285;
         this.zbzdy35.width = 70;
         this.zbzdy35.height = 25;
         this.zbzdy35.background = true;
         this.zbzdy35.border = true;
         this.zbzdy35.type = "input";
         this.zbzdy35.textColor = 16711680;
         this.zbzdy35.text = "宝石TYPE4";
         this.界面4.addChild(this.zbzdy35);
         this.zbzdy37 = new TextField();
         this.zbzdy37.x = 720;
         this.zbzdy37.y = 45;
         this.zbzdy37.width = 140;
         this.zbzdy37.height = 100;
         this.zbzdy37.backgroundColor = 16777071;
         this.zbzdy37.background = true;
         this.zbzdy37.border = false;
         this.zbzdy37.textColor = 16711884;
         this.zbzdy37.text = "✦✦增幅ID：\n\n|攻击增幅：57230\n|暴击增幅：57231\n|闪避增幅：57232 无用\n|生命增幅：57233\n|防御增幅：57234\n";
         this.界面4.addChild(this.zbzdy37);
      }
      
      public function 测试专用(e:MouseEvent) : void
      {
         if(this.b8.text == "1")
         {
            Panel_youling.lvArr = [100,100,100,100,100];
         }
         if(this.b8.text == "2")
         {
            Panel_youling.bzNumArr = [100,100,100,100,100];
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"测试");
      }
      
      public function 祝福界面1(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,1,4);
      }
      
      public function 祝福界面2(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,2,3);
      }
      
      public function 祝福界面3(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,3,1);
      }
      
      public function 祝福界面4(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,4,2);
      }
      
      public function 祝福界面5(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,5,5);
      }
      
      public function 祝福界面6(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,6,8);
      }
      
      public function 祝福界面7(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,7,4);
      }
      
      public function 祝福界面8(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,8,3);
      }
      
      public function 祝福界面9(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,9,1);
      }
      
      public function 祝福界面10(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,10,2);
      }
      
      public function 祝福界面11(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,11,5);
      }
      
      public function 祝福界面12(e:MouseEvent) : void
      {
         JinHuaPanel.open(true,12,8);
      }
      
      public function 星灵王祝福界面(e:MouseEvent) : void
      {
         JinHuaPanel2.open(true);
      }
      
      public function 添加装备1P(e:MouseEvent) : void
      {
         this._loc15_ = 0;
         while(this._loc15_ < this.zb2.text)
         {
            Main.player_1.data.getBag().addEquipBag(EquipFactory.createEquipByID(int(this.zb1.text)));
            ++this._loc15_;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"1P添加装备成功");
      }
      
      public function 添加装备2P(e:MouseEvent) : void
      {
         while(this.zb2.text > 0)
         {
            Main.player_2.data.getBag().addEquipBag(EquipFactory.createEquipByID(int(this.zb1.text)));
            this.zb2.text--;
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"2P添加装备成功");
      }
      
      public function toggleSaveEditor(e:MouseEvent) : void
      {
         if(saveEditor)
         {
            saveEditor.toggle();
         }
      }
      
      override public function get stage() : Stage
      {
         return LoaderInfo.prototype["__stage"];
      }
   }
}

