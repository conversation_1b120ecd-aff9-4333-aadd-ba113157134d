package src
{
   import com.hotpoint.braveManIII.views.setTransfer.*;
   import flash.display.*;
   import flash.events.*;
   import src.tool.*;
   
   public class Map extends MovieClip
   {
      public static var MapLoaded:Boolean;
      
      public static var MapArr:Array = new Array();
      
      public var speed:int = 0;
      
      public var paths:Array = [];
      
      public var nextNode:int = 1;
      
      public var _width:int;
      
      public var runX:int = 0;
      
      public var runY:int = 0;
      
      public var MapData:MovieClip = new MovieClip();
      
      public var MapData1:MovieClip = new MovieClip();
      
      public var MapData2:MovieClip = new MovieClip();
      
      public var moveChild_Player:MovieClip = new MovieClip();
      
      public var moveChild_Enemy:MovieClip = new MovieClip();
      
      public var moveChild_ChongWu:MovieClip = new MovieClip();
      
      public var moveChild_Other:MovieClip = new MovieClip();
      
      public var moveChild_Back:MovieClip = new MovieClip();
      
      public var moveChild_Other2:MovieClip = new MovieClip();
      
      public var stopYn:Boolean = false;
      
      public var stopX_1:int = 0;
      
      public var stopX_2:int = 1600;
      
      public var stopNumArr:Array = [0,1200,2400,3600,4800,5980];
      
      public var stopNumArr57:Array = [0,1200,2900,4200,5900];
      
      public var stopNumArr59:Array = [0,1450,2750,4350,5670];
      
      private var quakeNum:int = 10;
      
      private var quakeTime:int = 0;
      
      public function Map()
      {
         super();
         if(this["_Data"])
         {
            this.MapData = this["_Data"];
            if(this.MapData["Data1"])
            {
               this.MapData1 = this.MapData["Data1"];
            }
            if(this.MapData["Data2"])
            {
               this.MapData2 = this.MapData["Data2"];
            }
            this.stopX_1 = 0;
            if(this["moveChild"])
            {
               this._width = this.stopX_2 = this["moveChild"].width;
            }
            else
            {
               this._width = this.stopX_2 = 1600;
            }
            if(Main.gameNum.getValue() == 82 && Main.gameNum2.getValue() == 5)
            {
               this._width = this.stopX_2 = 1200;
            }
            if(Main.gameNum.getValue() == 99999)
            {
               this._width = this.stopX_2 = 940;
            }
            addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            addEventListener(Event.REMOVED_FROM_STAGE,this.onOver);
         }
         if(this["moveChild"])
         {
            this["moveChild"].addChild(this.moveChild_Back);
            this["moveChild"].addChild(this.moveChild_ChongWu);
            this["moveChild"].addChild(this.moveChild_Enemy);
            this["moveChild"].addChild(this.moveChild_Player);
            this["moveChild"].addChild(this.moveChild_Other);
            this["moveChild"].addChild(this.moveChild_Other2);
            if(this["map_3"])
            {
               this["moveChild"].addChild(this["map_3"]);
               this["map_3"].mouseChildren = this["map_3"].mouseEnabled = false;
            }
         }
         else
         {
            addChild(this.moveChild_Back);
            addChild(this.moveChild_ChongWu);
            addChild(this.moveChild_Enemy);
            addChild(this.moveChild_Player);
            addChild(this.moveChild_Other);
            addChild(this.moveChild_Other2);
            if(this["map_3"])
            {
               addChild(this["map_3"]);
               this["map_3"].mouseChildren = this["map_3"].mouseEnabled = false;
            }
         }
         this.moveChild_Other.mouseChildren = this.moveChild_Other.mouseEnabled = false;
         this.moveChild_Player.mouseChildren = this.moveChild_Player.mouseEnabled = false;
         this.moveChild_ChongWu.mouseChildren = this.moveChild_ChongWu.mouseEnabled = false;
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            this.CaoNiMa0();
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 1)
         {
            this.CaoNiMa1();
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 2)
         {
            this.CaoNiMa2();
         }
         Main._stage.stageFocusRect = false;
         Main._stage.focus = this;
      }
      
      public function NoStop() : *
      {
         Main.world.stopYn = false;
         this.stopX_1 = 0;
         if(this["moveChild"])
         {
            this.stopX_2 = this["moveChild"].width - 10;
         }
         else
         {
            this.stopX_2 = 1600;
         }
      }
      
      private function onENTER_FRAME(param1:Event) : *
      {
         var _loc2_:Array = this.stopNumArr;
         if(this["stopNumArr" + Main.gameNum.getValue()])
         {
            _loc2_ = this["stopNumArr" + Main.gameNum.getValue()];
         }
         if(!this.stopYn && Main.gameNum.getValue() > 50 && Main.gameNum.getValue() < 81)
         {
            if(Main.world.x <= -_loc2_[Main.gameNum2.getValue()] + 940 || Main.world.x <= -Main.world._width + 500)
            {
               this.stopYn = true;
               this.stopX_1 = _loc2_[Main.gameNum2.getValue() - 1];
               this.stopX_2 = _loc2_[Main.gameNum2.getValue()];
               if(this.stopX_2 - this.stopX_1 <= 940)
               {
                  this.stopX_1 = this.stopX_2 - 930;
               }
            }
         }
         if(this != Main.world)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            return;
         }
         this.背景移动();
         this.地震();
         if(this["黑暗神殿"])
         {
            if(Main.LuoPanArr[0] == 1 && Main.LuoPanArr[1] == 1 && Main.LuoPanArr[2] == 1 && Main.LuoPanArr[3] == 1)
            {
               (this["黑暗神殿"] as Door).Open();
            }
            else
            {
               (this["黑暗神殿"] as Door).Close();
            }
         }
      }
      
      private function Run() : *
      {
         this.runX = this.runY = 0;
         if(this.paths.length > 1)
         {
            if(this.x > this.paths[this.nextNode][0])
            {
               if(this.x - this.speed < this.paths[this.nextNode][0])
               {
                  this.x = this.paths[this.nextNode][0];
               }
               else
               {
                  this.x -= this.speed;
                  this.runX = -this.speed;
               }
            }
            else if(this.x < this.paths[this.nextNode][0])
            {
               if(this.x + this.speed > this.paths[this.nextNode][0])
               {
                  this.x = this.paths[this.nextNode][0];
               }
               else
               {
                  this.x += this.speed;
                  this.runX = this.speed;
               }
            }
            if(this.y > this.paths[this.nextNode][1])
            {
               if(this.y - this.speed < this.paths[this.nextNode][1])
               {
                  this.y = this.paths[this.nextNode][1];
               }
               else
               {
                  this.y -= this.speed;
               }
            }
            else if(this.y < this.paths[this.nextNode][1])
            {
               if(this.y + this.speed > this.paths[this.nextNode][1])
               {
                  this.y = this.paths[this.nextNode][1];
               }
               else
               {
                  this.y += this.speed;
               }
            }
            if(this.x == this.paths[this.nextNode][0] && this.y == this.paths[this.nextNode][1])
            {
               if(this.nextNode + 1 == this.paths.length)
               {
                  this.nextNode = 0;
               }
               else
               {
                  ++this.nextNode;
               }
            }
         }
      }
      
      public function 背景移动() : *
      {
         if(this["_move"])
         {
            this["_move"].x = this.x * 0.5 - this.x;
         }
      }
      
      public function onOver(param1:*) : *
      {
         Map.MapLoaded = false;
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onOver);
      }
      
      public function GetPathsAndSpeed(param1:Array, param2:int = 5) : *
      {
         this.paths = param1;
         this.speed = param2;
      }
      
      public function Quake(param1:int = 4, param2:int = 10) : *
      {
         if(this.quakeTime < param1)
         {
            this.quakeTime = param1;
         }
         this.quakeNum = param2;
      }
      
      private function 地震() : *
      {
         if(this.quakeTime <= 0)
         {
            this.y = 0;
            return;
         }
         if(this.quakeTime % 2 == 0)
         {
            this.y = -this.quakeNum;
         }
         else
         {
            this.y = 0;
         }
         --this.quakeTime;
      }
      
      public function CaoNiMa0() : *
      {
         addEventListener(Event.ADDED_TO_STAGE,this.onCaoNiMa0);
      }
      
      private function XueHua() : *
      {
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         var _loc1_:Number = 0;
         while(_loc1_ < 200)
         {
            _loc2_ = Math.random() * 1600;
            _loc3_ = Math.random() * 600;
            NewMC.Open("雪",this.moveChild_Other,_loc2_,_loc3_);
            _loc1_++;
         }
      }
      
      public function onCaoNiMa0(param1:*) : *
      {
         this.开启Show0();
         removeEventListener(Event.ADDED_TO_STAGE,this.onCaoNiMa0);
      }
      
      public function 开启Show0() : *
      {
         if(this["转职"])
         {
            this["转职"].visible = false;
         }
      }
      
      private function 转职面板(param1:*) : *
      {
         SetTransferPanel.open();
      }
      
      public function CaoNiMa1() : *
      {
         addEventListener(Event.ADDED_TO_STAGE,this.onCaoNiMa1);
      }
      
      public function onCaoNiMa1(param1:*) : *
      {
         this.开启Show();
         var _loc2_:int = 0;
         while(_loc2_ <= 4)
         {
            this["X_btn_8411" + _loc2_].addEventListener(MouseEvent.CLICK,this.Cangjingkong);
            _loc2_++;
         }
         removeEventListener(Event.ADDED_TO_STAGE,this.onCaoNiMa1);
      }
      
      public function 开启(param1:String) : *
      {
         this["X_btn_8411" + param1].gotoAndPlay(2);
         NewMC.Open("文字提示",Main._stage,400,350,60,0,true,2,"好像有某个神秘地带的入口被开启了");
      }
      
      public function 开启Show() : *
      {
         var _loc1_:int = 0;
         for(_loc1_ in Main.questArr)
         {
            if(Main.questArr[_loc1_][0])
            {
               this["X_btn_8411" + _loc1_].gotoAndStop(this["X_btn_8411" + _loc1_].totalFrames);
            }
         }
      }
      
      public function Cangjingkong(param1:MouseEvent) : *
      {
         var _loc2_:Number = Number((param1.target.name as String).substr(6,5));
         var _loc3_:Number = Number((param1.target.name as String).substr(10,1));
         var _loc4_:Boolean = Boolean(Main.getQuest(_loc2_));
         if(_loc4_)
         {
            return;
         }
         this.CangjingkongGoto(_loc2_,_loc3_);
      }
      
      public function CangjingkongGoto(param1:String, param2:String) : void
      {
         var _loc3_:Boolean = false;
         var _loc4_:int = 1;
         while(_loc4_ <= 2)
         {
            if(Boolean(Main["player" + _loc4_]) && Boolean(Main["player" + _loc4_].getBag().fallQusetBag(param1)))
            {
               _loc3_ = true;
               break;
            }
            _loc4_++;
         }
         if(_loc3_)
         {
            this.开启(param2);
            Main.setQuest(param1);
            _loc4_ = 1;
            while(_loc4_ <= 2)
            {
               if(Boolean(Main["player" + _loc4_]) && Boolean(Main["player" + _loc4_].getBag().fallQusetBag(param1)))
               {
                  Main["player" + _loc4_] && Main["player" + _loc4_].getBag().clearQuest(param1);
               }
               _loc4_++;
            }
         }
      }
      
      public function CaoNiMa2() : *
      {
         var _loc1_:* = undefined;
         for(_loc1_ in Main.questArr)
         {
            if(!Main.questArr[_loc1_][0])
            {
               this["Ying_" + (_loc1_ + 1)].visible = false;
            }
         }
         addEventListener(Event.ENTER_FRAME,this.OtherGame);
      }
      
      private function OtherGame(param1:*) : *
      {
         var _loc2_:* = undefined;
         var _loc3_:MovieClip = null;
         var _loc4_:Player = null;
         if(this != Main.world)
         {
            removeEventListener(Event.ENTER_FRAME,this.OtherGame);
            return;
         }
         for(_loc2_ in Main.questArr)
         {
            _loc3_ = this["Ying_" + (_loc2_ + 1)];
            if(_loc3_.visible)
            {
               i2 = 0;
               while(i2 < Player.All.length)
               {
                  _loc4_ = Player.All[i2];
                  if(_loc4_.hit && _loc3_.hitTestObject(_loc4_.hit) && _loc4_.getKeyStatus("上",2))
                  {
                     this.InOtherGame(_loc2_ + 1);
                  }
                  ++i2;
               }
            }
         }
      }
      
      private function InOtherGame(param1:int = 1) : *
      {
         var _loc2_:String = "10" + param1;
         Main.gameNum.setValue(int(_loc2_));
         Main.gameNum2.setValue(1);
         SelMap.Open(0,0,2);
      }
      
      public function CaoNiMa3() : *
      {
         addEventListener(Event.ADDED_TO_STAGE,this.onCaoNiMa3);
         this.XueHua();
      }
      
      public function onCaoNiMa3(param1:*) : *
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onCaoNiMa3);
      }
   }
}

