package src
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.repository.*;
   import com.hotpoint.braveManIII.repository.achievement.*;
   import com.hotpoint.braveManIII.repository.chest.*;
   import com.hotpoint.braveManIII.repository.elves.*;
   import com.hotpoint.braveManIII.repository.equipShop.*;
   import com.hotpoint.braveManIII.repository.jingLingCatch.*;
   import com.hotpoint.braveManIII.repository.monsterCard.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.petEquip.*;
   import com.hotpoint.braveManIII.repository.plan.*;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.repository.task.*;
   import com.hotpoint.braveManIII.repository.title.*;
   import com.hotpoint.braveManIII.repository.tuijianShop.*;
   import com.hotpoint.braveManIII.repository.wantedTask.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import src._data.*;
   import src.tool.*;
   
   public class Data2
   {
      public static var Enemy_md5_2:String;
      
      public static var skill_md5_2:String;
      
      public static var renWu_md5_2:String;
      
      public static const petHit_data:Class = Data2_petHit_data;
      
      public static const pet_set:Class = Data2_pet_set;
      
      public static const AchNum:Class = Data2_AchNum;
      
      public static const title:Class = Data2_title;
      
      public static const reward:Class = Data2_reward;
      
      public static const taskData:Class = Data2_taskData;
      
      public static const elves:Class = Data2_elves;
      
      public static const chestData:Class = Data2_chestData;
      
      public static const probability:Class = Data2_probability;
      
      public static const property:Class = Data2_property;
      
      public static const monster:Class = Data2_monster;
      
      public static const xingLing:Class = Data2_xingLing;
      
      public static const jihua:Class = Data2_jihua;
      
      public static const petEquip:Class = Data2_petEquip;
      
      public static const shopData:Class = Data2_shopData;
      
      public static const gameObjData:Class = Data2_gameObjData;
      
      public static const duiHuanData:Class = Data2_duiHuanData;
      
      public static const eShop:Class = Data2_eShop;
      
      public static const jinglingbuzhuo:Class = Data2_jinglingbuzhuo;
      
      public static const sengJi:Class = Data2_sengJi;
      
      public static const tjShop:Class = Data2_tjShop;
      
      public static const zhuFu2:Class = Data2_zhuFu2;
      
      public static const gkData1:Class = Data2_gkData1;
      
      public static const gkData2:Class = Data2_gkData2;
      
      public static const gkData3:Class = Data2_gkData3;
      
      public static const gkData4:Class = Data2_gkData4;
      
      public static const gkData5:Class = Data2_gkData5;
      
      public static const gkData6:Class = Data2_gkData6;
      
      public static const gwData1:Class = Data2_gwData1;
      
      public static const gwData2:Class = Data2_gwData2;
      
      public static const gwData3:Class = Data2_gwData3;
      
      public static const gwData4:Class = Data2_gwData4;
      
      public static const gwData5:Class = Data2_gwData5;
      
      public static const gwData6:Class = Data2_gwData6;
      
      public static const gwgjData1:Class = Data2_gwgjData1;
      
      public static const gwgjData2:Class = Data2_gwgjData2;
      
      public static const gwgjData3:Class = Data2_gwgjData3;
      
      public static const gwgjData4:Class = Data2_gwgjData4;
      
      public static const gwgjData5:Class = Data2_gwgjData5;
      
      public static const gwgjData6:Class = Data2_gwgjData6;
      
      public static var Enemy_md5_1:String = "3987d8c39a409f5a22357b41f71bcd76";
      
      public static var skill_md5_1:String = "4403453a814caa72e285c76e9ec5084a";
      
      public static var renWu_md5_1:String = "7315bf6aa95c5277bbe01d2ef8182dd5";
      
      public function Data2()
      {
         super();
      }
      
      public static function Init() : *
      {
         GameData.GameDataXmlArr[1] = XMLAsset.createXML(Data2.gkData1);
         GameData.GameDataXmlArr[2] = XMLAsset.createXML(Data2.gkData2);
         GameData.GameDataXmlArr[3] = XMLAsset.createXML(Data2.gkData3);
         GameData.GameDataXmlArr[4] = XMLAsset.createXML(Data2.gkData4);
         GameData.GameDataXmlArr[5] = XMLAsset.createXML(Data2.gkData5);
         GameData.GameDataXmlArr[6] = XMLAsset.createXML(Data2.gkData6);
         Enemy.EnemyXmlArr[1] = XMLAsset.createXML(Data2.gwData1);
         Enemy.EnemyXmlArr[2] = XMLAsset.createXML(Data2.gwData2);
         Enemy.EnemyXmlArr[3] = XMLAsset.createXML(Data2.gwData3);
         Enemy.EnemyXmlArr[4] = XMLAsset.createXML(Data2.gwData4);
         Enemy.EnemyXmlArr[5] = XMLAsset.createXML(Data2.gwData5);
         Enemy.EnemyXmlArr[6] = XMLAsset.createXML(Data2.gwData6);
         EnemySkin.EnemySkinXmlArr[1] = new Array();
         EnemySkin.EnemySkinXmlArr[1][0] = XMLAsset.createXML(Data2.gwgjData1);
         EnemySkin.EnemySkinXmlArr[2] = new Array();
         EnemySkin.EnemySkinXmlArr[2][0] = XMLAsset.createXML(Data2.gwgjData2);
         EnemySkin.EnemySkinXmlArr[3] = new Array();
         EnemySkin.EnemySkinXmlArr[3][0] = XMLAsset.createXML(Data2.gwgjData3);
         EnemySkin.EnemySkinXmlArr[4] = new Array();
         EnemySkin.EnemySkinXmlArr[4][0] = XMLAsset.createXML(Data2.gwgjData4);
         EnemySkin.EnemySkinXmlArr[5] = new Array();
         EnemySkin.EnemySkinXmlArr[5][0] = XMLAsset.createXML(Data2.gwgjData5);
         EnemySkin.EnemySkinXmlArr[6] = new Array();
         EnemySkin.EnemySkinXmlArr[6][0] = XMLAsset.createXML(Data2.gwgjData6);
         EnemySkinXML_Init();
         PaiHang_Data.creatFactory();
         PetEquipFactory.creatpetEquipFactory();
         PetFactory.creatPetFactory();
         ChongWu.get_XML_data();
         AchNumFactory.creatAchNumFactory();
         TitleFactory.creatTitleFactory();
         WantedFactory.creatWantedFactory();
         TaskFactory.creatTaskFactory();
         ElvesFactory.creatElvesFactory();
         ChestFactory.creatChestFactory();
         EquipProbabilityFactory.creatProbabilltyData();
         EquipPropertyFactory.creatEquipData();
         MonsterFactory.creatMonsterFactory();
         PlanFactory.creatPlanFactory();
         XingLingFactory.creatFactory();
         GaneObjFactory.creatFactory();
         ShopFactory.creatFactory();
         equipShopFactory.creatEquipShopFactory();
         tuijianFactory.creatTuiJianFactory();
         JLCFactory.creatJLCFactory();
         Zhufu2Factory.creatZhufu2Factory();
         DataMD5();
      }
      
      public static function EnemySkinXML_Init() : *
      {
         var _loc3_:XML = null;
         var _loc4_:* = undefined;
         var _loc5_:int = 0;
         var _loc6_:XML = null;
         var _loc7_:XML = null;
         var _loc1_:int = EnemySkin.EnemySkinXmlArr.length - 1;
         TiaoShi.txtShow("怪物攻击表分割关卡优化 max = " + _loc1_);
         var _loc2_:int = 1;
         while(_loc2_ <= _loc1_)
         {
            _loc3_ = EnemySkin.EnemySkinXmlArr[_loc2_][0];
            for(_loc4_ in _loc3_.怪物攻击)
            {
               _loc5_ = int(_loc3_.怪物攻击[_loc4_].关卡);
               if(!EnemySkin.EnemySkinXmlArr[_loc2_][_loc5_])
               {
                  EnemySkin.EnemySkinXmlArr[_loc2_][_loc5_] = new XML();
                  EnemySkin.EnemySkinXmlArr[_loc2_][_loc5_] = <root></root>;
               }
               _loc6_ = EnemySkin.EnemySkinXmlArr[_loc2_][_loc5_];
               _loc7_ = _loc3_.怪物攻击[_loc4_];
               _loc6_.appendChild(_loc7_);
            }
            _loc2_++;
         }
      }
      
      public static function DataMD5() : *
      {
         var _loc1_:int = 0;
         while(_loc1_ < Enemy.EnemyXmlArr.length + 1)
         {
            Enemy.EnemyXmlArrMd5[_loc1_] = Obj_Compare.getObj_ByteArray(Enemy.EnemyXmlArr[_loc1_]);
            _loc1_++;
         }
         Enemy_md5_2 = MD5contrast.getObj_MD5String(Enemy.EnemyXmlArr);
         if(Enemy_md5_1 != Enemy_md5_2)
         {
            Main.NoGame("怪物数据验证错误");
         }
         skill_md5_2 = MD5contrast.getObj_MD5String(SkillFactory.myXml);
         if(skill_md5_1 != skill_md5_2)
         {
            Main.NoGame("技能列表数据异常");
         }
         renWu_md5_2 = MD5contrast.getObj_MD5String(TaskFactory.myXml);
         if(renWu_md5_1 != renWu_md5_2)
         {
            Main.NoGame("任务列表数据异常");
         }
      }
      
      public static function EmenyDataMD5() : *
      {
         var _loc1_:int = 0;
         while(_loc1_ < Enemy.EnemyXmlArr.length + 1)
         {
            Enemy.EnemyXmlArrMd5[_loc1_] = Obj_Compare.getObj_ByteArray(Enemy.EnemyXmlArr[_loc1_]);
            _loc1_++;
         }
         Enemy_md5_2 = MD5contrast.getObj_MD5String(Enemy.EnemyXmlArr);
         if(Enemy_md5_1 != Enemy_md5_2)
         {
            Main.NoGame("怪物数据验证错误");
         }
      }
   }
}

