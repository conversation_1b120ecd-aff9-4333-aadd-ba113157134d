package com.hotpoint.braveManIII.views.taskPanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.models.task.Task;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.text.TextField;
   import src.*;
   import src.tool.*;
   
   public class TaskPanel extends MovieClip
   {
      private static var _instance:TaskPanel;
      
      public static var loadData:ClassLoader;
      
      public static var open_yn:Boolean;
      
      public static var loadName:String = "Panel_RW_v842.swf";
      
      private var state:uint = 0;
      
      private var smallState:uint = 0;
      
      private var targetId:uint = 0;
      
      private var taskSlot:TaskSlot;
      
      private var textDisplay:TaskDisplay;
      
      private var nowData:Array = [];
      
      private var glNum0:VT;
      
      private var glNum1:VT;
      
      private var glNum2:VT;
      
      private var tooltip:ItemsTooltip;
      
      private var taskXX_num:uint;
      
      public var xh_mc:*;
      
      public var task_mast:*;
      
      public var qx_btn:*;
      
      public var qd_btn:*;
      
      public var qd_btn2:*;
      
      public var finishBtn:*;
      
      public var new_1:*;
      
      public var new_2:*;
      
      public var new_3:*;
      
      public var new_4:*;
      
      public var new_5:*;
      
      public var b_0:*;
      
      public var b_1:*;
      
      public var b_2:*;
      
      public var b_3:*;
      
      public var b_4:*;
      
      public var b_5:*;
      
      public var qz_0:*;
      
      public var n_0:*;
      
      public var n_1:*;
      
      public var n_2:*;
      
      public var n_3:*;
      
      public var n_4:*;
      
      public var n_5:*;
      
      public var n_6:*;
      
      public var n_7:*;
      
      public var n_8:*;
      
      public var n_9:*;
      
      public var n_10:*;
      
      public var n_11:*;
      
      public var n_12:*;
      
      public var n_13:*;
      
      public var n_14:*;
      
      public var jieshao:*;
      
      public var x_0:*;
      
      public var x_1:*;
      
      public var x_2:*;
      
      public var x_3:*;
      
      public var x_4:*;
      
      public var x_5:*;
      
      public var x_6:*;
      
      public var x_7:*;
      
      public var x_8:*;
      
      public var x_9:*;
      
      public var x_10:*;
      
      public var x_11:*;
      
      public var x_12:*;
      
      public var x_13:*;
      
      public var x_14:*;
      
      public var y_0:*;
      
      public var y_1:*;
      
      public var yText:*;
      
      public var wtrName:*;
      
      public var xuqiu:*;
      
      public var t_0:*;
      
      public var t_1:*;
      
      public var t_2:*;
      
      public var t_3:*;
      
      public var t_4:*;
      
      public var t_5:*;
      
      public var exp:*;
      
      public var a_0:*;
      
      public var a_1:*;
      
      public var a_2:*;
      
      public var exp_mc:*;
      
      public var gold_mc:*;
      
      public var gold:*;
      
      public var j_0:*;
      
      public var j_1:*;
      
      public var j_2:*;
      
      public var jsBtn:*;
      
      private var ArrXX:Array = [410001,410002,410019,410003,410020,410004,410021,410005,410022,410006,410007,410023,410008,410024,410009,410026,410010,410027,410028,410011,410029,410012,410030,410031,410032,410013,410033,410034,410014,410036,410037,410039,410040,410016,410041,410017,410043,410018,410045,410046,410048,410049,410050,420002,420015,420016,420017,420018,420019,420020,420021,420022,420023,420024,420011,420012,420013,420014,420025,420026,420027];
      
      public function TaskPanel()
      {
         super();
         this.taskSlot = TaskSlot.creatSlot();
         this.textDisplay = new TaskDisplay();
         this.tooltip = new ItemsTooltip();
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         TaskData.XieZHeng();
         open_yn = true;
         if(TaskPanel._instance == null)
         {
            Loading();
            return;
         }
         Main._stage.addChild(TaskPanel._instance);
         TaskPanel._instance.initPanel();
         TaskPanel._instance.addEvent();
         TaskPanel._instance.visible = true;
         TaskPanel._instance.y = 0;
         TaskPanel._instance.x = 0;
         TaskPanel._instance.tooltip.visible = false;
         TaskPanel._instance.addChild(TaskPanel._instance.tooltip);
      }
      
      private static function InitIcon() : *
      {
         var _loc2_:MovieClip = null;
         var _loc3_:int = 0;
         var _loc1_:Number = 0;
         while(_loc1_ < 3)
         {
            _loc2_ = new Shop_picNEW();
            _loc3_ = int(_instance["j_" + _loc1_].getChildIndex(_instance["j_" + _loc1_].pic_xx));
            _loc2_.x = _instance["j_" + _loc1_].pic_xx.x;
            _loc2_.y = _instance["j_" + _loc1_].pic_xx.y;
            _loc2_.name = "pic_xx";
            _instance["j_" + _loc1_].removeChild(_instance["j_" + _loc1_].pic_xx);
            _instance["j_" + _loc1_].pic_xx = _loc2_;
            _instance["j_" + _loc1_].addChild(_loc2_);
            _instance["j_" + _loc1_].setChildIndex(_loc2_,_loc3_);
            _loc1_++;
         }
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("com.hotpoint.braveManIII.views.taskPanel.TaskPanel") as Class;
         TaskPanel._instance = new _loc2_();
         Play_Interface.interfaceX["load_mc"].visible = false;
         InitIcon();
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      public static function close() : void
      {
         open_yn = false;
         if(TaskPanel._instance != null)
         {
            if(TaskPanel._instance.visible == true)
            {
               TaskPanel._instance.visible = false;
            }
         }
      }
      
      public static function TiShi_jieSou() : *
      {
         var _loc1_:Class = null;
         var _loc2_:MovieClip = null;
         TiaoShi.txtShow("接受任务");
         if(Boolean(NewLoad.Other_YN) && open_yn)
         {
            if(NewLoad.OtherData.hasClass("_JieSou"))
            {
               _loc1_ = NewLoad.OtherData.getClass("_JieSou") as Class;
               _loc2_ = new _loc1_();
               Main._stage.addChild(_loc2_);
            }
         }
      }
      
      public static function TiShi_wanCeng() : *
      {
         var _loc1_:Class = null;
         var _loc2_:MovieClip = null;
         TiaoShi.txtShow("完成任务");
         if(Boolean(NewLoad.Other_YN) && !open_yn)
         {
            if(NewLoad.OtherData.hasClass("_WanCeng"))
            {
               _loc1_ = NewLoad.OtherData.getClass("_WanCeng") as Class;
               _loc2_ = new _loc1_();
               Main._stage.addChild(_loc2_);
            }
            else
            {
               TiaoShi.txtShow("TiShi_wanCeng ===> _WanCeng is null");
            }
         }
      }
      
      private function initPanel() : void
      {
         Play_Interface.TiShiShow(false);
         TaskData.updateEd();
         TaskData.addGoodsNum();
         TaskData.isOk();
         TaskData.addGoodsNumTow();
         TaskData.isOkTow();
         this.state = 0;
         this.smallState = 0;
         this.targetId = 0;
         this.initData(this.state,this.smallState);
         this.needTextVisible(this.targetId);
         this.btnDisplay();
         this.btnStata(0,6,"b_");
         this.btnStata(0,15,"n_");
         TaskData.setOldArr(0);
         TaskData.initNewText();
         this.sbtnNum();
         this.task_mast.visible = false;
         this.xhmcFun();
      }
      
      private function xhmcFun() : void
      {
         var _loc1_:Task = null;
         this.xh_mc.visible = false;
         if(this.state == 3)
         {
            this.xh_mc.visible = true;
         }
         else if(this.state == 0)
         {
            if(this.taskSlot.getTask(this.targetId) != null)
            {
               _loc1_ = this.taskSlot.getTask(this.targetId);
               if(_loc1_.getSmallType() == 1)
               {
                  this.xh_mc.visible = true;
               }
            }
         }
      }
      
      private function sbtnNum() : void
      {
         var _loc1_:Array = TaskData.nowArr;
         var _loc2_:Number = 1;
         while(_loc2_ < 6)
         {
            this["b_" + _loc2_].sbtn.visible = false;
            this["new_" + _loc2_].visible = false;
            if(_loc1_[_loc2_] != 0)
            {
               this["b_" + _loc2_].sbtn.visible = true;
               this["new_" + _loc2_].visible = true;
               this["new_" + _loc2_].numText.text = String(_loc1_[_loc2_]);
            }
            _loc2_++;
         }
      }
      
      private function btnDisplay() : void
      {
         var _loc1_:Task = null;
         if(this.state == 0)
         {
            this.jsBtn.visible = false;
            this.finishBtn.visible = true;
            if(this.taskSlot.getTask(this.targetId) != null)
            {
               _loc1_ = this.taskSlot.getTask(this.targetId);
               if(_loc1_.getState() == 1)
               {
                  this.finishBtn.gotoAndStop(1);
               }
               else if(_loc1_.getState() == 2)
               {
                  this.finishBtn.gotoAndStop(2);
               }
            }
         }
         else
         {
            this.jsBtn.visible = false;
            this.finishBtn.visible = false;
            if(this.taskSlot.getTask(this.targetId) != null)
            {
               this.jsBtn.visible = true;
            }
         }
      }
      
      private function initData(param1:uint, param2:uint) : void
      {
         this.nowData = TaskData.getTypeByBtn(param1);
         this.addSlot(this.nowData,param2);
         this.taskDisplay();
         this.taskClose(param1);
         this.byeNum(this.yText,param2,this.nowData);
      }
      
      private function addSlot(param1:Array, param2:Number) : void
      {
         var _loc3_:Array = null;
         var _loc4_:Number = 0;
         this.taskSlot.clearTask();
         if(param1 != null)
         {
            _loc3_ = param1[param2];
            if(_loc3_ != null && _loc3_.length != 0)
            {
               _loc4_ = 0;
               while(_loc4_ < _loc3_.length)
               {
                  this.taskSlot.addTask(_loc3_[_loc4_]);
                  _loc4_++;
               }
            }
         }
      }
      
      private function taskDisplay() : void
      {
         var _loc2_:Task = null;
         var _loc1_:Number = 0;
         while(_loc1_ < 15)
         {
            this["n_" + _loc1_].visible = false;
            if(this.taskSlot.getTask(_loc1_) != null)
            {
               _loc2_ = this.taskSlot.getTask(_loc1_);
               this["n_" + _loc1_].visible = true;
               this["n_" + _loc1_].name_text.text = _loc2_.getName();
            }
            _loc1_++;
         }
      }
      
      private function taskClose(param1:uint) : void
      {
         var _loc2_:Number = 0;
         if(param1 == 0)
         {
            _loc2_ = 0;
            while(_loc2_ < 15)
            {
               this["x_" + _loc2_].visible = false;
               if(this.taskSlot.getTask(_loc2_) != null)
               {
                  this["x_" + _loc2_].visible = true;
               }
               _loc2_++;
            }
         }
         else
         {
            _loc2_ = 0;
            while(_loc2_ < 15)
            {
               this["x_" + _loc2_].visible = false;
               _loc2_++;
            }
         }
      }
      
      private function jieShao(param1:Task) : void
      {
         this.jieshao.text = param1.getTaskIntroduction();
         this.xuqiu.text = param1.getDemand();
         this.wtrName.text = param1.getWtr();
      }
      
      public function textKong() : void
      {
         this.jieshao.text = "";
         this.xuqiu.text = "";
         this.exp.text = "";
         this.gold.text = "";
         this.wtrName.text = "";
         this.gold_mc.visible = false;
         this.exp_mc.visible = false;
         var _loc1_:Number = 0;
         while(_loc1_ < 6)
         {
            this["t_" + _loc1_].text = "";
            _loc1_++;
         }
         _loc1_ = 0;
         while(_loc1_ < 3)
         {
            this["a_" + _loc1_].text = "";
            this["j_" + _loc1_].visible = false;
            _loc1_++;
         }
      }
      
      public function needTextVisible(param1:Number) : void
      {
         var _loc2_:Task = null;
         this.textKong();
         if(this.taskSlot.getTask(param1) != null)
         {
            _loc2_ = this.taskSlot.getTask(param1);
            this.needTexting(_loc2_);
            this.jieShao(_loc2_);
            this.textGoldExp(_loc2_);
            this.awNumText(_loc2_);
            this.awFrame(_loc2_);
         }
      }
      
      private function awFrame(param1:Task) : void
      {
         var _loc2_:Array = null;
         var _loc3_:Number = 0;
         if(this.textDisplay.awObj(param1).length != 0)
         {
            _loc2_ = this.textDisplay.awObj(param1);
            _loc3_ = 0;
            while(_loc3_ < _loc2_.length)
            {
               this["j_" + _loc3_].visible = true;
               this["j_" + _loc3_].pic_xx.gotoAndStop(_loc2_[_loc3_].getFrame());
               _loc3_++;
            }
         }
      }
      
      private function textGoldExp(param1:Task) : void
      {
         if(param1.getAwardExp() != -1)
         {
            this.exp_mc.visible = true;
            this.exp.text = param1.getAwardExp();
         }
         if(param1.getAwardGold() != -1)
         {
            this.gold_mc.visible = true;
            this.gold.text = param1.getAwardGold();
         }
      }
      
      private function awNumText(param1:Task) : void
      {
         var _loc3_:Number = 0;
         var _loc2_:Array = param1.getAwardNum();
         if(_loc2_[0].getValue() != -1)
         {
            _loc3_ = 0;
            while(_loc3_ < _loc2_.length)
            {
               if(_loc2_[_loc3_].getValue() != -1)
               {
                  this["a_" + _loc3_].text = String(_loc2_[_loc3_].getValue());
               }
               _loc3_++;
            }
         }
      }
      
      private function needTexting(param1:Task) : void
      {
         var _loc5_:Number = 0;
         var _loc2_:Array = this.textDisplay.getAllId(param1);
         var _loc3_:Array = this.textDisplay.getAllNum(param1);
         var _loc4_:Array = this.textDisplay.getAllNuming(param1);
         if(_loc2_.length != 0)
         {
            _loc5_ = 0;
            while(_loc5_ < _loc2_.length)
            {
               this["t_" + _loc5_].text = _loc2_[_loc5_] + ":" + "(" + _loc4_[_loc5_] + "/" + _loc3_[_loc5_] + ")";
               if(this.finishBtn.currentFrame == 2)
               {
                  this["t_" + _loc5_].text = _loc2_[_loc5_] + ":" + "(" + _loc3_[_loc5_] + "/" + _loc3_[_loc5_] + ")";
               }
               _loc5_++;
            }
         }
      }
      
      private function addEvent() : void
      {
         this.addEventListener(BtnEvent.DO_CHANGE,this.doChange);
         this.addEventListener(BtnEvent.DO_CLICK,this.doClick);
         this.addEventListener(BtnEvent.DO_CLOSE,this.doClose);
         this.addEventListener(BtnEvent.DO_OVER,this.doOver);
         this.addEventListener(BtnEvent.DO_OUT,this.doOut);
      }
      
      private function doOut(param1:BtnEvent) : void
      {
         this.tooltip.visible = false;
      }
      
      private function doOver(param1:BtnEvent) : void
      {
         var _loc3_:Task = null;
         var _loc4_:Array = null;
         var _loc2_:Number = Number(String(param1.target.name).substr(2,1));
         if(this.taskSlot.getTask(this.targetId) != null)
         {
            _loc3_ = this.taskSlot.getTask(this.targetId);
            _loc4_ = this.textDisplay.awObj(_loc3_);
            if(_loc4_.length != 0)
            {
               switch(_loc2_)
               {
                  case 0:
                     this.tooVisible(_loc4_[0]);
                     break;
                  case 1:
                     this.tooVisible(_loc4_[1]);
                     break;
                  case 2:
                     this.tooVisible(_loc4_[2]);
               }
               this.tooltip.x = mouseX;
               this.tooltip.y = mouseY - 100;
               this.tooltip.visible = true;
            }
         }
      }
      
      private function tooVisible(param1:Object) : void
      {
         if(param1 as Equip)
         {
            this.tooltip.equipTooltip(param1);
         }
         else if(param1 as Gem)
         {
            this.tooltip.gemTooltip(param1);
         }
         else if(param1 as Supplies)
         {
            this.tooltip.suppliesTooltip(param1);
         }
         else if(param1 as Otherobj)
         {
            this.tooltip.otherTooltip(param1);
         }
      }
      
      private function doClose(param1:BtnEvent) : void
      {
         Play_Interface.TiShiShow(false);
         close();
      }
      
      private function doClick(param1:BtnEvent) : void
      {
         var _loc2_:String = String(param1.target.name).substr(0,1);
         var _loc3_:Number = Number(String(param1.target.name).substr(2,1));
         if(_loc2_ == "f")
         {
            this.taskAw();
         }
         else if(_loc2_ == "j")
         {
            this.taskJs();
            TiShi_jieSou();
         }
         else if(_loc2_ == "y")
         {
            this.yeFunction(_loc3_);
         }
         else if(_loc2_ == "x")
         {
            this.task_mast.visible = true;
            this.taskXX_num = _loc3_;
         }
         if(param1.target.name == "qd_btn")
         {
            this.taskClick();
            this.task_mast.visible = false;
         }
         else if(param1.target.name == "qx_btn")
         {
            this.task_mast.visible = false;
         }
      }
      
      private function goToOne() : void
      {
         this.targetId = 0;
         this.smallStateNum();
         this.initData(this.state,this.smallState);
         this.needTextVisible(this.targetId);
         this.btnDisplay();
         this.btnStata(0,15,"n_");
      }
      
      private function smallStateNum() : void
      {
         var _loc1_:Array = null;
         if(this.nowData != null)
         {
            _loc1_ = this.nowData[this.smallState];
            if(this.smallState > 0)
            {
               if(this.nowData[this.smallState].length == 1)
               {
                  --this.smallState;
               }
            }
         }
      }
      
      private function taskJs() : void
      {
         var _loc1_:Task = null;
         if(this.taskSlot.getTask(this.targetId) != null)
         {
            _loc1_ = this.taskSlot.getTask(this.targetId);
            _loc1_.setState(1);
            TaskData.statePlayer();
            TaskData.clearOldTaskNum(_loc1_);
            if(Main.gameNum.getValue() != 0)
            {
               _loc1_.setMapId(Main.gameNum.getValue());
               _loc1_.setMapStar(GameData.gameLV);
            }
            _loc1_.isTaskOk();
            this.goToOne();
         }
      }
      
      private function taskFinsh(param1:Task) : void
      {
         if(param1.getSmallType() == 0)
         {
            param1.setState(0);
            param1.setOldTime(param1.getOldTime() + 1);
         }
         else if(param1.getSmallType() == 1)
         {
            param1.setState(0);
            param1.setOldTime(param1.getOldTime() + 1);
         }
         else if(param1.getSmallType() == 2)
         {
            param1.setState(3);
            param1.setOldTime(param1.getOldTime() + 1);
            param1.setOverTime(Main.serverTime.getValue());
            AchData.setRcTask();
         }
      }
      
      private function taskAw() : void
      {
         var _loc1_:Task = null;
         var _loc2_:Array = null;
         if(this.taskSlot.getTask(this.targetId) != null && this.taskSlot.getTask(this.targetId).getState() == 2)
         {
            _loc1_ = this.taskSlot.getTask(this.targetId);
            if(this.textDisplay.bagNum(_loc1_))
            {
               this.taskFinsh(_loc1_);
               _loc1_.clearData();
               TaskData.clearGoods(_loc1_);
               TaskData.clearGold(_loc1_);
               this.glNum0 = VT.createVT(100);
               this.glNum1 = VT.createVT(100);
               this.glNum2 = VT.createVT(100);
               this.glNum0.setValue(this.glNum0.getValue() * Math.random());
               this.glNum1.setValue(this.glNum1.getValue() * Math.random());
               this.glNum2.setValue(this.glNum2.getValue() * Math.random());
               _loc2_ = [this.glNum0,this.glNum1,this.glNum2];
               this.textDisplay.overAward(_loc1_,_loc2_);
               this.textDisplay.overGoldAncExp(_loc1_);
               this.textDisplay.overPlayerStata(_loc1_);
               TaskData.addGoodsNum();
               TaskData.addGoodsNumTow();
               TaskData.isOkTow();
               this.goToOne();
               TaskData.initNewText();
               this.sbtnNum();
               this.SaveXX(_loc1_.getId());
               Play_Interface.TiShiShow(false,_loc1_.getId());
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
      }
      
      private function SaveXX(param1:Number) : *
      {
         var _loc2_:* = 0;
         for(_loc2_ in this.ArrXX)
         {
            if(param1 == this.ArrXX[_loc2_])
            {
               return;
            }
         }
         Main.Save();
      }
      
      private function taskClick() : void
      {
         var _loc1_:Task = null;
         TiaoShi.txtShow("放弃任务:" + this.taskXX_num);
         if(this.taskSlot.getTask(this.taskXX_num) != null)
         {
            _loc1_ = this.taskSlot.getTask(this.taskXX_num);
            _loc1_.setState(0);
            _loc1_.clearData();
            TaskData.clearGoods(_loc1_,1);
            this.goToOne();
            TaskData.initNewText();
            this.sbtnNum();
         }
      }
      
      private function yeFunction(param1:Number) : void
      {
         this.smallState = this.addOrJian(param1,this.smallState,this.nowData);
         this.goToOne();
      }
      
      private function addOrJian(param1:Number, param2:Number, param3:Array) : Number
      {
         var _loc4_:Number = param2;
         if(param3 != null)
         {
            if(param1 == 0)
            {
               if(_loc4_ > 0)
               {
                  _loc4_--;
               }
            }
            else if(param1 == 1)
            {
               if(_loc4_ < param3.length - 1)
               {
                  _loc4_++;
               }
            }
         }
         return _loc4_;
      }
      
      private function doChange(param1:BtnEvent) : void
      {
         var _loc2_:String = String(param1.target.name).substr(0,1);
         var _loc3_:Number = Number(String(param1.target.name).substr(2));
         if(_loc2_ == "b")
         {
            this.btnFunction(_loc3_);
         }
         else if(_loc2_ == "n")
         {
            this.tiaoFunction(_loc3_);
         }
      }
      
      private function btnFunction(param1:Number) : void
      {
         this.state = param1;
         this.smallState = 0;
         this.targetId = 0;
         this.initData(this.state,this.smallState);
         this.needTextVisible(this.targetId);
         this.btnDisplay();
         this.btnStata(0,15,"n_");
         this.btnStata(param1,6,"b_");
         TaskData.setOldArr(param1);
         TaskData.initNewText();
         this.sbtnNum();
         this.xhmcFun();
      }
      
      private function tiaoFunction(param1:Number) : void
      {
         this.targetId = param1;
         this.needTextVisible(this.targetId);
         this.btnDisplay();
         this.btnStata(param1,15,"n_");
         this.xhmcFun();
      }
      
      private function btnStata(param1:Number, param2:Number, param3:String) : void
      {
         this[param3 + param1].isClick = true;
         var _loc4_:Number = 0;
         while(_loc4_ < param2)
         {
            if(param1 != _loc4_)
            {
               this[param3 + _loc4_].isClick = false;
            }
            _loc4_++;
         }
      }
      
      private function byeNum(param1:TextField, param2:Number, param3:Array) : void
      {
         var _loc4_:* = undefined;
         if(param3 != null)
         {
            _loc4_ = param3.length;
            if(_loc4_ < 1)
            {
               _loc4_ = 1;
            }
            param1.text = param2 + 1 + "/" + _loc4_;
         }
         else
         {
            param1.text = param2 + 1 + "/" + 1;
         }
      }
   }
}

