package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class LingHunShi_Interface extends MovieClip
   {
      public static var lhs_Data:Array;
      
      public static var noMovPlayArr:Array;
      
      public static var _this:LingHunShi_Interface;
      
      public static var loadData:ClassLoader;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "LHS_Panel_v1720.swf";
      
      public static var pageNum:int = 1;
      
      public static var pageMax:int = 4;
      
      private static var fanYe_YN:Boolean = true;
      
      private static var dianQuanYN:Boolean = false;
      
      public var skin:MovieClip;
      
      public function LingHunShi_Interface()
      {
         super();
         InitDataX();
         this.LoadSkin();
         _this = this;
      }
      
      public static function InitDataX() : *
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         if(!lhs_Data)
         {
            lhs_Data = new Array();
            _loc1_ = 0;
            while(_loc1_ < 6)
            {
               lhs_Data[_loc1_] = VT.createVT();
               _loc1_++;
            }
         }
         if(!noMovPlayArr)
         {
            noMovPlayArr = new Array();
            _loc2_ = 0;
            while(_loc2_ < 6)
            {
               noMovPlayArr[_loc2_] = false;
               _loc2_++;
            }
         }
      }
      
      public static function Add_LHS(param1:int) : *
      {
         InitDataX();
         if((lhs_Data[param1] as VT).getValue() >= 10)
         {
            return;
         }
         (lhs_Data[param1] as VT).setValue((lhs_Data[param1] as VT).getValue() + 1);
         Open();
      }
      
      public static function get_LHS() : int
      {
         if(!lhs_Data)
         {
            return 0;
         }
         var _loc1_:int = 0;
         for(i in lhs_Data)
         {
            if((lhs_Data[i] as VT).getValue() >= 10)
            {
               _loc1_++;
            }
         }
         return _loc1_;
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.skin.visible = true;
            _this.y = 0;
            _this.x = 0;
            Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function Show() : *
      {
         var _loc1_:int = 0;
         _loc1_ = 1;
         while(_loc1_ <= pageMax)
         {
            _this.skin["LHS_mc_" + _loc1_].visible = false;
            _loc1_++;
         }
         _this.skin["LHS_mc_" + pageNum].visible = true;
         InitDataX();
         _this.skin.info_mc.gotoAndStop(pageNum);
         _this.skin.page_txt.text = pageNum + "/6";
         var _loc2_:int = int((lhs_Data[pageNum] as VT).getValue());
         _this.skin["DianQuan_btn"].visible = true;
         if(_loc2_ >= 10)
         {
            _this.skin["DianQuan_btn"].visible = false;
         }
         var _loc3_:MovieClip = _this.skin["LHS_mc_" + pageNum];
         _loc1_ = 1;
         while(_loc1_ <= 10)
         {
            if(_loc1_ > _loc2_)
            {
               _loc3_["mc" + _loc1_].visible = false;
            }
            else
            {
               _loc3_["mc" + _loc1_].visible = true;
               if(_loc1_ == _loc2_)
               {
                  _loc3_["mc" + _loc1_].gotoAndPlay(2);
               }
               else
               {
                  _loc3_["mc" + _loc1_].gotoAndStop("over");
               }
            }
            _loc1_++;
         }
         _this.skin.X0_txt.text = "已修复: " + _loc2_ + "/10";
         if(pageNum == 1)
         {
            _this.skin.X1_txt.text = "攻击力+" + _loc2_ + "%";
            _this.skin.X2_txt.text = "对暗黑王的伤害提高" + _loc2_ * 100 + "%";
         }
         else if(pageNum == 2)
         {
            _this.skin.X1_txt.text = "防御力+" + _loc2_ + "%";
            _this.skin.X2_txt.text = "受到暗黑王的伤害减少" + _loc2_ * 100 + "%";
         }
         else if(pageNum == 3)
         {
            _this.skin.X1_txt.text = "暴击伤害+" + _loc2_ + "%";
            _this.skin.X2_txt.text = "受到暗黑王的伤害减少" + _loc2_ * 100 + "%";
         }
         else if(pageNum == 4)
         {
            _this.skin.X1_txt.text = "生命+" + _loc2_ + "%";
            _this.skin.X2_txt.text = "对暗黑王的伤害提高" + _loc2_ * 100 + "%";
         }
      }
      
      private static function backFun(param1:*) : *
      {
         if(pageNum > 1 && Boolean(fanYe_YN))
         {
            --pageNum;
            Show();
         }
      }
      
      private static function nextFun(param1:*) : *
      {
         if(pageNum < pageMax && Boolean(fanYe_YN))
         {
            ++pageNum;
            Show();
         }
      }
      
      public static function DianquanGo() : *
      {
         if(dianQuanYN)
         {
            dianQuanYN = false;
            _this.skin["DianQuan_btn"].visible = true;
            fanYe_YN = true;
            Add_LHS(pageNum);
         }
      }
      
      private static function InitOpen() : *
      {
         var _loc1_:LingHunShi_Interface = new LingHunShi_Interface();
         Main._stage.addChild(_loc1_);
         OpenYN = true;
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.skin.visible = false;
         }
      }
      
      public function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("LHS_Panel") as Class;
         this.skin = new _loc2_();
         _this.skin.addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addChild(this.skin);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function onADDED_TO_STAGE(param1:*) : *
      {
         _this.skin["close_btn"].addEventListener(MouseEvent.CLICK,this.Close);
         _this.skin["DianQuan_btn"].addEventListener(MouseEvent.CLICK,this.DianQuanFun);
         _this.skin["back_btn"].addEventListener(MouseEvent.CLICK,backFun);
         _this.skin["next_btn"].addEventListener(MouseEvent.CLICK,nextFun);
      }
      
      private function DianQuanFun(param1:*) : *
      {
         if(Shop4399.moneyAll.getValue() >= 30)
         {
            Api_4399_All.BuyObj(261);
            dianQuanYN = true;
            _this.skin["DianQuan_btn"].visible = false;
            fanYe_YN = false;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"点券不足");
         }
      }
      
      private function Close(param1:*) : *
      {
         this.visible = false;
         this.y = 5000;
         this.x = 5000;
      }
   }
}

