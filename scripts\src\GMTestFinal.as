package src
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.utils.Timer;
   import flash.events.TimerEvent;
   
   public class GMTestFinal extends MovieClip
   {
      private static var instance:GMTestFinal;
      private var testTimer:Timer;
      
      public function GMTestFinal()
      {
         super();
         instance = this;
         addEventListener(Event.ADDED_TO_STAGE, onAddedToStage);
      }
      
      private function onAddedToStage(e:Event):void
      {
         removeEventListener(Event.ADDED_TO_STAGE, onAddedToStage);
         
         trace("GMTestFinal: 开始最终测试");
         
         // 立即测试GM系统
         testGMSystem();
         
         // 设置定时测试
         testTimer = new Timer(2000, 5); // 每2秒测试一次，最多5次
         testTimer.addEventListener(TimerEvent.TIMER, onTimerTest);
         testTimer.start();
         
         // 添加键盘监听
         stage.addEventListener(KeyboardEvent.KEY_DOWN, onKeyDown);
         
         trace("GMTestFinal: 测试系统已启动");
      }
      
      private function onTimerTest(e:TimerEvent):void
      {
         trace("GMTestFinal: 定时测试 #" + testTimer.currentCount);
         testGMSystem();
      }
      
      private function testGMSystem():void
      {
         trace("=== GM系统测试开始 ===");
         
         // 测试Main._stage
         if(Main._stage) {
            trace("✓ Main._stage 可用");
         } else {
            trace("✗ Main._stage 不可用");
            return;
         }
         
         // 测试GM类
         try {
            GM.smartInit();
            trace("✓ GM.smartInit() 调用成功");
         } catch(error:Error) {
            trace("✗ GM.smartInit() 失败: " + error.message);
         }
         
         // 测试GM界面创建
         try {
            var testPanel:SimpleCuteGMPanel = new SimpleCuteGMPanel();
            trace("✓ SimpleCuteGMPanel 创建成功");
            
            // 测试添加到舞台
            Main._stage.addChild(testPanel);
            testPanel.visible = false; // 隐藏测试面板
            trace("✓ 测试面板添加到舞台成功");
            
            // 清理测试面板
            Main._stage.removeChild(testPanel);
            trace("✓ 测试面板清理完成");
            
         } catch(error:Error) {
            trace("✗ GM界面测试失败: " + error.message);
         }
         
         trace("=== GM系统测试结束 ===");
      }
      
      private function onKeyDown(e:KeyboardEvent):void
      {
         if(e.keyCode == 192) { // ` 键
            trace("GMTestFinal: 检测到 ` 键，手动触发GM界面");
            
            try {
               GM.toggleGMPanel();
               trace("GMTestFinal: GM界面切换成功");
            } catch(error:Error) {
               trace("GMTestFinal: GM界面切换失败: " + error.message);
               
               // 备用方案：直接创建简化界面
               try {
                  var panel:SimpleCuteGMPanel = new SimpleCuteGMPanel();
                  Main._stage.addChild(panel);
                  Main._stage.setChildIndex(panel, Main._stage.numChildren - 1);
                  panel.show();
                  trace("GMTestFinal: 备用GM界面创建成功");
               } catch(error2:Error) {
                  trace("GMTestFinal: 备用方案也失败: " + error2.message);
               }
            }
         }
         
         // F1键 - 强制初始化
         if(e.keyCode == 112) { // F1
            trace("GMTestFinal: F1键 - 强制初始化GM系统");
            GM.globalInit();
         }
         
         // F2键 - 显示调试信息
         if(e.keyCode == 113) { // F2
            trace("GMTestFinal: F2键 - 显示调试信息");
            showDebugInfo();
         }
      }
      
      private function showDebugInfo():void
      {
         trace("=== 调试信息 ===");
         trace("Main._stage: " + (Main._stage ? "可用" : "不可用"));
         trace("Main._stageWidth: " + Main._stageWidth);
         trace("Main._stageHeight: " + Main._stageHeight);
         trace("舞台子对象数量: " + (Main._stage ? Main._stage.numChildren : "N/A"));
         
         // 检查GM相关类
         try {
            var gmTest:GM = new GM();
            trace("✓ GM类可实例化");
         } catch(e:Error) {
            trace("✗ GM类实例化失败: " + e.message);
         }
         
         try {
            var panelTest:SimpleCuteGMPanel = new SimpleCuteGMPanel();
            trace("✓ SimpleCuteGMPanel类可实例化");
         } catch(e:Error) {
            trace("✗ SimpleCuteGMPanel类实例化失败: " + e.message);
         }
         
         try {
            var funcTest:CuteGMFunctions = new CuteGMFunctions();
            trace("✓ CuteGMFunctions类可实例化");
         } catch(e:Error) {
            trace("✗ CuteGMFunctions类实例化失败: " + e.message);
         }
         
         trace("=== 调试信息结束 ===");
      }
      
      // 静态方法，可以从外部调用
      public static function runTest():void
      {
         if(!instance) {
            var test:GMTestFinal = new GMTestFinal();
            if(Main._stage) {
               Main._stage.addChild(test);
            }
         } else {
            instance.testGMSystem();
         }
      }
      
      // 静态方法，强制显示GM界面
      public static function forceShowGM():void
      {
         trace("GMTestFinal.forceShowGM() 被调用");
         
         try {
            var panel:SimpleCuteGMPanel = new SimpleCuteGMPanel();
            Main._stage.addChild(panel);
            Main._stage.setChildIndex(panel, Main._stage.numChildren - 1);
            panel.show();
            trace("强制GM界面显示成功");
         } catch(error:Error) {
            trace("强制GM界面显示失败: " + error.message);
         }
      }
   }
}
