package src.Skin
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.utils.*;
   import src.*;
   import src.other.*;
   import src.tool.*;
   
   public class EnemyBoss29 extends EnemySkin
   {
      public static var BOSSENG:int = 0;
      
      internal var rushNum:int = 0;
      
      public function EnemyBoss29()
      {
         super();
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
      }
      
      private function onADDED_TO_STAGE(param1:*) : *
      {
         BOSSENG = 0;
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
      }
      
      override public function boss14() : *
      {
         this.en1.visible = false;
         this.en2.visible = false;
         this.en3.visible = false;
         this.en4.visible = false;
         this.en5.visible = false;
         if(BOSSENG == 1)
         {
            this.en1.visible = true;
            this.parent.攻击力.setValue(2);
         }
         else if(BOSSENG == 2)
         {
            this.en1.visible = true;
            this.en2.visible = true;
            this.parent.攻击力.setValue(3);
         }
         else if(BOSSENG == 3)
         {
            this.en1.visible = true;
            this.en2.visible = true;
            this.en3.visible = true;
            this.parent.攻击力.setValue(4);
         }
         else if(BOSSENG == 4)
         {
            this.en1.visible = true;
            this.en2.visible = true;
            this.en3.visible = true;
            this.en4.visible = true;
            this.parent.攻击力.setValue(5);
         }
         else if(BOSSENG == 5)
         {
            this.en1.visible = true;
            this.en2.visible = true;
            this.en3.visible = true;
            this.en4.visible = true;
            this.en5.visible = true;
            this.parent.攻击力.setValue(6);
         }
      }
      
      override public function otherGoTo(param1:String) : *
      {
         if(param1 == "攻击1")
         {
         }
         if(param1 == "攻击2")
         {
         }
         if(param1 == "攻击3")
         {
         }
         if(param1 == "攻击4")
         {
         }
      }
   }
}

