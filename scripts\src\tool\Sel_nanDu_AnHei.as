package src.tool
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import src.Main;
   import src.SelMap;
   
   public class Sel_nanDu_An<PERSON>ei extends MovieClip
   {
      public var close_btn:SimpleButton;
      
      public var anHei_1_btn:SimpleButton;
      
      public function Sel_nanDu_AnHei()
      {
         super();
         this.close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.anHei_1_btn.addEventListener(MouseEvent.CLICK,this.NanDu1);
      }
      
      public function Open(param1:uint = 2000, param2:uint = 2000) : *
      {
         this.x = param1;
         this.y = param2;
         this.visible = true;
      }
      
      public function Close(param1:* = null) : *
      {
         this.y = -15000;
         this.x = -15000;
         this.visible = false;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(4);
         Main._this.Loading();
         SelMap.Close();
      }
      
      private function NanDu1(param1:*) : *
      {
         SelMap.Open(0,0,1,4);
         this.y = -15000;
         this.x = -15000;
         this.visible = false;
      }
      
      private function NanDu2(param1:*) : *
      {
      }
      
      private function NanDu3(param1:*) : *
      {
      }
   }
}

