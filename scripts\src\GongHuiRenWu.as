package src
{
   import com.*;
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.Tool.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.other.*;
   import src.tool.*;
   
   public class <PERSON><PERSON>uiRenWu extends MovieClip
   {
      public static var myXml:XML;
      
      public static var rwArr:Array;
      
      public static var rwArr2:Array;
      
      public static const data:Class = GongHuiRenWu_data;
      
      public static var rwArrData:Array = new Array();
      
      public static var CZ_yn:Boolean = false;
      
      public function GongHuiRenWu()
      {
         super();
      }
      
      public static function initData() : *
      {
         if(!myXml)
         {
            myXml = XMLAsset.createXML(GongHuiRenWu.data);
         }
         if(!rwArr2 || rwArr2[0] < Main.serverTime.getValue())
         {
            rwArr2 = [Main.serverTime.getValue(),false,false,false,false];
         }
      }
      
      public static function SX(param1:int) : *
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         CZ_yn = true;
         if(param1 >= 86)
         {
            param1 = 6;
         }
         else if(param1 >= 76)
         {
            param1 = 5;
         }
         else if(param1 >= 66)
         {
            param1 = 4;
         }
         else if(param1 >= 51)
         {
            param1 = 3;
         }
         else if(param1 >= 31)
         {
            param1 = 2;
         }
         else
         {
            param1 = 1;
         }
         var _loc2_:Array = new Array();
         for(_loc3_ in myXml.公会任务)
         {
            _loc4_ = int(myXml.公会任务[_loc3_].级数);
            if(param1 == _loc4_)
            {
               _loc6_ = [int(myXml.公会任务[_loc3_].ID),0,0,false,false];
               _loc2_[_loc2_.length] = _loc6_;
            }
            _loc5_ = int(myXml.公会任务[_loc3_].ID);
            _loc6_ = [int(myXml.公会任务[_loc3_].ID),int(myXml.公会任务[_loc3_].级数),String(myXml.公会任务[_loc3_].需求),int(myXml.公会任务[_loc3_].地图id),int(myXml.公会任务[_loc3_].地图星级),int(myXml.公会任务[_loc3_].通关次数),int(myXml.公会任务[_loc3_].怪物id),int(myXml.公会任务[_loc3_].怪物数量),int(myXml.公会任务[_loc3_].金币),int(myXml.公会任务[_loc3_].经验),int(myXml.公会任务[_loc3_].任务类型)];
            rwArrData[_loc5_] = _loc6_;
         }
         TiaoShi.txtShow("1筛选 = " + _loc2_.length);
         if(!rwArr || rwArr[0] < Main.serverTime.getValue())
         {
            rwArr = new Array();
            rwArr[0] = Main.serverTime.getValue();
            TiaoShi.txtShow("重置公会任务!!!!!!!!!!!!" + Main.serverTime.getValue());
            _loc3_ = 0;
            while(_loc3_ < 3)
            {
               _loc7_ = Math.random() * _loc2_.length;
               rwArr[_loc3_ + 1] = DeepCopyUtil.clone(_loc2_[_loc7_]);
               _loc2_.splice(_loc7_,1);
               TiaoShi.txtShow("2筛选:" + _loc3_ + " = " + rwArr[_loc3_ + 1]);
               _loc3_++;
            }
            TiaoShi.txtShow("3筛选 rwArr.length = " + rwArr.length);
            rwArr[4] = false;
            TiaoShi.txtShow("rwArr ============> " + rwArr);
            Main.Save(false);
         }
      }
      
      public static function isType(param1:int) : Array
      {
         if(rwArrData[param1])
         {
            return rwArrData[param1];
         }
         return null;
      }
      
      public static function enemyOK(param1:int) : *
      {
         var _loc3_:Array = null;
         if(!CZ_yn)
         {
            return;
         }
         var _loc2_:int = 1;
         while(_loc2_ <= 3)
         {
            if(!rwArr[_loc2_][3])
            {
               _loc3_ = isType(rwArr[_loc2_][0]);
               if(_loc3_[10] == 2 && param1 == _loc3_[6])
               {
                  ++rwArr[_loc2_][2];
                  TiaoShi.txtShow("击杀数:" + rwArr[_loc2_][2]);
                  if(rwArr[_loc2_][2] >= _loc3_[7])
                  {
                     rwArr[_loc2_][3] = true;
                     Api_4399_GongHui.setRW(67);
                     TiaoShi.txtShow("~~~~~~~~~~2公会任务完成:" + _loc2_);
                     Api_4399_GongHui.upNum(157);
                  }
               }
            }
            _loc2_++;
         }
      }
      
      public static function gameOK(param1:int, param2:int) : *
      {
         var _loc4_:Array = null;
         if(!CZ_yn)
         {
            return;
         }
         var _loc3_:int = 1;
         while(_loc3_ <= 3)
         {
            if(!rwArr[_loc3_][3])
            {
               _loc4_ = isType(rwArr[_loc3_][0]);
               if(_loc4_[10] == 1 && param1 == _loc4_[3] && param2 == _loc4_[4])
               {
                  ++rwArr[_loc3_][1];
                  TiaoShi.txtShow("通关数:" + rwArr[_loc3_][1]);
                  if(rwArr[_loc3_][1] >= _loc4_[5])
                  {
                     rwArr[_loc3_][3] = true;
                     Api_4399_GongHui.setRW(67);
                     TiaoShi.txtShow("~~~~~~~~~~1公会任务完成:" + _loc3_);
                     Api_4399_GongHui.upNum(157);
                  }
               }
            }
            _loc3_++;
         }
      }
   }
}

