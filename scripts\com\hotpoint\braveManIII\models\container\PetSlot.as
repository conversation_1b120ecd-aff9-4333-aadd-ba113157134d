package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.pet.*;
   
   public class PetSlot
   {
      private var _mode:Boolean = false;
      
      private var _jilu:int = -1;
      
      private var _slot:Array = new Array();
      
      private var _slotLimit:VT = VT.createVT(5);
      
      public function PetSlot()
      {
         super();
      }
      
      public static function createPetSlot() : PetSlot
      {
         var _loc1_:PetSlot = new PetSlot();
         var _loc2_:int = 0;
         while(_loc2_ < 50)
         {
            _loc1_._slot[_loc2_] = null;
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function get slotLimit() : VT
      {
         return this._slotLimit;
      }
      
      public function set slotLimit(param1:VT) : void
      {
         this._slotLimit = param1;
      }
      
      public function get slot() : Array
      {
         return this._slot;
      }
      
      public function set slot(param1:Array) : void
      {
         this._slot = param1;
      }
      
      public function get jilu() : int
      {
         return this._jilu;
      }
      
      public function set jilu(param1:int) : void
      {
         this._jilu = param1;
      }
      
      public function get mode() : <PERSON><PERSON>an
      {
         return this._mode;
      }
      
      public function set mode(param1:Boolean) : void
      {
         this._mode = param1;
      }
      
      public function getJilu() : int
      {
         return this._jilu;
      }
      
      public function setJilu(param1:int) : void
      {
         this._jilu = param1;
      }
      
      public function getMode() : Boolean
      {
         return this._mode;
      }
      
      public function setMode(param1:Boolean) : void
      {
         this._mode = param1;
      }
      
      public function getPetFromSlot(param1:Number) : Pet
      {
         if(this._slot[param1] != null)
         {
            return this._slot[param1];
         }
         return null;
      }
      
      public function addPetSlot(param1:Pet) : Boolean
      {
         var _loc2_:Number = 0;
         while(_loc2_ < this._slotLimit.getValue())
         {
            if(this._slot[_loc2_] == null)
            {
               this._slot[_loc2_] = param1;
               return true;
            }
            _loc2_++;
         }
         return false;
      }
      
      public function delPetSlot(param1:int) : Pet
      {
         var _loc2_:Pet = null;
         if(this._slot[param1] != null)
         {
            _loc2_ = this._slot[param1];
            this._slot[param1] = null;
         }
         return _loc2_;
      }
      
      public function getPetSlotNum() : int
      {
         return this._slotLimit.getValue();
      }
      
      public function backPetSlotNum() : int
      {
         if(this._slotLimit.getValue() == 3)
         {
            this._slotLimit.setValue(5);
         }
         var _loc1_:int = 0;
         var _loc2_:Number = 0;
         while(_loc2_ < this._slotLimit.getValue())
         {
            if(this._slot[_loc2_] == null)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function backPetNum() : int
      {
         var _loc1_:int = 0;
         var _loc2_:Number = 0;
         while(_loc2_ < this._slotLimit.getValue())
         {
            if(this._slot[_loc2_] != null)
            {
               _loc1_++;
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function addPetSlotNum(param1:int) : *
      {
         this._slotLimit.setValue(this._slotLimit.getValue() + param1);
      }
      
      public function plan6_3() : Boolean
      {
         var _loc1_:Number = 0;
         while(_loc1_ < this._slotLimit.getValue())
         {
            if(this._slot[_loc1_] != null)
            {
               if((this._slot[_loc1_] as Pet).getWX() > 0)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
      
      public function plan5_3() : Boolean
      {
         var _loc1_:Number = 0;
         while(_loc1_ < this._slotLimit.getValue())
         {
            if(this._slot[_loc1_] != null)
            {
               if((this._slot[_loc1_] as Pet).id.getValue() == 73101 || (this._slot[_loc1_] as Pet).id.getValue() == 73501 || (this._slot[_loc1_] as Pet).id.getValue() == 73103 || (this._slot[_loc1_] as Pet).id.getValue() == 73104)
               {
                  return true;
               }
            }
            _loc1_++;
         }
         return false;
      }
   }
}

