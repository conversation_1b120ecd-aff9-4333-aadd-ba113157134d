package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.utils.*;
   
   public class BuffEnemy extends MovieClip
   {
      public static var AllBuff:Array = [];
      
      private var who:Player;
      
      public var type:int;
      
      private var space:int;
      
      private var totalTime:int;
      
      public var numValue:Number;
      
      private var speedValue:int;
      
      private var effectMC:MovieClip;
      
      private var timeTemp:int = 0;
      
      private var tempNum:Number = 0;
      
      private var shandian_C:Class;
      
      private var zhongdu_C:Class;
      
      private var iceing_C:Class;
      
      private var sufu_C:Class;
      
      private var langtou_C:Class;
      
      private var bingshuang_C:Class;
      
      private var iceBroken_C:Class;
      
      private var burn_C:Class;
      
      private var burn_C2:Class;
      
      private var jiansu_C:Class;
      
      private var lei_C:Class;
      
      private var shandian:MovieClip;
      
      private var zhongdu:MovieClip;
      
      private var iceing:MovieClip;
      
      private var sufu:MovieClip;
      
      private var langtou:MovieClip;
      
      private var bingshuang:MovieClip;
      
      private var iceBroken:MovieClip;
      
      private var burn:MovieClip;
      
      private var burn2:MovieClip;
      
      private var jiansu:MovieClip;
      
      private var lei:MovieClip;
      
      public function BuffEnemy(param1:MovieClip, param2:MovieClip, param3:uint = 0)
      {
         super();
         this.type = (param1 as HitXX).type;
         this.space = (param1 as HitXX).space;
         this.totalTime = (param1 as HitXX).totalTime;
         this.numValue = (param1 as HitXX).numValue;
         this.speedValue = (param1 as HitXX).speedValue;
         this.timeTemp = (param1 as HitXX).space;
         if((param1 as HitXX).who is Player)
         {
            this.who = (param1 as HitXX).who;
         }
         this.effectMC = param2;
         this.effectMC.addChild(this);
         this.x = 0;
         this.y = 0;
         if(this.type >= 100 && this.type < 500)
         {
            addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            (this.effectMC as Enemy).cengshu[(this.effectMC as Enemy).cengshu.length] = this;
         }
         if(this.type >= 500)
         {
            if((this.effectMC as Enemy).cengshu.length == 0)
            {
               addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
               (this.effectMC as Enemy).cengshu[(this.effectMC as Enemy).cengshu.length] = this;
            }
            else if((this.effectMC as Enemy).cengshu.length == 1)
            {
               if((this.effectMC as Enemy).cengshu[0].type == this.type)
               {
                  (this.effectMC as Enemy).cengshu[0].totalTime = this.totalTime;
               }
            }
         }
      }
      
      public function onENTER_FRAME(param1:Event) : *
      {
         if(this.effectMC is Enemy)
         {
            this.realEffect();
         }
      }
      
      internal function bianXiao(param1:Event) : *
      {
         this.effectMC.scaleX -= 0.05;
         this.effectMC.scaleY -= 0.05;
         if(this.effectMC.scaleX <= 0.5 && this.effectMC.scaleY <= 0.5)
         {
            removeEventListener(Event.ENTER_FRAME,this.bianXiao);
         }
      }
      
      internal function removeIceBroken(param1:Event) : *
      {
         if(param1.target.currentFrame == MovieClip(param1.target).totalFrames)
         {
            this.effectMC.removeChild(this.iceBroken);
            param1.target.removeEventListener(Event.ENTER_FRAME,this.removeIceBroken);
         }
      }
      
      private function realEffect() : *
      {
         var _loc1_:* = undefined;
         var _loc2_:int = 0;
         --this.totalTime;
         if(this.type == 100)
         {
            if((this.effectMC as Enemy).changeMode == false)
            {
               return;
            }
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  this.addEventListener(Event.ENTER_FRAME,this.bianXiao);
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.effectMC.scaleX = 1;
               this.effectMC.scaleY = 1;
               this.dead();
            }
         }
         else if(this.type == 501)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  _loc1_ = this.numValue;
                  _loc2_ = (this.effectMC as Enemy).life.getValue() - _loc1_;
                  if(_loc2_ < 0)
                  {
                     this.dead();
                  }
                  (this.effectMC as Enemy).hpCount(_loc1_);
                  this.timeTemp = 0;
                  HPdown.Open(_loc1_,this.effectMC.x,this.effectMC.y - this.effectMC.height);
                  this.burn_C = NewLoad.XiaoGuoData.getClass("Burn") as Class;
                  this.burn = new this.burn_C();
                  this.addChild(this.burn);
               }
            }
            else
            {
               this.removeChild(this.burn);
               this.dead();
            }
         }
         else if(this.type == 5011)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  _loc1_ = this.numValue;
                  _loc2_ = (this.effectMC as Enemy).life.getValue() - _loc1_;
                  if(_loc2_ < 0)
                  {
                     this.dead();
                  }
                  (this.effectMC as Enemy).hpCount(_loc1_);
                  this.timeTemp = 0;
                  HPdown.Open(_loc1_,this.effectMC.x,this.effectMC.y - this.effectMC.height);
                  this.burn_C2 = NewLoad.XiaoGuoData.getClass("Burn2") as Class;
                  this.burn2 = new this.burn_C2();
                  this.addChild(this.burn2);
               }
            }
            else
            {
               this.removeChild(this.burn2);
               this.dead();
            }
         }
         else if(this.type == 502)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  _loc1_ = this.numValue;
                  _loc2_ = (this.effectMC as Enemy).life.getValue() - _loc1_;
                  if(_loc2_ < 0)
                  {
                     this.dead();
                  }
                  (this.effectMC as Enemy).hpCount(_loc1_);
                  this.timeTemp = 0;
                  HPdown.Open(_loc1_,this.effectMC.x,this.effectMC.y - this.effectMC.height);
                  this.zhongdu_C = NewLoad.XiaoGuoData.getClass("ZhongDu") as Class;
                  this.zhongdu = new this.zhongdu_C();
                  this.addChild(this.zhongdu);
               }
               if(this.speedValue > 0)
               {
                  (this.effectMC as Enemy).walk_power -= this.speedValue;
                  if((this.effectMC as Enemy).walk_power < 0)
                  {
                     (this.effectMC as Enemy).walk_power = 0;
                  }
                  this.speedValue = 0;
               }
            }
            else
            {
               (this.effectMC as Enemy).walk_power = (this.effectMC as Enemy).walk_temp;
               this.removeChild(this.zhongdu);
               this.dead();
            }
         }
         else if(this.type == 503)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  _loc1_ = this.numValue;
                  _loc2_ = (this.effectMC as Enemy).life.getValue() - _loc1_;
                  if(_loc2_ < 0)
                  {
                     this.dead();
                  }
                  (this.effectMC as Enemy).hpCount(_loc1_);
                  this.timeTemp = 0;
                  HPdown.Open(_loc1_,this.effectMC.x,this.effectMC.y - this.effectMC.height);
                  this.bingshuang_C = NewLoad.XiaoGuoData.getClass("Bingshuang") as Class;
                  this.bingshuang = new this.bingshuang_C();
                  this.addChild(this.bingshuang);
               }
               if(this.speedValue > 0)
               {
                  (this.effectMC as Enemy).walk_power -= this.speedValue;
                  if((this.effectMC as Enemy).walk_power < 0)
                  {
                     (this.effectMC as Enemy).walk_power = 0;
                  }
                  this.speedValue = 0;
               }
            }
            else
            {
               (this.effectMC as Enemy).walk_power = (this.effectMC as Enemy).walk_temp;
               this.removeChild(this.bingshuang);
               this.dead();
            }
         }
         else if(this.type == 504)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  this.shandian_C = NewLoad.XiaoGuoData.getClass("Shandian") as Class;
                  this.shandian = new this.shandian_C();
                  this.addChild(this.shandian);
                  _loc1_ = this.numValue;
                  _loc2_ = (this.effectMC as Enemy).life.getValue() - _loc1_;
                  if(_loc2_ < 0)
                  {
                     this.dead();
                  }
                  (this.effectMC as Enemy).hpCount(_loc1_);
                  this.timeTemp = 0;
                  HPdown.Open(_loc1_,this.effectMC.x,this.effectMC.y - this.effectMC.height);
               }
            }
            else
            {
               this.removeChild(this.shandian);
               this.dead();
            }
         }
         else if(this.type == 505)
         {
            if(this.totalTime >= 0)
            {
               (this.effectMC as Enemy).walk_power = 0;
               this.sufu_C = NewLoad.XiaoGuoData.getClass("Shufu") as Class;
               this.sufu = new this.sufu_C();
               this.addChild(this.sufu);
            }
            else
            {
               (this.effectMC as Enemy).walk_power = (this.effectMC as Enemy).walk_temp;
               this.removeChild(this.sufu);
               this.dead();
            }
         }
         else if(this.type == 506)
         {
            if(this.totalTime >= 0)
            {
               (this.effectMC as Enemy).攻击力.setValue(0);
               this.langtou_C = NewLoad.XiaoGuoData.getClass("Langtou") as Class;
               this.langtou = new this.langtou_C();
               this.addChild(this.langtou);
            }
            else
            {
               (this.effectMC as Enemy).攻击力.setValue((this.effectMC as Enemy).att_temp);
               this.removeChild(this.langtou);
               this.dead();
            }
         }
         else if(this.type == 507)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Enemy).walk_power -= this.numValue;
                  if((this.effectMC as Enemy).walk_power < 0)
                  {
                     (this.effectMC as Enemy).walk_power = 0;
                  }
                  this.jiansu_C = NewLoad.XiaoGuoData.getClass("Jiansu") as Class;
                  this.jiansu = new this.jiansu_C();
                  this.addChild(this.jiansu);
               }
            }
            else
            {
               (this.effectMC as Enemy).walk_power = (this.effectMC as Enemy).walk_temp;
               this.removeChild(this.jiansu);
               this.dead();
            }
         }
         else if(this.type == 508)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Enemy).walk_power -= this.numValue;
                  if((this.effectMC as Enemy).walk_power < 0)
                  {
                     (this.effectMC as Enemy).walk_power = 0;
                  }
               }
            }
            else
            {
               (this.effectMC as Enemy).walk_power = (this.effectMC as Enemy).walk_temp;
               this.dead();
            }
         }
         if(this.type == 101)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  if((this.effectMC as Enemy).cengshu.length > 3)
                  {
                     (this.effectMC as Enemy).isIce = true;
                     if((this.parent as Enemy).life.getValue() > 0)
                     {
                        this.iceing_C = NewLoad.XiaoGuoData.getClass("Iceing") as Class;
                        this.iceing = new this.iceing_C();
                        this.addChild(this.iceing);
                        (this.effectMC as Enemy).skin.continuousTime = 81;
                        (this.effectMC as Enemy).skin.GoTo("被打",81);
                     }
                  }
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
               if((this.effectMC as Enemy).cengshu.length <= 0 && (this.effectMC as Enemy).isIce == true)
               {
                  (this.effectMC as Enemy).isIce = false;
                  this.iceBroken_C = NewLoad.XiaoGuoData.getClass("IceBroken") as Class;
                  this.iceBroken = new this.iceBroken_C();
                  this.effectMC.addChild(this.iceBroken);
                  this.iceBroken.gotoAndPlay(0);
                  this.iceBroken.addEventListener(Event.ENTER_FRAME,this.removeIceBroken);
               }
            }
         }
         if(this.type == 102)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  if(this.effectMC as Enemy && (this.effectMC as Enemy).cengshu.length > 0 && (this.effectMC as Enemy).className != "龙卷风")
                  {
                     (this.effectMC as Enemy).isIce = true;
                     if((this.parent as Enemy).life.getValue() > 0)
                     {
                        this.iceing_C = NewLoad.XiaoGuoData.getClass("Iceing") as Class;
                        this.iceing = new this.iceing_C();
                        this.addChild(this.iceing);
                        (this.effectMC as Enemy).skin.continuousTime = 81;
                        (this.effectMC as Enemy).skin.GoTo("被打",81);
                     }
                  }
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
               if((this.effectMC as Enemy).cengshu.length <= 0 && (this.effectMC as Enemy).isIce == true)
               {
                  (this.effectMC as Enemy).isIce = false;
                  this.iceBroken_C = NewLoad.XiaoGuoData.getClass("IceBroken") as Class;
                  this.iceBroken = new this.iceBroken_C();
                  this.effectMC.addChild(this.iceBroken);
                  this.iceBroken.gotoAndPlay(0);
                  this.iceBroken.addEventListener(Event.ENTER_FRAME,this.removeIceBroken);
               }
            }
         }
         if(this.type == 603)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  if(Boolean(this.effectMC as Enemy) && (this.effectMC as Enemy).className != "龙卷风")
                  {
                     (this.effectMC as Enemy).isIce = true;
                     if((this.parent as Enemy).life.getValue() > 0)
                     {
                        this.iceing_C = NewLoad.XiaoGuoData.getClass("Iceing") as Class;
                        this.iceing = new this.iceing_C();
                        this.addChild(this.iceing);
                        (this.effectMC as Enemy).skin.continuousTime = 54;
                        (this.effectMC as Enemy).skin.GoTo("被打",54);
                     }
                  }
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
               if((this.effectMC as Enemy).isIce == true)
               {
                  (this.effectMC as Enemy).isIce = false;
                  this.iceBroken_C = NewLoad.XiaoGuoData.getClass("IceBroken") as Class;
                  this.iceBroken = new this.iceBroken_C();
                  this.effectMC.addChild(this.iceBroken);
                  this.iceBroken.gotoAndPlay(0);
                  this.iceBroken.addEventListener(Event.ENTER_FRAME,this.removeIceBroken);
               }
            }
         }
         if(this.type == 103)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  if(this.effectMC as Enemy && (this.effectMC as Enemy).cengshu.length > 0 && (this.effectMC as Enemy).className != "龙卷风")
                  {
                     (this.effectMC as Enemy).isIce = true;
                     if((this.parent as Enemy).life.getValue() > 0)
                     {
                        this.iceing_C = NewLoad.XiaoGuoData.getClass("Iceing") as Class;
                        this.iceing = new this.iceing_C();
                        this.addChild(this.iceing);
                        (this.effectMC as Enemy).skin.continuousTime = 54;
                        (this.effectMC as Enemy).skin.GoTo("被打",54);
                     }
                  }
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
               if((this.effectMC as Enemy).cengshu.length <= 0 && (this.effectMC as Enemy).isIce == true)
               {
                  (this.effectMC as Enemy).isIce = false;
                  this.iceBroken_C = NewLoad.XiaoGuoData.getClass("IceBroken") as Class;
                  this.iceBroken = new this.iceBroken_C();
                  this.effectMC.addChild(this.iceBroken);
                  this.iceBroken.gotoAndPlay(0);
                  this.iceBroken.addEventListener(Event.ENTER_FRAME,this.removeIceBroken);
               }
            }
         }
         if(this.type == 104)
         {
            if(this.timeTemp == this.space)
            {
               if((this.parent as Enemy).life.getValue() > 0)
               {
                  (this.effectMC as Enemy).skin.continuousTime = 54;
                  (this.effectMC as Enemy).skin.GoTo("被打",54);
                  this.dead();
               }
               this.timeTemp = 0;
            }
         }
         if(this.type == 605)
         {
            if(this.totalTime >= 0)
            {
               if((this.effectMC as Enemy).yingzhiTemp == 0)
               {
                  this.dead();
               }
               if(this.timeTemp == this.space)
               {
                  this.lei_C = NewLoad.XiaoGuoData.getClass("雷之印记") as Class;
                  this.lei = new this.lei_C();
                  this.addChild(this.lei);
               }
            }
            else
            {
               this.removeChild(this.lei);
               this.dead();
            }
         }
         if(this.type == 606)
         {
            if(this.totalTime < 0)
            {
               this.dead();
            }
         }
         ++this.timeTemp;
      }
      
      private function dead() : *
      {
         this.removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.parent.removeChild(this);
         var _loc1_:int = 0;
         while(_loc1_ < (this.effectMC as Enemy).cengshu.length)
         {
            if((this.effectMC as Enemy).cengshu[_loc1_] == this)
            {
               (this.effectMC as Enemy).cengshu.splice(_loc1_,1);
            }
            _loc1_++;
         }
      }
   }
}

